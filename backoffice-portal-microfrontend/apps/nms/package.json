{"name": "nms", "version": "0.1.0", "private": true, "type": "module", "scripts": {"dev": "next dev --turbopack --port 3006", "build": "next build", "start": "next start --port 3006", "lint": "next lint", "lint:fix": "next lint --fix", "clean": "rimraf .turbo .next __tests__/coverage", "test": "vitest run", "test:watch": "vitest --watch", "test:vitest-ui": "vitest --ui --coverage", "test:view-report": "open __tests__/coverage/index.html"}, "dependencies": {"@dtbx/store": "workspace:*", "@dtbx/ui": "workspace:*", "@mui/icons-material": "^6.2.0", "@mui/material": "^6.2.0", "@mui/system": "^6.1.10", "@reduxjs/toolkit": "^2.4.0", "@tiptap/extension-image": "^2.11.5", "@tiptap/extension-link": "^2.11.5", "@tiptap/extension-placeholder": "^2.11.5", "@tiptap/pm": "^2.11.5", "@tiptap/react": "^2.11.5", "@tiptap/starter-kit": "^2.11.5", "@types/lodash": "^4.17.13", "axios": "^1.8.4", "dayjs": "^1.11.13", "formik": "^2.4.6", "jwt-decode": "^4.0.0", "lodash": "^4.17.21", "mui-tel-input": "^8.0.1", "mui-tiptap": "^1.17.1", "next": "15.2.3", "react": "19.0.0", "react-dom": "19.0.0", "react-redux": "^9.1.2", "redux": "^5.0.1", "redux-persist": "^6.0.0", "tiny-case": "^1.0.3", "yup": "^1.5.0"}, "devDependencies": {"@dtbx/eslint-config": "workspace:*", "@dtbx/typescript-config": "workspace:*", "@dtbx/vitest-config": "workspace:*", "@istanbuljs/nyc-config-typescript": "^1.0.2", "@testing-library/dom": "^10.4.0", "@testing-library/react": "^16.1.0", "@types/node": "^20.17.10", "@types/react": "19.0.12", "@types/react-dom": "19.0.4", "@vitest/coverage-istanbul": "^3.0.9", "eslint": "^9.16.0", "eslint-config-next": "15.2.3", "jsdom": "^25.0.1", "rimraf": "^6.0.1", "swc-plugin-coverage-instrument": "0.0.26", "typescript": "^5.8.2", "vitest": "^3.0.9"}}