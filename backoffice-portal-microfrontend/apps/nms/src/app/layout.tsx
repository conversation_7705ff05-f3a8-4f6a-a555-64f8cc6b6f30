'use client'
import React, { ReactNode } from 'react'
import { Box } from '@mui/material'
import { NextAppDirEmotionCacheProvider, ThemeConfig } from '@dtbx/ui/theme'
import { useAppDispatch, useAppSelector } from '@/store'
import { refreshToken } from '@dtbx/store/actions'

import { clearNotification, setSidebarCollapsed } from '@dtbx/store/reducers'
import AppProvider from '@/store/AppProvider'
import {
  AuthWrapper,
  CustomScrollbar,
  InActivity,
  LocalNotification,
} from '@dtbx/ui/components'
import { Sidebar } from '@dtbx/ui/components/Sidebar'
import { InternalNavBar } from '@dtbx/ui/components/Appbar'
import { useRouter } from 'next/navigation'

import { sidebarConfig } from './sidebar'
import { isLoggedIn } from '@dtbx/store/utils'
import '@dtbx/ui/theme/index.css'

export default function RootLayout({
  children,
}: Readonly<{
  children: ReactNode
}>) {
  return (
    <html lang="en">
      <body>
        <AppProvider>
          <NextAppDirEmotionCacheProvider options={{ key: 'mui' }}>
            <ThemeConfig>
              <CustomScrollbar>
                <InActivity isLoggedIn={isLoggedIn}>
                  <DashboardLayout>{children}</DashboardLayout>
                </InActivity>
              </CustomScrollbar>
            </ThemeConfig>
          </NextAppDirEmotionCacheProvider>
        </AppProvider>
      </body>
    </html>
  )
}

export function DashboardLayout(props: { children: React.ReactNode }) {
  const dispatch = useAppDispatch()
  const router = useRouter()
  const profile = useAppSelector((state) => state.auth.decodedToken)
  const { isSidebarCollapsed } = useAppSelector((state) => state.navigation)
  const notification = useAppSelector(
    (state) => state.notifications.localNotification
  )
  const notificationType =
    useAppSelector((state) => state.notifications.localNotificationType) ||
    'info'
  return (
    <AuthWrapper requiresAuth={true} isLoggedIn={isLoggedIn}>
      <Box sx={{ display: 'flex', flexDirection: 'row' }}>
        <Sidebar
          sidebarConfig={sidebarConfig}
          sidebarCollapsed={(val) => dispatch(setSidebarCollapsed(val))}
        />
        <Box
          sx={{
            display: 'flex',
            flexDirection: 'column',
            width: isSidebarCollapsed ? '95vw' : '88vw',
          }}
        >
          <InternalNavBar profile={profile} refreshToken={refreshToken} />
          <Box
            sx={{
              width: '100%',
              backgroundColor: '#FCFCFC',
            }}
          >
            <LocalNotification
              clearNotification={() => dispatch(clearNotification())}
              notification={notification}
              notificationType={notificationType}
            />
            {props.children}
          </Box>
        </Box>
      </Box>
    </AuthWrapper>
  )
}
