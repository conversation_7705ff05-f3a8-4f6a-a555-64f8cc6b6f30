/**
 * <AUTHOR> on 29/10/2024
 */
import { ISidebarConfigItem } from '@dtbx/ui/components/Sidebar'
import {
  NotificationIcon,
  MobileIcon,
  LMSIcon,
  ApprovalsIcon,
} from '@dtbx/ui/icons'

export const sidebarConfig: ISidebarConfigItem[] = [
  {
    id: '1',
    title: 'All Templates',
    path: '/home',
    module: 'default',
    icon: <NotificationIcon />,
    isProductionReady: false,
  },
  {
    id: '2',
    title: 'DTB Mobile',
    path: '/dtb-mobile',
    module: 'default',
    icon: <MobileIcon />,
    isProductionReady: false,
  },
  {
    id: '3',
    title: 'LMS',
    path: '/lms',
    module: 'default',
    icon: <LMSIcon />,
    isProductionReady: false,
  },
  {
    id: '4',
    title: 'Approval Requests',
    path: '/approvals',
    module: 'default',
    icon: <ApprovalsIcon />,
    isProductionReady: false,
  },
]
