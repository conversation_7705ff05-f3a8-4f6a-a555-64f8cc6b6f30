import { Button, Stack } from '@mui/material'
import { SearchRounded } from '@mui/icons-material'
import { CustomSearchInput } from '@dtbx/ui/components/Input'
import React from 'react'
import FilterListOutlinedIcon from '@mui/icons-material/FilterListOutlined'
import { CreateNotificationTemplate } from '@/app/home/<USER>'

export const ListsHeader = () => {
  return (
    <Stack
      sx={{
        py: '2vh',
        flexDirection: 'row',
        justifyContent: 'space-between',
      }}
    >
      <Stack
        sx={{
          flexDirection: 'row',
          gap: '2vw',
        }}
      >
        <Stack
          sx={{
            flexDirection: 'row',
            alignItems: 'center',
            alignContent: 'center',
          }}
        >
          <CustomSearchInput
            placeholder="Search for Template by Name"
            sx={{
              width: '33vw',
              background: '#FFFFFF',
              borderRadius: '8px 0px 0px 8px',
              mb: '10px',
              boxShadow: 'none',
              border: '1px solid #D0D5DD',
              borderRight: 'none',
              '&.MuiOutlinedInput-root fieldset': {
                border: 'none !important',
              },
            }}
          />
          <SearchRounded
            sx={{
              color: 'black',
              backgroundColor: '#EDEEEE',
              fontSize: '40px',
              padding: '10px',
              border: '1px solid #D0D5DD',
              borderLeft: 'none',
              borderRadius: '0px 8px 8px 0px',
              mb: '10px',
            }}
          />
        </Stack>
        <Button
          variant="outlined"
          startIcon={<FilterListOutlinedIcon />}
          sx={{
            border: '1px solid #D0D5DD',
          }}
        >
          Filter
        </Button>
      </Stack>
      <CreateNotificationTemplate origin="list" />
    </Stack>
  )
}
