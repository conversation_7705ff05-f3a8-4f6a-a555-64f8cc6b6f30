import {
  Paper,
  Stack,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableRow,
  Typography,
} from '@mui/material'
import {
  CustomPagination,
  CustomTableHeader,
  PaginationOptions,
} from '@dtbx/ui/components/Table'
import React, { useState } from 'react'
import { INotificationTemplate } from '@/store/interfaces'
import { formatTimestamp } from '@dtbx/store/utils'
import { MoreVertOutlined } from '@mui/icons-material'
import { getAllTemplates } from '@/store/actions/templates'
import { useAppDispatch, useAppSelector } from '@/store'
const tableHead = [
  { id: 'templateName', label: 'Template Name', alignRight: false },
  { id: 'isHtmlContent', label: 'Platform', alignRight: false },
  { id: 'pushNotificationModule', label: 'Service', alignRight: false },
  { id: 'templateSubject', label: 'Template Subject', alignRight: false },
  { id: 'dateCreated', label: 'Date Created', alignRight: false },
  { id: '', label: 'Actions', alignRight: false },
]
type ListProps = {
  data: INotificationTemplate[]
}
export const ListsTable = ({ data }: ListProps) => {
  const [order, setOrder] = useState<'asc' | 'desc'>('asc')
  const [orderBy, setOrderBy] = useState<string>('id')
  const dispatch = useAppDispatch()
  const { templatesPagination } = useAppSelector((state) => state.templates)
  const [paginationOptions, setPaginationOptions] = useState({
    page: templatesPagination.pageNumber,
    size: templatesPagination.pageSize,
    totalPages: templatesPagination.totalNumberOfPages,
  })
  /*************************start pagination handlers***************************/
  const handlePagination = async (newOptions: PaginationOptions) => {
    setPaginationOptions(newOptions) // Update parent state
    await getAllTemplates(
      dispatch,
      `page=${newOptions.page}&size=${newOptions.size}`
    )
  }
  return (
    <Paper
      sx={{
        border: '1px solid #EAECF0',
        borderRadius: '12px',
        boxShadow: 'none',
      }}
    >
      <Stack sx={{ px: '2%', py: '2vh' }}>
        <Typography variant="subtitle1">Notification Templates</Typography>
        <Typography variant="body2">Showing {data.length} templates</Typography>
      </Stack>
      <TableContainer
        component={Paper}
        sx={{
          boxShadow: 'none',
          maxHeight: '60vh',
        }}
      >
        <Table sx={{ minWidth: 650 }} aria-label="loans table" size="small">
          <CustomTableHeader
            order={order}
            orderBy={orderBy}
            headLabel={tableHead}
            showCheckbox={false}
            rowCount={10}
            numSelected={0}
            onRequestSort={() => {}}
            onSelectAllClick={() => {}}
          />
          <TableBody>
            {data.map((template) => (
              <TableRow key={template.id}>
                <TableCell>{template.templateName}</TableCell>
                <TableCell>
                  {template.pushNotificationModule ||
                    template.emailAgent?.agentName}
                </TableCell>
                <TableCell>
                  {template.isHtmlContent ? 'Email' : 'SMS'}
                </TableCell>
                <TableCell>{template.templateSubject}</TableCell>
                <TableCell>{formatTimestamp(template.dateCreated)}</TableCell>
                <TableCell>
                  <MoreVertOutlined />
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>
      <CustomPagination
        options={{
          ...paginationOptions,
          totalPages: paginationOptions.totalPages,
        }}
        handlePagination={handlePagination}
      />
    </Paper>
  )
}
