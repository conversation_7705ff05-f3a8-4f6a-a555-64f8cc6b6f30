import { Stack } from '@mui/material'
import { ListsHeader } from '@/app/tables/Header'
import { ListsTable } from '@/app/tables/List'
import { INotificationTemplate } from '@/store/interfaces'
type TemplateListsProps = {
  type: string
  data: INotificationTemplate[]
}
export const TemplateLists = ({ type, data }: TemplateListsProps) => {
  return (
    <Stack
      sx={{
        px: '2%',
      }}
    >
      <ListsHeader />
      <ListsTable data={data} />
    </Stack>
  )
}
