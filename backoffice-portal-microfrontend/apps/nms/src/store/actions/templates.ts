import { secureapi2 } from '@dtbx/store/utils'
import { Dispatch } from '@reduxjs/toolkit'
import {
  setIsLoadingNotificationEvents,
  setIsLoadingTemplates,
  setNotificationEvents,
  setNotificationTemplates,
  setTemplatesPagination,
} from '@/store/reducers'
import { setNotification } from '@dtbx/store/reducers'
import { ICreateNotificationTemplate } from '@/store/interfaces'

export const getAllTemplates = async (
  dispatch: Dispatch,
  paginationOptions: string
) => {
  try {
    dispatch(setIsLoadingTemplates(true))
    const resp = await secureapi2.get(
      `notifications/templates?${paginationOptions}`
    )
    dispatch(setIsLoadingTemplates(false))
    dispatch(setNotificationTemplates(resp.data.data.data))
    const { pageNumber, pageSize, totalElements, totalNumberOfPages } =
      resp.data.data
    dispatch(
      setTemplatesPagination({
        pageNumber,
        pageSize,
        totalElements,
        totalNumberOfPages,
      })
    )
  } catch (e) {
    console.error('Error fetching notification templates: ', e)
    const message = (e as Error).message
    dispatch(
      setNotification({
        message,
        type: 'error',
      })
    )
    dispatch(setIsLoadingTemplates(false))
  }
}

export const getNotificationEvents = async (dispatch: Dispatch) => {
  try {
    dispatch(setIsLoadingNotificationEvents(true))
    const response = await secureapi2.get('notifications/events')
    dispatch(setNotificationEvents(response.data))
    dispatch(setIsLoadingNotificationEvents(false))
  } catch (e) {
    const message = (e as Error).message
    dispatch(
      setNotification({
        message,
        type: 'error',
      })
    )
    dispatch(setIsLoadingNotificationEvents(false))
  }
}

export const createNotificationTemplate = async (
  payload: ICreateNotificationTemplate,
  dispatch: Dispatch
) => {
  type ITemplate = {
    templateName: string
    templateSubject: string
    templateContent: string
    templateDescription: string
    eventId: string
    htmlContent: boolean | string
  }
  try {
    dispatch(setIsLoadingNotificationEvents(true))
    // Remove placeholders
    const {
      placeholders,
      ...modifiedPayload
    }: Partial<ITemplate> & ICreateNotificationTemplate = payload
    // Unified templateSubject
    let templateSubject: string =
      modifiedPayload.smsTemplateSubject || modifiedPayload.emailTemplateSubject

    // Unified templateContent
    let templateContent: string =
      modifiedPayload.smsTemplateContent || modifiedPayload.emailTemplateContent

    // Assign values
    modifiedPayload.templateSubject = templateSubject
    modifiedPayload.templateContent = templateContent

    // Delete unnecessary fields
    const {
      smsTemplateSubject,
      emailTemplateSubject,
      smsTemplateContent,
      emailTemplateContent,
      ...apiPayload
    } = modifiedPayload

    let data = apiPayload as ITemplate
    // Convert htmlContent to a boolean as expected by the api
    data.htmlContent === 'email'
      ? (data.htmlContent = true)
      : (data.htmlContent = false)

    const response = await secureapi2.post('/notifications/templates', data)
    const message = response.data.message
    dispatch(setNotification({ message: message, type: 'success' }))
  } catch (e) {
    const message = (e as Error).message
    dispatch(
      setNotification({
        message,
        type: 'error',
      })
    )
  } finally {
    dispatch(setIsLoadingNotificationEvents(false))
  }
}
