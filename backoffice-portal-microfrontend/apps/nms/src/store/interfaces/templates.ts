export interface INotificationTemplate {
  id: string
  templateName: string
  templateSubject: string
  templateContent: string
  templateDescription: string
  isHtmlContent: boolean
  emailAgent: {
    agentName: string
    emailAddress: string
  }
  smsSenderId: string
  pushNotificationModule: string
  dateCreated: string
}
export interface ICreateNotificationTemplate {
  templateName: string
  smsTemplateSubject: string
  emailTemplateSubject: string
  smsTemplateContent: string
  emailTemplateContent: string
  templateDescription: string
  eventId: string
  htmlContent: string
  placeholders: string[]
}
export interface IPagination {
  pageNumber: number
  pageSize: number
  totalElements: number
  totalNumberOfPages: number
}
export interface INotificationEvent {
  id: string
  eventType: string
  eventName: string
  settings: IEventSettings[]
  subscribers: IEventSubscribers[]
  placeHolders: string[]
  templates: ITemplate[]
}
interface IEventSettings {
  id: string
  notificationType: string
  deliveryMode: string
  templateName: string
}
interface IEventSubscribers {
  id: string
  recipient: string
  deliveryMode: string
}
export interface ITemplate {
  id: string
  templateName: string
  templateSubject: string
  templateContent: string
  templateDescription: string
  htmlContent: boolean
}
