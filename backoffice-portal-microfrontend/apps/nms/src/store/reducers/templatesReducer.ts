import { createSlice, PayloadAction } from '@reduxjs/toolkit'
import {
  INotificationEvent,
  INotificationTemplate,
  IPagination,
} from '@/store/interfaces'
import { boolean } from 'yup'

interface INewTemplateFormData {
  platform: string
  triggerEvent: string
  recipient: string
  templateName: string
  modeOfDelivery: {
    sms: boolean
    email: boolean
  }
  smsBody: string
  emailSubject: string
  emailBody: string
}

interface ITemplatesInitialState {
  isLoadingTemplates: boolean
  notificationTemplates: INotificationTemplate[]
  templatesPagination: IPagination
  isLoadingNotificationEvents: boolean
  notificationEvents: INotificationEvent[]
  newTemplateFormData: INewTemplateFormData
  focusedField: string
}
const initialState: ITemplatesInitialState = {
  isLoadingTemplates: false,
  notificationTemplates: [],
  templatesPagination: {} as IPagination,
  isLoadingNotificationEvents: false,
  notificationEvents: [],
  newTemplateFormData: {
    platform: '',
    triggerEvent: '',
    recipient: '',
    templateName: '',
    modeOfDelivery: {
      sms: false,
      email: false,
    },
    smsBody: '',
    emailSubject: '',
    emailBody: '',
  },
  focusedField: '',
}
const templatesSlice = createSlice({
  name: 'notif-templates',
  initialState,
  reducers: {
    setIsLoadingTemplates: (state, action: PayloadAction<boolean>) => {
      state.isLoadingTemplates = action.payload
    },
    setNotificationTemplates: (
      state,
      action: PayloadAction<INotificationTemplate[]>
    ) => {
      state.notificationTemplates = action.payload
    },
    setTemplatesPagination: (state, action: PayloadAction<IPagination>) => {
      state.templatesPagination = action.payload
    },
    setIsLoadingNotificationEvents: (state, action: PayloadAction<boolean>) => {
      state.isLoadingNotificationEvents = action.payload
    },
    setNotificationEvents: (
      state,
      action: PayloadAction<INotificationEvent[]>
    ) => {
      state.notificationEvents = action.payload
    },
    setFocusedField: (state, action) => {
      state.focusedField = action.payload
    },
  },
})
export const {
  setIsLoadingTemplates,
  setNotificationTemplates,
  setTemplatesPagination,
  setIsLoadingNotificationEvents,
  setNotificationEvents,
  setFocusedField,
} = templatesSlice.actions
export default templatesSlice.reducer
