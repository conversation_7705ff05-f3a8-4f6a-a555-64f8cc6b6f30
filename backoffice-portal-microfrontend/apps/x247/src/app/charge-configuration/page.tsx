'use client'
import { <PERSON>, Divider, Stack, Typography } from '@mui/material'
import React, { useEffect, useState } from 'react'
import { AntTab, AntTabs, TabPanel } from '@dtbx/ui/components/Tabs'
import TariffPage from './tariffs/page'
import ApprovalRequestsPage from './approval-requests/page'
import { getApprovals } from '@/store/actions'
import { useAppDispatch } from '@/store'
import { useAppSelector } from '@/store'
import { CompressOutlined } from '@mui/icons-material'

const ChargeConfigurationListPage = () => {
  const [value, setValue] = useState<number>(0)
  const handleChange = (event: React.SyntheticEvent, newValue: number) => {
    setValue(newValue)
  }

  const dispatch = useAppDispatch()
  const { approvalRequestsCount } = useAppSelector(
    (state) => state.approvalRequests
  )
  useEffect(() => {
    getApprovals(dispatch, `?channel=DBP&module=Tariffs&page=1&size=10`)
  }, [])

  return (
    <Stack
      sx={{
        backgroundColor: '#FCFCFC',
      }}
    >
      <AntTabs
        value={value}
        onChange={handleChange}
        aria-label="ant example"
        sx={{
          marginLeft: '2%',
          marginTop: '-1.5rem',
        }}
      >
        <AntTab
          label={`Tariffs`}
          sx={{
            marginBottom: '-0.625rem',
            fontSize: '1rem',
            fontWeight: 500,
            color: '#2A3339',
          }}
          iconPosition="end"
        />
        <AntTab
          label={`Approval Requests `}
          sx={{
            marginBottom: '-0.625rem',
            fontSize: '1rem',
            fontWeight: 500,
            color: '#2A3339',
          }}
          icon={
            <Chip
              label={
                <Typography
                  variant="label3"
                  sx={{
                    textAlign: 'center',
                    fontWeight: 500,
                  }}
                >
                  {approvalRequestsCount}
                </Typography>
              }
              sx={{
                borderRadius: '12px',
                minWidth: '39px',
                height: '22px',
                border: '1px solid  #EAECF0',
                background: ' #F9FAFB',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',

                padding: '2px 8px',
              }}
            />
          }
          iconPosition="end"
        />
        {/* <AntTab
          label={`Payment Services`}
          sx={{
            marginBottom: '-0.625rem',
            fontSize: '1rem',
            fontWeight: 500,
            color: '#2A3339',
          }}
        /> */}
      </AntTabs>
      <Divider sx={{ marginBottom: '0.5rem' }} />
      <TabPanel value={value} index={0}>
        <></>
      </TabPanel>
      <TabPanel value={value} index={0}>
        <TariffPage />
      </TabPanel>
      <TabPanel value={value} index={1}>
        <ApprovalRequestsPage />
      </TabPanel>
    </Stack>
  )
}

export default ChargeConfigurationListPage
