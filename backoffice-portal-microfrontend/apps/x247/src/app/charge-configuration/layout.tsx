'use client'

import React from 'react'
import { Stack, Typography } from '@mui/material'
import { ChargesIcon } from '@dtbx/ui/icons'

export default function ChargeConfigurationLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <Stack>
      <Stack
        sx={{
          marginLeft: '1.5%',
          marginTop: '0.2%',
          flexDirection: 'row',
          justifyContent: 'flex-start',
          alignItems: 'center',
          gap: '2%',
          padding: '1.5%',
          backgroundColor: '#FCFCFC',
          borderRight: '1px solid #EAECF0',
        }}
      >
        <ChargesIcon width="28" height="26" />
        <Typography variant="h5">Charge Configuration</Typography>
      </Stack>
      <Stack
        sx={{
          flexGrow: 1,
          overflowY: 'auto',
        }}
      >
        {children}
      </Stack>
    </Stack>
  )
}
