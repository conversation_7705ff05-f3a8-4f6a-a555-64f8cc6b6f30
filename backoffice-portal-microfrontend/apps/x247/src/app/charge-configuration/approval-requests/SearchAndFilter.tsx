'use client'
import { useState } from 'react'
import FilterListOutlinedIcon from '@mui/icons-material/FilterListOutlined'
import { CustomSearchInput } from '@dtbx/ui/components/Input'
import { CloseRounded, SearchRounded } from '@mui/icons-material'
import { Box, Button, Typography, Stack } from '@mui/material'
import {
  DateRangePicker,
  DropDownMenu,
  DropDownMenuRadio,
} from '@dtbx/ui/components/DropDownMenus'
import dayjs from 'dayjs'

function SearchAndFilter() {
  const [openFilters, setOpenFilters] = useState<boolean>(false)

  const handleDateRangeFilterApply = (date: {
    start: dayjs.Dayjs
    end: dayjs.Dayjs
  }) => {
    // setDateRange(date)
    // setPage(1)
  }

  function handleClickFilter() {
    // Add logic for the filter button click
  }

  return (
    <Stack spacing={2}>
      <Box sx={{ display: 'flex', gap: '1rem' }}>
        {/* Search input */}
        <CustomSearchInput
          placeholder="Search for approval request"
          width="33vw"
          sx={{
            background: '#FFFFFF',
            '& fieldset': {
              border: '1px solid #D0D5DD !important',
            },
          }}
          endAdornment={
            <Box
              sx={{
                backgroundColor: '#EDEEEE',
                width: '38px',
                height: '100%',
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
                position: 'absolute',
                right: 0,
                top: 0,
                borderTopRightRadius: '4px',
                borderBottomRightRadius: '4px',
              }}
            >
              <SearchRounded
                sx={{
                  color: '#555C61',
                }}
              />
            </Box>
          }
        />

        {/* Filter button */}
        <Button
          variant="outlined"
          sx={{ border: '1px solid #D0D5DD', width: '8.1rem', height: '40px' }}
          startIcon={<FilterListOutlinedIcon />}
          onClick={handleClickFilter}
        >
          Filter
        </Button>
      </Box>
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'row',
          justifyContent: 'flex-start',
          gap: '16px',
        }}
      >
        <Button
          onClick={() => {
            // setOpenFilterBar(false)
            // setPage(1)
            // setStatus('')
            // setMakerName('')
            // setRequestType('')
            // setDateRange(null)
            // fetchApprovals()
          }}
          sx={{
            minWidth: '131px',
            height: '40px',
            gap: '0px',
            boxShadow: '0px 1px 2px 0px rgba(16, 24, 40, 0.05)',
          }}
          endIcon={<CloseRounded sx={{ color: 'text.primary' }} />}
        >
          <Typography>Clear All</Typography>
        </Button>
        {/* <DateRangePicker
          onApplyDateRange={handleDateRangeFilterApply}
          buttonText="Date Created"
        />
        <DropDownMenuRadio
          menuItems={['Approved', 'Pending', 'Rejected']}
          // onClick={handleStatusFilter}
          buttonText={'Status'}
        /> */}
      </Box>
    </Stack>
  )
}

export default SearchAndFilter
