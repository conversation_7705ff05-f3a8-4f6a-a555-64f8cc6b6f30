'use client'
import React, { useEffect, useState } from 'react'
import SearchAndFilter from './SearchAndFilter'
import ApprovalRequestsTable from './ApprovalRequestsTable'
import { Stack } from '@mui/material'
import { getApprovals } from '@/store/actions'
import { useAppDispatch } from '@/store'


function ApprovalRequestsPage() {

  const dispatch = useAppDispatch()
  useEffect(() => {
    getApprovals(dispatch, `?channel=DBP&module=Tariffs&page=1&size=10`)
  }, [])
  return (
    <Stack spacing="1.3rem" sx={{ padding: '2.1rem 2.6rem' }}>
      <SearchAndFilter />
      <ApprovalRequestsTable />
    </Stack>
  )
}

export default ApprovalRequestsPage
