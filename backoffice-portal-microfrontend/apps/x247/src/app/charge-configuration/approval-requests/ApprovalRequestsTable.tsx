import { useCustomRouter } from '@dtbx/ui/hooks'
import { TariffsMoreMenu } from '../TariffsMoreMenu'
import { IHeadCell } from '@dtbx/store/interfaces'
import ApprovalRequestDrawer from '../tariffs/tariff/ApprovalRequestDrawer'
import {
  Box,
  Paper,
  Stack,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableRow,
  Typography,
} from '@mui/material'
import {
  CustomPagination,
  CustomTableHeader,
  PaginationOptions,
} from '@dtbx/ui/components/Table'
import React, { useEffect, useState } from 'react'
import { useAppDispatch, useAppSelector } from '@/store'
import { setSelectedApprovalRequest } from '@/store/reducers'
import { Dispatch } from 'redux'
import {
  CustomerStatusChip,
  CustomActiveBrokerChip,
} from '@dtbx/ui/components/Chip'
import { Status } from '@/store/interfaces'
import { getApprovals } from '@/store/actions'

const headersList: IHeadCell[] = [
  {
    id: 'request-type',
    label: 'Request type',
    alignCenter: false,
    alignRight: false,
  },
  {
    id: 'module',
    label: 'Module',
    alignCenter: false,
    alignRight: false,
  },
  {
    id: 'status',
    label: 'Status',
    alignCenter: false,
    alignRight: false,
  },
  {
    id: 'maker',
    label: 'Maker',
    alignCenter: false,
    alignRight: false,
  },
  {
    id: 'maker-timestamp',
    label: 'Maker timestamp',
    alignCenter: false,
    alignRight: false,
  },
  {
    id: 'actions',
    label: 'Actions',
    alignCenter: false,
    alignRight: false,
  },
]
const renderStatusChip = (status: Status | undefined) => {
  switch (status) {
    case 'APPROVED':
      return <CustomerStatusChip label="APPROVED" />
    case 'REJECTED':
      return <CustomerStatusChip label="REJECTED" />
    case 'PENDING':
      return <CustomerStatusChip label="PENDING" />
    default:
      return
  }
}
function ApprovalRequestsTable() {
  const { approvalRequests, approvalRequestResponse } = useAppSelector(
    (state) => state.approvalRequests
  )

  const router = useCustomRouter()
  const [drawerOpen, setDrawerOpen] = useState(false)

  const [paginationOptions, setPaginationOptions] = useState({
    page: approvalRequestResponse.pageNumber,
    size: approvalRequestResponse.pageSize,
    totalPages: approvalRequestResponse.totalNumberOfPages,
  })
  /*************************start pagination handlers***************************/
  const handlePagination = async (newOptions: PaginationOptions) => {
    setPaginationOptions(newOptions)
    // Call API to get new data
    await getApprovals(
      dispatch,
      `?channel=DBP&module=Tariffs&page=${newOptions.page}&size=${newOptions.size}`
    )
  }
  /*************************end pagination handlers**************************/

  const dispatch = useAppDispatch()

  const handleOpenDrawer = () => {
    // dispatch(setSelectedApprovalRequest())
    setDrawerOpen(true)
  }

  const handleCloseDrawer = () => {
    setDrawerOpen(false)
  }

  return (
    <Paper
      sx={{
        boxShadow:
          '0px 1px 3px 0px rgba(16, 24, 40, 0.10), 0px 1px 2px 0px rgba(16, 24, 40, 0.06)',
        border: '1px solid #EAECF0',
      }}
    >
      {/* Table title */}
      <Stack
        sx={{
          borderBottom: '1px solid #E3E4E4',
          padding: '1rem 1.5rem 0.75rem 1rem',
        }}
      >
        <Typography
          sx={{ fontSize: '1.1rem', fontWeight: '700', color: '#2A3339' }}
        >
          Approval requests
        </Typography>
        <Typography sx={{ fontSize: '0.75rem', color: '#555C61' }}>
          Showing {approvalRequests?.length} approval requests{' '}
          {/* Make the count dynamic */}
        </Typography>
      </Stack>

      {/* Table section */}
      <TableContainer
        component={Paper}
        elevation={0}
        sx={{
          boxShadow: 'none',
        }}
      >
        <Table
          sx={{ minWidth: 650 }}
          aria-label="approval requests table"
          size="medium"
        >
          {/* Table headers */}
          <CustomTableHeader
            order={'asc'}
            orderBy={'id'}
            headLabel={headersList}
            rowCount={10}
            numSelected={0}
            onRequestSort={() => {}}
          />

          {/* Table content */}
          <TableBody>
            {approvalRequests.map((request) => (
              <TableRow
                key={request.id}
                onClick={() => dispatch(setSelectedApprovalRequest(request))}
              >
                <TableCell>
                  <Box
                    sx={{
                      width: 'fit-content',
                      backgroundColor: '#F3F5F5',
                      borderRadius: '1em',
                      padding: '2px 8px',
                      display: 'flex',
                      justifyContent: 'center',
                      alignItems: 'center',
                    }}
                  >
                    <Typography
                      sx={{
                        color: '#2A3339',
                        fontSize: '0.75rem',
                        fontWeight: '500',
                      }}
                    >
                      {request.makerCheckerType.name}
                    </Typography>
                  </Box>
                </TableCell>
                <TableCell>
                  <Typography variant="body2">
                    {request.makerCheckerType.module}
                  </Typography>
                </TableCell>
                <TableCell>
                  <Typography variant="body2">
                    {renderStatusChip(request.status as Status)}
                  </Typography>
                </TableCell>
                <TableCell>
                  <Typography variant="body2">{request.maker}</Typography>
                </TableCell>
                <TableCell>
                  <Typography variant="body2">{request.dateCreated}</Typography>
                </TableCell>
                <TableCell>
                  <Stack direction="row" spacing={1}>
                    <Typography
                      variant="body2"
                      sx={{ cursor: 'pointer', color: 'primary.main' }}
                    >
                      <TariffsMoreMenu
                        onClick={handleOpenDrawer}
                        //   () =>
                        //     // Add route to specific approval request
                        //     router.push('')
                        //   // handleOpenDrawer
                        // }
                        label="Review"
                      />
                    </Typography>
                  </Stack>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>
      <CustomPagination
        options={{
          ...paginationOptions,
          totalPages: approvalRequestResponse.totalNumberOfPages,
        }}
        handlePagination={handlePagination}
      />
      <ApprovalRequestDrawer
        drawerOpen={drawerOpen}
        onClose={handleCloseDrawer}
        title="Approval Request"
        description="Review and approve changes."
      />
    </Paper>
  )
}

export default ApprovalRequestsTable
