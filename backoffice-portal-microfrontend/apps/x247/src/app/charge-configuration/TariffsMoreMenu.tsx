import React, { useState } from 'react'
import { handleDiff } from '@dtbx/store/utils'
import {
  Box,
  Button,
  Paper,
  Stack,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Typography,
} from '@mui/material'
import { IDiffValues, Status } from '@/store/interfaces'
import { Dialog } from '@dtbx/ui/components/Overlay'
import { IHeadCell } from '@dtbx/store/interfaces'
import { CustomTableHeader } from '@dtbx/ui/components/Table'

export type Label = 'View' | 'Edit' | 'Review' | 'Save changes'
interface ITariffViewButtonProps {
  label: Label
  onClick: () => void
  visible?: boolean
  disabled?: boolean
}

interface ITariffStatusModalProps {
  label?: string
  name: string
  status: Status
  disabled?: boolean
  visible?: boolean
}

export const TariffsMoreMenu: React.FC<ITariffViewButtonProps> = ({
  label,
  onClick,
  visible = true,
  disabled = false,
}) => {
  if (!visible) return null

  return (
    <Button
      onClick={onClick}
      disabled={disabled}
      variant="outlined"
      sx={{
        padding: label === 'Save changes' ? '13px 60px' : '9px 30px',
        borderRadius: '7px',
        textTransform: 'none',
        border: label === 'Save changes' ? 'none' : '1px solid #AAADB0',
        color: label === 'Save changes' ? '#FFFF' : '#555C61',
        backgroundColor: label === 'Save changes' ? ' #BDC0C3' : '#FFFF',
        fontWeight: 400,
        width: '100%',
        maxWidth: {
          xs: '80vw',
          sm: '60vw',
          md: '5vw',
        },
        height: '3vh',
        textWrap: 'noWrap',
        transition: 'all 0.3s ease',
        '&:hover': {
          color: label === 'Save changes' ? '#FFFF' : '#555C61',
          backgroundColor: label === 'Save changes' ? ' #BDC0C3' : '#FFFF',
          border: label === 'Save changes' ? 'none' : '1px solid #AAADB0',
          transform: 'scale(1.05)',
        },
      }}
    >
      {label}
    </Button>
  )
}

interface ConfigurationComparisonTableProps {
  data: IDiffValues[]
}

// Tariff Status Modal Button
const TariffStatusModal: React.FC<ITariffStatusModalProps> = ({
  name,
  status,
  label,
}) => {
  const [open, setOpen] = React.useState(false)

  const reasonsForDeactivating: string[] = [
    'Regulatory Compliance',
    'Service Integration Issue',
    'Operational Changes',
    'Fraud Mitigation',
    'Other',
  ]

  const reasonsForActivating: string[] = [
    'Reinstatement',
    'Resolved Issues',
    'Other',
  ]
  const reasons =
    status === 'ACTIVE' ? reasonsForDeactivating : reasonsForActivating

  // const handleStatusUpdate = async (reasons: string[]) => {
  //   const comments = reasons.join(', ')
  //   if (status === 'Active') {
  //     if (HasAccessToRights(['SUPER_DEACTIVATE_TARIFF'])) {
  //       await changeTariffStatus(id, 'deactivate', dispatch, comments)
  //     } else {
  //       await deactivateTariff(id, 'make', dispatch, comments)
  //     }
  //   } else {
  //     if (HasAccessToRights(['SUPER_ACTIVATE_TARIFF'])) {
  //       await changeTariffStatus(id, 'activate', dispatch, comments)
  //     } else {
  //       await activateTariff(id, 'make', dispatch, comments)
  //     }
  //   }
  //   setOpen(false)
  // }

  return (
    <Box>
      <Button
        sx={{
          border: '1px solid #D0D5DD',
          borderRadius: '6px',
          padding: '9px 28px',
          backgroundColor: '#E3E4E4',
          textWrap: 'nowrap',
        }}
        variant="outlined"
        onClick={() => {
          setOpen((prev) => !prev)
        }}
      >
        <Typography
          sx={{
            color: '#555C61',
            fontSize: '0.875rem',
            fontWeight: 500,
          }}
        >
          {status === 'ACTIVE' ? `Deactivate ${name}` : `Activate ${name}`}
        </Typography>
      </Button>

      <Dialog
        buttonText={status === 'ACTIVE' ? 'Deactivate' : 'Activate'}
        title={status === 'ACTIVE' ? `Deactivate ${name}` : `Activate ${name}`}
        open={open}
        descriptionText={`${name} will be ${status === 'ACTIVE' ? 'deactivated' : 'activated'}.
        Are you sure you want to ${status === 'ACTIVE' ? 'deactivate' : 'activate'} ${name}?`}
        setOpen={setOpen}
        buttonProps={{
          color: status === 'ACTIVE' ? '#EB0045' : '#12B76A',
        }}
        reasons={reasons}
        onClick={() => {}}
      />
    </Box>
  )
}

export default TariffStatusModal

export const ConfigurationComparisonTable: React.FC<
  ConfigurationComparisonTableProps
> = ({ data }) => {
  const headerList: IHeadCell[] = [
    {
      id: 'field',
      label: 'Field',
      alignCenter: false,
      alignRight: false,
    },
    {
      id: 'previousData',
      label: 'Previous Data',
      alignCenter: false,
      alignRight: false,
    },
    {
      id: 'updatedData',
      label: 'Updated Data',
      alignCenter: false,
      alignRight: false,
    },
  ]

  const renderValue = (value: any) => {
    if (Array.isArray(value)) {
      // Handle arrays (e.g., feeTiers)
      return value.map((item, index) => (
        <span key={index}>{JSON.stringify(item)}</span>
      ))
    } else if (typeof value === 'object' && value !== null) {
      // Handle objects (e.g., exciseDuty, value, etc.)
      return Object.entries(value)
        .filter(([key]) => key !== 'feeTiers') // Exclude feeTiers from rendering here
        .map(([key, val]) => `${key}: ${val}`)
        .join(', ')
    } else {
      // Handle primitive values
      return value || 'null'
    }
  }

  return (
    <Paper
      sx={{
        boxShadow:
          '0px 1px 3px 0px rgba(16, , 24, 40, 0.10), 0px 1px 2px 0px rgba(16, 24, 40, 0.06)',
      }}
    >
      <TableContainer
        component={Paper}
        elevation={0}
        sx={{
          boxShadow: 'none',
          width: '100%',
        }}
      >
        <Table
          sx={{ minWidth: 350 }}
          aria-label="designations table"
          size="small"
        >
          <CustomTableHeader
            order={'asc'}
            orderBy={'id'}
            headLabel={headerList}
            rowCount={10}
            numSelected={0}
            onRequestSort={() => {}}
          />
          <TableBody>
            {data &&
              data.map((row) => {
                const { field, oldValue, newValue } = row
                return (
                  <TableRow hover key={`${Date.now()}-${field}`}>
                    <TableCell>
                      <Stack direction="column">
                        <Typography
                          variant="subtitle2"
                          sx={{ color: '#555C61' }}
                        >
                          {field}
                        </Typography>
                      </Stack>
                    </TableCell>

                    <TableCell>
                      <Typography
                        variant="body2"
                        sx={{
                          backgroundColor: ' #FEE4E2',
                          color: ' #2A3339',
                          px: 2.5,
                          py: 0.5,
                          borderRadius: '16px',
                          display: 'flex',
                          justifyContent: 'center',
                          alignItems: 'center',
                          textDecorationLine: 'line-through',
                          textDecorationThickness: '1px',
                        }}
                      >
                        {renderValue(oldValue)}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Typography
                        sx={{
                          backgroundColor: '#DCFAE6',
                          color: '#2A3339',
                          px: 2.5,
                          py: 0.5,
                          borderRadius: '16px',
                          display: 'flex',
                          alignItems: 'center',
                        }}
                        variant="body2"
                      >
                        {renderValue(newValue)}
                      </Typography>
                    </TableCell>
                  </TableRow>
                )
              })}
          </TableBody>
        </Table>
      </TableContainer>
    </Paper>
  )
}

export const ExciseLimitsDisplay: React.FC = () => {
  const [limits, setLimits] = useState([
    { type: 'Excise Duty', value: '15%' },
    { type: 'Daily Limit ', value: 'KES 999,999' },
    { type: 'Frequency Limit', value: '15' },
    { type: 'Per Transaction Lower Limit ', value: 'KES 10' },
    { type: 'Per Transaction Higher Limit', value: 'KES 999,999' },
  ])

  return (
    <Stack sx={{ width: '100%' }}>
      <Stack sx={{ p: '1%' }} direction="row" gap={2}>
        <Box sx={{ mb: '0.5rem' }}>
          <Typography
            fontWeight="medium"
            sx={{ fontSize: '0.85rem', marginLeft: '1rem' }}
          >
            Excise Duty and Limits
          </Typography>
        </Box>
      </Stack>

      <TableContainer
        sx={{
          border: '0.1px solid rgba(189, 189, 189, 0.6)',
          borderRadius: '0.5rem',
          boxShadow:
            '0px 1px 3px 0px rgba(16, 24, 40, 0.10), 0px 1px 2px 0px rgba(16, 24, 40, 0.06)',
          overflow: 'hidden',
        }}
      >
        <Table sx={{ width: '100%' }}>
          <TableHead>
            <TableRow>
              <TableCell
                sx={{
                  border: '0.1px solid rgba(189, 189, 189, 0.6)',
                  width: '50%',
                  height: '2.6rem',
                  padding: '0.5rem',
                  background: '#F9FAFB',
                }}
              >
                <Typography
                  sx={{
                    fontWeight: 550,
                    marginLeft: '1rem',
                  }}
                >
                  Field
                </Typography>
              </TableCell>

              <TableCell
                sx={{
                  border: '0.1px solid rgba(189, 189, 189, 0.6)',
                  width: '50%',
                  height: '2.6rem',
                  padding: '0.5rem',
                  background: '#F9FAFB',
                }}
              >
                <Typography
                  sx={{
                    fontWeight: 550,
                    marginLeft: '1rem',
                  }}
                >
                  Data
                </Typography>
              </TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {limits.map((limit, rowIndex) => {
              return (
                <TableRow key={rowIndex}>
                  <TableCell
                    sx={{
                      border: '0.1px solid rgba(189, 189, 189, 0.6)',
                      height: '2.6rem',
                      padding: '0.5rem',
                      '&:hover': {
                        backgroundColor: '#F9FAFB',
                        cursor: 'pointer',
                      },
                    }}
                  >
                    <Typography
                      sx={{
                        fontWeight: 550,
                        marginLeft: '1rem',
                      }}
                    >
                      {limit.type}
                    </Typography>
                  </TableCell>
                  <TableCell
                    sx={{
                      border: '0.1px solid rgba(189, 189, 189, 0.6)',
                      height: '2.6rem',
                      padding: '0.5rem',
                      '&:hover': {
                        backgroundColor: '#F9FAFB',
                        cursor: 'pointer',
                      },
                    }}
                  >
                    <Typography
                      sx={{
                        fontWeight: 550,
                        marginLeft: '1rem',
                      }}
                    >
                      {limit.value}
                    </Typography>
                  </TableCell>
                </TableRow>
              )
            })}
          </TableBody>
        </Table>
      </TableContainer>
    </Stack>
  )
}

export const TierInfoDisplay: React.FC<{ data: any[] }> = ({ data }) => {
  // Filter data to include only items with feeTiers
  const tieredData =
    data && data.filter((item) => item?.newValue?.feeTiers?.length)

  if (tieredData && tieredData.length === 0) {
    return null
  }

  return (
    <Stack sx={{ width: '100%' }}>
      {tieredData &&
        tieredData.map((item, index) => {
          const { feeTiers } = item.newValue

          return (
            <Stack key={index} sx={{ p: '1%' }}>
              <Typography fontWeight="medium" sx={{ fontSize: '0.85rem' }}>
                Tiers
              </Typography>

              <TableContainer
                sx={{
                  border: '0.1px solid rgba(189, 189, 189, 0.6)',
                  borderRadius: '0.5rem',
                  boxShadow:
                    '0px 1px 3px 0px rgba(16, 24, 40, 0.10), 0px 1px 2px 0px rgba(16, 24, 40, 0.06)',
                  overflow: 'hidden',
                }}
              >
                <Table sx={{ width: '100%' }}>
                  <TableHead>
                    <TableRow>
                      <TableCell
                        sx={{
                          border: '0.1px solid rgba(189, 189, 189, 0.6)',
                          width: '33%',
                          height: '2.6rem',
                          padding: '0.5rem',
                          background: '#F9FAFB',
                        }}
                      >
                        <Typography
                          sx={{
                            fontWeight: 550,
                            marginLeft: '1rem',
                          }}
                        >
                          Minimum Threshold
                        </Typography>
                      </TableCell>
                      <TableCell
                        sx={{
                          border: '0.1px solid rgba(189, 189, 189, 0.6)',
                          width: '33%',
                          height: '2.6rem',
                          padding: '0.5rem',
                          background: '#F9FAFB',
                        }}
                      >
                        <Typography
                          sx={{
                            fontWeight: 550,
                            marginLeft: '1rem',
                          }}
                        >
                          Maximum Threshold
                        </Typography>
                      </TableCell>
                      <TableCell
                        sx={{
                          border: '0.1px solid rgba(189, 189, 189, 0.6)',
                          width: '33%',
                          height: '2.6rem',
                          padding: '0.5rem',
                          background: '#F9FAFB',
                        }}
                      >
                        <Typography
                          sx={{
                            fontWeight: 550,
                            marginLeft: '1rem',
                          }}
                        >
                          Tier Fee
                        </Typography>
                      </TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {feeTiers?.map((tier: any, rowIndex: number) => (
                      <TableRow key={rowIndex}>
                        <TableCell
                          sx={{
                            border: '0.1px solid rgba(189, 189, 189, 0.6)',
                            height: '2.6rem',
                            padding: '0.5rem',
                            '&:hover': {
                              backgroundColor: '#F9FAFB',
                              cursor: 'pointer',
                            },
                          }}
                        >
                          <Typography
                            sx={{
                              fontWeight: 550,
                              marginLeft: '1rem',
                            }}
                          >
                            {tier.minimum}
                          </Typography>
                        </TableCell>
                        <TableCell
                          sx={{
                            border: '0.1px solid rgba(189, 189, 189, 0.6)',
                            height: '2.6rem',
                            padding: '0.5rem',
                            '&:hover': {
                              backgroundColor: '#F9FAFB',
                              cursor: 'pointer',
                            },
                          }}
                        >
                          <Typography
                            sx={{
                              fontWeight: 550,
                              marginLeft: '1rem',
                            }}
                          >
                            {tier.maximum}
                          </Typography>
                        </TableCell>
                        <TableCell
                          sx={{
                            border: '0.1px solid rgba(189, 189, 189, 0.6)',
                            height: '2.6rem',
                            padding: '0.5rem',
                            '&:hover': {
                              backgroundColor: '#F9FAFB',
                              cursor: 'pointer',
                            },
                          }}
                        >
                          <Typography
                            sx={{
                              fontWeight: 550,
                              marginLeft: '1rem',
                            }}
                          >
                            {tier.feeAmount}
                          </Typography>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            </Stack>
          )
        })}
    </Stack>
  )
}
