'use client'
import type React from 'react'
import { useState, useEffect } from 'react'
import {
  <PERSON>,
  <PERSON><PERSON>,
  DialogT<PERSON>le,
  <PERSON><PERSON><PERSON>,
  <PERSON>er,
  FormControlLabel,
  FormGroup,
  IconButton,
  Stack,
  Step,
  StepButton,
  StepConnector,
  stepConnectorClasses,
  <PERSON>per,
  styled,
  Text<PERSON>ield,
  Typography,
} from '@mui/material'
import AddIcon from '@mui/icons-material/Add'
import { Close } from '@mui/icons-material'
import ArrowBackIcon from '@mui/icons-material/ArrowBack'

import {
  GBPIcon,
  EURIcon,
  KESIcon,
  USDIcon,
  TarrifIcon,
  PaymentService,
  NotificationTarrifIcon,
  BIFIcon,
  TZSIcon,
  UGXIcon,
  ZARIcon,
} from '@dtbx/ui/components/SvgIcons'
import { ACCESS_CONTROLS, AccessControlWrapper } from '@dtbx/store/utils'
import { CustomCheckBox } from '@dtbx/ui/components/CheckBox'
import { useAppDispatch, useAppSelector } from '@/store'
import {
  clearEditingConfigs,
  clearNotificationStep,
  setNotificationStep,
  setSelectedTariffState,
} from '@/store/reducers'
import {
  createTariffByName,
  getConfigurableServices,
  createServiceConfig,
  getAllTariffs,
} from '@/store/actions'
import { addUnderscores, HasAccessToRights } from '@dtbx/store/utils'
import ServiceAccordion from './components/ServiceAccordion'

interface Step {
  icon: React.ReactNode
  title: string
  description: string
  id: string
  disabled: boolean
  step: string
}

const steps: Step[] = [
  {
    icon: <TarrifIcon />,
    title: 'Tariff name and currencies',
    description: 'Set unique name for your tariff.',
    id: 'tariff-name-currency-stepper',
    disabled: false,
    step: 'Tariff name and currencies',
  },
  {
    icon: <PaymentService />,
    title: 'Payment services',
    description: 'Define flat and tiered charges.',
    id: 'payment-service-stepper',
    disabled: true,
    step: 'Payment services',
  },
  {
    icon: <NotificationTarrifIcon />,
    title: 'Notifications',
    description: 'Define charges for notifications.',
    id: 'notification-stepper',
    disabled: true,
    step: 'Notifications',
  },
]

const CustomStepConnector = styled(StepConnector)(({ theme }) => ({
  [`& .${stepConnectorClasses.line}`]: {
    borderColor: '#EAECF0',
    borderLeftWidth: 2,
    marginLeft: 12,
  },
}))

const TarrifNameCurrency: React.FC<{
  setCurrentStep: (step: string) => void
  setOpen: (string: boolean) => void
  setCompletedSteps: React.Dispatch<
    React.SetStateAction<{ [key: string]: boolean }>
  >
}> = ({ setCurrentStep, setCompletedSteps, setOpen }) => {
  const initialState = {
    name: '',
    currency: 'KES',
  }
  const [tarrifState, setTarrifState] = useState(initialState)

  const dispatch = useAppDispatch()

  const handleCheckboxChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const { value } = event.target
    setTarrifState((prev) => ({
      ...prev,
      currency: value,
    }))
  }

  const handleNext = async () => {
    if (tarrifState.name && tarrifState.currency) {
      if (HasAccessToRights(['SUPER_CREATE_TARIFFS', 'MAKE_CREATE_TARIFFS'])) {
        try {
          await createTariffByName(
            dispatch,
            'tariff created',
            addUnderscores(tarrifState.name),
            tarrifState.currency
          )
          await setCompletedSteps((prev) => ({ ...prev, 0: true }))
          setCurrentStep('Payment services')
        } catch (error) {
          console.error('Failed to create tariff:', error)
          setCurrentStep('Tariff name and currencies')
          setOpen(false)
          return
        }
      }
    } else {
      console.error('Tariff name or currency is not selected')
    }
    await setCompletedSteps((prev) => ({ ...prev, 0: true }))
    setCurrentStep('Payment services')
  }

  const handleCancel = () => {
    setCompletedSteps((prev) => ({ ...prev, 0: false }))
    setTarrifState(initialState)
    setCurrentStep('Tariff name and currencies')
    setOpen(false)
  }

  return (
    <Stack
      sx={{
        alignItems: 'center',
        justifyContent: 'center',
        gap: 2,
        marginTop: '40px',
      }}
    >
      <Stack component="form" sx={{ maxWidth: '500px' }}>
        <TextField
          label="Tariff name"
          placeholder="Enter tariff name"
          required
          value={tarrifState.name}
          onChange={(e) => {
            setTarrifState((prev) => ({ ...prev, name: e.target.value }))
            dispatch(setSelectedTariffState(tarrifState))
          }}
          sx={{ minWidth: '450px' }}
          fullWidth
          margin="dense"
        />
        <Stack sx={{ padding: '20px' }}>
          <Typography variant="label1" sx={{ fontWeight: 500 }}>
            Currencies applicable
          </Typography>
          <FormGroup>
            {[
              { value: 'KES', icon: <KESIcon /> },
              { value: 'USD', icon: <USDIcon /> },
              { value: 'EUR', icon: <EURIcon /> },
              { value: 'GBP', icon: <GBPIcon /> },
              { value: 'TZS', icon: <TZSIcon /> },
              { value: 'UGX', icon: <UGXIcon /> },
              { value: 'ZAR', icon: <ZARIcon /> },
              { value: 'BIF', icon: <BIFIcon /> },
            ].map((currency) => (
              <FormControlLabel
                key={currency.value}
                control={
                  <CustomCheckBox
                    checked={tarrifState.currency === currency.value}
                    onChange={handleCheckboxChange}
                    value={currency.value}
                  />
                }
                label={
                  <Box sx={{ display: 'flex', gap: 1, alignItems: 'center' }}>
                    {currency.icon}
                    <Typography variant="body1">{currency.value}</Typography>
                  </Box>
                }
              />
            ))}
          </FormGroup>
        </Stack>
        <Stack sx={{ flexDirection: 'row', gap: 2, width: '100%' }}>
          <Button variant="outlined" fullWidth onClick={handleCancel}>
            Cancel
          </Button>
          <Button
            variant="contained"
            fullWidth
            onClick={handleNext}
            disabled={!tarrifState.name || !tarrifState.currency}
          >
            Next
          </Button>
        </Stack>
      </Stack>
    </Stack>
  )
}

const PaymentServiceComponent: React.FC<{
  setCurrentStep: (step: string) => void
  handlePrevious: () => void
  onFinalSubmit: (data: any[]) => void
  setCompletedSteps: React.Dispatch<
    React.SetStateAction<{ [key: string]: boolean }>
  >
}> = ({ setCurrentStep, setCompletedSteps, handlePrevious, onFinalSubmit }) => {
  const [currentChargeType, setCurrentChargeType] = useState<string>('')
  const { configurableServices, editingConfigs, selectedTariffState } =
    useAppSelector((state) => state.chargeConfiguration)
  const dispatch = useAppDispatch()

  const paymentServices = configurableServices.map((service) => {
    return {
      title: service.serviceName,
      id: service.id ?? '',
      serviceCode: service.serviceCode,
      country: service.country,
    }
  })

  const getChargeType = (selectedType: string) => {
    if (selectedType) {
      setCurrentChargeType(selectedType)
    }
    return
  }

  const handleFinalSubmit = async () => {
    await setCompletedSteps((prev) => ({ ...prev, 1: true }))

    const finalData = Object.values(editingConfigs).map(
      ({ item, charges, limits, tiers }) => ({
        serviceId: item.id,
        tariff: selectedTariffState.name,
        currency: selectedTariffState.currency,
        name: item.title,
        comments: '',

        feeConfiguration: {
          chargeType: currentChargeType.toUpperCase(),
          ...(currentChargeType.toUpperCase() === 'FIXED' && {
            fixedChargeType: 'PERCENTAGE',
          }),
          value: charges[0]?.charge,
          exciseDuty: charges[0]?.exciseDuty,

          ...(currentChargeType.toUpperCase() === 'TIERED' &&
            tiers && {
              feeTiers: tiers.filter(
                (tier) =>
                  !(
                    tier.minimum === 0 &&
                    tier.maximum === 0 &&
                    tier.feeAmount === 0
                  )
              ),
            }),
        },
        limitConfiguration: {
          dailyLimit:
            limits.find((l) => l.type.includes('Daily Limit'))?.value ?? 0,
          perTransactionLowerLimit:
            limits.find((l) => l.type.includes('Lower Limit'))?.value ?? 0,
          perTransactionUpperLimit:
            limits.find((l) => l.type.includes('Higher Limits'))?.value ?? 0,
          frequencyLimit:
            limits.find((l) => l.type.includes('Frequency Limit'))?.value ?? 0,
        },
      })
    )

    onFinalSubmit(finalData)
    dispatch(setNotificationStep('Notifications'))
    setCurrentStep('Notifications')
  }

  return (
    <Stack
      sx={{
        alignItems: 'center',
        justifyContent: 'center',
        gap: 2,
        marginTop: '40px',
        width: '60vw',
        margin: 'auto',
      }}
    >
      <Stack>
        <Stack
          sx={{
            height: '73vh',
            overflowY: 'scroll',
            '&::-webkit-scrollbar': { display: 'none' },
          }}
        >
          {/* <NewAccordion data={paymentServices} getChargeType={getChargeType} /> */}
          <ServiceAccordion
            data={paymentServices}
            getChargeType={getChargeType}
            serviceType="PAYMENT"
          />
        </Stack>
        <Stack
          sx={{
            flexDirection: 'row',
            justifyContent: 'center',
            gap: '20px',
            marginTop: '1rem',
          }}
        >
          <Button
            variant="outlined"
            sx={{
              width: '100%',
            }}
            onClick={handlePrevious}
            // disabled={Object.keys(editingConfigs).length === 0}
          >
            Cancel
          </Button>
          <Button
            variant="contained"
            onClick={handleFinalSubmit}
            disabled={Object.keys(editingConfigs).length === 0}
            sx={{
              width: '100%',
              '&:disabled': {
                backgroundColor: '#BDC0C3',
              },
            }}
          >
            Next
          </Button>
        </Stack>
      </Stack>
    </Stack>
  )
}

const NotificationComponent: React.FC<{
  setCurrentStep: (step: string) => void
  handlePrevious: () => void
  setOpen: (string: boolean) => void
  onSubmit: (combinedFinalData: any[]) => void
  paymentFinalData: any[]
  setCompletedSteps: React.Dispatch<
    React.SetStateAction<{ [key: string]: boolean }>
  >
}> = ({
  setCurrentStep,
  setCompletedSteps,
  handlePrevious,
  setOpen,
  onSubmit,
  paymentFinalData,
}) => {
  const { configurableServices } = useAppSelector(
    (state) => state.chargeConfiguration
  )

  const [currentChargeType, setCurrentChargeType] = useState<string>('')
  const { editingConfigs, selectedTariffState } = useAppSelector(
    (state) => state.chargeConfiguration
  )

  const getChargeType = (selectedType: string) => {
    if (selectedType) {
      setCurrentChargeType(selectedType)
    }
    return
  }

  const notificationServices = configurableServices.map((service) => {
    return {
      title: service.serviceName,
      id: service.id ?? '',
      serviceCode: service.serviceCode,
      country: service.country,
    }
  })

  const handleFinalSubmit = async () => {
    await setCompletedSteps((prev) => ({ ...prev, 2: true }))

    const notificationFinalData = Object.values(editingConfigs).map(
      ({ item, charges }) => ({
        serviceId: item.id,
        tariff: selectedTariffState.name,
        currency: selectedTariffState.currency,
        name: item.title,
        feeConfiguration: {
          chargeType: currentChargeType.toUpperCase(),
          value: charges[0]?.charge,
          exciseDuty: charges[0]?.exciseDuty,
        },
      })
    )

    const filteredNotificationFinalData = notificationFinalData.filter(
      (notificationItem) =>
        !paymentFinalData.some(
          (paymentItem) => paymentItem.serviceId === notificationItem.serviceId
        )
    )

    const combinedFinalData = [
      ...paymentFinalData,
      ...filteredNotificationFinalData,
    ]

    onSubmit(combinedFinalData)
    setCurrentStep('Tariff name and currencies')
    setOpen(false)
  }
  return (
    <Stack
      sx={{
        alignItems: 'center',
        justifyContent: 'center',
        gap: 2,
        marginTop: '40px',
        width: '60vw',
        margin: 'auto',
      }}
    >
      <Stack>
        <Stack
          sx={{
            height: '73vh',
            overflowY: 'scroll',
            '&::-webkit-scrollbar': { display: 'none' },
          }}
        >
          <ServiceAccordion
            data={notificationServices}
            getChargeType={getChargeType}
            serviceType="NOTIFICATION"
          />
        </Stack>
        <Stack
          sx={{
            width: '100%',
            flexDirection: 'row',
            justifyContent: 'center',
            gap: '20px',
            marginTop: '1rem',
          }}
        >
          <Button
            variant="outlined"
            sx={{
              width: '100%',
            }}
            onClick={handlePrevious}
            // disabled={Object.keys(editingConfigs).length === 0}
          >
            Cancel
          </Button>
          <Button
            variant="contained"
            onClick={handleFinalSubmit}
            disabled={Object.keys(editingConfigs).length === 0}
            sx={{
              width: '100%',
              '&:disabled': {
                backgroundColor: '#BDC0C3',
              },
            }}
          >
            Submit
          </Button>
        </Stack>
      </Stack>
    </Stack>
  )
}

const CreateTarrif: React.FC = () => {
  const [open, setOpen] = useState(false)
  const [currentStepIndex, setCurrentStepIndex] = useState(0)
  const [completedSteps, setCompletedSteps] = useState<{
    [key: string]: boolean
  }>({})
  const [stepsState, setStepsState] = useState<Step[]>(steps)
  const { selectedTariffState } = useAppSelector(
    (state) => state.chargeConfiguration
  )
  const [paymentFinalData, setPaymentFinalData] = useState<any[]>([])
  // const [notificationFinalData, setNotificationFinalData] = useState<any[]>([])

  const dispatch = useAppDispatch()
  const isPreviousStepCompleted = (stepIndex: number) => {
    if (stepIndex === 0) return true
    return completedSteps[stepIndex - 1] || false
  }

  const HandleNextStep = (stepName: string) => {
    const stepIndex = steps.findIndex((s) => s.step === stepName)

    if (stepIndex !== -1) {
      setCurrentStepIndex(stepIndex)
      setStepsState((prev) =>
        prev.map((step, index) => ({
          ...step,
          disabled:
            (index > stepIndex && !completedSteps[index]) ||
            (index > 0 && !completedSteps[index - 1]),
        }))
      )
    }
  }

  useEffect(() => {
    if (
      currentStepIndex === 1 &&
      selectedTariffState.name &&
      selectedTariffState.currency
    ) {
      getConfigurableServices(
        selectedTariffState.name,
        selectedTariffState.currency,
        dispatch,
        'PAYMENT'
      )
    } else if (
      currentStepIndex === 2 &&
      selectedTariffState.name &&
      selectedTariffState.currency
    ) {
      getConfigurableServices(
        selectedTariffState.name,
        selectedTariffState.currency,
        dispatch,
        'NOTIFICATION'
      )
    }
  }, [
    currentStepIndex,
    selectedTariffState.name,
    selectedTariffState.currency,
    dispatch,
  ])

  const isStepCompleted = (stepIndex: number) => {
    return completedSteps[stepIndex] || false
  }

  const isStepDisabled = (stepIndex: number) => {
    const step = stepsState[stepIndex]
    const completed = completedSteps[stepIndex]
    const previousCompleted = isPreviousStepCompleted(stepIndex)

    // If step is completed, it's always enabled
    if (completed) {
      return {
        color: '#101828',
        disabled: false,
      }
    }
    if (!previousCompleted && stepIndex > 0) {
      return {
        color: '#BDC0C3',
        disabled: true,
      }
    }
    if (stepIndex === currentStepIndex) {
      return {
        color: '#2A3339',
        disabled: false,
      }
    }
    return {
      color: '#BDC0C3',
      disabled: true,
    }
  }

  const handlePrevious = () => {
    const stepIndex = steps.findIndex(
      (s) => s.step === stepsState[currentStepIndex].step
    )

    if (stepIndex <= 0) return

    const newStepIndex = stepIndex - 1

    setCurrentStepIndex(newStepIndex)
    setStepsState((prev) =>
      prev.map((step, index) => ({
        ...step,
        disabled: shouldDisableStep(index, newStepIndex),
      }))
    )
    dispatch(clearEditingConfigs())
  }

  const shouldDisableStep = (index: number, currentIndex: number) => {
    return (
      (index > currentIndex && !completedSteps[index]) ||
      (index > 0 && !completedSteps[index - 1])
    )
  }

  const DisabledIcon: React.FC<{
    isDisabled: boolean
    children: React.ReactNode
  }> = ({ isDisabled, children }) => {
    return (
      <span
        style={{
          opacity: isDisabled ? 0.5 : 1,
          cursor: isDisabled ? 'not-allowed' : 'pointer',
        }}
      >
        {children}
      </span>
    )
  }
  const stepperContent = [
    {
      id: 'tariff-name-currency-stepper',
      title: 'Tariff name and currencies',
      description:
        'Enter a unique and descriptive name for your new tariff. This name will help you identify and manage the tariff later.',
    },
    {
      id: 'payment-service-stepper',
      title: 'Payment services',
      description:
        'Click on the checkbox of the services you want to include in this tariff, then on the dropdown icon to configure parameters.',
    },
    {
      id: 'notification-stepper',
      title: 'Notifications',
      description:
        'Click on the checkbox of the notifications you want to include in this tariff, then on the dropdown icon to configure parameters.',
    },
  ]

  const handlePaymentFinalSubmit = (data: any[]) => {
    setPaymentFinalData(data)
  }

  const handleFinalSubmit = async (combinedFinalData: any[]) => {
    // if (HasAccessToRights(['SUPER_CREATE_PAYMENT_SERVICE_CONFIGURATIONS'])) {
    //   await createServiceConfig(combinedFinalData, 'super', dispatch)
    //   dispatch(clearEditingConfigs())
    // } else {
    //   await createServiceConfig(combinedFinalData, 'make', dispatch)
    //   dispatch(clearEditingConfigs())
    // }
    // dispatch(clearNotificationStep())
    // await getAllTariffs(dispatch)
    console.log('combined final data >>', combinedFinalData)
    setOpen(false)
  }

  return (
    <>
      <AccessControlWrapper rights={[...ACCESS_CONTROLS.CREATE_TARIFF]}>
        <Button
          onClick={() => setOpen(true)}
          startIcon={<AddIcon sx={{ color: 'white' }} />}
          variant="contained"
          sx={{
            display: 'flex',
            gap: 1,
            justifyContent: 'center',
            alignItems: 'center',
            px: 3,
            py: 2,
            textTransform: 'none',
            backgroundColor: '#101828',
            borderRadius: '8px',
          }}
        >
          <Typography
            variant="body3"
            sx={{
              textWrap: 'nowrap',
              color: 'white',
              fontWeight: '700',
              fontSize: '14px',
            }}
          >
            Create new tariff
          </Typography>
        </Button>
      </AccessControlWrapper>
      <Drawer
        open={open}
        anchor="right"
        onClose={() => setOpen(false)}
        PaperProps={{ sx: { width: '95vw' } }}
      >
        <Stack
          sx={{
            background: '#F9FAFB',
            flexDirection: 'row',
            alignItems: 'center',
            justifyContent: 'space-between',
          }}
        >
          <DialogTitle
            sx={{
              display: 'flex',
              flexDirection: 'row',
              gap: 4,
              alignItems: 'center',
              alignContent: 'center',
              py: '0.5rem',
            }}
          >
            <IconButton onClick={() => setOpen(false)} sx={{ py: '0.2rem' }}>
              <ArrowBackIcon />
            </IconButton>
            <Typography
              variant="h6"
              sx={{
                fontSize: '1rem',
                fontWeight: 700,
                color: '#2A3339',
                marginLeft: '-1.5rem',
              }}
            >
              Create new tariff
            </Typography>
          </DialogTitle>
          <IconButton
            onClick={() => setOpen(false)}
            sx={{
              border: '1px solid #D0D5DD',
              position: 'absolute',
              right: 8,
              borderRadius: '50%',
              mx: '1rem',
              py: '0.5rem',
            }}
          >
            <Close />
          </IconButton>
        </Stack>
        <Divider />

        <Stack sx={{ flexDirection: 'row', height: '100%' }}>
          <Stack sx={{ width: '20vw', padding: '32px' }}>
            <Stepper
              orientation="vertical"
              sx={{
                width: '100%',
                display: 'flex',
                flexDirection: 'column',
                justifyContent: 'center',
                alignItems: 'flex-start',
              }}
              nonLinear
              activeStep={currentStepIndex}
              connector={<CustomStepConnector />}
            >
              {steps.map((step, index) => {
                const { color, disabled } = isStepDisabled(index)

                return (
                  <Step key={step.id} completed={isStepCompleted(index)}>
                    <StepButton
                      onClick={() => HandleNextStep(step.step)}
                      disabled={disabled}
                      sx={{
                        '& .MuiStepLabel-label': {
                          color: color,
                        },
                      }}
                      icon={
                        <DisabledIcon isDisabled={disabled}>
                          {step.icon}
                        </DisabledIcon>
                      }
                    >
                      <Typography
                        variant="subtitle1"
                        sx={{
                          fontSize: '1rem',
                          fontWeight: 700,
                          color: color,
                        }}
                      >
                        {step.title}
                      </Typography>
                      <Typography
                        variant="subtitle2"
                        sx={{
                          fontSize: '1rem',
                          fontWeight: 400,
                          color: disabled ? '#BDC0C3' : '#555C61',
                        }}
                      >
                        {step.description}
                      </Typography>
                    </StepButton>
                  </Step>
                )
              })}
            </Stepper>
          </Stack>

          <Divider orientation="vertical" />
          <Stack
            sx={{
              padding: '32px',
              width: '100%',
              height: '100%',
              justifyContent: 'flex-start',
              alignItems: 'center',
              overflowY: 'auto',
              mb: '1rem',
            }}
          >
            <Stack
              sx={{
                width: '100%',
                display: 'flex',
                flexDirection: 'column',
                justifyContent: 'center',
                alignItems: 'center',
                gap: '10px',
              }}
            >
              <Typography
                variant="h6"
                sx={{
                  color: '#555C61',
                  fontSize: '16px',
                  fontWeight: 500,
                  lineHeight: '24px',
                }}
              >
                STEP {currentStepIndex + 1} of {steps.length}
              </Typography>
              <Typography
                variant="h6"
                sx={{
                  color: '#101828',
                  fontSize: '30px',
                  fontWeight: 700,
                  lineHeight: '38px',
                }}
              >
                {stepperContent[currentStepIndex].title}
              </Typography>
              <Typography
                variant="body2"
                sx={{
                  color: '#555C61',
                  fontSize: '16px',
                  fontWeight: 400,
                  lineHeight: '24px',
                  textAlign: 'center',
                }}
              >
                {stepperContent[currentStepIndex].description}
              </Typography>
            </Stack>

            <Stack>
              <Box>
                {currentStepIndex === 0 ? (
                  <TarrifNameCurrency
                    setCurrentStep={HandleNextStep}
                    setCompletedSteps={setCompletedSteps}
                    setOpen={setOpen}
                  />
                ) : currentStepIndex === 1 ? (
                  <PaymentServiceComponent
                    setCurrentStep={HandleNextStep}
                    setCompletedSteps={setCompletedSteps}
                    handlePrevious={handlePrevious}
                    onFinalSubmit={handlePaymentFinalSubmit}
                  />
                ) : (
                  <NotificationComponent
                    setCurrentStep={HandleNextStep}
                    setCompletedSteps={setCompletedSteps}
                    handlePrevious={handlePrevious}
                    setOpen={setOpen}
                    onSubmit={handleFinalSubmit}
                    paymentFinalData={paymentFinalData}
                  />
                )}
              </Box>
            </Stack>
          </Stack>
        </Stack>
      </Drawer>
    </>
  )
}

export default CreateTarrif
