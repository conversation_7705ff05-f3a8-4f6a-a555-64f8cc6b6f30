import React, { useState } from 'react'
import { Box, Button, Stack, Typography } from '@mui/material'
import { useAppDispatch } from '@/store'
import { formatText } from '@dtbx/store/utils'
import { CustomCheckBox } from '@dtbx/ui/components/CheckBox'
import { KeyboardArrowDownRounded } from '@mui/icons-material'
import ConfigPanel from './ConfigPanel'
import { updateEditingConfig } from '@/store/reducers'

interface AccordionItem {
  title: string
  id: string
  serviceCode?: string
  country: string
}

interface ServiceAccordionProps {
  data: AccordionItem[]
  getChargeType: (selectedType: string) => void
  serviceType?: 'PAYMENT' | 'NOTIFICATION'
}

const ServiceAccordion = ({
  data,
  getChargeType,
  serviceType,
}: ServiceAccordionProps) => {
  const [expandedItemId, setExpandedItemId] = useState<string | null>(null)
  const [selectedConfig, setSelectedConfig] = useState<
    Record<string, AccordionItem>
  >({})
  const dispatch = useAppDispatch()

  const toggleSelection = (item: AccordionItem) => {
    setSelectedConfig((prev) => {
      const newConfig = { ...prev }
      if (newConfig[item.id]) {
        delete newConfig[item.id]
        setExpandedItemId(null)
      } else {
        newConfig[item.id] = item
        setExpandedItemId(item.id)
      }
      return newConfig
    })
  }

  const toggleExpansion = (itemId: string) => {
    setExpandedItemId((prev) => {
      if (prev === itemId) {
        setSelectedConfig((prevConfig) => {
          const newConfig = { ...prevConfig }
          delete newConfig[itemId]
          return newConfig
        })
        return null
      }

      setSelectedConfig((prevConfig) => ({
        ...prevConfig,
        [itemId]: data.find((item) => item.id === itemId)!,
      }))
      return itemId
    })
  }
  return (
    <Stack
      sx={{
        flexDirection: 'row',
        gap: '1rem',
        width: '100%',
      }}
    >
      <Stack
        sx={{
          transition: 'width 0.5s ease',
          flexDirection: 'row',
          gap: '18px',
          justifyContent: 'center',
        }}
      >
        <Stack
          sx={{
            gap: '8px',
          }}
        >
          {data &&
            data.map((item: AccordionItem) => (
              <Stack
                sx={{
                  border: '1px solid #E0E0E0',
                  padding: '12px 16px',
                  //   width: expanded ? '15vw' : '30vw',
                  justifyContent: 'flex-start',
                  borderRadius: '4px',
                  transition: 'width 0.5s ease',
                  minWidth: '500px',
                }}
              >
                <Stack
                  sx={{
                    flexDirection: 'row',
                    justifyContent: 'space-between',
                  }}
                >
                  <CustomCheckBox
                    checked={!!selectedConfig[item.id]}
                    onChange={() => toggleSelection(item)}
                  />
                  <Box
                    sx={{
                      display: 'flex',
                      flexDirection: 'row',
                      justifyContent: 'space-between',
                      alignItems: 'center',
                      width: '90%',
                    }}
                  >
                    <Typography>{formatText(item.title)}</Typography>
                    <Button
                      variant="outlined"
                      onClick={() => toggleExpansion(item.id)}
                      sx={{
                        height: '24px',
                        border: '0.1px solid rgba(189, 189, 189, 0.6)',
                        boxShadow:
                          '0px 1px 3px 0px rgba(16, 24, 40, 0.10), 0px 1px 2px 0px rgba(16, 24, 40, 0.06)',
                        borderRadius: '6px',
                        padding: '4px 12px',
                        minWidth: '86px',
                        fontSize: '13px',
                        fontWeight: 500,
                        lineHeight: '16px',
                        gap: '1px',
                      }}
                      endIcon={
                        <KeyboardArrowDownRounded
                          sx={{
                            transformOrigin: 'center',
                            transition: 'transform 0.2s',
                            transform:
                              expandedItemId === item.id
                                ? 'rotate(90deg)'
                                : 'rotate(0deg)',
                          }}
                        />
                      }
                    >
                      {expandedItemId === item.id ? 'Collapse' : 'Expand'}
                    </Button>
                  </Box>
                </Stack>
              </Stack>
            ))}
        </Stack>
      </Stack>

      {expandedItemId && (
        <Stack sx={{ width: `${expandedItemId ? '40vw' : '0'}` }}>
          <ConfigPanel
            origin={serviceType}
            key={expandedItemId}
            onSave={(charges, limits, tiers) => {
              const item = data.find((item) => item.id === expandedItemId)
              if (item) {
                dispatch(
                  updateEditingConfig({
                    id: expandedItemId,
                    item,
                    config: {
                      charges,
                      limits: serviceType === 'PAYMENT' ? limits : [],
                      tiers: serviceType === 'PAYMENT' ? tiers : [],
                    },
                  })
                )
              }
            }}
            handleCollapse={() => setExpandedItemId(null)}
            item={data.find(
              (item: AccordionItem) => item.id === expandedItemId
            )}
            getChargeType={getChargeType}
          />
        </Stack>
      )}
    </Stack>
  )
}

export default ServiceAccordion
