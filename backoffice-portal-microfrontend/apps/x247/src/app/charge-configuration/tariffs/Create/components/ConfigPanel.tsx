import { CheckIcon } from '@dtbx/ui/components/SvgIcons'
import { KeyboardArrowDownRounded, CloseRounded } from '@mui/icons-material'
import {
  Box,
  Button,
  ClickAwayListener,
  Divider,
  Grow,
  IconButton,
  MenuItem,
  MenuList,
  Paper,
  Popper,
  Stack,
  Typography,
} from '@mui/material'
import React, { useEffect, useState } from 'react'
import { useAppSelector, useAppDispatch } from '@/store'
import { sentenceCase } from 'tiny-case'
import {
  ChargesLayout,
  LimitsLayout,
  TierChargesLayout,
  NotificationsChargeLayout,
} from './Layouts'
import { ICharges, ILimit, updateEditingConfig } from '@/store/reducers'

// interface IConfigPanelProps {
//   expanded: boolean
//   handleCollapse: () => void
// }
interface AccordionItem {
  title: string
  id: string
  serviceCode?: string
  country: string
}
interface IConfigPanelProps {
  // expanded: boolean
  handleCollapse: () => void
  item?: AccordionItem
  onSave: (updatedCharges: any, updatedLimits: any, updatedTiers: any) => void
  getChargeType: (selectedType: string) => void
  origin?: string
}

interface ITierCharge {
  minimum: number
  maximum: number
  feeAmount: number
}
const ConfigPanel = ({
  handleCollapse,
  item,
  onSave,
  getChargeType,
  // origin,
  origin,
}: IConfigPanelProps) => {
  const [open, setOpen] = React.useState(false)
  const anchorRef = React.useRef<HTMLButtonElement>(null)
  const [selectedType, setSelectedType] = React.useState<string>('Fixed')

  const { editingConfigs, notificationStep } = useAppSelector(
    (state) => state.chargeConfiguration
  )

  useEffect(() => {
    if (item?.id) {
      const initialData = editingConfigs[item.id] || {
        charges: [{ charge: 0, exciseDuty: 0 }],
        limits:
          origin === 'NOTIFICATION'
            ? []
            : [
                { type: 'Daily Limit (KES)', value: 0 },
                { type: 'Frequency Limit', value: 0 },
                { type: 'Per Transaction Lower Limit (KES)', value: 0 },
                { type: 'Per Transaction Higher Limits (KES)', value: 0 },
              ],
        tiers:
          origin === 'PAYMENT'
            ? [{ minimum: 0, maximum: 0, feeAmount: 0 }]
            : [],
      }

      setCharges(initialData.charges)
      setLimits(initialData.limits)
      setTiers(initialData.tiers || [])
      getChargeType(selectedType)
    }
  }, [item?.id, editingConfigs, origin])

  const initialCharges =
    item?.id && editingConfigs[item.id]?.charges
      ? editingConfigs[item.id].charges
      : [{ charge: 0, exciseDuty: 0 }]

  const initialLimits =
    item?.id && editingConfigs[item.id]?.limits
      ? editingConfigs[item.id].limits
      : [
          { type: 'Daily Limit (KES)', value: 0 },
          { type: 'Frequency Limit', value: 0 },
          { type: 'Per Transaction Lower Limit (KES)', value: 0 },
          { type: 'Per Transaction Higher Limits (KES)', value: 0 },
        ]

  const initialTiers = (item?.id && editingConfigs[item.id]?.tiers) || [
    { minimum: 0, maximum: 0, feeAmount: 0 },
  ]

  const [charges, setCharges] = useState<ICharges[]>(initialCharges)
  const [limits, setLimits] = useState<ILimit[]>(initialLimits)
  const [tiers, setTiers] = useState<ITierCharge[]>(initialTiers)

  const dispatch = useAppDispatch()

  const handleClose = (event: Event | React.SyntheticEvent) => {
    if (
      anchorRef.current &&
      anchorRef.current.contains(event.target as HTMLElement)
    ) {
      return
    }
    setOpen(false)
  }
  const handleLimitChange = (
    updatedLimits: { type: string; value: number }[]
  ) => {
    setLimits(updatedLimits)
  }

  const handleSave = () => {
    if (!item) return

    const feeTiers = tiers
      .filter((t) => t.minimum && t.maximum && t.feeAmount)
      .map((tier) => ({
        minimum: tier.minimum,
        maximum: tier.maximum,
        feeAmount: tier.feeAmount,
      }))

    dispatch(
      updateEditingConfig({
        id: item.id,
        item,
        config: {
          charges,
          limits,
          tiers: feeTiers,
        },
      })
    )

    onSave(charges, limits, tiers)
    handleCollapse()
  }

  return (
    <Stack
      sx={{
        border: '1px solid #E0E0E0',

        padding: '12px 16px',
        borderRadius: '4px',
        transition: 'width 0.5s ease',
      }}
    >
      <Stack>
        <Box
          sx={{
            display: 'flex',
            flexDirection: 'row',
            justifyContent: 'space-between',
            alignItems: 'center',
            padding: '1rem 0.2rem',
            width: '100%',
          }}
        >
          <Stack
            sx={{
              flexDirection: 'row',
              justifyContent: 'flex-start',
              alignItems: 'center',
              gap: '10px',
              width: '100%',
            }}
          >
            <Typography
              variant="h6"
              sx={{
                fontWeight: 550,
                fontSize: '1rem',
                whiteSpace: 'nowrap',
                color: ' #2A3339',
                maxWidth: '100%',

                overflowWrap: 'break-word',
              }}
            >
              Configure {item?.title}
            </Typography>
            <Stack
              sx={{
                display: `${notificationStep === 'Notifications' ? 'none' : 'flex'}`,
              }}
            >
              <Button
                variant="outlined"
                aria-controls={open ? 'menu-button-type' : undefined}
                aria-haspopup="true"
                aria-expanded={open ? 'true' : undefined}
                onClick={() => {
                  setOpen((prevOpen) => !prevOpen)
                }}
                ref={anchorRef}
                sx={{
                  padding: '1px 4px',
                  border: '1px solid #AAADB0',
                }}
                endIcon={
                  <KeyboardArrowDownRounded
                    sx={{
                      transformOrigin: 'center',
                      transition: 'transform padding 0.2s',
                      transform: open ? 'rotate(90deg)' : 'rotate(0deg)',
                    }}
                  />
                }
              >
                <Typography
                  sx={{
                    fontWeight: 400,
                    fontsize: '0.75rem',
                    color: '#555C61',
                  }}
                >
                  {sentenceCase(selectedType)}
                </Typography>
              </Button>
              <Popper
                open={open}
                anchorEl={anchorRef.current}
                role={undefined}
                placement="bottom-start"
                transition
                disablePortal
                sx={{
                  zIndex: '2000',
                }}
              >
                {({ TransitionProps, placement }) => (
                  <Grow
                    {...TransitionProps}
                    style={{
                      transformOrigin:
                        placement === 'bottom-start'
                          ? 'left top'
                          : 'left bottom',
                    }}
                  >
                    <Paper
                      sx={{
                        minWidth: '95px',
                        marginTop: '8px',
                        borderRadius: '8px',
                        border: '1px solid #ECECEC',
                        background: '#FFF',
                        boxShadow:
                          '0px 12.514px 15.017px -2.503px rgba(16, 24, 40, 0.08), 0px 5.006px 5.006px -2.503px rgba(16, 24, 40, 0.03)',
                      }}
                    >
                      <ClickAwayListener onClickAway={handleClose}>
                        <MenuList
                          autoFocusItem={open}
                          id="composition-menu"
                          aria-labelledby="composition-button"
                          // onKeyDown={handleListKeyDown}
                        >
                          {['Tiered', 'Fixed'].map((item) => (
                            <MenuItem
                              key={item}
                              onClick={() => {
                                setSelectedType(item)
                                setOpen(false)
                                getChargeType(item)
                              }}
                            >
                              {item}
                            </MenuItem>
                          ))}
                        </MenuList>
                      </ClickAwayListener>
                    </Paper>
                  </Grow>
                )}
              </Popper>
            </Stack>
            <Stack
              sx={{
                flexDirection: 'row',
                justifyContent: 'flex-end',
                alignItems: 'center',
                width: '100%',
                gap: '24px',
              }}
            >
              <Button
                onClick={handleSave}
                startIcon={
                  <Box sx={{ color: 'white' }}>
                    <CheckIcon />
                  </Box>
                }
                variant="contained"
                sx={{
                  display: 'flex',
                  gap: 1,
                  justifyContent: 'center',
                  alignItems: 'center',
                  px: 2,
                  py: 1,
                  textTransform: 'none',
                  backgroundColor: '#101828',
                  borderRadius: '8px',
                  height: '2rem',
                }}
              >
                <Typography
                  variant="body3"
                  sx={{
                    whiteSpace: 'nowrap',
                    color: 'white',
                    fontWeight: '700',
                    fontSize: '14px',
                  }}
                >
                  Save
                </Typography>
              </Button>
              <IconButton
                sx={{
                  padding: '9px 12px',
                  borderRadius: '6px',
                  border: '1px solid #E3E4E4',
                }}
                onClick={handleCollapse}
              >
                <CloseRounded
                  sx={{
                    fontSize: '14px',
                  }}
                />
              </IconButton>
            </Stack>
          </Stack>
        </Box>
        <Divider />
      </Stack>
      <Stack
        sx={{
          width: '100%',
          padding: '7px 32px',
          height: 'auto',
          justifyContent: 'flex-start',
          alignItems: 'flex-start',
          gap: '22px',
          // border: '2px solid red',
        }}
      >
        {origin === 'NOTIFICATION' ? (
          <NotificationsChargeLayout
            readOnly={false}
            charges={charges}
            setCharges={setCharges}
          />
        ) : (
          <>
            {selectedType !== 'Fixed' && (
              <ChargesLayout
                readOnly={false}
                charges={charges}
                setCharges={setCharges}
              />
            )}

            {selectedType === 'Fixed' && (
              <ChargesLayout
                readOnly={false}
                charges={charges}
                setCharges={setCharges}
              />
            )}

            <LimitsLayout
              limits={limits}
              setLimits={handleLimitChange}
              readOnly={false}
            />

            {selectedType === 'Tiered' && (
              <TierChargesLayout
                readOnly={false}
                tiers={tiers}
                setTiers={setTiers}
              />
            )}
          </>
        )}
      </Stack>
    </Stack>
  )
}

export default ConfigPanel
