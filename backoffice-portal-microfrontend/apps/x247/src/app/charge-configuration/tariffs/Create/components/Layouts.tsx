import React, { useState, useEffect } from 'react'
import {
  <PERSON><PERSON>,
  Box,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TextField,
  Paper,
} from '@mui/material'
import {
  ChargeIcon,
  LimitIcon,
  TierChargeIcon,
} from '@dtbx/ui/components/SvgIcons'
import { CloseRounded } from '@mui/icons-material'

interface ICharges {
  charge: number
  exciseDuty: number
}

interface ChargesLayoutProps {
  charges: ICharges[]
  setCharges: React.Dispatch<React.SetStateAction<ICharges[]>>
  readOnly?: boolean
  getEditData?: (data: ICharges[]) => void
}
interface LimitsLayoutProps {
  limits: { type: string; value: number }[]
  setLimits: (updatedLimits: { type: string; value: number }[]) => void
  readOnly: boolean
  // getEditData: (limits: { type: string; value: number }[]) => void
}
interface TierCharge {
  minimum: number
  maximum: number
  feeAmount: number
}

interface TierChargesLayoutProps {
  tiers: TierCharge[]
  setTiers: React.Dispatch<React.SetStateAction<TierCharge[]>>
  readOnly?: boolean
}

export const ChargesLayout: React.FC<ChargesLayoutProps> = ({
  charges,
  setCharges,
  readOnly = false,
  getEditData,
}) => {
  const [editingCell, setEditingCell] = useState<{
    row: number
    col: 'charge' | 'exciseDuty'
  } | null>(null)

  const [editValue, setEditValue] = useState<string>('')


  const handleCellClick = (row: number, col: 'charge' | 'exciseDuty'): void => {
    if (readOnly) return
    setEditingCell({ row, col })
    setEditValue(charges[row][col].toString())
  }

  const handleCellBlur = (): void => {
    if (editingCell) {
      const updatedCharges = [...charges]
      updatedCharges[editingCell.row][editingCell.col] =
        parseFloat(editValue) || 0
      setCharges(updatedCharges)
      setEditingCell(null)
    }
  }

  const handleCellChange = (
    event: React.ChangeEvent<HTMLInputElement>
  ): void => {
    const value = event.target.value

    if (/^\d*\.?\d*$/.test(value)) {
      setEditValue(value)
    }
  }

  return (
    <Stack sx={{ width: '100%' }}>
      <Stack sx={{ p: '1%', mb: '0.5rem' }} direction="row" gap={2}>
        <Stack
          sx={{
            px: '10px',
            height: '2rem',
            width: '2rem',
            border: '0.1px solid rgba(189, 189, 189, 0.6)',
            borderRadius: '50%',
            justifyContent: 'center',
            alignItems: 'center',
          }}
        >
          <ChargeIcon />
        </Stack>
        <Box>
          <Typography fontWeight={550}> Charges</Typography>
          <Typography>
            The fee for this service regardless of the value of the transaction.
          </Typography>
        </Box>
      </Stack>
      <TableContainer
        sx={{
          border: '0.1px solid rgba(189, 189, 189, 0.6)',
          borderRadius: '0.5rem',
          boxShadow:
            '0px 1px 3px 0px rgba(16, 24, 40, 0.10), 0px 1px 2px 0px rgba(16, 24, 40, 0.06)',
          overflow: 'hidden',
        }}
      >
        <Table sx={{ width: '100%' }}>
          <TableHead>
            <TableRow>
              <TableCell
                sx={{
                  border: '0.1px solid rgba(189, 189, 189, 0.6)',
                  width: '33%',
                }}
              >
                <Typography>Charge (KES)</Typography>
              </TableCell>
              <TableCell
                sx={{
                  border: '0.1px solid rgba(189, 189, 189, 0.6)',
                  width: '33%',
                }}
              >
                <Typography>Excise Duty (%)</Typography>
              </TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {charges.map((charge, rowIndex) => (
              <TableRow key={rowIndex}>
                {['charge', 'exciseDuty'].map((col) => (
                  <TableCell
                    key={col}
                    sx={{
                      border: '0.1px solid rgba(189, 189, 189, 0.6)',
                      p: '0.5rem',
                      '&:hover': {
                        backgroundColor: '#F9FAFB',
                        cursor: readOnly ? 'default' : 'pointer',
                      },
                    }}
                    onClick={() =>
                      !readOnly &&
                      handleCellClick(rowIndex, col as 'charge' | 'exciseDuty')
                    }
                  >
                    {editingCell?.row === rowIndex &&
                    editingCell?.col === col ? (
                      <TextField
                        value={editValue}
                        onChange={handleCellChange}
                        onBlur={handleCellBlur}
                        autoFocus
                        fullWidth
                        size="small"
                        sx={{
                          height: '100%',
                          '& .MuiInputBase-root': {
                            height: '100%',
                            padding: 0,
                            margin: 0,
                          },
                        }}
                        InputProps={{
                          sx: {
                            '&:hover .MuiOutlinedInput-notchedOutline': {
                              border: 'none',
                              p: 0,
                              m: 0,
                            },
                            '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                              border: 'none',
                              p: 0,
                              m: 0,
                            },
                          },
                        }}
                      />
                    ) : (
                      <Typography
                        fontWeight={550}
                        sx={{ marginLeft: '0.5rem' }}
                      >
                        {charge[col as keyof ICharges]}
                      </Typography>
                    )}
                  </TableCell>
                ))}
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>
    </Stack>
  )
}

export const LimitsLayout: React.FC<LimitsLayoutProps> = ({
  limits,
  setLimits,
  readOnly,
}) => {
  const [editingCell, setEditingCell] = useState<{
    row: number
    col: 'value'
  } | null>(null)

  const [editValue, setEditValue] = useState<string>('')

  const handleCellClick = (row: number, col: 'value'): void => {
    if (readOnly) return
    setEditingCell({ row, col })
    setEditValue(limits[row][col].toString())
  }

  const handleCellBlur = (): void => {
    if (editingCell) {
      const updatedLimits = [...limits]
      updatedLimits[editingCell.row][editingCell.col] =
        parseFloat(editValue) || 0
      setLimits(updatedLimits)
      setEditingCell(null)
    }
  }

  const handleCellChange = (
    event: React.ChangeEvent<HTMLInputElement>
  ): void => {
    const value = event.target.value

    if (/^\d*\.?\d*$/.test(value)) {
      setEditValue(value)
    }
  }

  return (
    <Stack sx={{ width: '100%' }}>
      <Stack sx={{ p: '1%', mb: '0.5rem' }} direction="row" gap={2}>
        <Stack
          sx={{
            px: '10px',
            height: '2rem',
            width: '2rem',
            border: '0.1px solid rgba(189, 189, 189, 0.6)',
            borderRadius: '50%',
            justifyContent: 'center',
            alignItems: 'center',
          }}
        >
          <LimitIcon />
        </Stack>
        <Box>
          <Typography fontWeight={550}> Limits</Typography>
          <Typography>
            Restrictions or caps placed on specific aspects of this payment
            service.{' '}
          </Typography>
        </Box>
      </Stack>
      <TableContainer
        sx={{
          border: '0.1px solid rgba(189, 189, 189, 0.6)',
          borderRadius: '0.5rem',
          boxShadow:
            '0px 1px 3px 0px rgba(16, 24, 40, 0.10), 0px 1px 2px 0px rgba(16, 24, 40, 0.06)',
          overflow: 'hidden',
        }}
      >
        <Table sx={{ width: '100%' }}>
          <TableHead>
            <TableRow>
              <TableCell
                sx={{
                  border: '0.1px solid rgba(189, 189, 189, 0.6)',
                  width: '33%',
                }}
              >
                {' '}
                <Typography> Limit Type</Typography>{' '}
              </TableCell>
              <TableCell
                sx={{
                  border: '0.1px solid rgba(189, 189, 189, 0.6)',
                  width: '33%',
                }}
              >
                {' '}
                <Typography> Limit Value</Typography>{' '}
              </TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {limits.map((limit, rowIndex) => {
              return (
                <TableRow key={rowIndex}>
                  <TableCell
                    sx={{
                      border: '0.1px solid rgba(189, 189, 189, 0.6)',
                      '&:hover': {
                        backgroundColor: '#F9FAFB',
                        cursor: 'pointer',
                      },
                    }}
                  >
                    {' '}
                    <Typography fontWeight={550}> {limit.type}</Typography>
                  </TableCell>
                  <TableCell
                    sx={{
                      border: '0.1px solid rgba(189, 189, 189, 0.6)',
                      p: '0.5rem',
                      '&:hover': {
                        backgroundColor: '#F9FAFB',
                        cursor: 'pointer',
                      },
                    }}
                    onClick={() => handleCellClick(rowIndex, 'value')}
                  >
                    {editingCell?.row === rowIndex &&
                    editingCell?.col === 'value' &&
                    !readOnly ? (
                      <TextField
                        value={editValue}
                        onChange={handleCellChange}
                        onBlur={handleCellBlur}
                        autoFocus
                        fullWidth
                        size="small"
                        sx={{
                          height: '100%',
                          '& .MuiInputBase-root': {
                            height: '100%',
                            padding: 0,
                            margin: 0,
                          },
                        }}
                        InputProps={{
                          sx: {
                            '&:hover .MuiOutlinedInput-notchedOutline': {
                              border: 'none',
                              p: 0,
                              m: 0,
                            },
                            '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                              border: 'none',
                              p: 0,
                              m: 0,
                            },
                          },
                        }}
                      />
                    ) : (
                      <Typography
                        fontWeight={550}
                        sx={{ marginLeft: '0.5rem' }}
                      >
                        {limit.value}
                      </Typography>
                    )}
                  </TableCell>
                </TableRow>
              )
            })}
          </TableBody>
        </Table>
      </TableContainer>
    </Stack>
  )
}

export const TierChargesLayout: React.FC<TierChargesLayoutProps> = ({
  tiers,
  setTiers,
  readOnly = false,
}) => {
  const [editingCell, setEditingCell] = useState<{
    row: number
    col: keyof TierCharge
  } | null>(null)
  const [editValue, setEditValue] = useState<number>(0)

  const handleCellClick = (row: number, col: keyof TierCharge): void => {
    if (readOnly || minMaxError) return
    setEditingCell({ row, col })
    setEditValue(tiers[row][col])
  }
  const [minMaxError, setMinMaxError] = useState<string>('')

  const handleCellBlur = (): void => {
    if (editingCell) {
      const updatedTiers = [...tiers]
      const row = editingCell.row
      const col = editingCell.col
      const originalValue = tiers[row][col]

      updatedTiers[row] = {
        ...updatedTiers[row],
        [col]: editValue,
      }

      if (
        col === 'maximum' &&
        updatedTiers[row].maximum < updatedTiers[row].minimum
      ) {
        // Revert to the original value if invalid and set error
        updatedTiers[row][col] = originalValue
        setMinMaxError(
          'Maximum threshold cannot be less than minimum threshold.'
        )

        //Prevent state update and keep the cell in edit mode
        setEditValue(originalValue)
        return
      }

      // Clear the error if validation passes
      setMinMaxError('')

      // Check if all columns in the last row are filled
      const lastRow = updatedTiers[updatedTiers.length - 1]
      const allColumnsFilled = Object.keys(lastRow).every(
        (key) => lastRow[key as keyof TierCharge] !== 0
      )

      if (row === tiers.length - 1 && allColumnsFilled) {
        updatedTiers.push({ minimum: 0, maximum: 0, feeAmount: 0 })
      }

      setTiers(updatedTiers)
      setEditingCell(null)
    }
  }
  const handleCellChange = (
    event: React.ChangeEvent<HTMLInputElement>
  ): void => {
    const value = event.target.value

    if (/^\d*\.?\d*$/.test(value)) {
      setEditValue(Number(value))
    }
  }
  return (
    <Stack sx={{ width: '100%' }}>
      <Stack sx={{ p: '1%' }} direction="row" gap={2}>
        <Stack
          sx={{
            px: '10px',
            height: '2rem',
            width: '2rem',
            border: '0.1px solid rgba(189, 189, 189, 0.6)',
            borderRadius: '50%',
            justifyContent: 'center',
            alignItems: 'center',
          }}
        >
          <TierChargeIcon />
        </Stack>
        <Box>
          <Typography fontWeight={550}>Tier Charges</Typography>
          <Typography>
            The fee structure for this service based on the value of
            transactions.
          </Typography>
        </Box>
      </Stack>

      <TableContainer
        sx={{
          border: '0.1px solid rgba(189, 189, 189, 0.6)',
          borderRadius: '0.5rem',
          boxShadow:
            '0px 1px 3px 0px rgba(16, 24, 40, 0.10), 0px 1px 2px 0px rgba(16, 24, 40, 0.06)',
          overflow: 'hidden',
        }}
      >
        <Table sx={{ width: '100%' }}>
          <TableHead>
            <TableRow>
              {[
                'Minimum Threshold (KES)',
                'Maximum Threshold (KES)',
                'Tier Fee (KES)',
              ].map((header) => (
                <TableCell
                  key={header}
                  sx={{
                    border: '0.1px solid rgba(189, 189, 189, 0.6)',
                    width: '35%',
                    height: '2.6rem',
                    padding: '0.5rem',
                  }}
                >
                  <Typography
                    sx={{
                      marginLeft: '0.5rem',
                      whiteSpace: 'nowrap',
                      overflow: 'hidden',
                      textOverflow: 'ellipsis',
                    }}
                    fontWeight={550}
                  >
                    {header}
                  </Typography>
                </TableCell>
              ))}
            </TableRow>
          </TableHead>
          <TableBody>
            {tiers.map((tier, rowIndex) => (
              <TableRow key={rowIndex}>
                {(['minimum', 'maximum', 'feeAmount'] as const).map((col) => (
                  <TableCell
                    key={col}
                    sx={{
                      border: '0.1px solid rgba(189, 189, 189, 0.6)',
                      height: '2.6rem',
                      padding:
                        editingCell?.row === rowIndex &&
                        editingCell?.col === col
                          ? '0'
                          : '0.5rem',
                      '&:hover': {
                        backgroundColor: '#F9FAFB',
                        cursor: 'pointer',
                      },
                    }}
                    onClick={() => handleCellClick(rowIndex, col)}
                  >
                    {editingCell?.row === rowIndex &&
                    editingCell?.col === col ? (
                      <TextField
                        value={editValue}
                        onChange={handleCellChange}
                        onBlur={handleCellBlur}
                        autoFocus
                        fullWidth
                        size="small"
                        sx={{
                          '& .MuiInputBase-root': {
                            height: '2.6rem',
                            padding: 0,
                          },
                        }}
                        InputProps={{
                          sx: {
                            '&:hover .MuiOutlinedInput-notchedOutline': {
                              border: 'none',
                            },
                            '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                              border: 'none',
                            },
                          },
                        }}
                      />
                    ) : (
                      <Typography
                        sx={{ marginLeft: '0.5rem' }}
                        fontWeight={550}
                      >
                        {tier[col]}
                      </Typography>
                    )}
                  </TableCell>
                ))}
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>
      {minMaxError && (
        <Typography sx={{ color: 'red', fontSize: '0.8rem' }}>
          {minMaxError}
        </Typography>
      )}
    </Stack>
  )
}

interface NotificationsChargeLayoutProps {
  readOnly: boolean
  charges: ICharges[]
  setCharges: React.Dispatch<React.SetStateAction<ICharges[]>>
}

// interface NotificationsChargeLayoutProps {
//   readOnly: boolean
//   // charges: { exciseDuty: number; value: number }
//   // setCharges: (charges: { exciseDuty: number; value: number }) => void
// }
interface NotificationsChargeLayoutProps {
  readOnly: boolean
  charges: ICharges[]
  setCharges: React.Dispatch<React.SetStateAction<ICharges[]>>
}

export const NotificationsChargeLayout = ({
  readOnly,
  charges,
  setCharges,
}: NotificationsChargeLayoutProps) => {
  const [editingField, setEditingField] = useState<keyof ICharges | null>(null)
  const [editValue, setEditValue] = useState<string>('')

  const currentCharge = charges[0] || { charge: 0, exciseDuty: 0 }


  const handleUpdate = (field: keyof ICharges, value: number) => {
    const updatedCharges = [
      {
        ...currentCharge,
        [field]: value,
      },
    ]
    setCharges(updatedCharges)
  }

  const handleCellBlur = () => {
    if (editingField) {
      handleUpdate(editingField, parseFloat(editValue) || 0)
      setEditingField(null)
    }
  }

  const handleCellChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value
    if (/^\d*\.?\d*$/.test(value)) setEditValue(value)
  }

  return (
    <TableContainer
      component={Paper}
      sx={{
        width: '100%',
        boxShadow:
          '0px 1px 3px 0px rgba(16, 24, 40, 0.10), 0px 1px 2px 0px rgba(16, 24, 40, 0.06)',
        overflow: 'hidden',
      }}
    >
      <Table sx={{ width: '100%' }} size="small">
        <TableHead>
          <TableRow>
            <TableCell
              sx={{
                width: '50%',
                border: '0.1px solid rgba(189, 189, 189, 0.6)',
                p: '0.5rem',
              }}
            >
              <Typography variant="subtitle2">Charge (KES)</Typography>
            </TableCell>
            <TableCell
              sx={{ border: '0.1px solid rgba(189, 189, 189, 0.6)' }}
              onClick={() => {
                setEditingField('charge')
                setEditValue(currentCharge.charge.toString())
              }}
            >
              {editingField === 'charge' ? (
                <TextField
                  value={editValue}
                  onChange={handleCellChange}
                  onBlur={handleCellBlur}
                  autoFocus
                  fullWidth
                  size="small"
                  sx={{
                    height: '100%',
                    '& .MuiInputBase-root': { height: '100%', padding: 0 },
                  }}
                  InputProps={{
                    sx: {
                      '&:hover .MuiOutlinedInput-notchedOutline': {
                        border: 'none',
                      },
                      '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                        border: 'none',
                      },
                    },
                  }}
                />
              ) : (
                <Typography variant="body2" fontSize="1rem">
                  {currentCharge.charge}
                </Typography>
              )}
            </TableCell>
          </TableRow>
        </TableHead>
        <TableBody>
          <TableRow>
            <TableCell
              sx={{
                border: '0.1px solid rgba(189, 189, 189, 0.6)',
                p: '0.5rem',
              }}
            >
              <Typography fontWeight={550}>Excise Duty (%)</Typography>
            </TableCell>
            <TableCell
              sx={{
                border: '0.1px solid rgba(189, 189, 189, 0.6)',
                width: '50%',
              }}
              onClick={() => {
                setEditingField('exciseDuty')
                setEditValue(currentCharge.exciseDuty.toString())
              }}
            >
              {editingField === 'exciseDuty' ? (
                <TextField
                  value={editValue}
                  onChange={handleCellChange}
                  onBlur={handleCellBlur}
                  autoFocus
                  fullWidth
                  size="small"
                  sx={{
                    height: '100%',
                    '& .MuiInputBase-root': { height: '100%', padding: 0 },
                  }}
                  InputProps={{
                    sx: {
                      '&:hover .MuiOutlinedInput-notchedOutline': {
                        border: 'none',
                      },
                      '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                        border: 'none',
                      },
                    },
                  }}
                />
              ) : (
                <Typography variant="body2" fontSize="1rem">
                  {currentCharge.exciseDuty}
                </Typography>
              )}
            </TableCell>
          </TableRow>
        </TableBody>
      </Table>
    </TableContainer>
  )
}
