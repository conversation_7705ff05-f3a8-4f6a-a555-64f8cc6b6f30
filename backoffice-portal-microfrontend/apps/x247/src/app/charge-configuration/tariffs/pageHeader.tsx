'use client'

import { useState } from 'react'
import { SearchRounded } from '@mui/icons-material'
import FilterListOutlinedIcon from '@mui/icons-material/FilterListOutlined'
import {
  Box,
  Button,
  FormControl,
  InputLabel,
  MenuItem,
  Select,
  Stack,
  SelectChangeEvent,
} from '@mui/material'
import { GrowProvider } from '@dtbx/ui/components/Transitions'
import React from 'react'
import { CustomSearchInput } from '@dtbx/ui/components/Input'
import CreateTarrif from './Create/CreateTarrif'

interface IPageHeaderProps {
  onSearchChange: (value: string) => void
  onStatusChange: (value: string) => void
  selectedStatus: string
}

const PageHeader = ({
  onSearchChange,
  onStatusChange,
  selectedStatus,
}: IPageHeaderProps) => {
  const [showFilters, setShowFilters] = useState<boolean>(false)
  const handleStatusChange = (e: SelectChangeEvent<string>) => {
    onStatusChange(e.target.value)
  }

  return (
    <Stack
      sx={{ flexDirection: 'column', gap: 3, padding: '2%', marginLeft: '1%' }}
    >
      <Stack
        sx={{
          flexDirection: 'row',
          alignItems: 'center',
          justifyContent: 'space-between',
          gap: 3,
        }}
      >
        <Stack sx={{ flexDirection: 'row', gap: 3, alignItems: 'center' }}>
          <CustomSearchInput
            placeholder="Search for tariff"
            onChange={(e) => onSearchChange(e.target.value)}
            endAdornment={
              <Box
                sx={{
                  backgroundColor: '#EDEEEE',
                  padding: '8px 12px',
                  width: '38px',
                  height: '100%',
                  display: 'flex',
                  justifyContent: 'center',
                  alignItems: 'center',
                  position: 'absolute',
                  right: 0,
                  top: 0,
                  borderTopRightRadius: '4px',
                  borderBottomRightRadius: '4px',
                }}
              >
                <SearchRounded
                  sx={{
                    color: '#555C61',
                  }}
                />
              </Box>
            }
            sx={{
              width: '33vw',
              '&.Mui-focused': {
                width: '35vw',
              },
              background: '#FFFFFF',
              borderRadius: '4px',
              '& fieldset': {
                border: '1px solid #D0D5DD !important',
              },
            }}
          />
          <Button
            variant="outlined"
            sx={{ border: '1px solid #D0D5DD' }}
            startIcon={<FilterListOutlinedIcon />}
            onClick={() => setShowFilters(!showFilters)}
          >
            Filter
          </Button>
        </Stack>
        <CreateTarrif />
      </Stack>
      {showFilters && (
        <GrowProvider in={showFilters}>
          <FormControl sx={{ width: '10vw' }}>
            <InputLabel id="demo-simple-select-label">status</InputLabel>
            <Select
              size="small"
              value={selectedStatus}
              labelId="demo-simple-select-label"
              id="demo-simple-select"
              label={'Customer Status'}
              onChange={handleStatusChange}
            >
              {['Active', 'Inactive'].map((item) => (
                <MenuItem key={item} value={item}>
                  {item}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </GrowProvider>
      )}
    </Stack>
  )
}

export default PageHeader
