import { IHeadCell } from '@dtbx/store/interfaces'
import { CustomerStatusChip } from '@dtbx/ui/components/Chip'
import {
  CustomPagination,
  CustomTableHeader,
  PaginationOptions,
} from '@dtbx/ui/components/Table'
import { useCustomRouter } from '@dtbx/ui/hooks'
import { TariffsMoreMenu } from '../TariffsMoreMenu'
import {
  Paper,
  Stack,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableRow,
  Typography,
} from '@mui/material'
import React, { useState } from 'react'
import { ITariffData, Status } from '@/store/interfaces'
import { RootState, useAppDispatch, useAppSelector } from '@/store'
import { setSelectedTariff } from '@/store/reducers'
import { getAllTariffs } from '@/store/actions'
import { formatTimestamp } from '@dtbx/store/utils'

interface ITariffsListProps {
  paginationOptions: PaginationOptions
  onPaginationChange: (options: PaginationOptions) => void
}

const headerList: IHeadCell[] = [
  {
    id: 'name',
    label: 'Name',
    alignCenter: false,
    alignRight: false,
  },
  {
    id: 'status',
    label: 'Status',
    alignCenter: false,
    alignRight: false,
  },
  {
    id: 'createdOn',
    label: 'Date Created',
    alignCenter: false,
    alignRight: false,
  },
  {
    id: 'actions',
    label: 'Actions',
    alignCenter: false,
    alignRight: false,
  },
]

export const TariffsList = ({
  paginationOptions,
  onPaginationChange,
}: ITariffsListProps) => {
  const dispatch = useAppDispatch()
  const router = useCustomRouter()
  const { tariffs, tariffsSummary } = useAppSelector(
    (state: RootState) => state.chargeConfiguration
  )

  const renderStatusChip = (status: Status | undefined) => {
    switch (status) {
      case 'ACTIVE':
        return <CustomerStatusChip label="ACTIVE" />
      case 'INACTIVE':
        return <CustomerStatusChip label="INACTIVE" />
      case 'PENDING':
        return <CustomerStatusChip label="PENDING" />
      default:
        return <CustomerStatusChip label="ACTIVE" />
    }
  }
  const handleRowClick = (tariff: ITariffData) => {
    dispatch(setSelectedTariff(tariff))
    router.push('/charge-configuration/tariffs/tariff')
  }

  // const [paginationOptions, setPaginationOptions] = useState({
  //   page: tariffsSummary.pageNumber,
  //   size: tariffsSummary.pageSize,
  //   totalPages: tariffsSummary.totalNumberOfPages,
  // })
  /*************************start pagination handlers***************************/
  const handlePagination = async (newOptions: PaginationOptions) => {
    onPaginationChange(newOptions)
    // Call API to get new data
    // await getAllTariffs(dispatch, newOptions)
  }
  /*************************end pagination handlers**************************/
  return (
    <Stack
      sx={{
        padding: '0.5% 2% 0.5% 2%',
        flexDirection: 'column',
        marginLeft: '1%',
      }}
    >
      <Stack
        sx={{
          flexDirection: 'column',
          boxShadow:
            '0px 1px 3px 0px rgba(16, 24, 40, 0.10), 0px 1px 2px 0px rgba(16, 24, 40, 0.06)',
          border: '1px solid #EAECF0',
          borderRadius: '8px',
        }}
      >
        <Stack sx={{ padding: '1% 1.5%' }}>
          <Typography variant="subtitle1" fontWeight={600}>
            Tarrifs
          </Typography>
          <Typography variant="subtitle3" color=" #555C61">
            Showing {tariffs?.length} tariffs
          </Typography>
        </Stack>

        <Paper
          elevation={0}
          sx={{
            boxShadow:
              '0px 1px 3px 0px rgba(16, 24, 40, 0.10), 0px 1px 2px 0px rgba(16, 24, 40, 0.06)',
            border: '1px solid #EAECF0',
          }}
        >
          <TableContainer
            component={Paper}
            sx={{
              boxShadow: 'none',
            }}
          >
            <Table sx={{ minWidth: 650, tableLayout: 'fixed' }} size="small">
              <CustomTableHeader
                order={'asc'}
                orderBy={'id'}
                headLabel={headerList}
                rowCount={10}
                numSelected={0}
                onRequestSort={() => {}}
              />
              <TableBody>
                {tariffs &&
                  tariffs.map((row) => {
                    const { id, name, status, createdOn } = row
                    return (
                      <TableRow
                        hover
                        key={`${id}-${name}`}
                        onClick={() => handleRowClick(row)}
                      >
                        <TableCell>
                          <Stack direction="column">
                            <Typography
                              variant="subtitle2"
                              sx={{ color: 'primary.main' }}
                            >
                              {name}
                            </Typography>
                          </Stack>
                        </TableCell>
                        <TableCell>
                          {renderStatusChip(status as Status)}
                        </TableCell>
                        <TableCell>
                          <Typography variant="body2">
                            {createdOn && formatTimestamp(createdOn)}
                          </Typography>
                        </TableCell>
                        <TableCell>
                          <Stack direction="row" spacing={1}>
                            <TariffsMoreMenu
                              onClick={() => handleRowClick(row)}
                              label="View"
                            />
                          </Stack>
                        </TableCell>
                      </TableRow>
                    )
                  })}
              </TableBody>
            </Table>
          </TableContainer>
          {tariffsSummary.totalNumberOfPages > 0 && (
            <CustomPagination
              options={{
                ...paginationOptions,
                totalPages: tariffsSummary.totalNumberOfPages,
              }}
              handlePagination={handlePagination}
            />
          )}
        </Paper>
      </Stack>
    </Stack>
  )
}
