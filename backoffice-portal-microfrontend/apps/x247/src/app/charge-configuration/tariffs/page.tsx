'use client'
import React, { useEffect, useState } from 'react'

import PageHeader from './pageHeader'
import { TariffsList } from './TariffsList'
import { getAllTariffs } from '@/store/actions'
import { useAppDispatch, useAppSelector } from '@/store'
import { LoadingListsSkeleton } from '@dtbx/ui/components'
import { PaginationOptions } from '@dtbx/ui/components/Table'
import { useDebounce } from '@dtbx/ui/hooks'

const TariffPage = () => {
  const { isLoading } = useAppSelector((state) => state.chargeConfiguration)
  const [searchQuery, setSearchQuery] = useState<string>('')
  const [statusFilter, setStatusFilter] = useState<string>('')
  const [paginationOptions, setPaginationOptions] = useState<PaginationOptions>(
    {
      page: 1,
      size: 10,
      totalPages: 1,
    }
  )

  const dispatch = useAppDispatch()
  const debouncedSearchQuery = useDebounce(searchQuery, 800)

  useEffect(() => {
    setPaginationOptions((prev) => ({ ...prev, page: 1 }))
  }, [statusFilter, debouncedSearchQuery])

  useEffect(() => {
    getAllTariffs(dispatch, {
      page: paginationOptions.page,
      size: paginationOptions.size,
      searchQuery: debouncedSearchQuery,
      status: statusFilter.toUpperCase(),
    })
  }, [dispatch, paginationOptions, debouncedSearchQuery, statusFilter])

  return (
    <>
      <PageHeader
        onSearchChange={setSearchQuery}
        selectedStatus={statusFilter}
        onStatusChange={setStatusFilter}
      />
      {isLoading ? (
        <LoadingListsSkeleton />
      ) : (
        <TariffsList
          paginationOptions={paginationOptions}
          onPaginationChange={setPaginationOptions}
        />
      )}
    </>
  )
}

export default TariffPage
