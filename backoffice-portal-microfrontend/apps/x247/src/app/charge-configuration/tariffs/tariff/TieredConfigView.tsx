'use client'
import React, { useEffect, useState } from 'react'
import {
  <PERSON><PERSON>,
  Typography,
  Box,
  TableContainer,
  Table,
  TableHead,
  TableRow,
  TableCell,
  TextField,
  TableBody,
  Button,
} from '@mui/material'

import {
  PercentChargeIcon,
  LimitIcon,
  TierChargeIcon,
} from '@dtbx/ui/components/SvgIcons'
import { FeeTier, LimitConfiguration } from '@/store/interfaces'
import { formatCamelCaseToWords } from '@dtbx/store/utils'
import {
  ChargesLayout,
  LimitsLayout as FixedLimitsLayout,
} from './FixedConfigView'

interface TieredViewConfigProps {
  data?: {
    chargeType: 'Fixed' | 'Tiered'
    value: number
    exciseDuty: number
    limits?: number
    feeTiers?: FeeTier[]
    limitConfiguration?: LimitConfiguration
  }
  origin?: string
  readOnly?: boolean
}

//===============Start fixes here =============================
interface IConfigProps {
  getChargesData?: (data: any) => void
  getLimitsData?: (data: [{ type: string; value: number }]) => void
  getTiersData?: (data: FeeTier[]) => void
}

//===============end fixes here =============================
export const TieredViewConfig = ({
  getChargesData,
  getLimitsData,
  getTiersData,
}: IConfigProps) => {
  // if (!data) return null
  return (
    <Stack sx={{ alignItems: 'center', width: '95%', mx: 'auto' }} gap={'1rem'}>
      {/* Excise Duty */}

      <ChargesLayout getEditData={getChargesData} />
      {/* Limits */}

      <FixedLimitsLayout getEditData={getLimitsData} />

      <TierChargesLayout
        readOnly={false}
        onTiersChange={(tiers) => getTiersData && getTiersData(tiers)}
      />
    </Stack>
  )
}

interface ExciseDutyLayoutProps {
  readOnly: boolean
  exciseDuty: number
  onExciseDutyChange: (value: number) => void
}
interface LimitsLayoutProps {
  // readOnly: boolean
  // data?: LimitConfiguration
  readOnly: boolean
  limits: LimitConfiguration
  onLimitChange: (limits: LimitConfiguration) => void
}
interface TierChargesLayoutProps {
  readOnly?: boolean
  tiers?: FeeTier[]
  onTiersChange: (tiers: FeeTier[]) => void
  isEditMode?: boolean
}

interface TierCharge {
  minThreshold: string
  maxThreshold: string
  tierFee: string
}

{
  /* Excise Duty Layout Component*/
}

interface ExciseDutyLayoutProps {
  readOnly: boolean
  exciseDuty: number
  charge: number
  onExciseDutyChange: (value: number) => void
  onChargeChange: (value: number) => void
}

export const ExciseDutyLayout: React.FC<ExciseDutyLayoutProps> = ({
  readOnly = false,
  exciseDuty,
  charge,
  onExciseDutyChange,
  onChargeChange,
}) => {
  const [editingExcise, setEditingExcise] = useState(false)
  const [editingCharge, setEditingCharge] = useState(false)
  const [editExciseValue, setEditExciseValue] = useState(exciseDuty.toString())
  const [editChargeValue, setEditChargeValue] = useState(charge.toString())

  const handleExciseSave = () => {
    const value = parseFloat(editExciseValue)
    if (!isNaN(value)) onExciseDutyChange(value)
    setEditingExcise(false)
  }

  const handleChargeSave = () => {
    const value = parseFloat(editChargeValue)
    if (!isNaN(value)) onChargeChange(value)
    setEditingCharge(false)
  }

  return (
    <Stack sx={{ width: '100%' }}>
      <Stack sx={{ p: '1%', mb: '0.5rem' }} direction="row" gap={2}>
        <Stack
          sx={{
            px: '10px',
            height: '1.4rem',
            width: '1.4rem',
            border: '0.1px solid rgba(189, 189, 189, 0.6)',
            borderRadius: '50%',
            justifyContent: 'center',
            alignItems: 'center',
          }}
        >
          <PercentChargeIcon />
        </Stack>
        <Box>
          <Typography fontWeight={550}>Charges</Typography>
          <Typography>Fee structure for this service</Typography>
        </Box>
      </Stack>

      <TableContainer
        sx={{
          boxShadow:
            '0px 1px 3px 0px rgba(16, 24, 40, 0.10), 0px 1px 2px 0px rgba(16, 24, 40, 0.06)',
          border: '0.1px solid rgba(189, 189, 189, 0.6)',
          borderRadius: '0.5rem',
          overflow: 'hidden',
          width: '100%',
        }}
      >
        <Table sx={{ width: '100%' }}>
          {/* Excise Duty Row */}
          <TableRow
            sx={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
              flex: 1,
              p: 0,
              height: '2.6rem',
            }}
          >
            <TableCell
              sx={{
                display: 'flex',
                alignItems: 'center',
                // flex: 1,
                height: '2.6rem',
                width: '50%',
                '&:hover': {
                  backgroundColor: '#F9FAFB',
                  cursor: 'pointer',
                },
              }}
            >
              <Typography fontWeight={550}>Excise Duty (%)</Typography>
            </TableCell>
            <TableCell
              sx={{
                // flex: 1,
                width: '50%',
                display: 'flex',
                cursor: 'pointer',
                alignItems: 'center',
                alignContent: 'center',
                justifyContent: 'center',
                height: '2.6rem',
                border: '0.1px solid rgba(189, 189, 189, 0.6)',

                '&:hover': {
                  backgroundColor: readOnly ? undefined : '#F9FAFB',
                  cursor: readOnly ? undefined : 'pointer',
                },
              }}
            >
              {editingExcise ? (
                <TextField
                  value={editExciseValue}
                  onChange={(e) => setEditExciseValue(e.target.value)}
                  onBlur={handleExciseSave}
                  onKeyPress={(e) => e.key === 'Enter' && handleExciseSave()}
                  autoFocus
                  fullWidth
                  size="small"
                  sx={{
                    '& .MuiInputBase-root': {
                      height: '2.6rem',
                      padding: 0,
                      flex: '1',
                      border: 'none',
                    },
                  }}
                  InputProps={{
                    sx: {
                      '&:hover .MuiOutlinedInput-notchedOutline': {
                        border: 'none',
                        width: '100%',
                      },
                      '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                        width: '100%',
                        border: 'none',
                        margin: 0,
                      },
                    },
                  }}
                />
              ) : (
                <Stack
                  sx={{ width: '100%' }}
                  onClick={() => !readOnly && setEditingExcise(true)}
                >
                  <Typography>{exciseDuty}</Typography>
                </Stack>
              )}
            </TableCell>
          </TableRow>

          {/* Charge Value Row */}
          <TableRow
            sx={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
              p: 0,
              height: '2.6rem',
              flex: 1,
            }}
          >
            <TableCell
              sx={{
                display: 'flex',
                width: '50%',
                height: '2.6rem',
                alignItems: 'center',

                '&:hover': {
                  backgroundColor: '#F9FAFB',
                  cursor: 'pointer',
                },
              }}
            >
              <Typography fontWeight={550}>Charge (KES)</Typography>
            </TableCell>
            <TableCell
              sx={{
                width: '50%',
                display: 'flex',
                cursor: 'pointer',
                alignItems: 'center',
                alignContent: 'center',
                justifyContent: 'center',
                height: '2.6rem',
                border: '0.1px solid rgba(189, 189, 189, 0.6)',

                '&:hover': {
                  backgroundColor: readOnly ? undefined : '#F9FAFB',
                  cursor: readOnly ? undefined : 'pointer',
                },
              }}
            >
              {editingCharge ? (
                <TextField
                  value={editChargeValue}
                  onChange={(e) => setEditChargeValue(e.target.value)}
                  onBlur={handleChargeSave}
                  onKeyPress={(e) => e.key === 'Enter' && handleChargeSave()}
                  autoFocus
                  fullWidth
                  size="small"
                  sx={{
                    height: '2.6rem',
                    '& .MuiInputBase-root': {
                      flex: '1',
                      height: '2.6rem',
                      padding: 0,
                      border: 'none',
                    },
                  }}
                  InputProps={{
                    sx: {
                      '&:hover .MuiOutlinedInput-notchedOutline': {
                        border: 'none',
                        width: '100%',
                      },
                      '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                        width: '100%',
                        border: 'none',
                        margin: 0,
                      },
                    },
                  }}
                />
              ) : (
                <Stack
                  sx={{ width: '100%' }}
                  onClick={() => !readOnly && setEditingCharge(true)}
                >
                  <Typography>{charge}</Typography>
                </Stack>
              )}
            </TableCell>
          </TableRow>
        </Table>
      </TableContainer>
    </Stack>
  )
}

{
  /* Limits Layout Component*/
}
export const LimitsLayout: React.FC<LimitsLayoutProps> = ({
  readOnly,
  limits,
  onLimitChange,
}) => {
  const [editingCell, setEditingCell] = useState<{
    row: number
    col: keyof typeof limits
  } | null>(null)

  const [editValue, setEditValue] = useState<string>('')

  const handleChange = (field: keyof LimitConfiguration, value: string) => {
    onLimitChange({
      ...limits,
      [field]: parseFloat(value) || 0,
    })
  }

  return (
    <Stack sx={{ width: '100%' }}>
      <Stack sx={{ p: '1%' }} direction="row" gap={2}>
        <Stack
          sx={{
            px: '10px',
            height: '2rem',
            width: '2rem',
            border: '0.1px solid rgba(189, 189, 189, 0.6)',
            borderRadius: '50%',
            justifyContent: 'center',
            alignItems: 'center',
          }}
        >
          <LimitIcon />
        </Stack>
        <Box>
          <Typography fontWeight={550}>Limits</Typography>
          <Typography>
            Restrictions or caps placed on specific aspects of this payment
            service.
          </Typography>
        </Box>
      </Stack>

      <TableContainer
        sx={{
          border: '0.1px solid rgba(189, 189, 189, 0.6)',
          borderRadius: '0.5rem',
          boxShadow:
            '0px 1px 3px 0px rgba(16, 24, 40, 0.10), 0px 1px 2px 0px rgba(16, 24, 40, 0.06)',
          overflow: 'hidden',
        }}
      >
        <Table sx={{ width: '100%' }}>
          <TableHead>
            <TableRow>
              <TableCell
                sx={{
                  border: '0.1px solid rgba(189, 189, 189, 0.6)',
                  width: '57%',
                  height: '2.6rem',
                  padding: '0.5rem',
                }}
              >
                <Typography
                  sx={{ marginLeft: '0.5rem', whiteSpace: 'no wrap' }}
                  fontWeight={550}
                >
                  Limit Type
                </Typography>
              </TableCell>
              <TableCell
                sx={{
                  border: '0.1px solid rgba(189, 189, 189, 0.6)',
                  width: '50%',
                  height: '2.6rem',
                  padding: '0.5rem',
                }}
              >
                <Typography sx={{ marginLeft: '0.5rem' }} fontWeight={550}>
                  Limit Value
                </Typography>
              </TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {Object.entries(limits)
              .filter(([type]) =>
                [
                  'dailyLimit',
                  'perTransactionLowerLimit',
                  'perTransactionUpperLimit',
                  'frequencyLimit',
                ].includes(type)
              )
              .map(([type, value], rowIndex) => (
                <TableRow key={rowIndex} sx={{ height: '2.6rem', padding: 0 }}>
                  <TableCell
                    sx={{
                      border: '0.1px solid rgba(189, 189, 189, 0.6)',
                      height: '2.6rem',
                      padding: '0.5rem',
                      '&:hover': {
                        backgroundColor: '#F9FAFB',
                        cursor: 'pointer',
                      },
                    }}
                  >
                    <Typography sx={{ marginLeft: '0.5rem' }} fontWeight={550}>
                      {formatCamelCaseToWords(type)}
                    </Typography>
                  </TableCell>
                  <TableCell
                    sx={{
                      border: '0.1px solid rgba(189, 189, 189, 0.6)',
                      height: '2.6rem',
                      padding:
                        editingCell?.row === rowIndex &&
                        editingCell?.col === type
                          ? 0
                          : '0.5rem',
                      '&:hover': {
                        backgroundColor: '#F9FAFB',
                        cursor: 'pointer',
                      },
                    }}
                  >
                    {!readOnly ? (
                      <TextField
                        value={value.toString()}
                        onChange={(e) =>
                          handleChange(
                            type as keyof LimitConfiguration,
                            e.target.value
                          )
                        }
                        autoFocus
                        fullWidth
                        size="small"
                        sx={{
                          height: '100%',
                          '& .MuiInputBase-root': {
                            height: '100%',
                            padding: 0,
                          },
                        }}
                        InputProps={{
                          sx: {
                            '&:hover .MuiOutlinedInput-notchedOutline': {
                              border: 'none',
                              padding: 0,
                            },
                            '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                              border: 'none',
                              padding: 0,
                            },
                          },
                        }}
                      />
                    ) : (
                      <Typography
                        sx={{ marginLeft: '0.5rem' }}
                        fontWeight={550}
                      >
                        {value}
                      </Typography>
                    )}
                  </TableCell>
                </TableRow>
              ))}
          </TableBody>
        </Table>
      </TableContainer>
    </Stack>
  )
}

export const TierChargesLayout: React.FC<TierChargesLayoutProps> = ({
  readOnly = false,
  tiers: initialTiers = [], // Default to empty array for creation mode
  onTiersChange,
  isEditMode = false, // Default to false (creation mode)
}) => {
  // State to manage tiers
  const [tiers, setTiers] = useState<FeeTier[]>(
    initialTiers.length > 0
      ? initialTiers
      : [{ minimum: 0, maximum: 0, feeAmount: 0 }]
  )

  // State for editing
  const [editingCell, setEditingCell] = useState<{
    row: number
    col: keyof FeeTier
  } | null>(null)

  const [editValue, setEditValue] = useState<string>('')

  // Add a new tier (only for creation mode)
  const addTier = () => {
    const newTiers = [...tiers, { minimum: 0, maximum: 0, feeAmount: 0 }]
    setTiers(newTiers)
    onTiersChange?.(newTiers)
  }

  // Handle cell click for editing
  const handleCellClick = (row: number, col: keyof FeeTier): void => {
    if (readOnly) return
    setEditingCell({ row, col })
    setEditValue(
      tiers[row] && tiers[row][col] ? tiers[row][col].toString() : ''
    )
  }

  // Save edited value

  const handleCellBlur = (): void => {
    if (editingCell) {
      const updatedTiers = [...tiers]
      // @ts-ignore
      updatedTiers[editingCell.row][editingCell.col] =
        parseFloat(editValue) || 0
      setTiers(updatedTiers)
      setEditingCell(null)
      onTiersChange?.(updatedTiers)
    }
  }

  // Handle input changes
  const handleCellChange = (
    event: React.ChangeEvent<HTMLInputElement>
  ): void => {
    setEditValue(event.target.value)
  }

  return (
    <Stack sx={{ width: '100%' }}>
      <Stack sx={{ p: '1%' }} direction="row" gap={2}>
        <Stack
          sx={{
            px: '10px',
            height: '2rem',
            width: '2rem',
            border: '0.1px solid rgba(189, 189, 189, 0.6)',
            borderRadius: '50%',
            justifyContent: 'center',
            alignItems: 'center',
          }}
        >
          <TierChargeIcon />
        </Stack>
        <Box>
          <Typography fontWeight={550}>Tier Charges</Typography>
          <Typography>
            The fee structure for this service based on the value of
            transactions.
          </Typography>
        </Box>
      </Stack>

      <TableContainer
        sx={{
          border: '0.1px solid rgba(189, 189, 189, 0.6)',
          borderRadius: '0.5rem',
          boxShadow:
            '0px 1px 3px 0px rgba(16, 24, 40, 0.10), 0px 1px 2px 0px rgba(16, 24, 40, 0.06)',
          overflow: 'hidden',
        }}
      >
        <Table sx={{ width: '100%' }}>
          <TableHead>
            <TableRow>
              {[
                'Minimum Threshold (KES)',
                'Maximum Threshold (KES)',
                'Tier Fee (KES)',
              ].map((header) => (
                <TableCell
                  key={header}
                  sx={{
                    border: '0.1px solid rgba(189, 189, 189, 0.6)',
                    width: '35%',
                    height: '2.6rem',
                    padding: '0.5rem',
                  }}
                >
                  <Typography
                    sx={{
                      marginLeft: '0.5rem',
                      whiteSpace: 'nowrap',
                      overflow: 'hidden',
                      textOverflow: 'ellipsis',
                    }}
                    fontWeight={550}
                  >
                    {header}
                  </Typography>
                </TableCell>
              ))}
            </TableRow>
          </TableHead>
          <TableBody>
            {tiers.map((tier, rowIndex) => (
              <TableRow key={rowIndex}>
                {(['minimum', 'maximum', 'feeAmount'] as const).map((field) => (
                  <TableCell
                    key={field}
                    sx={{
                      border: '0.1px solid rgba(189, 189, 189, 0.6)',
                      height: '2.6rem',
                      padding:
                        editingCell?.row === rowIndex &&
                        editingCell?.col === field
                          ? '0'
                          : '0.5rem',
                      '&:hover': {
                        backgroundColor: '#F9FAFB',
                        cursor: readOnly ? 'default' : 'pointer',
                      },
                    }}
                    onClick={() =>
                      !readOnly && handleCellClick(rowIndex, field)
                    }
                  >
                    {editingCell?.row === rowIndex &&
                    editingCell?.col === field ? (
                      <TextField
                        value={editValue}
                        onChange={handleCellChange}
                        onBlur={handleCellBlur}
                        autoFocus
                        fullWidth
                        size="small"
                        sx={{
                          '& .MuiInputBase-root': {
                            height: '2.6rem',
                            padding: 0,
                          },
                        }}
                        InputProps={{
                          sx: {
                            '&:hover .MuiOutlinedInput-notchedOutline': {
                              border: 'none',
                            },
                            '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                              border: 'none',
                            },
                          },
                        }}
                      />
                    ) : (
                      <Typography
                        sx={{ marginLeft: '0.5rem' }}
                        fontWeight={550}
                      >
                        {tier[field]}
                      </Typography>
                    )}
                  </TableCell>
                ))}
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>

      {/* Show "Add Tier" button only in creation mode */}
      {!isEditMode && !readOnly && (
        <Button onClick={addTier} variant="outlined" sx={{ mt: 2 }}>
          Add Tier
        </Button>
      )}
    </Stack>
  )
}
