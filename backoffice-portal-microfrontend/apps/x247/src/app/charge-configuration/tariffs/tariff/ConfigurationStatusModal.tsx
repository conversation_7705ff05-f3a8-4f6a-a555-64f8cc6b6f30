import { Status } from '@/store/interfaces'
import { HasAccessToRights } from '@dtbx/store/utils'
import { Dialog } from '@dtbx/ui/components/Overlay'
import { Box, Button, Typography } from '@mui/material'
import { AccessControlWrapper } from '@dtbx/store/utils'
import { useAppDispatch } from '@/store'
import React from 'react'

import { deactivateServiceConfiguration } from '@/store/actions'

interface ConfigurationStatusModalProps {
  label?: string
  name: string
  status: Status
  disabled?: boolean
  serviceId?: string
}

const ConfigurationStatusModal: React.FC<ConfigurationStatusModalProps> = ({
  label,
  name,
  status,
  disabled = false,
  serviceId,
}) => {
  const [open, setOpen] = React.useState(false)
  const dispatch = useAppDispatch()

  const reasonsForDeactivating: string[] = [
    'Regulatory Compliance',
    'Service Integration Issue',
    'Operational Changes',
    'Fraud Mitigation',
    'Other',
  ]

  const reasonsForActivating: string[] = [
    'Reinstatement',
    'Resolved Issues',
    'Other',
  ]

  const reasons =
    status === 'ACTIVE' ? reasonsForDeactivating : reasonsForActivating

  const handleClick = () => {
    if (!disabled) {
      setOpen((prev) => !prev)
    }
  }

  const buttonText = label || (status === 'ACTIVE' ? 'Deactivate' : 'Activate')
  const handleActivate = async (reason: string[]) => {
    if (serviceId) {
      if (
        HasAccessToRights(['SUPER_DEACTIVATE_PAYMENT_SERVICE_CONFIGURATIONS'])
      ) {
        await deactivateServiceConfiguration(
          serviceId,
          dispatch,
          reason[0],
          'super'
        )
      } else {
        await deactivateServiceConfiguration(
          serviceId,
          dispatch,
          reason[0],
          'make'
        )
      }
    } else {
      console.error('serviceId is required')
    }
  }
  return (
    <Box>
      <AccessControlWrapper
        rights={[
          'SUPER_DEACTIVATE_PAYMENT_SERVICE_CONFIGURATIONS',
          'MAKE_DEACTIVATE_PAYMENT_SERVICE_CONFIGURATIONS',
        ]}
      >
        <Button
          sx={{
            padding: '0.25em 0.5em',
            borderRadius: '7px',
            textTransform: 'none',
            border: '1px solid #AAADB0',
            color: '#555C61',
            fontWeight: 400,
            width: '100%',
            backgroundColor: '#FFFF',
            height: '3vh',
            whiteSpace: 'nowrap',
            opacity: disabled ? 0.5 : 1,
            cursor: disabled ? 'not-allowed' : 'pointer',
          }}
          variant="outlined"
          onClick={handleClick}
          disabled={disabled}
        >
          <Typography
            sx={{
              color: '#555C61',
              fontSize: '0.875rem',
              fontWeight: 500,
            }}
          >
            {buttonText}
          </Typography>
        </Button>
      </AccessControlWrapper>

      <Dialog
        buttonText={status === 'ACTIVE' ? 'Deactivate' : 'Activate'}
        title={status === 'ACTIVE' ? `Deactivate ${name}` : `Activate ${name}`}
        open={open}
        descriptionText={`${name} will be ${status === 'ACTIVE' ? 'deactivated' : 'activated'}.
          Are you sure you want to ${status === 'ACTIVE' ? 'deactivate' : 'activate'} ${name}?`}
        setOpen={setOpen}
        buttonProps={{
          color: status === 'ACTIVE' ? '#EB0045' : '#12B76A',
        }}
        reasons={reasons}
        onClick={async (reason) => {
          await handleActivate(reason)
        }}
      />
    </Box>
  )
}

export default ConfigurationStatusModal
