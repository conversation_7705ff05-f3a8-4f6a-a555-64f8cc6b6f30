import { Stack, Typography } from '@mui/material'
import React from 'react'
import { EmptyFolder } from '@dtbx/ui/icons'

const EmptyPage = () => {
  return (
    <Stack
      sx={{
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        gap: '10px',
        paddingTop: '10%',
        height: '70vh',
        border: '1px solid #EAECF0',
        borderRadius: '4px',
        boxShadow:
          '0px 1px 3px 0px rgba(16, 24, 40, 0.10), 0px 1px 2px 0px rgba(16, 24, 40, 0.06)',
      }}
    >
      <EmptyFolder />
      <Typography
        sx={{
          fontWeight: 700,
        }}
      >
        There are no records to show.
      </Typography>
    </Stack>
  )
}

export default EmptyPage
