import React, { useState } from 'react'
import {
  <PERSON>er,
  <PERSON>ack,
  Typo<PERSON>,
  IconButton,
  TextField,
  Button,
  Avatar,
  Paper,
} from '@mui/material'
import { Close } from '@mui/icons-material'
import { formatTimestamp } from '@dtbx/store/utils'
import { ConfigurationItemIcon, InfoIcon } from '@dtbx/ui/icons'
import { useAppDispatch, useAppSelector } from '@/store'
import { setApprovalDrawerOpen } from '@/store/reducers/ApprovalRequests'
import {
  ConfigurationComparisonTable,
  ExciseLimitsDisplay,
  TierInfoDisplay,
} from '../../TariffsMoreMenu'

interface ChangesLogMoreMenuProps {
  hideButton?: boolean
  onClick: () => void
  title: string
  description: string
}

const ChangesLogMoreMenu: React.FC<ChangesLogMoreMenuProps> = ({
  hideButton = false,
  title,
  description,
}) => {
  const dispatch = useAppDispatch()
  const [open, setOpen] = useState<boolean>(false)

  const { selectedApprovalRequest } = useAppSelector(
    (state) => state.approvalRequests
  )
  const { selectedConfigurationLog } = useAppSelector(
    (state) => state.chargeConfiguration
  )

  const handleOpen = () => {
    setOpen(true)
    dispatch(setApprovalDrawerOpen(true))
  }

  const handleClose = () => {
    setOpen(false)
    dispatch(setApprovalDrawerOpen(false))
  }

  const ConfigurationLogItem = [
    {
      title: selectedConfigurationLog?.name || 'Pesalink',
      description:
        selectedConfigurationLog?.status === 'Active'
          ? `Deactivate ${selectedConfigurationLog.name || 'Pesalink'}`
          : `Activate ${selectedConfigurationLog.name || 'Pesalink'}`,
    },
  ]

  return (
    <>
      {!hideButton && (
        <Button
          onClick={handleOpen}
          variant="outlined"
          sx={{
            padding: '0.25em 0.5em',
            borderRadius: '7px',
            textTransform: 'none',
            border: '1px solid #AAADB0',
            color: '#555C61',
            fontWeight: 400,
            width: '100%',
            maxWidth: {
              xs: '80vw',
              sm: '60vw',
              md: '5vw',
            },
            height: '3vh',
            textWrap: 'noWrap',
          }}
        >
          View
        </Button>
      )}
      <Drawer
        sx={{
          '.MuiDrawer-paper': {
            width: '32%',
          },
        }}
        open={open}
        anchor="right"
        onClose={handleClose}
      >
        <Stack
          sx={{
            justifyContent: 'space-between',
            alignItems: 'center',
            flexDirection: 'row',
            px: '1.5vw',
            py: '1vh',
            background: '#FFFFF',
            borderBottom: '2px solid #F2F4F7',
          }}
        >
          <Typography
            variant="h6"
            sx={{
              alignItems: 'start',
              fontWeight: 700,
              fontSize: '1rem',
            }}
          >
            {`${selectedConfigurationLog.name || 'Pesalink'}`}
          </Typography>
          <IconButton onClick={handleClose} sx={{ background: '#F1F5F9' }}>
            <Close />
          </IconButton>
        </Stack>

        <Stack
          sx={{
            p: '2vw',
            gap: '2vh',
          }}
        >
          <TextField
            fullWidth
            label="Approval request type"
            value={
              selectedApprovalRequest.makerCheckerType?.name ||
              'Deactivate Pesalink'
            }
          />

          <Stack direction="column" maxWidth={447} spacing={2}>
            <Typography
              variant="body2"
              fontWeight="medium"
              color="text.secondary"
              sx={{ lineHeight: 'normal' }}
            >
              Changes made
            </Typography>
            <Paper
              elevation={0}
              sx={{
                overflow: 'hidden',
                px: 2.5,
                pb: 2.5,
                mt: 2.5,
                width: '100%',
                height: 'auto',
                border: '1px solid #EAECF0',
                boxShadow: '0px 1px 2px 0px rgba(16, 24, 40, 0.05)',
                borderRadius: 1,
                backgroundColor: '#FFFF',
              }}
            >
              {ConfigurationLogItem.map((item, index) => (
                <Stack key={index} direction="row" spacing={1} width="100%">
                  <Stack
                    direction="row"
                    spacing={2.5}
                    alignItems="start"
                    pt={3}
                    width="28px"
                  >
                    <Avatar
                      sx={{
                        backgroundColor: '#E7E8E9',
                        borderRadius: 1,
                        width: '35px',
                        height: '35px',
                      }}
                      alt={`${item.title} Icon`}
                    >
                      <ConfigurationItemIcon />
                    </Avatar>
                  </Stack>
                  <Stack
                    direction="column"
                    justifyContent="center"
                    spacing={0.5}
                    p={2.5}
                  >
                    <Typography
                      variant="body1"
                      sx={{ fontWeight: '700', fontSize: '1rem' }}
                    >
                      {item.title}
                    </Typography>
                    <Typography
                      variant="body1"
                      sx={{ fontWeight: '400', fontSize: '0.8125rem' }}
                    >
                      &nbsp; &#8226; {item.description}
                    </Typography>
                  </Stack>
                </Stack>
              ))}
              <Stack spacing={1} sx={{ width: '100%' }}>
                {/* <ConfigurationComparisonTable /> */}
                <ExciseLimitsDisplay />
                {/* <TierInfoDisplay /> */}
              </Stack>
            </Paper>
          </Stack>

          <TextField
            fullWidth
            label="Maker"
            value={
              selectedApprovalRequest.makerCheckerType?.name ||
              'Derrick Karanja'
            }
            InputProps={{
              readOnly: true,
            }}
          />

          <TextField
            fullWidth
            label="Maker Timestamp"
            value={formatTimestamp(
              selectedApprovalRequest.dateCreated || '3 Jan 2024, 11:30'
            )}
            InputProps={{
              readOnly: true,
            }}
          />

          <TextField
            fullWidth
            label="Maker comment"
            value={selectedApprovalRequest.makerComments || 'Business request.'}
            InputProps={{
              readOnly: true,
            }}
          />
          <TextField
            fullWidth
            label="Checker"
            value={selectedApprovalRequest.checker || 'Alice Atieno'}
            InputProps={{
              readOnly: true,
            }}
          />

          <TextField
            fullWidth
            label="Checker Timestamp"
            value={formatTimestamp(
              selectedApprovalRequest.dateCreated || 'June 6, 2023 12:23 PM'
            )}
            InputProps={{
              readOnly: true,
            }}
          />
          <TextField
            fullWidth
            label="Checker comment"
            value={selectedApprovalRequest.checkerComments || 'Approved.'}
            InputProps={{
              readOnly: true,
            }}
          />
        </Stack>
      </Drawer>
    </>
  )
}

export default ChangesLogMoreMenu
