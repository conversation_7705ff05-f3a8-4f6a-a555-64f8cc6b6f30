import {
  <PERSON><PERSON>,
  <PERSON><PERSON>T<PERSON>le,
  Drawer,
  IconButton,
  Paper,
  Stack,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableRow,
  Typography,
} from '@mui/material'

import { IHeadCell } from '@dtbx/store/interfaces'
import { useAppDispatch, useAppSelector } from '@/store'
import { CustomTableHeader } from '@dtbx/ui/components/Table'
import { CustomSkeleton } from '@dtbx/ui/components'
import { CustomSearchInput } from '@dtbx/ui/components/Input'
import { IConfigurationLogs } from '@/store/interfaces/chargeConfiguration'
import { useCustomRouter } from '@dtbx/ui/hooks'
import {
  setOpenDrawerAction,
  setSelectedConfigurationLog,
} from '@/store/reducers/chargeConfigurationReducer'
import { Close, SearchRounded } from '@mui/icons-material'
import EmptyPage from './EmptyPage'
import ChangesLogMoreMenu from './ChangesLogMoreMenu'

const header: IHeadCell[] = [
  {
    id: 'event',
    label: 'Event Type',
    alignCenter: false,
    alignRight: false,
  },
  {
    id: 'maker',
    label: 'Maker',
    alignCenter: false,
    alignRight: false,
  },

  {
    id: 'checker',
    label: 'Checker',
    alignCenter: false,
    alignRight: false,
  },
  {
    id: 'actions',
    label: 'Actions',
    alignCenter: false,
    alignRight: false,
  },
]

interface ConfigurationLogsDrawerProps {
  hideButton?: boolean
}

const ChangesLogDrawer: React.FC<ConfigurationLogsDrawerProps> = ({
  hideButton = false,
}) => {
  const dispatch = useAppDispatch()
  const router = useCustomRouter()
  const { isLoading, openChangeLogsDrawer } = useAppSelector(
    (state) => state.chargeConfiguration
  )
  const configurationLogs: IConfigurationLogs[] = [
    {
      id: 'T001',
      name: 'Deactivate Pesalink',
      maker: 'Derrick Karanja',
      checker: 'Alice Atieno',
      actions: 'view',
    },
    {
      id: 'T002',
      name: 'Edit RTGS',
      maker: 'Derrick Karanja',
      checker: 'Alice Atieno',
      actions: 'view',
    },
    {
      id: 'T003',
      name: 'Edit Airtel',
      maker: 'Derrick Karanja',
      checker: 'Alice Atieno',
      actions: 'view',
    },
    {
      id: 'T004',
      name: 'Create tariff',
      maker: 'Derrick Karanja',
      checker: 'Alice Atieno',
      actions: 'view',
    },
  ]
  const handleRowClick = (configurationLog: (typeof configurationLogs)[0]) => {
    dispatch(setSelectedConfigurationLog(configurationLog))
    router.push('') // open configuration view log details Drawer
  }

  const toggleDrawer = (open: boolean) => {
    dispatch(setOpenDrawerAction(open))
  }
  return (
    <>
      {!hideButton && (
        <Button
          onClick={() => dispatch(setOpenDrawerAction(true))}
          sx={{
            border: '1px solid #D0D5DD',
            borderRadius: '6px',
            padding: '9px 28px',
            textWrap: 'nowrap',
          }}
          variant="outlined"
        >
          <Typography
            sx={{
              color: '#555C61',
              fontSize: '0.875rem',
              fontWeight: 500,
            }}
          >
            Changes log
          </Typography>
        </Button>
      )}
      <Drawer
        sx={{
          '.MuiDrawer-paper': {
            width: '88%',
          },
        }}
        anchor="right"
        open={openChangeLogsDrawer}
        onClose={() => toggleDrawer(false)}
      >
        <Stack
          sx={{
            background: '#F2F4F7',
            flexDirection: 'row',
            justifyContent: 'space-between',
            alignItems: 'center',
            alignContent: 'center',
            padding: '1rem 0.5rem 0.5rem',
          }}
        >
          <Stack
            flexDirection="row"
            sx={{
              alignItems: 'center',
            }}
          >
            <DialogTitle
              sx={{
                display: 'flex',
                flexDirection: 'row',
                gap: 4,
                alignItems: 'center',
                alignContent: 'center',
                py: '0.5rem',
              }}
            >
              <Typography
                variant="subtitle2"
                sx={{ color: '#2A3339', fontSize: '1rem', fontWeight: '700' }}
              >
                Changes Log
              </Typography>
            </DialogTitle>
          </Stack>
          <IconButton
            onClick={() => toggleDrawer(false)}
            sx={{
              background: '#F7F7F7',
              border: '1px solid #EAECF0',
              marginRight: '1%',
            }}
          >
            <Close />
          </IconButton>
        </Stack>
        <Stack spacing={2} padding={{ xs: 1.5, sm: 1.5, md: '1.5rem 2.5rem' }}>
          <CustomSearchInput
            sx={{
              width: '30%',
              '&.Mui-focused': {
                width: '40%',
              },
              borderRadius: '4px',
              '& fieldset': {
                border: '1px solid #D0D5DD !important',
              },
            }}
            startAdornment={<SearchRounded />}
            placeholder="Search"
          />
          <Stack>
            <Paper
              elevation={0}
              sx={{
                border: '1px solid #EAECF0',
                boxShadow:
                  '0px 1px 3px 0px rgba(16, 24, 40, 0.10), 0px 1px 2px 0px rgba(16, 24, 40, 0.06)',
              }}
            >
              <TableContainer
                component={Paper}
                elevation={0}
                sx={{
                  boxShadow: 'none',
                }}
              >
                {!isLoading ? (
                  configurationLogs?.length > 0 ? (
                    <Table
                      sx={{ minWidth: 650 }}
                      aria-label="designations table"
                    >
                      <CustomTableHeader
                        order={'desc'}
                        orderBy={''}
                        rowCount={0}
                        headLabel={[...header]}
                        numSelected={0}
                      />
                      <TableBody>
                        {configurationLogs &&
                          configurationLogs.map((row) => {
                            return (
                              <TableRow key={row.id} hover>
                                <TableCell>{row.name}</TableCell>
                                <TableCell>{row.maker}</TableCell>
                                <TableCell>{row.checker || 'N/A'}</TableCell>
                                <TableCell>
                                  <ChangesLogMoreMenu
                                    onClick={() => handleRowClick(row)}
                                    title={''}
                                    description={''}
                                  />
                                </TableCell>
                              </TableRow>
                            )
                          })}
                      </TableBody>
                    </Table>
                  ) : (
                    <EmptyPage />
                  )
                ) : (
                  <CustomSkeleton
                    variant="rectangular"
                    animation="wave"
                    sx={{
                      height: '70vh',
                      width: '100%',
                    }}
                  />
                )}
              </TableContainer>
            </Paper>
          </Stack>
        </Stack>
      </Drawer>
    </>
  )
}

export default ChangesLogDrawer
