import { Stack, Box, Typography } from '@mui/material'
import {
  DeleteConfigIcon,
  CheckConfigIcon,
  EditConfigIcon,
} from '@dtbx/ui/components/SvgIcons'
import { useAppDispatch } from '@/store'
import { removeConfiguredService } from '@/store/reducers'

interface ISelectedServiceProps {
  service: { serviceId: string; name: string }
}

export const SelectedService = ({ service }: ISelectedServiceProps) => {
  const dispatch = useAppDispatch()

  const handleDelete = () => {
    dispatch(removeConfiguredService(service.serviceId))
  }

  return (
    <Stack
      sx={{
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        border: '1px solid #555c61',
        borderRadius: '4px',
        boxShadow:
          '0px 12.514px 15.017px -2.503px rgba(16, 24, 40, 0.08), 0px 5.006px 5.006px -2.503px rgba(16, 24, 40, 0.03)',
        px: '0.5rem',
        height: '6vh',
      }}
    >
      <Stack sx={{ flexDirection: 'row', gap: '0.5rem' }}>
        {' '}
        <Typography>{service.name}</Typography>
        <Box
          sx={{
            height: '1.5rem',
            width: '1.8rem',
            border: '1px solid #555c61',
            borderRadius: '0.3rem',
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
          }}
        >
          <CheckConfigIcon />
        </Box>
      </Stack>
      <Stack sx={{ flexDirection: 'row', gap: '0.2rem' }}>
        <Box
          sx={{
            height: '1.5rem',
            width: '1.8rem',
            border: '1px solid #555c61',
            borderRadius: '0.3rem',
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            cursor: 'pointer',
          }}
        >
          <EditConfigIcon />
        </Box>
        <Box
          onClick={handleDelete}
          sx={{
            height: '1.5rem',
            width: '1.8rem',
            border: '1px solid #555c61',
            borderRadius: '0.3rem',
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            cursor: 'pointer',
          }}
        >
          <Typography>
            <DeleteConfigIcon />
          </Typography>
        </Box>
      </Stack>
    </Stack>
  )
}
