'use client'
import { Divider, Stack } from '@mui/material'
import React, { useState } from 'react'
import { AntTab, AntTabs, TabPanel } from '@dtbx/ui/components/Tabs'
import { ConfigurationsList } from './ConfigurationsList'
import { TieredServiceList } from './TieredServiceList'
import { FixedServiceList } from './FixedServiceList'
import { NotificationServiceList } from './NotificationServiceList'

interface TariffConfigurationsPageProps {
  setType: (type: string) => void
  setServiceType?: (type: string) => void
}
const TariffConfigurationsPage = ({
  setType,
  setServiceType,
}: TariffConfigurationsPageProps) => {
  const [value, setValue] = useState<number>(0)
  const handleChange = (event: React.SyntheticEvent, newValue: number) => {
    setValue(newValue)
  }

  return (
    <Stack
      sx={{
        backgroundColor: '#FFFFFF',
        padding: '1.25rem',
      }}
    >
      <AntTabs
        value={value}
        onChange={handleChange}
        aria-label="ant example"
        sx={{
          '& .MuiTabs-flexContainer': {
            gap: '2.5rem',
            marginLeft: '2%',
          },
        }}
      >
        <AntTab
          label={`All configurations`}
          sx={{
            marginBottom: '-0.625rem',
            fontSize: '1rem',
          }}
        />
        <AntTab
          label={`Tiered Services`}
          sx={{
            marginBottom: '-0.625rem',
            fontSize: '1rem',
          }}
          iconPosition="end"
        />
        <AntTab
          label={`Fixed Services`}
          sx={{
            marginBottom: '-0.625rem',
            fontSize: '1rem',
          }}
          iconPosition="end"
        />
        <AntTab
          label={`Trigger based notifications`}
          sx={{
            marginBottom: '-0.625rem',
            fontSize: '1rem',
          }}
          iconPosition="end"
        />
      </AntTabs>
      <Divider sx={{ width: '95%', margin: '0 auto', marginBottom: '1rem' }} />

      <TabPanel value={value} index={0}>
        <ConfigurationsList setType={setType} />
      </TabPanel>
      <TabPanel value={value} index={1}>
        <TieredServiceList
          setType={setType}
          setServiceType={setServiceType ?? (() => {})}
        />
      </TabPanel>
      <TabPanel value={value} index={2}>
        <FixedServiceList
          setType={setType}
          setServiceType={setServiceType ?? (() => {})}
        />
      </TabPanel>
      <TabPanel value={value} index={3}>
        <NotificationServiceList
          setType={setType}
          setServiceType={setServiceType ?? (() => {})}
        />
      </TabPanel>
    </Stack>
  )
}

export default TariffConfigurationsPage
