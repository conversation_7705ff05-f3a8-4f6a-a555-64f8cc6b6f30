import React, { useEffect, useState } from 'react'
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  Typo<PERSON>,
  IconButton,
  TextField,
  Button,
  Avatar,
  Paper,
} from '@mui/material'
import { Close, Check } from '@mui/icons-material'
import { formatTimestamp, HasAccessToRights } from '@dtbx/store/utils'
import { ConfigurationItemIcon, InfoIcon } from '@dtbx/ui/icons'
import { useAppDispatch, useAppSelector } from '@/store'
import { setApprovalDrawerOpen } from '@/store/reducers/ApprovalRequests'
import {
  acceptRejectConfigCreate,
  approveUpdateServiceConfiguration,
} from '@/store/actions'
import { setDrawer } from '@dtbx/store/reducers'
import {
  ConfigurationComparisonTable,
  ExciseLimitsDisplay,
  TierInfoDisplay,
} from '../../TariffsMoreMenu'

interface ApprovalRequestDrawerProps {
  // hideButton?: boolean
  // title: string
  // description: string
  // selectedApprovalRequest: any
  // drawerOpen?: boolean
  // onClose: () => void
  // handleOpenDrawer?: () => void
  drawerOpen: boolean
  title: string
  description: string
  onClose: () => void
}

const ApprovalRequestDrawer: React.FC<ApprovalRequestDrawerProps> = ({
  drawerOpen,
  title,
  description,
  onClose,
}) => {
  const dispatch = useAppDispatch()
  const [open, setOpen] = useState<boolean>(false)
  const [checkerComments, setCheckerComments] = useState<string>('')
  const [commentsError, setCommentsError] = useState<boolean>(false)

  const { selectedApprovalRequest } = useAppSelector(
    (state) => state.approvalRequests
  )
  const { selectedConfiguration } = useAppSelector(
    (state) => state.chargeConfiguration
  )

  const handleReject = async () => {
    if (!checkerComments) {
      setCommentsError(true)
      return
    }

    if (
      HasAccessToRights([
        'REJECT_CREATE_TARIFFS',
        'REJECT_UPDATE_PAYMENT_SERVICE_CONFIGURATIONS',
      ])
    ) {
      switch (selectedApprovalRequest.makerCheckerType.type) {
        case 'UPDATE_PAYMENT_SERVICE_CONFIGURATIONS':
          await approveUpdateServiceConfiguration(
            selectedApprovalRequest.id,
            dispatch,
            checkerComments,
            'reject'
          )
          break

        case 'CREATE_PAYMENT_SERVICE_CONFIGURATIONS':
          await acceptRejectConfigCreate(
            'reject',
            selectedApprovalRequest.id,
            dispatch,
            checkerComments
          )
          break

        default:
          console.warn(
            'Unhandled makerCheckerType:',
            selectedApprovalRequest.makerCheckerType.type
          )
          break
      }

      onClose()
      dispatch(
        setDrawer({
          open: false,
          drawerChildren: null,
          header: '',
        })
      )
    }

    onClose()
    dispatch(
      setDrawer({
        open: false,
        drawerChildren: null,
        header: '',
      })
    )
  }

  const handleApprove = async () => {
    if (!checkerComments) {
      setCommentsError(true)
      return
    }

    if (
      HasAccessToRights([
        'ACCEPT_CREATE_TARIFFS',
        'ACCEPT_UPDATE_PAYMENT_SERVICE_CONFIGURATIONS',
      ])
    ) {
      switch (selectedApprovalRequest.makerCheckerType.type) {
        case 'UPDATE_PAYMENT_SERVICE_CONFIGURATIONS':
          await approveUpdateServiceConfiguration(
            selectedApprovalRequest.id,
            dispatch,
            checkerComments,
            'approve'
          )
          break

        case 'CREATE_PAYMENT_SERVICE_CONFIGURATIONS':
          await acceptRejectConfigCreate(
            'accept',
            selectedApprovalRequest.id,
            dispatch,
            checkerComments
          )
          break
      }

      onClose()
      dispatch(
        setDrawer({
          open: false,
          drawerChildren: null,
          header: '',
        })
      )
    }

    onClose()
    dispatch(
      setDrawer({
        open: false,
        drawerChildren: null,
        header: '',
      })
    )
  }

  const ConfigurationItem = selectedConfiguration
    ? [
        {
          title: selectedConfiguration.service?.serviceName || '',
          description:
            selectedConfiguration.status === 'ACTIVE'
              ? `Deactivate ${selectedConfiguration.service?.serviceName || ''}`
              : `Activate ${selectedConfiguration.service?.serviceName || ''}`,
        },
      ]
    : []

  const entity =
    typeof selectedApprovalRequest?.entity === 'string'
      ? JSON.parse(selectedApprovalRequest.entity)
      : selectedApprovalRequest?.entity

  return (
    <>
      <Drawer
        sx={{
          '.MuiDrawer-paper': {
            width: '50%',
          },
        }}
        open={drawerOpen}
        anchor="right"
        onClose={onClose}
        // onClose={handleClose}
      >
        <Stack
          sx={{
            justifyContent: 'space-between',
            alignItems: 'center',
            flexDirection: 'row',
            px: '1.5vw',
            py: '1vh',
            background: '#FFFFF',
            borderBottom: '2px solid #F2F4F7',
          }}
        >
          <Typography
            variant="h6"
            sx={{
              alignItems: 'start',
              fontWeight: 700,
              fontSize: '1rem',
            }}
          >
            Approval request details
          </Typography>
          <IconButton onClick={onClose} sx={{ background: '#F1F5F9' }}>
            <Close />
          </IconButton>
        </Stack>

        <Stack
          sx={{
            p: '2vw',
            gap: '2vh',
          }}
        >
          <TextField
            fullWidth
            label="Approval request type"
            value={
              selectedApprovalRequest.makerCheckerType?.name ||
              'Deactivate Pesalink'
            }
          />

          <Stack
            direction="column"
            // maxWidth={447}
            spacing={2}
            height="max-content"
          >
            <Typography
              variant="body2"
              fontWeight="medium"
              color="#555C61"
              sx={{ lineHeight: 'normal' }}
            >
              Changes made
            </Typography>
            <Paper
              elevation={0}
              sx={{
                overflowY: 'auto',
                px: 2.5,
                pb: 2.5,
                mt: 2.5,
                width: '100%',
                height: 'auto',
                border: '1px solid #EAECF0',
                boxShadow: '0px 1px 2px 0px rgba(16, 24, 40, 0.05)',
                borderRadius: 1,
                backgroundColor: '#FFFF',
              }}
            >
              {/* {ConfigurationItem.map((item, index) => ( */}
              <Stack direction="row" spacing={1} width="100%">
                <Stack
                  direction="row"
                  spacing={2.5}
                  alignItems="start"
                  pt={3}
                  width="28px"
                >
                  <Avatar
                    sx={{
                      backgroundColor: '#E7E8E9',
                      borderRadius: 1,
                      width: '35px',
                      height: '35px',
                    }}
                    alt={` Icon`}
                  >
                    <ConfigurationItemIcon />
                  </Avatar>
                </Stack>
                <Stack
                  direction="column"
                  justifyContent="center"
                  spacing={0.5}
                  p={2.5}
                >
                  <Typography
                    variant="body1"
                    sx={{ fontWeight: '400', fontSize: '0.8125rem' }}
                  >
                    Tariff
                    {/* &nbsp; &#8226; {item.description} */}
                  </Typography>
                  <Typography
                    variant="body1"
                    sx={{ fontWeight: '700', fontSize: '1rem' }}
                  >
                    {/* {item.title} */}
                    {/* {entity.tariff && entity.tariff} */}
                  </Typography>
                </Stack>
              </Stack>
              {/* ))} */}

              <Stack spacing={1} sx={{ width: '100%' }}>
                <ConfigurationComparisonTable
                  data={selectedApprovalRequest.diff}
                />
                {/* <ExciseLimitsDisplay /> */}
                <TierInfoDisplay data={selectedApprovalRequest.diff} />
              </Stack>
            </Paper>
          </Stack>

          <TextField
            fullWidth
            label="Maker"
            value={selectedApprovalRequest.maker}
            InputProps={{
              readOnly: true,
            }}
          />

          <TextField
            fullWidth
            label="Maker Timestamp"
            value={formatTimestamp(selectedApprovalRequest.dateCreated)}
            InputProps={{
              readOnly: true,
            }}
          />

          <TextField
            fullWidth
            label="Maker comment"
            value={
              selectedApprovalRequest.makerComments || 'Regulatory Compliance'
            }
            InputProps={{
              readOnly: true,
            }}
          />

          <TextField
            label="Checker Comments"
            placeholder="Write your comment here"
            value={checkerComments}
            onChange={(e) => {
              setCheckerComments(e.target.value)
              e.target.value.length > 0
                ? setCommentsError(false)
                : setCommentsError(true)
            }}
            error={commentsError}
            helperText={commentsError ? 'Please enter comments' : ''}
            multiline
            rows={4}
            fullWidth
          />

          <Stack direction="row" gap={2}>
            <Button
              variant="outlined"
              fullWidth
              sx={{
                height: '40px',
                background: '#E3E4E4',
                border: '1px solid #AAADB0',
                boxShadow: '0px 1px 2px 0px rgba(16, 24, 40, 0.05)',
              }}
              onClick={handleReject}
            >
              Reject
            </Button>
            <Button
              variant="contained"
              fullWidth
              sx={{
                height: '40px',
              }}
              onClick={handleApprove}
            >
              Approve
              <Check sx={{ marginLeft: 1 }} />
            </Button>
          </Stack>

          <Stack
            direction="row"
            gap={'2%'}
            sx={{
              alignItems: 'center',
              alignContent: 'center',
            }}
          >
            <InfoIcon />
            <Typography variant="body2" sx={{ color: '#555C61' }}>
              You are approving or rejecting all the changes made as listed
              above.
            </Typography>
          </Stack>
        </Stack>
      </Drawer>
    </>
  )
}

export default ApprovalRequestDrawer
