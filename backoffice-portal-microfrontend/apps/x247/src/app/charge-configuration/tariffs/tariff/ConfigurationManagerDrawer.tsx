import React, { useState, useEffect } from 'react'
import { <PERSON><PERSON>, <PERSON>ack, Typo<PERSON>, IconButton, Button } from '@mui/material'
import { Close } from '@mui/icons-material'
import { useAppDispatch, useAppSelector } from '@/store'
import {
  ExciseDutyLayout,
  LimitsLayout,
  TierChargesLayout,
} from './TieredConfigView'
import { TariffsMoreMenu } from '../../TariffsMoreMenu'
import ConfigurationStatusModal from './ConfigurationStatusModal'
import { setEditableState } from '@/store/reducers/chargeConfigurationReducer'
import { LoadingListsSkeleton } from '@dtbx/ui/components'
import { FeeTier, LimitConfiguration, Service, Status } from '@/store/interfaces'
import { updateServiceConfig } from '@/store/actions'
import { HasAccessToRights } from '@dtbx/store/utils'

interface ConfigurationManagerDrawerProps {
  hideButton?: boolean
  onDrawerOpen?: () => void
  label: string
  row: {
    serviceName: string
    status: Status
    serviceType: 'PAYMENT' | 'NOTIFICATION'
    chargeType: string
    id?: string | number
  }
}
const getValidConfigurationStatus = (status: string | undefined): Status => {
  switch (status) {
    case 'ACTIVE':
    case 'INACTIVE':
    case 'PENDING':
      return status
    default:
      return 'ACTIVE'
  }
}
const ConfigurationManagerDrawer: React.FC<ConfigurationManagerDrawerProps> = ({
  hideButton = false,
  label,
  row,
  onDrawerOpen,
}) => {
  const dispatch = useAppDispatch()
  const [open, setOpen] = useState<boolean>(false)
  const [formData, setFormData] = useState<{
    feeConfiguration: {
      exciseDuty: number
      value: number
      feeTiers: FeeTier[]
    }
    limitConfiguration: LimitConfiguration
  }>({
    feeConfiguration: {
      exciseDuty: 0,
      value: 0,
      feeTiers: [],
    },
    limitConfiguration: {
      dailyLimit: 0,
      perTransactionLowerLimit: 0,
      perTransactionUpperLimit: 0,
      frequencyLimit: 0,
    },
  })

  const { selectedConfiguration, isEditable, isLoading } = useAppSelector(
    (state) => state.chargeConfiguration
  )
  useEffect(() => {
    if (selectedConfiguration) {
      setFormData({
        feeConfiguration: {
          exciseDuty: selectedConfiguration.feeConfiguration?.exciseDuty || 0,
          value: selectedConfiguration.feeConfiguration?.value || 0,
          feeTiers:
            selectedConfiguration.feeConfiguration?.feeTiers?.map(
              ({ id, minimum, maximum, feeAmount }) => ({
                tierId: id,
                minimum,
                maximum,
                feeAmount,
              })
            ) || [],
        },
        limitConfiguration: (() => {
          const {
            dailyLimit,
            perTransactionLowerLimit,
            perTransactionUpperLimit,
            frequencyLimit,
          } = selectedConfiguration.limitConfiguration || {}

          return {
            dailyLimit: dailyLimit || 0,
            perTransactionLowerLimit: perTransactionLowerLimit || 0,
            perTransactionUpperLimit: perTransactionUpperLimit || 0,
            frequencyLimit: frequencyLimit || 0,
          }
        })(),
      })
    }
  }, [selectedConfiguration])

  const handleOpen = () => {
    onDrawerOpen && onDrawerOpen()
    setOpen(true)
    dispatch(setEditableState(false))
  }

  const handleClose = () => {
    setOpen(false)
    if (isEditable) {
      dispatch(setEditableState(false))
    }
  }

  const handleToggleEditable = async () => {
    if (isEditable) {
      const chargeType =
        selectedConfiguration?.feeConfiguration?.chargeType.toUpperCase()

      const feeConfiguration = {
        exciseDuty: formData.feeConfiguration.exciseDuty,
        value: formData.feeConfiguration.value,
        chargeType,
        ...(chargeType !== 'FIXED' && {
          feeTiers: formData.feeConfiguration.feeTiers,
        }),
      }

      // console.log('Saving configuration:', {
      //   comments: 'update configuration',
      //   feeConfiguration,
      //   limitConfiguration: formData.limitConfiguration,
      // })
      if (selectedConfiguration && selectedConfiguration.id) {
        if (HasAccessToRights(['SUPER_UPDATE_PAYMENT_SERVICE_CONFIGURATION'])) {
          await updateServiceConfig(
            'super',
            selectedConfiguration.id,
            {
              comments: 'update configuration',
              feeConfiguration,
              limitConfiguration: formData.limitConfiguration,
            },
            dispatch
          )
        } else {
          await updateServiceConfig(
            'make',
            selectedConfiguration.id,
            {
              comments: 'update configuration',
              feeConfiguration,
              limitConfiguration: formData.limitConfiguration,
            },
            dispatch
          )
        }
      }
    }

    dispatch(setEditableState(!isEditable))
  }

  const TariffConfigurationData = () => {
    if (!selectedConfiguration) return null
    return isLoading ? (
      <Stack alignItems="center" justifyContent="center">
        <LoadingListsSkeleton />
      </Stack>
    ) : (
      <Stack direction="column" spacing={2}>
        <ExciseDutyLayout
          readOnly={!isEditable}
          exciseDuty={formData.feeConfiguration.exciseDuty}
          charge={formData.feeConfiguration.value}
          onExciseDutyChange={(value) =>
            setFormData((prev) => ({
              ...prev,
              feeConfiguration: { ...prev.feeConfiguration, exciseDuty: value },
            }))
          }
          onChargeChange={(value) =>
            setFormData((prev) => ({
              ...prev,
              feeConfiguration: { ...prev.feeConfiguration, value: value },
            }))
          }
        />

        {selectedConfiguration &&
          selectedConfiguration?.service?.serviceType !== 'NOTIFICATION' && (
            <LimitsLayout
              readOnly={!isEditable}
              limits={formData.limitConfiguration}
              onLimitChange={(limits) =>
                setFormData((prev) => ({ ...prev, limitConfiguration: limits }))
              }
            />
          )}

        {selectedConfiguration?.feeConfiguration?.chargeType === 'Tiered' && (
          <TierChargesLayout
            readOnly={!isEditable}
            tiers={formData.feeConfiguration.feeTiers}
            onTiersChange={(tiers) =>
              setFormData((prev) => ({
                ...prev,
                feeConfiguration: { ...prev.feeConfiguration, feeTiers: tiers },
              }))
            }
            isEditMode={true}
          />
        )}
      </Stack>
    )
  }

  return (
    <>
      {!hideButton && (
        <Button
          onClick={handleOpen}
          variant="outlined"
          sx={{
            padding: '0.25em 0.5em',
            borderRadius: '7px',
            textTransform: 'none',
            border: '1px solid #AAADB0',
            color: '#555C61',
            fontWeight: 400,
            width: '100%',
            maxWidth: {
              xs: '80vw',
              sm: '60vw',
              md: '5vw',
            },
            height: '3vh',
            textWrap: 'noWrap',
          }}
        >
          {label}
        </Button>
      )}
      <Drawer
        sx={{
          '.MuiDrawer-paper': {
            width: '40%',
          },
        }}
        open={open}
        anchor="right"
        onClose={handleClose}
      >
        <Stack
          sx={{
            justifyContent: 'space-between',
            alignItems: 'center',
            flexDirection: 'row',
            px: '1.5vw',
            py: '1vh',
            background: '#F2F4F7',
            borderBottom: '1px solid #F2F4F7',
          }}
        >
          <Typography
            variant="h6"
            sx={{
              alignItems: 'start',
              fontWeight: 700,
              fontSize: '1rem',
            }}
          >
            {row?.serviceName || ''}
          </Typography>
          <Stack
            direction="row"
            sx={{
              display: 'flex',
              alignItems: 'center',
              marginLeft: 'auto',
              gap: '8px',
            }}
          >
            <TariffsMoreMenu
              onClick={handleToggleEditable}
              label={isEditable ? 'Save changes' : 'Edit'}
            />
            <ConfigurationStatusModal
              name={row?.serviceName || ''}
              serviceId={row.id?.toString()}
              status={getValidConfigurationStatus(row?.status)}
              label={
                getValidConfigurationStatus(row?.status) === 'ACTIVE'
                  ? 'Deactivate'
                  : 'Activate'
              }
            />
            <IconButton
              onClick={handleClose}
              sx={{ border: '1px solid  #AAADB0' }}
            >
              <Close fontSize="small" />
            </IconButton>
          </Stack>
        </Stack>

        <Stack
          sx={{
            p: '2vw',
            gap: '2vh',
          }}
        >
          {TariffConfigurationData()}
        </Stack>
      </Drawer>
    </>
  )
}

export default ConfigurationManagerDrawer
