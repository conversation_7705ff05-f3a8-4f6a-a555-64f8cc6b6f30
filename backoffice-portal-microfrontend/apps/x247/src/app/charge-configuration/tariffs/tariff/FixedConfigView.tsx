import { useState, useEffect } from 'react'
import {
  Stack,
  Typography,
  Box,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TextField,
} from '@mui/material'
import { ChargeIcon, LimitIcon } from '@dtbx/ui/components/SvgIcons'

interface IConfigProps {
  getChargesData?: (data: any) => void
  getLimitsData?: (data: [{ type: string; value: number }]) => void
}
interface ILayoutProps {
  readOnly?: boolean
  getEditData?: (data: any) => void
}

export const FixedViewConfig = ({
  getChargesData,
  getLimitsData,
}: IConfigProps) => {
  return (
    <Stack sx={{ alignItems: 'center', width: '95%', mx: 'auto' }} gap={'1rem'}>
      {/* Charges Layout */}
      <ChargesLayout
        // readOnly={false}
        getEditData={getChargesData}
      />
      {/* Limits layout */}
      <LimitsLayout
        // readOnly={false}
        getEditData={getLimitsData}
      />
    </Stack>
  )
}

{
  /* Charges Layout Component*/
}
export const ChargesLayout: React.FC<ILayoutProps> = ({
  readOnly = false,
  getEditData,
}) => {
  const [charges, setCharges] = useState([{ charge: 0, exciseDuty: 0 }])

  const [editingCell, setEditingCell] = useState<{
    row: number
    col: 'charge' | 'exciseDuty'
  } | null>(null)

  const [editValue, setEditValue] = useState<string>('')

  useEffect(() => {
    getEditData && getEditData(charges)
  }, [charges])

  const handleCellClick = (row: number, col: 'charge' | 'exciseDuty'): void => {
    if (readOnly) return
    setEditingCell({ row, col })
    setEditValue(charges[row][col].toString())
  }

  const handleCellBlur = (): void => {
    if (editingCell) {
      const updatedCharges = [...charges]
      // updatedCharges[editingCell.row][editingCell.col] = editValue
      updatedCharges[editingCell.row][editingCell.col] =
        parseFloat(editValue) || 0
      setCharges(updatedCharges)
      setEditingCell(null)
    }
  }

  const handleCellChange = (
    event: React.ChangeEvent<HTMLInputElement>
  ): void => {
    setEditValue(event.target.value)
  }

  return (
    <Stack sx={{ width: '100%' }}>
      <Stack sx={{ p: '1%', mb: '0.5rem' }} direction="row" gap={2}>
        <Stack
          sx={{
            px: '10px',
            height: '2rem',
            width: '2rem',
            border: '0.1px solid rgba(189, 189, 189, 0.6)',
            borderRadius: '50%',
            justifyContent: 'center',
            alignItems: 'center',
          }}
        >
          <ChargeIcon />
        </Stack>
        <Box>
          <Typography fontWeight={550}> Charges</Typography>
          <Typography>
            The fee for this service regardless of the value of the transaction.
          </Typography>
        </Box>
      </Stack>
      <TableContainer
        sx={{
          border: '0.1px solid rgba(189, 189, 189, 0.6)',
          borderRadius: '0.5rem',
          boxShadow:
            '0px 1px 3px 0px rgba(16, 24, 40, 0.10), 0px 1px 2px 0px rgba(16, 24, 40, 0.06)',
          overflow: 'hidden',
        }}
      >
        <Table sx={{ width: '100%' }}>
          <TableHead>
            <TableRow>
              <TableCell
                sx={{
                  border: '0.1px solid rgba(189, 189, 189, 0.6)',
                  width: '33%',
                }}
              >
                <Typography>Charge (KES)</Typography>
              </TableCell>
              <TableCell
                sx={{
                  border: '0.1px solid rgba(189, 189, 189, 0.6)',
                  width: '33%',
                }}
              >
                <Typography>Excise Duty (%)</Typography>
              </TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {charges.map((charge, rowIndex) => (
              <TableRow key={rowIndex}>
                {['charge', 'exciseDuty'].map((col) => (
                  <TableCell
                    key={col}
                    sx={{
                      border: '0.1px solid rgba(189, 189, 189, 0.6)',
                      p: '0.5rem',
                      '&:hover': {
                        backgroundColor: '#F9FAFB',
                        cursor: readOnly ? 'default' : 'pointer',
                      },
                    }}
                    onClick={() =>
                      !readOnly &&
                      handleCellClick(rowIndex, col as 'charge' | 'exciseDuty')
                    }
                  >
                    {editingCell?.row === rowIndex &&
                    editingCell?.col === col ? (
                      <TextField
                        value={editValue}
                        onChange={handleCellChange}
                        onBlur={handleCellBlur}
                        autoFocus
                        fullWidth
                        size="small"
                        sx={{
                          height: '100%',
                          '& .MuiInputBase-root': {
                            height: '100%',
                            padding: 0,
                            margin: 0,
                          },
                        }}
                        InputProps={{
                          sx: {
                            '&:hover .MuiOutlinedInput-notchedOutline': {
                              border: 'none',
                              p: 0,
                              m: 0,
                            },
                            '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                              border: 'none',
                              p: 0,
                              m: 0,
                            },
                          },
                        }}
                      />
                    ) : (
                      <Typography
                        fontWeight={550}
                        sx={{ marginLeft: '0.5rem' }}
                      >
                        {charge[col as keyof typeof charge]}
                      </Typography>
                    )}
                  </TableCell>
                ))}
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>
    </Stack>
  )
}

{
  /* Limits Layout Component*/
}
export const LimitsLayout: React.FC<ILayoutProps> = ({
  readOnly = false,
  getEditData,
}) => {
  const [limits, setLimits] = useState([
    { type: 'Daily Limit (KES)', value: 0 },
    { type: 'Frequency Limit', value: 0 },
    { type: 'Per Transaction Lower Limit (KES)', value: 0 },
    { type: 'Per Transaction Higher Limits (KES)', value: 0 },
  ])

  const [editingCell, setEditingCell] = useState<{
    row: number
    col: 'value'
  } | null>(null)

  const [editValue, setEditValue] = useState<string>('')

  useEffect(() => {
    getEditData && getEditData(limits)
  }, [limits])

  const handleCellClick = (row: number, col: 'value'): void => {
    if (readOnly) return
    setEditingCell({ row, col })
    setEditValue(limits[row][col].toString())
  }

  const handleCellBlur = (): void => {
    if (editingCell) {
      const updatedLimits = [...limits]
      updatedLimits[editingCell.row][editingCell.col] =
        parseFloat(editValue) || 0
      setLimits(updatedLimits)
      setEditingCell(null)
    }
  }

  const handleCellChange = (
    event: React.ChangeEvent<HTMLInputElement>
  ): void => {
    setEditValue(event.target.value)
  }
  return (
    <Stack sx={{ width: '100%' }}>
      <Stack sx={{ p: '1%', mb: '0.5rem' }} direction="row" gap={2}>
        <Stack
          sx={{
            px: '10px',
            height: '2rem',
            width: '2rem',
            border: '0.1px solid rgba(189, 189, 189, 0.6)',
            borderRadius: '50%',
            justifyContent: 'center',
            alignItems: 'center',
          }}
        >
          <LimitIcon />
        </Stack>
        <Box>
          <Typography fontWeight={550}> Limits</Typography>
          <Typography>
            Restrictions or caps placed on specific aspects of this payment
            service.{' '}
          </Typography>
        </Box>
      </Stack>
      <TableContainer
        sx={{
          border: '0.1px solid rgba(189, 189, 189, 0.6)',
          borderRadius: '0.5rem',
          boxShadow:
            '0px 1px 3px 0px rgba(16, 24, 40, 0.10), 0px 1px 2px 0px rgba(16, 24, 40, 0.06)',
          overflow: 'hidden',
        }}
      >
        <Table sx={{ width: '100%' }}>
          <TableHead>
            <TableRow>
              <TableCell
                sx={{
                  border: '0.1px solid rgba(189, 189, 189, 0.6)',
                  width: '33%',
                }}
              >
                {' '}
                <Typography> Limit Type</Typography>{' '}
              </TableCell>
              <TableCell
                sx={{
                  border: '0.1px solid rgba(189, 189, 189, 0.6)',
                  width: '33%',
                }}
              >
                {' '}
                <Typography> Limit Value</Typography>{' '}
              </TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {limits.map((limit, rowIndex) => {
              return (
                <TableRow key={rowIndex}>
                  <TableCell
                    sx={{
                      border: '0.1px solid rgba(189, 189, 189, 0.6)',
                      '&:hover': {
                        backgroundColor: '#F9FAFB',
                        cursor: 'pointer',
                      },
                    }}
                  >
                    {' '}
                    <Typography fontWeight={550}> {limit.type}</Typography>
                  </TableCell>
                  <TableCell
                    sx={{
                      border: '0.1px solid rgba(189, 189, 189, 0.6)',
                      p: '0.5rem',
                      '&:hover': {
                        backgroundColor: '#F9FAFB',
                        cursor: 'pointer',
                      },
                    }}
                    onClick={() => handleCellClick(rowIndex, 'value')}
                  >
                    {editingCell?.row === rowIndex &&
                    editingCell?.col === 'value' &&
                    !readOnly ? (
                      <TextField
                        // value={limit.value}
                        value={editValue}
                        onChange={handleCellChange}
                        onBlur={handleCellBlur}
                        autoFocus
                        fullWidth
                        type="number"
                        size="small"
                        sx={{
                          height: '100%',
                          '& .MuiInputBase-root': {
                            height: '100%',
                            padding: 0,
                            margin: 0,
                          },
                        }}
                        InputProps={{
                          sx: {
                            '&:hover .MuiOutlinedInput-notchedOutline': {
                              border: 'none',
                              p: 0,
                              m: 0,
                            },
                            '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                              border: 'none',
                              p: 0,
                              m: 0,
                            },
                          },
                        }}
                      />
                    ) : (
                      <Typography
                        fontWeight={550}
                        sx={{ marginLeft: '0.5rem' }}
                      >
                        {limit.value}
                      </Typography>
                    )}
                  </TableCell>
                </TableRow>
              )
            })}
          </TableBody>
        </Table>
      </TableContainer>
    </Stack>
  )
}
