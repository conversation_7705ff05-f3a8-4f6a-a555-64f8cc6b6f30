import { useState, useRef, useEffect } from 'react'
import {
  <PERSON><PERSON>,
  Typo<PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>ton,
  ClickAwayListener,
  Grow,
  MenuItem,
  MenuList,
  Paper,
  Popper,
} from '@mui/material'
import { Expandmore } from '@dtbx/ui/components/SvgIcons'
import { FixedViewConfig } from './FixedConfigView'
import { TieredViewConfig } from './TieredConfigView'

import CheckIcon from '@mui/icons-material/Check'
import CloseIcon from '@mui/icons-material/Close'
import { KeyboardArrowDownRounded } from '@mui/icons-material'
import {
  setConfiguredServices,
  removeConfiguredService,
} from '@/store/reducers'
import { useAppDispatch, useAppSelector } from '@/store'
import { Service } from '@/store/interfaces'

interface FeeTier {
  minimum: number
  maximum: number
  feeAmount: number
}

export const ConfigurationCard = ({
  service,
  currency,
}: {
  service: Service
  currency: string
}) => {
  const [isExpanded, setIsExpanded] = useState<boolean>(false)
  const [open, setOpen] = useState(false)
  const [selectedConfig, setSelectedConfig] = useState<string>('Tiered')

  const [editedCharges, setEditedCharges] = useState([
    { charge: 0, exciseDuty: 0 },
  ])

  const [editedLimits, setEditedLimits] = useState([{ type: '', value: 0 }])

  const [tieredCharges, setTieredCharges] = useState([
    {
      charge: 0,
      exciseDuty: 0,
    },
  ])
  const [tieredLimits, setTieredLimits] = useState({
    dailyLimit: 0,
    perTransactionLowerLimit: 0,
    perTransactionUpperLimit: 0,
    frequencyLimit: 0,
  })
  const [tieredTiers, setTieredTiers] = useState<FeeTier[]>([])

  const anchorRef = useRef<HTMLButtonElement>(null)
  const dispatch = useAppDispatch()
  const { selectedTariff } = useAppSelector(
    (state) => state.chargeConfiguration
  )

  const handleChargesData = (
    data: [{ charge: number; exciseDuty: number }]
  ) => {
    setEditedCharges(data)
  }

  const handleLimitsData = (data: [{ type: string; value: number }]) => {
    setEditedLimits(data)
  }
  const handleTieredChargesData = (data: {
    charge: number
    exciseDuty: number
  }) => {
    setTieredCharges([data])
  }

  // Save Configuration
  const handleSelectConfigService = () => {
    setIsExpanded(false)
    const flattenedTieredCharges = tieredCharges.flat()
    console.log('Tiered Tiers >', tieredTiers)
    const feeConfiguration = {
      chargeType: selectedConfig.toUpperCase(),
      fixedChargeType: 'PERCENTAGE',
      value:
        selectedConfig === 'Fixed'
          ? editedCharges[0]?.charge || 0
          : flattenedTieredCharges.length > 0
            ? flattenedTieredCharges[0]?.charge || 0
            : 0,
      exciseDuty:
        selectedConfig === 'Fixed'
          ? editedCharges[0]?.exciseDuty || 0
          : flattenedTieredCharges.length > 0
            ? flattenedTieredCharges[0]?.exciseDuty || 0
            : 0,
      ...(selectedConfig === 'Tiered' && {
        feeTiers: tieredTiers.map((tier) => ({
          ...tier,
          minimum: tier.minimum,
          maximum: tier.maximum,
        })),
      }),
    }

    const limitConfiguration =
      selectedConfig === 'Fixed'
        ? editedLimits.reduce(
            (acc, limit) => {
              switch (limit.type) {
                case 'Daily Limit (KES)':
                  acc.dailyLimit = limit.value
                  break
                case 'Frequency Limit':
                  acc.frequencyLimit = limit.value
                  break
                case 'Per Transaction Lower Limit (KES)':
                  acc.perTransactionLowerLimit = limit.value
                  break
                case 'Per Transaction Higher Limits (KES)':
                  acc.perTransactionUpperLimit = limit.value
                  break
              }
              return acc
            },
            {
              dailyLimit: 0,
              perTransactionLowerLimit: 0,
              perTransactionUpperLimit: 0,
              frequencyLimit: 0,
            }
          )
        : tieredLimits

    const configuredService = {
      serviceId: service.id ?? '',
      name: service.serviceName,
      feeConfiguration,
      limitConfiguration,
      comments: 'Create a new service configuration',
      tariff: selectedTariff.name ?? '',
      currency: currency,
    }

    dispatch(setConfiguredServices(configuredService))
  }

  const handleTieredTiersData = (data: FeeTier[]) => {
    setTieredTiers(data)
  }

  const handleTieredLimitsData = (data: any) => {
    const transformedData = data.reduce(
      (acc: any, limit: any) => {
        switch (limit.type) {
          case 'Daily Limit (KES)':
            acc.dailyLimit = limit.value
            break
          case 'Frequency Limit':
            acc.frequencyLimit = limit.value
            break
          case 'Per Transaction Lower Limit (KES)':
            acc.perTransactionLowerLimit = limit.value
            break
          case 'Per Transaction Higher Limits (KES)':
            acc.perTransactionUpperLimit = limit.value
            break
          default:
            break
        }
        return acc
      },
      {
        dailyLimit: 0,
        frequencyLimit: 0,
        perTransactionLowerLimit: 0,
        perTransactionUpperLimit: 0,
      }
    )

    setTieredLimits(transformedData)
  }

  const handleToggle = () => {
    setOpen((prevOpen) => !prevOpen)
  }

  const handleClose = (event: Event | React.SyntheticEvent) => {
    if (
      anchorRef.current &&
      anchorRef.current.contains(event.target as HTMLElement)
    ) {
      return
    }
    setOpen(false)
  }
  const handleListKeyDown = (event: React.KeyboardEvent) => {
    if (event.key === 'Tab') {
      event.preventDefault()
      setOpen(false)
    }
  }

  const prevOpen = useRef(open)

  useEffect(() => {
    if (prevOpen.current && !open) {
      anchorRef.current!.focus()
    }

    prevOpen.current = open
  }, [open])

  const handleMenuItemClick = (item: string) => {
    setSelectedConfig(item)
    setOpen(false)
  }

  const handleDeleteConfigService = () => {
    dispatch(removeConfiguredService(service.serviceId))
    setIsExpanded(false)
  }

  return (
    <Stack
      sx={{
        width: '62.5%',
        borderRadius: '4px',
        border: '1px solid #D0D5DD',
      }}
    >
      <Stack
        height={40}
        sx={{
          py: '1%',
          gap: '2.5rem',
          padding: '10px',
          width: '100%',
          justifyContent: 'space-between',
          alignItems: 'center',
          flexDirection: 'row',
        }}
      >
        <Typography variant="body2" sx={{ marginLeft: '2%' }} fontWeight={700}>
          {isExpanded
            ? `Configure ${service.serviceName}`
            : `${service.serviceName}`}
        </Typography>
        {!isExpanded ? (
          <Stack
            direction="row"
            gap={'5px'}
            sx={{
              marginRight: '3%',
              padding: '0 1px',
              alignItems: 'center',
              alignContent: 'center',
              cursor: 'pointer',
              border: '1px solid #D0D5DD',
              borderRadius: '6px',
            }}
            onClick={() => {
              setIsExpanded(!isExpanded)
            }}
          >
            <Typography
              variant="body2"
              sx={{
                padding: '0.1rem 0.2rem',
              }}
            >
              Configure
            </Typography>
            <Expandmore />
          </Stack>
        ) : (
          <Stack>
            <Button
              variant="outlined"
              aria-controls={open ? 'menu-button-type' : undefined}
              aria-haspopup="true"
              aria-expanded={open ? 'true' : undefined}
              onClick={handleToggle}
              ref={anchorRef}
              sx={{
                padding: '1px 4px',
                border: '1px solid #AAADB0',
              }}
              endIcon={
                <KeyboardArrowDownRounded
                  sx={{
                    transformOrigin: 'center',
                    transition: 'transform padding 0.2s',
                    transform: open ? 'rotate(90deg)' : 'rotate(0deg)',
                  }}
                />
              }
            >
              <Typography>{selectedConfig}</Typography>
            </Button>
            <Popper
              open={open}
              anchorEl={anchorRef.current}
              role={undefined}
              placement="bottom-start"
              transition
              disablePortal
              sx={{
                zIndex: '2000',
              }}
            >
              {({ TransitionProps, placement }) => (
                <Grow
                  {...TransitionProps}
                  style={{
                    transformOrigin:
                      placement === 'bottom-start' ? 'left top' : 'left bottom',
                  }}
                >
                  <Paper
                    sx={{
                      minWidth: '95px',
                      marginTop: '8px',
                      borderRadius: '8px',
                      border: '1px solid #ECECEC',
                      background: '#FFF',
                      boxShadow:
                        '0px 12.514px 15.017px -2.503px rgba(16, 24, 40, 0.08), 0px 5.006px 5.006px -2.503px rgba(16, 24, 40, 0.03)',
                    }}
                  >
                    <ClickAwayListener onClickAway={handleClose}>
                      <MenuList
                        autoFocusItem={open}
                        id="composition-menu"
                        aria-labelledby="composition-button"
                        onKeyDown={handleListKeyDown}
                      >
                        {['Tiered', 'Fixed'].map((item) => (
                          <MenuItem
                            key={item}
                            onClick={() => handleMenuItemClick(item)}
                          >
                            {item}
                          </MenuItem>
                        ))}
                      </MenuList>
                    </ClickAwayListener>
                  </Paper>
                </Grow>
              )}
            </Popper>
          </Stack>
        )}
        {isExpanded && (
          <Stack direction={'row'} gap={2}>
            <Button
              onClick={handleSelectConfigService}
              startIcon={<CheckIcon sx={{ color: 'white' }} />}
              variant="contained"
              sx={{
                display: 'flex',
                gap: 1,
                justifyContent: 'center',
                alignItems: 'center',
                px: 2,
                py: 1,
                textTransform: 'none',
                backgroundColor: '#101828',
                borderRadius: '8px',
                height: '2rem',
              }}
            >
              <Typography
                variant="body3"
                sx={{
                  textWrap: 'nowrap',
                  color: 'white',
                  fontWeight: '700',
                  fontSize: '14px',
                }}
              >
                Save
              </Typography>
            </Button>
            <Button
              onClick={handleDeleteConfigService}
              variant="outlined"
              sx={{
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
                px: 0,
                py: 0,
                textTransform: 'none',
                border: '1px solid #E3E4E4',
                borderRadius: '8px',
                height: '2rem',
                width: '0.5rem',
              }}
            >
              <CloseIcon sx={{ color: 'black' }} />
            </Button>
          </Stack>
        )}
      </Stack>
      {isExpanded && <Divider />}
      <Stack
        sx={{ display: isExpanded ? 'block' : 'none', px: '2%', my: '1rem' }}
      >
        {selectedConfig === 'Tiered' ? (
          <TieredViewConfig
            getChargesData={handleTieredChargesData}
            getLimitsData={handleTieredLimitsData}
            // @ts-ignore
            getTiersData={handleTieredTiersData}
          />
        ) : (
          <FixedViewConfig
            getChargesData={handleChargesData}
            getLimitsData={handleLimitsData}
          />
        )}
      </Stack>
    </Stack>
  )
}
