'use-client'
import { IHeadCell } from '@dtbx/store/interfaces'
import { CustomerStatusChip } from '@dtbx/ui/components/Chip'
import {
  CustomPagination,
  CustomTableHeader,
  PaginationOptions,
} from '@dtbx/ui/components/Table'

import {
  Paper,
  Stack,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableRow,
  Typography,
} from '@mui/material'
import React, { useState, useEffect } from 'react'
import ConfigurationStatusModal from './ConfigurationStatusModal'
import { Status } from '@/store/interfaces'
import { RootState, useAppDispatch, useAppSelector } from '@/store'
import { CustomSkeleton } from '@dtbx/ui/components'
import { setSelectedConfiguration } from '@/store/reducers'
import ConfigurationManagerDrawer from './ConfigurationManagerDrawer'
import { getTariffConfigByTariffCurrency } from '@/store/actions'
import { IConfigurationItem } from '@/store/interfaces/chargeConfiguration'

const headerList: IHeadCell[] = [
  {
    id: 'serviceName',
    label: 'Service Name',
    alignCenter: false,
    alignRight: false,
  },
  {
    id: 'status',
    label: 'Status',
    alignCenter: false,
    alignRight: false,
  },
  {
    id: 'serviceType',
    label: ' Service Type',
    alignCenter: false,
    alignRight: false,
  },
  {
    id: 'chargeType',
    label: 'Charge Type',
    alignCenter: false,
    alignRight: false,
  },
  {
    id: 'dailyFrequencyLimit',
    label: 'Daily frequency limit',
    alignCenter: false,
    alignRight: false,
  },

  {
    id: 'actions',
    label: 'Actions',
    alignCenter: false,
    alignRight: false,
  },
]

interface NotificationServiceListProps {
  setType: (type: string) => void
  setServiceType: (type: string) => void
}
export const NotificationServiceList = ({
  setType,
  setServiceType,
}: NotificationServiceListProps) => {
  const dispatch = useAppDispatch()
  const { isLoading, tariffConfig, selectedTariff } = useAppSelector(
    (state: RootState) => state.chargeConfiguration
  )

  const configs = tariffConfig?.data || []
  const selectedCurrency = tariffConfig?.data?.[0]?.currency || 'KES'

  const {
    pageNumber = 0,
    pageSize = 10,
    totalNumberOfPages = 0,
  } = tariffConfig || {}

  const [paginationOptions, setPaginationOptions] = useState({
    page: pageNumber,
    size: pageSize,
    totalPages: totalNumberOfPages,
  })
  /*************************start pagination handlers***************************/
  const handlePagination = async (newOptions: PaginationOptions) => {
    setPaginationOptions(newOptions)

    if (selectedTariff?.name) {
      await getTariffConfigByTariffCurrency(
        {
          tariff: selectedTariff.name,
          currency: selectedCurrency,
          page: newOptions.page,
          size: newOptions.size,
        },
        dispatch
      )
    }
  }
  /*************************end pagination handlers**************************/

  useEffect(() => {
    setType('')
    setServiceType('NOTIFICATION')
  }, [paginationOptions.page, setServiceType])
  const renderStatusChip = (status: Status | undefined) => {
    switch (status) {
      case 'ACTIVE':
        return <CustomerStatusChip label="ACTIVE" />
      case 'INACTIVE':
        return <CustomerStatusChip label="INACTIVE" />
      case 'PENDING':
        return <CustomerStatusChip label="PENDING" />
      default:
        return <CustomerStatusChip label="ACTIVE" />
    }
  }
  const getValidconfigurationStatus = (status: string | undefined): Status => {
    switch (status) {
      case 'ACTIVE':
      case 'INACTIVE':
      case 'PENDING':
        return status as Status
      default:
        return 'ACTIVE'
    }
  }
  const handleViewClick = (configurationData: IConfigurationItem) => {
    dispatch(setSelectedConfiguration(configurationData))
  }

  return (
    <Stack
      sx={{
        padding: '1.5% 2% 1.5% 2%',
        flexDirection: 'column',
      }}
    >
      {isLoading ? (
        <CustomSkeleton
          animation="pulse"
          variant="rectangular"
          width={'100%'}
          height={'60vh'}
        />
      ) : (
        <Stack
          sx={{
            flexDirection: 'column',
            boxShadow:
              '0px 1px 3px 0px rgba(16, 24, 40, 0.10), 0px 1px 2px 0px rgba(16, 24, 40, 0.06)',
            border: '1px solid #EAECF0',
            borderRadius: '8px',
          }}
        >
          <Stack sx={{ padding: '1% 1.5%' }}>
            <Typography variant="subtitle1" fontWeight={600}>
              Trigger based Notification Configurations
            </Typography>
            <Typography variant="subtitle3" color=" #555C61">
              Showing {tariffConfig?.totalElements} configurations
            </Typography>
          </Stack>
          <Paper
            sx={{
              boxShadow:
                '0px 1px 3px 0px rgba(16, 24, 40, 0.10), 0px 1px 2px 0px rgba(16, 24, 40, 0.06)',
              border: '1px solid #EAECF0',
            }}
          >
            <TableContainer
              component={Paper}
              elevation={0}
              sx={{
                boxShadow: 'none',
              }}
            >
              <Table
                sx={{ minWidth: 650 }}
                aria-label="designations table"
                size="small"
              >
                <CustomTableHeader
                  order={'asc'}
                  orderBy={'id'}
                  headLabel={headerList}
                  rowCount={10}
                  numSelected={0}
                  onRequestSort={() => {}}
                />
                <TableBody>
                  {configs &&
                    configs.map((row: IConfigurationItem) => {
                      const {
                        status,
                        feeConfiguration: { chargeType },
                        limitConfiguration,
                        service,
                      } = row

                      return (
                        <TableRow
                          hover
                          key={`${service.serviceName}-${row.createdOn}`}
                        >
                          <TableCell>
                            <Stack direction="column">
                              <Typography
                                variant="subtitle2"
                                sx={{ color: 'primary.main' }}
                              >
                                {service.serviceName}
                              </Typography>
                            </Stack>
                          </TableCell>
                          <TableCell>
                            {renderStatusChip(status as Status)}
                          </TableCell>
                          <TableCell>
                            <Typography variant="body2">
                              {service.serviceType}
                            </Typography>
                          </TableCell>
                          <TableCell>
                            <Typography variant="body2">
                              {chargeType}
                            </Typography>
                          </TableCell>
                          <TableCell>
                            <Typography variant="body2">
                              {limitConfiguration?.frequencyLimit}
                            </Typography>
                          </TableCell>
                          <TableCell>
                            <Stack direction="row" spacing={2}>
                              <ConfigurationManagerDrawer
                                onDrawerOpen={() => handleViewClick(row)}
                                label="View"
                                row={{
                                  serviceName: service.serviceName,
                                  status: status as Status,
                                  serviceType: service.serviceType,
                                  chargeType,
                                }}
                              />
                              <ConfigurationStatusModal
                                name={service.serviceName}
                                status={getValidconfigurationStatus(
                                  status as Status
                                )}
                                label={
                                  getValidconfigurationStatus(
                                    status as Status
                                  ) === 'ACTIVE'
                                    ? 'Deactivate'
                                    : 'Activate'
                                }
                              />
                            </Stack>
                          </TableCell>
                        </TableRow>
                      )
                    })}
                </TableBody>
              </Table>
            </TableContainer>
            {totalNumberOfPages > 0 && (
              <CustomPagination
                options={{
                  ...paginationOptions,
                  totalPages: totalNumberOfPages,
                }}
                handlePagination={handlePagination}
              />
            )}
          </Paper>
        </Stack>
      )}
    </Stack>
  )
}
