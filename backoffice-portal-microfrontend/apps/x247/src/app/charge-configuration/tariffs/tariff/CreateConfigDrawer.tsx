'use client'
import { useState, useEffect } from 'react'
import { SyntheticEvent } from 'react'
import {
  Drawer,
  DialogTitle,
  Divider,
  IconButton,
  Stack,
  Typography,
  Box,
  Button,
} from '@mui/material'
import { ChargeConfigIcon } from '@dtbx/ui/components/SvgIcons'
import { CustomSearchInput } from '@dtbx/ui/components/Input'
import ArrowBackIcon from '@mui/icons-material/ArrowBack'
import ArrowForwardIcon from '@mui/icons-material/ArrowForward'
import CloseIcon from '@mui/icons-material/Close'
import { SearchRounded } from '@mui/icons-material'
import { ConfigurationCard } from './ConfigurationCard'
import { SelectedService } from './SelectedService'
import { useAppSelector } from '@/store'
import { Service } from '@/store/interfaces'
import { createServiceConfig } from '@/store/actions'
import { clearConfiguredServices } from '@/store/reducers'
import { useAppDispatch } from '@/store'
import { HasAccessToRights } from '@dtbx/store/utils'

interface IConfigProps {
  open: boolean
  setOpen: (open: boolean) => void
  currency: string
}
export const CreateConfig = ({ open, setOpen, currency }: IConfigProps) => {
  const [searchTerm, setSearchTerm] = useState<string>('')
  const [selectedFilteredServices, setSelectedFilteredServices] = useState<
    Service[]
  >([])

  const { configuredServices, configurableServices } = useAppSelector(
    (state) => state.chargeConfiguration
  )
  const dispatch = useAppDispatch()

  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value
    setSearchTerm(value)
    const filteredServices = configurableServices.filter((service) =>
      service.serviceName.toLowerCase().includes(value.toLowerCase())
    )

    setSelectedFilteredServices(filteredServices)
  }

  const handleClose = (e: SyntheticEvent, reason: string) => {
    if (reason === 'backdropClick') {
      return
    }
    setOpen(false)
  }

  const handleSubmit = async (e: SyntheticEvent) => {
    handleClose(e, 'close')
    if (HasAccessToRights(['SUPER_CREATE_PAYMENT_SERVICE_CONFIGURATIONS'])) {
      await createServiceConfig(configuredServices, 'super', dispatch)
      dispatch(clearConfiguredServices())
    } else {
      await createServiceConfig(configuredServices, 'make', dispatch)
      dispatch(clearConfiguredServices())
    }
  }

  return (
    <Drawer
      sx={{
        '.MuiDrawer-paper': {
          width: '98%',
          height: '100vh',
        },
      }}
      open={open}
      anchor={'right'}
      onClose={handleClose}
      PaperProps={{
        sx: {
          width: '98%',
          height: '100%',
        },
      }}
    >
      <Stack
        sx={{
          background: '#F9FAFB',
          flexDirection: 'row',
          justifyContent: 'space-between',
          alignItems: 'center',
          alignContent: 'center',
        }}
      >
        <Stack
          flexDirection="row"
          sx={{
            alignItems: 'center',
          }}
        >
          <DialogTitle
            sx={{
              display: 'flex',
              flexDirection: 'row',
              gap: 4,
              alignItems: 'center',
              alignContent: 'center',
              py: '0.8rem',
            }}
          >
            <IconButton
              onClick={(e) => handleClose(e, 'close')}
              sx={{ py: '0.2rem' }}
            >
              <ArrowBackIcon />
            </IconButton>
            <Typography variant="subtitle2" color={'primary.main'}>
              Add new Configuration
            </Typography>
          </DialogTitle>
        </Stack>
        <IconButton
          aria-label="close"
          onClick={(e) => handleClose(e, 'close')}
          sx={{
            position: 'absolute',
            right: 8,
            top: 8,
            color: '',
            border: '1px solid #CBD5E1',
            borderRadius: '50%',
            mx: '1rem',
            py: '0.5rem',
          }}
        >
          <CloseIcon />
        </IconButton>
      </Stack>
      <Stack sx={{ flexDirection: 'row', height: '100%' }}>
        <Stack sx={{ width: '80%', height: '98%', overflowY: 'auto' }}>
          <Stack
            sx={{
              padding: '1rem',
              width: '95%',
              mx: 'auto',
              textAlign: 'center',
              fontWeight: 400,
              gap: '1rem',
              mb: '1rem',
            }}
          >
            <Typography
              variant="subtitle2"
              fontWeight={700}
              fontSize={'1.5rem'}
            >
              Add new configuration
            </Typography>
            <Typography variant="subtitle2" color="#667085">
              Search for the service you want to configure for this tariff, then
              on expand to configure parameters.
            </Typography>
          </Stack>
          <Stack
            sx={{
              width: '95%',
              mx: 'auto',
              alignItems: 'center',
              gap: '0.5rem',
            }}
          >
            <CustomSearchInput
              placeholder="Search Service"
              height={40}
              value={searchTerm}
              onChange={handleSearch}
              endAdornment={
                <Box
                  sx={{
                    backgroundColor: '#EDEEEE',
                    padding: '1rem 2rem',
                    width: '4rem',
                    height: '100%',
                    display: 'flex',
                    justifyContent: 'center',
                    alignItems: 'center',
                    position: 'absolute',
                    right: 0,
                    top: 0,
                    borderTopRightRadius: '4px',
                    borderBottomRightRadius: '4px',
                  }}
                >
                  <SearchRounded
                    sx={{
                      color: '#555C61',
                    }}
                  />
                </Box>
              }
              sx={{
                width: '46.5vw',
                background: '#FFFFFF',
                borderRadius: '4px',
                border: '1px solid #D0D5DD',
              }}
            />

            {searchTerm &&
              selectedFilteredServices.map((service) => (
                <ConfigurationCard
                  key={service.serviceId}
                  service={service}
                  currency={currency}
                />
              ))}
            {configurableServices.length === 0 && (
              <Stack
                sx={{
                  width: '62.5%',
                  borderRadius: '4px',
                  border: '1px solid #D0D5DD',
                  height: '5vh',
                  justifyContent: 'center',
                  alignItems: 'center',
                  cursor: 'pointer',
                  background: '#F9FAFB',
                }}
              >
                <Typography
                  variant="body2"
                  sx={{
                    padding: '0.1rem 0.2rem',
                  }}
                >
                  There is no service to configure for this Tariff
                </Typography>
              </Stack>
            )}
          </Stack>
        </Stack>
        <Divider orientation="vertical" flexItem />
        <Stack sx={{ width: '20%' }}>
          <Stack sx={{ p: 2, mb: 2, height: '3vh' }}>
            <Typography
              variant="subtitle2"
              fontWeight={700}
              sx={{ fontSize: '0.8rem' }}
            >
              Configured services
            </Typography>
          </Stack>
          <Divider sx={{ width: '100%' }} />
          <Stack sx={{ margin: '0.5rem', height: '100%', gap: '1rem' }}>
            {configuredServices.map((service) => (
              <SelectedService
                key={service.serviceId}
                service={{
                  serviceId: service.serviceId ?? '',
                  name: service.name ?? '',
                }}
              />
            ))}

            {configuredServices.length > 0 && (
              <Button
                onClick={handleSubmit}
                variant="contained"
                sx={{
                  display: 'flex',
                  gap: 1,
                  justifyContent: 'center',
                  alignItems: 'center',
                  px: 3,
                  py: 2,
                  textTransform: 'none',
                  backgroundColor: '#101828',
                  borderRadius: '8px',
                }}
              >
                <Typography
                  variant="body3"
                  sx={{
                    textWrap: 'nowrap',
                    color: 'white',
                    fontWeight: '700',
                    fontSize: '14px',
                  }}
                >
                  Submit to checker
                </Typography>
                <ArrowForwardIcon />
              </Button>
            )}
            {configuredServices.length === 0 && (
              <Stack
                sx={{
                  width: '98%',
                  height: '20vh',
                  margin: 'auto',
                  justifyContent: 'center',
                  alignItems: 'center',
                }}
              >
                <Stack>
                  <ChargeConfigIcon />
                </Stack>
                <Stack sx={{ textAlign: 'center' }}>
                  <Typography fontWeight={700} sx={{ fontSize: '0.8rem' }}>
                    {' '}
                    No services configured Yet{' '}
                  </Typography>
                  <Typography color="#475467" sx={{ fontSize: '0.8rem' }}>
                    The services you configure will appear here
                  </Typography>
                </Stack>
              </Stack>
            )}
          </Stack>
        </Stack>
      </Stack>
    </Drawer>
  )
}
