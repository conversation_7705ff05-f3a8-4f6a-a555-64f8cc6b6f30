'use client'

import * as React from 'react'
import { Stack, Typography, Avatar, IconButton, Box } from '@mui/material'
import {
  ChargesIcon,
  EditTariffIcon,
  StatsIcon,
} from '@dtbx/ui/components/SvgIcons'
import { CustomerStatusChip } from '@dtbx/ui/components/Chip'

import Menu from '@mui/material/Menu'
import MenuItem from '@mui/material/MenuItem'
import Button from '@mui/material/Button'
import KeyboardArrowDownRounded from '@mui/icons-material/KeyboardArrowDownRounded'
import {
  KESIcon,
  USDIcon,
  EURIcon,
  GBPIcon,
  ZARIcon,
  TZSIcon,
  UGXIcon,
  BIFIcon,
} from '@dtbx/ui/components/SvgIcons'
import { Status } from '@/store/interfaces'

interface PageHeaderProps {
  Tariffname: string
  TariffStatus?: Status
  getSelectedCurrency: (currency: string) => void
}

interface Currency {
  code: string
  name: string
  icon: React.ElementType
}

const currencies: Currency[] = [
  { code: 'KES', name: 'Kenyan Shilling', icon: KESIcon },
  { code: 'USD', name: 'US Dollar', icon: USDIcon },
  { code: 'EUR', name: 'Euro', icon: EURIcon },
  { code: 'GBP', name: 'British Pound', icon: GBPIcon },
  { code: 'TZS', name: 'Tanzanian Shilling', icon: TZSIcon },
  { code: 'UGX', name: 'Ugandan Shilling', icon: UGXIcon },
  { code: 'ZAR', name: 'South African Rand', icon: ZARIcon },
  { code: 'BIF', name: 'Burundian Franc', icon: BIFIcon },
]

const renderStatusChip = (status: Status | undefined) => {
  switch (status) {
    case 'ACTIVE':
      return <CustomerStatusChip label="ACTIVE" />
    case 'INACTIVE':
      return <CustomerStatusChip label="INACTIVE" />
    case 'PENDING':
      return <CustomerStatusChip label="PENDING" />
    default:
      return <CustomerStatusChip label="ACTIVE" />
  }
}
//  Tariff status Header
export const TariffTypeStatus: React.FC<PageHeaderProps> = ({
  TariffStatus,
  Tariffname,
  getSelectedCurrency,
 
}) => {
  return (
    <Stack
      direction="row"
      flexWrap="wrap"
      gap={5}
      justifyContent="space-between"
      alignItems="center"
      marginLeft="1%"
      padding={{ xs: 1.5, sm: 1.5, md: '1.5rem 2rem' }}
    >
      <Stack direction="row" gap={1.25} alignItems="center">
        <Avatar
          sx={{
            width: 50,
            height: 50,
            backgroundColor: '#E7E8E9',
          }}
          alt="Charges Icon"
        >
          <ChargesIcon width="28" height="26" />
        </Avatar>
        <Box>
          <Stack direction="row" alignItems="center" gap={1}>
            <Typography variant="body1" fontWeight="medium">
              {Tariffname}
            </Typography>
            <IconButton size="small" onClick={() => console.log('clicked!')}>
              <EditTariffIcon />
            </IconButton>
          </Stack>
          {renderStatusChip(TariffStatus)}
        </Box>
      </Stack>
      <Stack direction="row" gap={2.5} alignItems="center" alignSelf="stretch">
        <CurrencyDropdown
          getSelectedCurrency={getSelectedCurrency}
        />
        <IconButton
          size="small"
          onClick={() => console.log('clicked!')}
          sx={{
            textTransform: 'none',
            fontSize: '13px',
            fontWeight: 500,
            padding: '9px 28px',
            borderRadius: '7px',
            border: '1px solid #AAADB0',
            color: '#555C61',
          }}
        >
          <StatsIcon />
          <Typography sx={{ ml: 1 }}>Stats</Typography>
        </IconButton>
      </Stack>
    </Stack>
  )
}

// Currency Dropdown
// const CurrencyDropdown: React.FC = ({ getSelectedCurrency }) => {
const CurrencyDropdown: React.FC<{
  getSelectedCurrency: (currency: string) => void
}> = ({ getSelectedCurrency }) => {
  const [anchorEl, setAnchorEl] = React.useState<null | HTMLElement>(null)
  const open = Boolean(anchorEl)
  const [selectedCurrency, setSelectedCurrency] = React.useState<string>('KES')

  const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    setAnchorEl(event.currentTarget)
  }

  const handleClose = () => {
    setAnchorEl(null)
  }

  const handleSelect = (currency: string) => {
    setSelectedCurrency(currency)
    // console.log('Selecting currency >', selectedCurrency)
    getSelectedCurrency(currency)
    handleClose()
  }

  const getCurrencyIcon = (code: string): React.ElementType => {
    const currency = currencies.find((c) => c.code === code)
    return currency?.icon || KESIcon
  }

  const SelectedIcon = getCurrencyIcon(selectedCurrency)

  return (
    <Box>
      <Button
        variant="outlined"
        sx={{
          width: '150px',
          padding: '9px 28px',
          borderRadius: '6px',
          border: '1px solid #AAADB0',
          boxShadow: '0px 1px 2px rgba(16, 24, 40, 0.05)',
          fontWeight: 500,
        }}
        endIcon={<KeyboardArrowDownRounded />}
        onClick={handleClick}
        aria-controls={open ? 'currency-menu' : undefined}
        aria-haspopup="true"
        aria-expanded={open ? 'true' : undefined}
      >
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <SelectedIcon style={{ width: 20, height: 14 }} />
          {selectedCurrency}
        </Box>
      </Button>
      <Menu
        open={open}
        anchorEl={anchorEl}
        onClose={handleClose}
        sx={{
          sx: {
            mt: 1,
            borderRadius: '6px',
            boxShadow:
              '0px 1px 2px rgba(16, 24, 40, 0.06), 0px 1px 3px rgba(16, 24, 40, 0.10)',
          },
        }}
      >
        {currencies.map((currency) => {
          const CurrencyIcon = currency.icon
          return (
            <MenuItem
              key={currency.code}
              onClick={() => handleSelect(currency.code)}
              sx={{
                display: 'flex',
                alignItems: 'center',
                gap: 1,
                '&:hover': { backgroundColor: '#F5F5F5' },
              }}
            >
              <CurrencyIcon style={{ width: 20, height: 14 }} />
              {currency.name}
            </MenuItem>
          )
        })}
      </Menu>
    </Box>
  )
}
