'use client'
import { Divider, Stack, Typography } from '@mui/material'
import React, { useEffect } from 'react'
import { useAppDispatch } from '@/store'
import { getMakerCheckerTypes } from '@/store/actions'
import { HomeIcon } from '@dtbx/ui/icons'

const HomePage = () => {
  const dispatch = useAppDispatch()
  useEffect(() => {
    getMakerCheckerTypes(dispatch, '?channel=DBP')
  }, [])
  return (
    <Stack>
      <Stack
        sx={{
          marginLeft: '2%',
          marginTop: '0.2%',
          flexDirection: 'row',
          justifyContent: 'flex-start',
          alignItems: 'center',
          gap: '8px',
          padding: '8px',
        }}
      >
        <HomeIcon width="28" height="26" />
        <Typography variant="h5">Home</Typography>
      </Stack>
      <Divider />
    </Stack>
  )
}

export default HomePage
