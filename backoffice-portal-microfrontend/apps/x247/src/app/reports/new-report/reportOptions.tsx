import React from 'react'
import {
  <PERSON><PERSON><PERSON>ort<PERSON><PERSON><PERSON><PERSON>,
  IReportFilter,
} from '@/app/reports/new-report/filters'
import { getTransactionChannels } from '@/store/actions'
export const TransactionsIcon = () => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="14"
      height="14"
      viewBox="0 0 14 14"
      fill="none"
    >
      <g clip-path="url(#clip0_9319_221673)">
        <path
          d="M9.29733 9.29831C11.2939 9.03102 12.8337 7.32098 12.8337 5.2513C12.8337 2.99614 11.0055 1.16797 8.75033 1.16797C6.68065 1.16797 4.97061 2.70778 4.70331 4.70429M9.33366 8.7513C9.33366 11.0065 7.50549 12.8346 5.25033 12.8346C2.99516 12.8346 1.16699 11.0065 1.16699 8.7513C1.16699 6.49614 2.99516 4.66797 5.25033 4.66797C7.50549 4.66797 9.33366 6.49614 9.33366 8.7513Z"
          stroke="#555C61"
          stroke-width="1.16667"
          stroke-linecap="round"
          stroke-linejoin="round"
        />
      </g>
      <defs>
        <clipPath id="clip0_9319_221673">
          <rect width="14" height="14" fill="white" />
        </clipPath>
      </defs>
    </svg>
  )
}
export const ServicesIcon = () => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="14"
      height="14"
      viewBox="0 0 14 14"
      fill="none"
    >
      <path
        d="M8.75033 1.16797V1.98464C8.75033 2.31133 8.75033 2.47468 8.68675 2.59946C8.63082 2.70922 8.54158 2.79846 8.43182 2.85439C8.30704 2.91797 8.14369 2.91797 7.81699 2.91797H6.18366C5.85696 2.91797 5.69361 2.91797 5.56883 2.85439C5.45907 2.79846 5.36983 2.70922 5.3139 2.59946C5.25033 2.47468 5.25033 2.31133 5.25033 1.98464V1.16797M4.78366 12.8346H9.21699C9.87039 12.8346 10.1971 12.8346 10.4466 12.7075C10.6662 12.5956 10.8446 12.4171 10.9565 12.1976C11.0837 11.9481 11.0837 11.6214 11.0837 10.968V3.03464C11.0837 2.38124 11.0837 2.05454 10.9565 1.80498C10.8446 1.58546 10.6662 1.40698 10.4466 1.29513C10.1971 1.16797 9.87039 1.16797 9.21699 1.16797H4.78366C4.13026 1.16797 3.80357 1.16797 3.554 1.29513C3.33448 1.40698 3.156 1.58546 3.04415 1.80498C2.91699 2.05454 2.91699 2.38124 2.91699 3.03464V10.968C2.91699 11.6214 2.91699 11.9481 3.04415 12.1976C3.156 12.4171 3.33448 12.5956 3.554 12.7075C3.80357 12.8346 4.13026 12.8346 4.78366 12.8346Z"
        stroke="#555C61"
        stroke-width="1.16667"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
    </svg>
  )
}
export const CustomerDetsIcon = () => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="14"
      height="14"
      viewBox="0 0 14 14"
      fill="none"
    >
      <path
        d="M12.8337 12.25V11.0833C12.8337 9.99609 12.09 9.08253 11.0837 8.82351M9.04199 1.91961C9.89711 2.26575 10.5003 3.1041 10.5003 4.08333C10.5003 5.06257 9.89711 5.90091 9.04199 6.24706M9.91699 12.25C9.91699 11.1628 9.91699 10.6192 9.73938 10.1904C9.50256 9.61867 9.04832 9.16443 8.47659 8.92761C8.04779 8.75 7.50419 8.75 6.41699 8.75H4.66699C3.5798 8.75 3.0362 8.75 2.6074 8.92761C2.03567 9.16443 1.58143 9.61867 1.34461 10.1904C1.16699 10.6192 1.16699 11.1628 1.16699 12.25M7.87533 4.08333C7.87533 5.372 6.83066 6.41667 5.54199 6.41667C4.25333 6.41667 3.20866 5.372 3.20866 4.08333C3.20866 2.79467 4.25333 1.75 5.54199 1.75C6.83066 1.75 7.87533 2.79467 7.87533 4.08333Z"
        stroke="#555C61"
        stroke-width="1.16667"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
    </svg>
  )
}
export const FinancialIcon = () => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="14"
      height="14"
      viewBox="0 0 14 14"
      fill="none"
    >
      <path
        d="M12.25 12.25H2.68333C2.35664 12.25 2.19329 12.25 2.06851 12.1864C1.95874 12.1305 1.86951 12.0413 1.81358 11.9315C1.75 11.8067 1.75 11.6434 1.75 11.3167V1.75M12.25 4.08333L9.07998 7.25335C8.96448 7.36886 8.90673 7.42661 8.84013 7.44825C8.78155 7.46728 8.71845 7.46728 8.65987 7.44825C8.59327 7.42661 8.53552 7.36886 8.42002 7.25335L7.32998 6.16332C7.21448 6.04781 7.15673 5.99006 7.09013 5.96842C7.03155 5.94939 6.96845 5.94939 6.90987 5.96842C6.84327 5.99006 6.78552 6.04781 6.67002 6.16332L4.08333 8.75M12.25 4.08333H9.91667M12.25 4.08333V6.41667"
        stroke="#555C61"
        stroke-width="1.16667"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
    </svg>
  )
}
export const ModuleIcon = () => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="14"
      height="14"
      viewBox="0 0 14 14"
      fill="none"
    >
      <path
        d="M4.66667 7.58333V9.91667M9.33333 6.41667V9.91667M7 4.08333V9.91667M4.55 12.25H9.45C10.4301 12.25 10.9201 12.25 11.2945 12.0593C11.6238 11.8915 11.8915 11.6238 12.0593 11.2945C12.25 10.9201 12.25 10.4301 12.25 9.45V4.55C12.25 3.56991 12.25 3.07986 12.0593 2.70552C11.8915 2.37623 11.6238 2.10852 11.2945 1.94074C10.9201 1.75 10.4301 1.75 9.45 1.75H4.55C3.56991 1.75 3.07986 1.75 2.70552 1.94074C2.37623 2.10852 2.10852 2.37623 1.94074 2.70552C1.75 3.07986 1.75 3.56991 1.75 4.55V9.45C1.75 10.4301 1.75 10.9201 1.94074 11.2945C2.10852 11.6238 2.37623 11.8915 2.70552 12.0593C3.07986 12.25 3.56991 12.25 4.55 12.25Z"
        stroke="#555C61"
        stroke-width="1.16667"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
    </svg>
  )
}
export const reportCategories: IReportCategory[] = [
  {
    title: 'Transaction Reports',
    category: 'Transaction Reports',
    icon: <TransactionsIcon />,
    items: [
      {
        title: 'Internal Funds Transfer (IFT)',
        key: 'ift',
        category: 'Transaction Reports',
        filters: [
          {
            filterName: 'channel',
            options: [],
            type: 'autocomplete',
            apiAction: getTransactionChannels,
          },
          {
            filterName: 'status',
            options: [
              { key: 'success', value: 'SUCCESS', label: 'Successful' },
              { key: 'pending', value: 'PENDING', label: 'Pending' },
              { key: 'failed', value: 'FAILED', label: 'Failed' },
            ],
            type: 'dropdown/single',
          },
          {
            filterName: 'timestamp',
            options: [
              {
                label: 'Date',
                value: 'date',
                key: '',
              },
            ],
            type: 'datetime',
          },
          {
            filterName: 'time',
            options: [
              {
                label: 'Time',
                value: 'time',
                key: '',
              },
            ],
            type: 'time',
          },
        ] as IReportFilter[],
      },
      {
        title: 'Mpesa C2B',
        category: 'Transaction Reports',
        key: 'mpesa/c2b',
        filters: [
          {
            filterName: 'channel',
            options: [],
            type: 'autocomplete',
            apiAction: getTransactionChannels,
          },
          {
            filterName: 'status',
            options: [
              { key: 'success', value: 'SUCCESS', label: 'Successful' },
              { key: 'pending', value: 'PENDING', label: 'Pending' },
              { key: 'failed', value: 'FAILED', label: 'Failed' },
            ],
            type: 'dropdown/single',
          },
          {
            filterName: 'timestamp',
            options: [
              {
                label: 'Date',
                value: 'date',
                key: '',
              },
            ],
            type: 'datetime',
          },
          {
            filterName: 'time',
            options: [
              {
                label: 'Time',
                value: 'time',
                key: '',
              },
            ],
            type: 'time',
          },
        ] as IReportFilter[],
      },
      {
        title: 'Mpesa Account Top-Up (B2C)',
        key: 'mpesa/account-top-up',
        category: 'Transaction Reports',
        filters: [
          {
            filterName: 'channel',
            options: [],
            type: 'autocomplete',
            apiAction: getTransactionChannels,
          },
          {
            filterName: 'status',
            options: [
              { key: 'success', value: 'SUCCESS', label: 'Successful' },
              { key: 'pending', value: 'PENDING', label: 'Pending' },
              { key: 'failed', value: 'FAILED', label: 'Failed' },
            ],
            type: 'dropdown/single',
          },
          {
            filterName: 'timestamp',
            options: [
              {
                label: 'Date',
                value: 'date',
                key: '',
              },
            ],
            type: 'datetime',
          },
          {
            filterName: 'time',
            options: [
              {
                label: 'Time',
                value: 'time',
                key: '',
              },
            ],
            type: 'time',
          },
        ] as IReportFilter[],
      },

      {
        title: 'RTGS Transfer',
        category: 'Transaction Reports',
        key: 'rtgs',
        filters: [
          {
            filterName: 'channel',
            options: [],
            type: 'autocomplete',
            apiAction: getTransactionChannels,
          },
          {
            filterName: 'status',
            options: [
              { key: 'success', value: 'SUCCESS', label: 'Successful' },
              { key: 'pending', value: 'PENDING', label: 'Pending' },
              { key: 'failed', value: 'FAILED', label: 'Failed' },
            ],
            type: 'dropdown/single',
          },
          {
            filterName: 'timestamp',
            options: [
              {
                label: 'Date',
                value: 'date',
                key: '',
              },
            ],
            type: 'datetime',
          },
          {
            filterName: 'time',
            options: [
              {
                label: 'Time',
                value: 'time',
                key: '',
              },
            ],
            type: 'time',
          },
        ] as IReportFilter[],
      },
      {
        title: 'Pesalink Transfer',
        category: 'Transaction Reports',
        key: 'pesalink',
        filters: [
          {
            filterName: 'channel',
            options: [],
            type: 'autocomplete',
            apiAction: getTransactionChannels,
          },
          {
            filterName: 'status',
            options: [
              { key: 'success', value: 'SUCCESS', label: 'Successful' },
              { key: 'pending', value: 'PENDING', label: 'Pending' },
              { key: 'failed', value: 'FAILED', label: 'Failed' },
            ],
            type: 'dropdown/single',
          },
          {
            filterName: 'timestamp',
            options: [
              {
                label: 'Date',
                value: 'date',
                key: '',
              },
            ],
            type: 'datetime',
          },
          {
            filterName: 'time',
            options: [
              {
                label: 'Time',
                value: 'time',
                key: '',
              },
            ],
            type: 'time',
          },
        ] as IReportFilter[],
      },
      {
        title: 'Buy Airtime',
        category: 'Transaction Reports',
        key: 'buy-airtime',
        filters: [
          {
            filterName: 'channel',
            options: [],
            type: 'autocomplete',
            apiAction: getTransactionChannels,
          },
          {
            filterName: 'status',
            options: [
              { key: 'SUCCESSFUL', value: 'SUCCESSFUL', label: 'Successful' },
              { key: 'PENDING', value: 'PENDING', label: 'Pending' },
              {
                key: 'PENDING_2FA',
                value: 'PENDING_2FA',
                label: 'Pending 2FA',
              },
              {
                key: 'PENDING_DEBIT',
                value: 'PENDING_DEBIT',
                label: 'Pending Debit',
              },
              {
                key: 'PENDING_BILL_PAYMENT',
                value: 'PENDING_BILL_PAYMENT',
                label: 'Pending Bill Payment',
              },
              {
                key: 'PENDING_BILLER_NOTIFICATION',
                value: 'PENDING_BILLER_NOTIFICATION',
                label: 'Pending Biller Notification',
              },
              {
                key: 'FAILED_BILLER_NOTIFICATION',
                value: 'FAILED_BILLER_NOTIFICATION',
                label: 'Failed Biller Notification',
              },
              {
                key: 'REVERSED',
                value: 'REVERSED',
                label: 'Reversed',
              },
              { key: 'FAILED', value: 'FAILED', label: 'Failed' },
              {
                key: 'ACCOUNT_MANDATE_CHECK_FAILED',
                value: 'ACCOUNT_MANDATE_CHECK_FAILED',
                label: 'Account Mandate Check Failed',
              },
              {
                key: 'PENDING_SIGNATORY_APPROVAL',
                value: 'PENDING_SIGNATORY_APPROVAL',
                label: 'Pending Signatory Approval',
              },
              {
                key: 'PROCESSED',
                value: 'PROCESSED',
                label: 'Processed',
              },
            ],
            type: 'dropdown/single',
          },
          {
            filterName: 'timestamp',
            options: [
              {
                label: 'Date',
                value: 'date',
                key: '',
              },
            ],
            type: 'datetime',
          },
          {
            filterName: 'time',
            options: [
              {
                label: 'Time',
                value: 'time',
                key: '',
              },
            ],
            type: 'time',
          },
        ] as IReportFilter[],
      },
      {
        title: 'Pay Bills',
        category: 'Transaction Reports',
        key: 'paybill',
        filters: [
          {
            filterName: 'channel',
            options: [],
            type: 'autocomplete',
            apiAction: getTransactionChannels,
          },
          {
            filterName: 'status',
            options: [
              { key: 'SUCCESSFUL', value: 'SUCCESSFUL', label: 'Successful' },
              { key: 'PENDING', value: 'PENDING', label: 'Pending' },
              {
                key: 'PENDING_2FA',
                value: 'PENDING_2FA',
                label: 'Pending 2FA',
              },
              {
                key: 'PENDING_DEBIT',
                value: 'PENDING_DEBIT',
                label: 'Pending Debit',
              },
              {
                key: 'PENDING_BILL_PAYMENT',
                value: 'PENDING_BILL_PAYMENT',
                label: 'Pending Bill Payment',
              },
              {
                key: 'PENDING_BILLER_NOTIFICATION',
                value: 'PENDING_BILLER_NOTIFICATION',
                label: 'Pending Biller Notification',
              },
              {
                key: 'FAILED_BILLER_NOTIFICATION',
                value: 'FAILED_BILLER_NOTIFICATION',
                label: 'Failed Biller Notification',
              },
              {
                key: 'REVERSED',
                value: 'REVERSED',
                label: 'Reversed',
              },
              { key: 'FAILED', value: 'FAILED', label: 'Failed' },
              {
                key: 'ACCOUNT_MANDATE_CHECK_FAILED',
                value: 'ACCOUNT_MANDATE_CHECK_FAILED',
                label: 'Account Mandate Check Failed',
              },
              {
                key: 'PENDING_SIGNATORY_APPROVAL',
                value: 'PENDING_SIGNATORY_APPROVAL',
                label: 'Pending Signatory Approval',
              },
              {
                key: 'PROCESSED',
                value: 'PROCESSED',
                label: 'Processed',
              },
            ],
            type: 'dropdown/single',
          },
          {
            filterName: 'timestamp',
            options: [
              {
                label: 'Date',
                value: 'date',
                key: '',
              },
            ],
            type: 'datetime',
          },
          {
            filterName: 'time',
            options: [
              {
                label: 'Time',
                value: 'time',
                key: '',
              },
            ],
            type: 'time',
          },
        ] as IReportFilter[],
      },
    ],
  },
  {
    category: 'Service Reports',
    title: 'Service Reports',
    icon: <ServicesIcon />,
    items: [],
  },
  {
    category: 'Customer Details Reports',
    title: 'Customer Details Reports',
    icon: <CustomerDetsIcon />,
    items: [
      {
        title: 'Change Pin',
        category: 'Customer Details Reports',
        key: 'Change_Pin',
        filters: [
          {
            filterName: 'timestamp',
            options: [
              {
                label: 'Date',
                value: 'date',
                key: '',
              },
            ],
            type: 'datetime',
          },
          {
            filterName: 'time',
            options: [
              {
                label: 'Time',
                value: 'time',
                key: '',
              },
            ],
            type: 'time',
          },
        ] as IReportFilter[],
      },
      {
        title: 'Reset Pin',
        category: 'Customer Details Reports',
        key: 'Reset_Pin',
        filters: [
          {
            filterName: 'timestamp',
            options: [
              {
                label: 'Date',
                value: 'date',
                key: '',
              },
            ],
            type: 'datetime',
          },
          {
            filterName: 'time',
            options: [
              {
                label: 'Time',
                value: 'time',
                key: '',
              },
            ],
            type: 'time',
          },
        ] as IReportFilter[],
      },
      {
        title: 'Change Security Questions',
        category: 'Customer Details Reports',
        key: 'Change_Security_Questions',
        filters: [
          {
            filterName: 'timestamp',
            options: [
              {
                label: 'Date',
                value: 'date',
                key: '',
              },
            ],
            type: 'datetime',
          },
          {
            filterName: 'time',
            options: [
              {
                label: 'Time',
                value: 'time',
                key: '',
              },
            ],
            type: 'time',
          },
        ] as IReportFilter[],
      },
      {
        title: 'Customer Accounts',
        category: 'Customer Details Reports',
        key: 'CUSTOMER_ACCOUNTS',
        filters: [
          {
            filterName: 'timestamp',
            options: [
              {
                label: 'Date',
                value: 'date',
                key: '',
              },
            ],
            type: 'datetime',
          },
          {
            filterName: 'time',
            options: [
              {
                label: 'Time',
                value: 'time',
                key: '',
              },
            ],
            type: 'time',
          },
        ] as IReportFilter[],
      },
      {
        title: 'Customers',
        category: 'Customer Details Reports',
        key: 'CUSTOMERS',
        filters: [
          {
            filterName: 'timestamp',
            options: [
              {
                label: 'Date',
                value: 'date',
                key: '',
              },
            ],
            type: 'datetime',
          },
          {
            filterName: 'time',
            options: [
              {
                label: 'Time',
                value: 'time',
                key: '',
              },
            ],
            type: 'time',
          },
        ] as IReportFilter[],
      },
      {
        title: 'Customer Devices',
        category: 'Customer Details Reports',
        key: 'CUSTOMER_DEVICES',
        filters: [
          {
            filterName: 'timestamp',
            options: [
              {
                label: 'Date',
                value: 'date',
                key: '',
              },
            ],
            type: 'datetime',
          },
          {
            filterName: 'time',
            options: [
              {
                label: 'Time',
                value: 'time',
                key: '',
              },
            ],
            type: 'time',
          },
        ] as IReportFilter[],
      },
      {
        title: 'Customer Security Status',
        category: 'Customer Details Reports',
        key: 'CUSTOMER_SECURITY_STATUS',
        filters: [
          {
            filterName: 'timestamp',
            options: [
              {
                label: 'Date',
                value: 'date',
                key: '',
              },
            ],
            type: 'datetime',
          },
          {
            filterName: 'time',
            options: [
              {
                label: 'Time',
                value: 'time',
                key: '',
              },
            ],
            type: 'time',
          },
        ] as IReportFilter[],
      },
    ],
  },
  {
    category: 'Financial Reports',
    title: 'Financial Reports',
    icon: <FinancialIcon />,
    items: [],
  },
  {
    category: 'Module Performance Reports',
    title: 'Module Performance Reports',
    icon: <ModuleIcon />,
    items: [],
  },
]
