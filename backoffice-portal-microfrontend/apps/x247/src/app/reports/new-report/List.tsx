'use client'
import {
  Paper,
  Stack,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableRow,
  Typography,
  Box,
  Button,
  Drawer,
  IconButton,
} from '@mui/material'
import { useAppDispatch, useAppSelector } from '@/store'
import {
  CustomPagination,
  CustomTableHeader,
  type PaginationOptions,
} from '@dtbx/ui/components/Table'
import { useEffect, useState } from 'react'
import {
  CustomErrorChip,
  CustomSuccessChip,
  CustomWarningChip,
} from '@dtbx/ui/components/Chip'
import { sentenceCase } from 'tiny-case'
import { formatTimestamp } from '@dtbx/store/utils'
import {
  CustomerReportTypes,
  IAccountReportRecord,
  IBuyAirtimeReportRecord,
  IC2BReportRecord,
  ICustomerAccountsRecord,
  ICustomerDevicesRecord,
  ICustomerSecurityStatusRecord,
  ICustomerReportRecord,
  IDeviceRecordReport,
  IIFTReportRecord,
  IPayBillReportRecord,
  IPesalinkReportRecord,
  IProfilePinRecord,
  IRTGSReportRecord,
  TransactionReportTypes,
  IProfileAccountStoreIdReport,
  Imsi,
} from '@/store/interfaces'
import { getCustomerReports, getReports } from '@/store/actions/reports'
import { Close } from '@mui/icons-material'

type IReportItem =
  | IIFTReportRecord
  | IRTGSReportRecord
  | IPesalinkReportRecord
  | IC2BReportRecord
  | IPayBillReportRecord
  | IBuyAirtimeReportRecord
  | ICustomerReportRecord
  | ICustomerAccountsRecord
  | ICustomerDevicesRecord
  | ICustomerSecurityStatusRecord

export const ReportsList = ({
  buildQueryParams,
  selectedCategory,
}: {
  buildQueryParams: (page?: number, size?: number) => string
  selectedCategory: string
}) => {
  const [headerRow, setHeaderRow] = useState<{ id: string; label: string }[]>(
    []
  )
  const [currentList, setCurrentList] = useState<IReportItem[]>([])
  const dispatch = useAppDispatch()

  const {
    iftReportList,
    rtgsReportList,
    pesalinkReportList,
    c2bReportList,
    mpesaAccountTopUpReportList,
    payBillReportList,
    buyAirtimeReportList,
    customerReportList,
    reportPaginationData,
    selectedReportType,
    customerAccountsReportList,
    customerDevicesReportList,
    customerSecurityStatusReportList,
  } = useAppSelector((state) => state.reports)

  const [paginationOptions, setPaginationOptions] = useState({
    page: reportPaginationData.pageNumber,
    size: reportPaginationData.pageSize,
    totalPages: reportPaginationData.totalNumberOfPages,
  })

  // Handle pagination changes
  const handlePagination = async (newOptions: PaginationOptions) => {
    setPaginationOptions(newOptions)
    const queryParams = buildQueryParams(newOptions.page, newOptions.size)

    if (selectedCategory === 'Transaction Reports') {
      await getReports(
        dispatch,
        selectedReportType as TransactionReportTypes,
        queryParams
      )
    }

    if (selectedCategory === 'Customer Details Reports') {
      await getCustomerReports(
        dispatch,
        selectedReportType as CustomerReportTypes,
        queryParams
      )
    }
  }

  // Extract headers from data item
  const getFlattenedHeaders = (
    dataItem: IReportItem
  ): { id: string; label: string }[] => {
    const headers = new Set<string>()
    const specialColumns = new Set<string>(['accounts', 'devices', 'imsis'])

    //regular headers
    Object.keys(dataItem).forEach((key) => {
      if (
        !specialColumns.has(key) &&
        key !== 'profilePins' &&
        key !== 'customerIds' &&
        key !== 'profileAccountStoreIds'
      ) {
        headers.add(key)
      }
    })

    // Add special columns at the end
    Object.keys(dataItem).forEach((key) => {
      if (specialColumns.has(key)) {
        headers.add(key)
      }
    })

    return Array.from(headers).map((key) => ({
      id: key,
      label: sentenceCase(key),
    }))
  }

  // Flatten customer data for display
  const flattenCustomerData = (
    data: ICustomerReportRecord[]
  ): ICustomerReportRecord[] => {
    return data.map((item) => {
      const flattenedItem: Record<string, any> = { ...item }

      if (item.profilePins) {
        item.profilePins.forEach((pin: IProfilePinRecord) => {
          const typeKey = pin.type.toLowerCase().replace(/ /g, '_')

          Object.entries(pin).forEach(([key, value]) => {
            if (key === 'type') return
            const newKey = `${typeKey}_${key}`
            flattenedItem[newKey] = value
          })
        })
      }

      return flattenedItem as ICustomerReportRecord
    })
  }

  // Flatten customer accounts data for display
  const flattenCustomerAccountsData = (
    data: ICustomerAccountsRecord[]
  ): ICustomerAccountsRecord[] => {
    return data.map((item) => {
      const flattenedItem: Record<string, any> = { ...item }

      if (item.profilePins) {
        item.profilePins.forEach((pin: IProfilePinRecord) => {
          const typeKey = pin.type.toLowerCase().replace(/ /g, '_')

          Object.entries(pin).forEach(([key, value]) => {
            if (key === 'type') return
            const newKey = `${typeKey}_${key}`
            flattenedItem[newKey] = value
          })
        })
      }

      return flattenedItem as ICustomerAccountsRecord
    })
  }

  const flattenCustomerSecurityStatusData = (
    data: ICustomerSecurityStatusRecord[]
  ): ICustomerSecurityStatusRecord[] => {
    return data.map((item) => {
      const flattenedItem: Record<string, any> = { ...item }

      // Flatten profilePins
      if (item.profilePins) {
        item.profilePins.forEach((pin: IProfilePinRecord) => {
          const typeKey = pin.type.toLowerCase().replace(/ /g, '_')
          Object.entries(pin).forEach(([key, value]) => {
            if (key === 'type') return
            flattenedItem[`${typeKey}_${key}`] = value
          })
        })
        delete flattenedItem.profilePins
      }

      // fields that only exist in ICustomerReportRecord
      const safeItem: Partial<ICustomerReportRecord> = {}
      const allowedFields = [
        'initiatorPhoneNumber',
        'initiatorDeviceId',
        'eventType',
        'eventDate',
        'firstName',
        'lastName',
        'email',
        'idNumber',
        'isBlocked',
        'status',
        'createdOn',
        'security_questions_status',
        'security_questions_attempts',
        'pin_status',
        'pin_attempts',
        'customerIds',
        'imsis',
      ]

      allowedFields.forEach((field) => {
        if (flattenedItem[field as keyof ICustomerReportRecord] !== undefined) {
          safeItem[field as keyof ICustomerReportRecord] =
            flattenedItem[field as keyof ICustomerReportRecord]
        }
      })

      //  customerIds if zina exist
      if (flattenedItem.customerIds) {
        safeItem.customerIds = Array.isArray(flattenedItem.customerIds)
          ? flattenedItem.customerIds.join(', ')
          : flattenedItem.customerIds
      }

      return safeItem as ICustomerSecurityStatusRecord
    })
  }

  // Update current list and headers when report type changes
  useEffect(() => {
    const updateListAndHeaders = (
      list: IReportItem[] | null,
      flattenFn?: Function
    ) => {
      if (!list || list.length === 0) return
      const processedList = flattenFn ? flattenFn(list) : list
      setCurrentList(processedList)
      setHeaderRow(getFlattenedHeaders(processedList[0]))
    }

    switch (selectedReportType) {
      case 'ift':
        updateListAndHeaders(iftReportList)
        break
      case 'rtgs':
        updateListAndHeaders(rtgsReportList)
        break
      case 'pesalink':
        updateListAndHeaders(pesalinkReportList)
        break
      case 'mpesa/c2b':
        updateListAndHeaders(c2bReportList)
        break
      case 'mpesa/account-top-up':
        updateListAndHeaders(mpesaAccountTopUpReportList)
        break
      case 'paybill':
        updateListAndHeaders(payBillReportList)
        break
      case 'buy-airtime':
        updateListAndHeaders(buyAirtimeReportList)
        break
      case 'Change_Pin':
      case 'Reset_Pin':
      case 'Change_Security_Questions':
      case 'CUSTOMERS':
        updateListAndHeaders(customerReportList, flattenCustomerData)
        break
      case 'CUSTOMER_SECURITY_STATUS':
        updateListAndHeaders(
          customerSecurityStatusReportList,
          flattenCustomerSecurityStatusData
        )
        break
      case 'CUSTOMER_DEVICES':
        updateListAndHeaders(customerDevicesReportList)
        break
      case 'CUSTOMER_ACCOUNTS':
        updateListAndHeaders(
          customerAccountsReportList,
          flattenCustomerAccountsData
        )
        break
      default:
        setHeaderRow([])
        setCurrentList([])
    }
  }, [
    selectedReportType,
    iftReportList,
    rtgsReportList,
    pesalinkReportList,
    c2bReportList,
    mpesaAccountTopUpReportList,
    payBillReportList,
    buyAirtimeReportList,
    customerReportList,
    customerAccountsReportList,
    reportPaginationData,
  ])

  const renderCellContent = (row: IReportItem, headerId: string) => {
    // Safely get value (undefined if property doesn't exist)
    const value = row?.[headerId as keyof typeof row]

    switch (headerId) {
      case 'status': {
        const status = String(value || '')
        if (status.toUpperCase().includes('SUCCESS')) {
          return <CustomSuccessChip label={status} />
        }
        if (status.toUpperCase().includes('FAILED')) {
          return <CustomErrorChip label={status} />
        }
        return <CustomWarningChip label={status} />
      }

      case 'createdOn':
      case 'eventDate':
      case 'dateCreated':
        return value ? formatTimestamp(String(value)) : ''

      case 'isBlocked':
        return value === true ? 'Yes' : 'No'

      case 'accounts':
        return 'accounts' in row ? (
          <AccountsDrawerButton
            accounts={(row as ICustomerAccountsRecord).accounts}
          />
        ) : (
          ''
        )

      case 'devices':
        return 'devices' in row ? (
          <DevicesView devices={(row as ICustomerDevicesRecord).devices} />
        ) : (
          ''
        )

      case 'imsis':
        return 'imsis' in row ? (
          <IMSIView imsis={(row as ICustomerSecurityStatusRecord).imsis} />
        ) : (
          ''
        )

      default:
        if (value == null) return ''
        return String(value)
    }
  }

  return (
    <Stack>
      <Paper
        sx={{
          width: '100%',
          overflow: 'hidden',
          borderRadius: '4px',
          border: '1px solid #EAECF0',
          background: '#FEFEFE',
          boxShadow:
            '0px 1px 2px 0px rgba(16, 24, 40, 0.06), 0px 1px 3px 0px rgba(16, 24, 40, 0.10)',
        }}
      >
        <Stack sx={{ px: '1vw', py: 1 }}>
          <Typography variant="body2">
            Selected {selectedReportType.toUpperCase()} report
          </Typography>
          <Typography variant="label2">
            Showing {currentList?.length} of{' '}
            {reportPaginationData.totalElements} records
          </Typography>
        </Stack>

        <TableContainer component={Paper} sx={{ boxShadow: 'none' }}>
          <Table sx={{ minWidth: 650 }} aria-label="reports table" size="small">
            <CustomTableHeader
              order={'asc'}
              orderBy={'id'}
              headLabel={headerRow}
              showCheckbox={false}
              rowCount={reportPaginationData.pageSize}
              numSelected={0}
              onRequestSort={() => {}}
              onSelectAllClick={() => {}}
            />
            <TableBody>
              {currentList &&
                currentList.map((row, index) => (
                  <TableRow hover key={index} tabIndex={-1}>
                    {headerRow.map((header) => (
                      <TableCell key={header.id} sx={{ minWidth: '10vw' }}>
                        {renderCellContent(row, header.id)}
                      </TableCell>
                    ))}
                  </TableRow>
                ))}
            </TableBody>
          </Table>
        </TableContainer>

        <CustomPagination
          options={{
            ...paginationOptions,
            totalPages: reportPaginationData.totalNumberOfPages,
          }}
          handlePagination={handlePagination}
        />
      </Paper>
    </Stack>
  )
}

// Separate component for displaying accounts in a nested table
const AccountsDrawerButton = ({
  accounts,
}: {
  accounts: IAccountReportRecord[]
}) => {
  const [open, setOpen] = useState(false)
  const [accountHeaders, setAccountHeaders] = useState<
    { id: string; label: string }[]
  >([])

  useEffect(() => {
    if (accounts && accounts.length > 0) {
      setAccountHeaders(
        Object.keys(accounts[0]).map((key) => ({
          id: key,
          label: sentenceCase(key),
        }))
      )
    } else {
      setAccountHeaders([])
    }
  }, [accounts])

  const toggleDrawer = (newOpen: boolean) => () => {
    setOpen(newOpen)
  }

  if (!accounts || accounts.length === 0)
    return (
      <Typography color="text.secondary" sx={{ fontSize: '14px' }}>
        No accounts
      </Typography>
    )

  return (
    <>
      <Button
        variant="outlined"
        size="small"
        onClick={toggleDrawer(true)}
        sx={{
          px: '10px',
          py: '5px',
          border: '1px solid',
          fontSize: '12px',
          '&:hover': { border: '1px solid' },
        }}
        startIcon={
          <Box component="span" sx={{ fontSize: '14px !important' }}>
            {accounts.length}
          </Box>
        }
      >
        View Accounts
      </Button>
      <Drawer
        anchor="right"
        open={open}
        onClose={toggleDrawer(false)}
        slotProps={{
          paper: {
            sx: { maxWidth: '75%' },
          },
        }}
      >
        <Box sx={{ p: 3 }}>
          <Stack
            direction="row"
            justifyContent="space-between"
            alignItems="center"
            mb={2}
          >
            <Typography variant="h6">Account Details</Typography>
            <IconButton onClick={toggleDrawer(false)} size="small">
              <Close />
            </IconButton>
          </Stack>

          <Typography variant="body2" color="text.secondary" mb={2}>
            Showing {accounts.length} account{accounts.length !== 1 ? 's' : ''}
          </Typography>

          <TableContainer
            component={Paper}
            sx={{ boxShadow: 'none', border: '1px solid #f0f0f0' }}
          >
            <Table size="small">
              <CustomTableHeader
                order={'asc'}
                orderBy={'accountNo'}
                headLabel={accountHeaders}
                showCheckbox={false}
                rowCount={accounts.length}
                numSelected={0}
                onRequestSort={() => {}}
                onSelectAllClick={() => {}}
              />
              <TableBody>
                {accounts.map((account, idx) => (
                  <TableRow key={idx} hover>
                    {accountHeaders.map((header) => (
                      <TableCell key={header.id}>
                        {header.id === 'dateCreated' ||
                        header.id === 'dateActivated'
                          ? formatTimestamp(
                              account[header.id as keyof IAccountReportRecord]
                            )
                          : account[header.id as keyof IAccountReportRecord]}
                      </TableCell>
                    ))}
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </Box>
      </Drawer>
    </>
  )
}

const DevicesView = ({ devices }: { devices: IDeviceRecordReport[] }) => {
  const [open, setOpen] = useState(false)
  const [deviceHeaders, setDeviceHeaders] = useState<
    { id: string; label: string }[]
  >([])

  const toggleDrawer = (newOpen: boolean) => () => {
    setOpen(newOpen)
  }
  useEffect(() => {
    if (devices && devices.length > 0) {
      setDeviceHeaders(
        Object.keys(devices[0]).map((key) => ({
          id: key,
          label: sentenceCase(key),
        }))
      )
    } else {
      setDeviceHeaders([])
    }
  }, [devices])
  if (!devices || devices.length === 0)
    return (
      <Typography color="text.secondary" sx={{ fontSize: '14px' }}>
        No Devices
      </Typography>
    )
  return (
    <>
      <Button
        variant="outlined"
        size="small"
        onClick={toggleDrawer(true)}
        sx={{
          px: '10px',
          py: '5px',
          border: '1px solid',
          fontSize: '12px',
          '&:hover': { border: '1px solid' },
        }}
        startIcon={
          <Box component="span" sx={{ fontSize: '14px !important' }}>
            {devices.length}
          </Box>
        }
      >
        View Devices
      </Button>
      <Drawer
        anchor="right"
        open={open}
        onClose={toggleDrawer(false)}
        slotProps={{
          paper: {
            sx: { maxWidth: '75%' },
          },
        }}
      >
        <Box sx={{ p: 3 }}>
          <Stack
            direction="row"
            justifyContent="space-between"
            alignItems="center"
            mb={2}
          >
            <Typography variant="h6">Device Details</Typography>
            <IconButton onClick={toggleDrawer(false)} size="small">
              <Close />
            </IconButton>
          </Stack>

          <Typography variant="body2" color="text.secondary" mb={2}>
            Showing {devices.length} device {devices.length !== 1 ? 's' : ''}
          </Typography>
          <TableContainer
            component={Paper}
            sx={{ boxShadow: 'none', border: 'none' }}
          >
            <Table size="small">
              <CustomTableHeader
                order={'asc'}
                orderBy={''}
                headLabel={deviceHeaders}
                showCheckbox={false}
                rowCount={0}
                numSelected={0}
                onRequestSort={() => {}}
                onSelectAllClick={() => {}}
              />
              <TableBody>
                {devices.map((device, idx) => (
                  <TableRow key={idx}>
                    {deviceHeaders.map((header) => (
                      <TableCell key={header.id}>
                        {header.id === 'dateCreated' ||
                        header.id === 'dateModified'
                          ? formatTimestamp(
                              device[header.id as keyof IDeviceRecordReport]
                            )
                          : device[header.id as keyof IDeviceRecordReport]}
                      </TableCell>
                    ))}
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </Box>
      </Drawer>
    </>
  )
}

const IMSIView = ({ imsis }: { imsis: Imsi[] }) => {
  const [open, setOpen] = useState(false)
  const [imsiHeaders, setImsiHeaders] = useState<
    { id: string; label: string }[]
  >([])
  const toggleDrawer = (newOpen: boolean) => () => {
    setOpen(newOpen)
  }
  useEffect(() => {
    if (imsis && imsis.length > 0) {
      setImsiHeaders(
        Object.keys(imsis[0]).map((key) => ({
          id: key,
          label: sentenceCase(key),
        }))
      )
    } else {
      setImsiHeaders([])
    }
  }, [imsis])
  if (!imsis || imsis.length === 0)
    return (
      <Typography color="text.secondary" sx={{ fontSize: '14px' }}>
        No Imsi
      </Typography>
    )
  return (
    <>
      {' '}
      <Button
        variant="outlined"
        size="small"
        onClick={toggleDrawer(true)}
        sx={{
          px: '10px',
          py: '5px',
          border: '1px solid',
          fontSize: '12px',
          '&:hover': { border: '1px solid' },
        }}
        startIcon={
          <Box component="span" sx={{ fontSize: '14px !important' }}>
            {imsis.length}
          </Box>
        }
      >
        {`View IMsi${imsis.length > 1 ? 's' : ''}`}
      </Button>
      <Drawer
        anchor="right"
        open={open}
        onClose={toggleDrawer(false)}
        slotProps={{
          paper: {
            sx: { maxWidth: '50%' },
          },
        }}
      >
        <Box sx={{ p: 3 }}>
          <Stack
            direction="row"
            justifyContent="space-between"
            alignItems="center"
            mb={2}
          >
            <Typography variant="h6">Security Status Details</Typography>
            <IconButton onClick={toggleDrawer(false)} size="small">
              <Close />
            </IconButton>
          </Stack>

          <Typography variant="body2" color="text.secondary" mb={2}>
            Showing {imsis.length} imsi {imsis.length !== 1 ? '' : ''}
          </Typography>
          <TableContainer
            component={Paper}
            sx={{ boxShadow: 'none', border: 'none' }}
          >
            <Table size="small">
              <CustomTableHeader
                order={'asc'}
                orderBy={''}
                headLabel={imsiHeaders}
                showCheckbox={false}
                rowCount={0}
                numSelected={0}
                onRequestSort={() => {}}
                onSelectAllClick={() => {}}
              />
              <TableBody>
                {imsis.map((imsi, idx) => (
                  <TableRow key={idx}>
                    {imsiHeaders.map((header) => (
                      <TableCell key={header.id}>
                        {header.id === 'lastUpdateDate' ||
                        header.id === 'lastSwapDate'
                          ? //@ts-ignore
                            formatTimestamp(imsi[header.id as keyof Imsi])
                          : imsi[header.id as keyof Imsi].toString()}
                      </TableCell>
                    ))}
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </Box>
      </Drawer>
    </>
  )
}
