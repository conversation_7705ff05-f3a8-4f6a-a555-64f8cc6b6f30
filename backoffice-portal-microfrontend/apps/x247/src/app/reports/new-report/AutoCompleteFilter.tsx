import { useState, useEffect } from 'react'
import TextField from '@mui/material/TextField'
import Autocomplete from '@mui/material/Autocomplete'
import CircularProgress from '@mui/material/CircularProgress'
import Box from '@mui/material/Box'
import { IReportFilterOption } from '@/app/reports/new-report/filters'
import { KeyboardArrowDown } from '@mui/icons-material'

interface ApiAutocompleteProps {
  label?: string
  placeholder?: string
  options: IReportFilterOption[]
  loading?: boolean
  onSelect?: (filterName: string, filter: string) => void
  onInputChange?: (value: string) => void
  minChars?: number
  value?: string | null
}

export default function ApiAutocomplete({
  label = 'Search',
  placeholder = 'Start typing to search...',
  options,
  loading = false,
  onSelect = () => {},
  onInputChange,
  minChars = 2,
  value = null,
}: ApiAutocompleteProps) {
  const [open, setOpen] = useState(false)
  const [inputValue, setInputValue] = useState('')
  const [internalValue, setInternalValue] = useState<string | null>(value)

  const handleInputChange = (value: string) => {
    if (value && value.length > minChars) {
      setInputValue(value)
      if (onInputChange) {
        onInputChange(value)
      }
    }
    setInputValue(value)
  }
  // Update internal value when external value changes
  useEffect(() => {
    setInternalValue(value)
  }, [value])
  return (
    <Autocomplete
      id="api-autocomplete"
      open={open}
      onOpen={() => setOpen(true)}
      onClose={() => setOpen(false)}
      sx={{ width: '15vw' }}
      size="small"
      slotProps={{}}
      filterOptions={(x) => x}
      options={options}
      loading={loading}
      value={options.find((opt) => opt.value === internalValue) || null}
      onChange={(event, newValue) => {
        setInternalValue(newValue?.value || null)
        if (onSelect) {
          onSelect(label, newValue?.value || '')
        }
      }}
      onInputChange={(event, newInputValue) => {
        handleInputChange(newInputValue)
      }}
      popupIcon={<KeyboardArrowDown />}
      isOptionEqualToValue={(option, value) => option.value === value.value}
      getOptionLabel={(option) => option.label}
      renderInput={(params) => (
        <TextField
          {...params}
          label={label}
          placeholder={placeholder}
          slotProps={{
            input: {
              ...params.InputProps,
              endAdornment: (
                <>
                  {loading ? (
                    <CircularProgress color="inherit" size={20} />
                  ) : null}
                  {params.InputProps.endAdornment}
                </>
              ),
            },
          }}
        />
      )}
      renderOption={(props, option) => (
        <Box component="li" {...props} key={option.key}>
          {option.label}
        </Box>
      )}
      noOptionsText={
        inputValue.length < minChars
          ? `Type at least ${minChars} characters to search`
          : 'No options found'
      }
    />
  )
}
