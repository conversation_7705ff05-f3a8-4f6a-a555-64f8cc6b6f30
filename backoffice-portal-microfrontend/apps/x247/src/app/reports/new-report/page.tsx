'use client'
import { Stack } from '@mui/material'
import { NewReportFilters } from '@/app/reports/new-report/filters'
import { ReportsList } from '@/app/reports/new-report/List'
import { useAppSelector, useAppDispatch } from '@/store'
import { EmptyStateReports } from '@/app/reports/new-report/EmptyState'
import { useEffect, useState } from 'react'
import { LoadingListsSkeleton } from '@dtbx/ui/components'
import { clearReports } from '@/store/reducers'

export default function NewReportPage() {
  const { reportPaginationData, isLoadingReports, selectedReportFilters } =
    useAppSelector((state) => state.reports)
  const dispatch = useAppDispatch()
  const [selectedFilters, setSelectedFilters] = useState<
    Record<string, string | string[]>
  >(selectedReportFilters)
  const [selectedCategory, setSelectedCategory] = useState<string>('')

  /*************************Query params handlers***************************/
  const buildQueryParams = (page: number = 1, size: number = 10) => {
    const params = new URLSearchParams()

    params.append('page', String(page))
    params.append('size', String(size))

    Object.entries(selectedReportFilters).forEach(([key, value]) => {
      if (selectedReportFilters[key]) {
        if (Array.isArray(selectedReportFilters[key])) {
          if (selectedReportFilters[key].length > 0) {
            params.append('dateCreatedFrom', selectedReportFilters[key][0])
            params.append('dateCreatedTo', selectedReportFilters[key][1])
          }
        } else if (key === 'channel') {
          params.append('clientId', String(value))
        } else {
          params.append(key, String(value))
        }
      }
    })

    return `?${params.toString()}`
  }
  useEffect(() => {
    dispatch(clearReports())
  }, [])

  useEffect(() => {
    if (
      Object.keys(selectedFilters).length === 0 &&
      Object.keys(selectedReportFilters).length > 0
    ) {
      setSelectedFilters(selectedReportFilters)
    }
  }, [selectedFilters])
  return (
    <Stack sx={{ px: '1%', py: '2vh' }}>
      <NewReportFilters
        buildQueryParams={buildQueryParams}
        selectedFilters={selectedFilters}
        setSelectedFilters={setSelectedFilters}
        selectedCategory={selectedCategory}
        setSelectedCategory={setSelectedCategory}
      />
      {isLoadingReports ? (
        <LoadingListsSkeleton />
      ) : reportPaginationData?.totalElements > 0 ? (
        <ReportsList
          buildQueryParams={buildQueryParams}
          selectedCategory={selectedCategory}
        />
      ) : (
        <EmptyStateReports
          subtitle="No Records Found"
          description="The current query does not have any data. You can adjust the report filters or generate a report with the new filters"
        />
      )}
    </Stack>
  )
}
