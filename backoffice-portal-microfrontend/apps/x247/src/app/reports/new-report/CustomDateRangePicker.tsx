import React, { useEffect, useRef, useState } from 'react'
import { IReportFilter } from './filters'
import dayjs, { Dayjs } from 'dayjs'
import {
  DateCalendar,
  LocalizationProvider,
  PickersDay,
  DesktopDateTimePicker,
  PickersLayout,
} from '@mui/x-date-pickers'
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs'
import {
  Box,
  Button,
  ClickAwayListener,
  Paper,
  Popper,
  Stack,
  TextField,
  Typography,
} from '@mui/material'
import { sentenceCase } from 'tiny-case'

export const CustomDateRangePicker = ({
  filter,
  handleFilterChangeAction,
  selectedDates,
}: {
  filter: IReportFilter
  handleFilterChangeAction: (
    filterName: string,
    selectedOption: string[]
  ) => void
  selectedDates: string[]
}) => {
  const [dateRange, setDateRange] = useState<[Dayjs | null, Dayjs | null]>([
    selectedDates ? dayjs(selectedDates[0], 'DD/MM/YYYY HH:mm') : null,
    selectedDates ? dayjs(selectedDates[1], 'DD/MM/YYYY HH:mm') : null,
  ])
  const [tempDateRange, setTempDateRange] = useState<
    [Dayjs | null, Dayjs | null]
  >([null, null])
  const [isOpen, setIsOpen] = useState(false)
  const anchorRef = useRef<HTMLDivElement>(null)

  const handleDateChange = (date: Dayjs | null) => {
    if (!tempDateRange[0] || (tempDateRange[0] && tempDateRange[1])) {
      setTempDateRange([date, null])
    } else if (tempDateRange[0] && !tempDateRange[1]) {
      if (date && date.isBefore(tempDateRange[0])) {
        setTempDateRange([date, tempDateRange[0]])
      } else {
        setTempDateRange([tempDateRange[0], date])
      }
    } else {
      setTempDateRange([tempDateRange[0], date])
    }
  }

  const handleApply = () => {
    if (tempDateRange[0] && tempDateRange[1]) {
      setDateRange(
        tempDateRange[0].isBefore(tempDateRange[1])
          ? tempDateRange
          : [tempDateRange[1], tempDateRange[0]]
      )
      const dateRange = tempDateRange[0].isBefore(tempDateRange[1])
        ? [
            tempDateRange[0].format('YYYY-MM-DDTHH:mm:ss.SSS'),
            tempDateRange[1].format('YYYY-MM-DDTHH:mm:ss.SSS'),
          ]
        : [
            tempDateRange[1].format('YYYY-MM-DDTHH:mm:ss.SSS'),
            tempDateRange[0].format('YYYY-MM-DDTHH:mm:ss.SSS'),
          ]
      handleFilterChangeAction(filter.filterName, dateRange)
    }
    setIsOpen(false)
  }

  const handleCancel = () => {
    setTempDateRange(dateRange)
    setIsOpen(false)
  }

  const formatDateRange = (range: [Dayjs | null, Dayjs | null]) => {
    if (!range[0] || !range[1]) {
      return 'All'
    } else {
      return `From: ${range[0].format('MM/DD/YYYY')} - To:${range[1].format('MM/DD/YYYY')}`
    }
  }

  const isDateInRange = (date: Dayjs) => {
    if (tempDateRange[0] && tempDateRange[1]) {
      return (
        date.isSame(tempDateRange[0]) ||
        date.isSame(tempDateRange[1]) ||
        (date.isAfter(tempDateRange[0]) && date.isBefore(tempDateRange[1]))
      )
    }
    return false
  }
  const isInBetweenRange = (date: Dayjs) => {
    return date.isAfter(tempDateRange[0]) && date.isBefore(tempDateRange[1])
  }
  useEffect(() => {
    !selectedDates && setDateRange([null, null])
  }, [selectedDates])
  return (
    <Stack>
      <LocalizationProvider dateAdapter={AdapterDayjs}>
        <div ref={anchorRef}>
          <TextField
            label={sentenceCase(filter.filterName)}
            size="small"
            value={`${filter.filterName.charAt(0).toUpperCase() + filter.filterName.slice(1)} : ${formatDateRange(dateRange)}`}
            sx={{ minWidth: '27vw' }}
            onClick={() => setIsOpen(true)}
            slotProps={{
              input: {
                readOnly: true,
              },
            }}
          />
        </div>
        <Popper
          open={isOpen}
          anchorEl={anchorRef.current}
          placement="bottom-start"
        >
          <ClickAwayListener onClickAway={handleCancel}>
            <Paper elevation={8} sx={{ p: 2 }}>
              <Stack direction="row">
                <Typography>
                  {formatDateRange(tempDateRange) || 'N/A'}
                </Typography>
              </Stack>
              <DateCalendar
                value={tempDateRange[1] || tempDateRange[0]}
                showDaysOutsideCurrentMonth
                onChange={handleDateChange}
                slots={{
                  day: (props) => {
                    const { day } = props
                    const isSelected = isDateInRange(day)
                    return (
                      <PickersDay
                        disableMargin
                        sx={{
                          px:
                            tempDateRange[0] === day || tempDateRange[1] === day
                              ? 'inherit'
                              : 2.5,
                          color:
                            tempDateRange[0] === day
                              ? '#FFFFFF'
                              : tempDateRange[1] === day
                                ? '#FFFFFF'
                                : isSelected
                                  ? 'primary.main'
                                  : 'text.main',
                          '&:hover': {
                            backgroundColor:
                              tempDateRange[0] === day
                                ? 'text.primary'
                                : tempDateRange[1] === day
                                  ? 'text.primary'
                                  : isSelected
                                    ? '#F5F5F5'
                                    : '#FFFFFF',
                          },
                          backgroundColor:
                            tempDateRange[0] === day
                              ? 'text.primary'
                              : tempDateRange[1] === day
                                ? 'text.primary'
                                : isSelected
                                  ? '#F5F5F5'
                                  : '#FFFFFF',
                          borderTopLeftRadius:
                            isInBetweenRange(day) || tempDateRange[1] === day
                              ? 'inherit'
                              : '50%',
                          borderBottomLeftRadius:
                            isInBetweenRange(day) || tempDateRange[1] === day
                              ? 'inherit'
                              : '50%',
                          borderTopRightRadius:
                            isInBetweenRange(day) || tempDateRange[0] === day
                              ? 'inherit'
                              : '50%',
                          borderBottomRightRadius:
                            isInBetweenRange(day) || tempDateRange[0] === day
                              ? 'inherit'
                              : '50%',
                        }}
                        day={day}
                        onDaySelect={function (day: dayjs.Dayjs): void {
                          handleDateChange(day)
                        }}
                        outsideCurrentMonth={false}
                        isFirstVisibleCell={false}
                        isLastVisibleCell={false}
                      />
                    )
                  },
                }}
              />
              <Box sx={{ display: 'flex', justifyContent: 'flex-end', gap: 1 }}>
                <Button onClick={handleCancel} variant="outlined">
                  Cancel
                </Button>
                <Button
                  onClick={handleApply}
                  variant="contained"
                  disabled={!tempDateRange[0] || !tempDateRange[1]}
                >
                  Apply
                </Button>
              </Box>
            </Paper>
          </ClickAwayListener>
        </Popper>
      </LocalizationProvider>
    </Stack>
  )
}

export const CustomDateTimeRangePicker = ({
  filterName,
  handleDateTimeChange,
  selectedDateTimeRange,
}: {
  filterName: string
  handleDateTimeChange: (filterName: string, selectedOption: string[]) => void
  selectedDateTimeRange: string[]
}) => {
  //date from variables
  const [localDateTimeFrom, setLocalDateTimeFrom] = useState<Dayjs | null>(
    selectedDateTimeRange
      ? dayjs(selectedDateTimeRange[0], 'DD/MM/YYYY HH:mm')
      : null
  )
  const [isFromOpen, setIsFromOpen] = useState(false)

  //date to variables
  const [localDateTimeTo, setLocalDateTimeTo] = useState<Dayjs | null>(
    selectedDateTimeRange
      ? dayjs(selectedDateTimeRange[1], 'DD/MM/YYYY HH:mm')
      : null
  )
  const [isToOpen, setIsToOpen] = useState(false)

  const handleLocalDateTimeChange = (date: Dayjs, type: string) => {
    if (type === 'from') {
      setLocalDateTimeFrom(date)
    } else {
      setLocalDateTimeTo(date)
    }
    if (type === 'to' && date.isBefore(localDateTimeFrom)) {
      setLocalDateTimeTo(localDateTimeFrom)
      setLocalDateTimeFrom(date)
    }
  }

  const handleApply = (type?: string) => {
    if (type === 'from') {
      setIsFromOpen(false)
    } else if (type === 'to') {
      setIsToOpen(false)
    }
    if (!localDateTimeFrom || !localDateTimeTo) return
    const range = [
      localDateTimeFrom?.format('YYYY-MM-DDTHH:mm:ss.SSS') || '',
      localDateTimeTo?.format('YYYY-MM-DDTHH:mm:ss.SSS') || '',
    ]

    handleDateTimeChange(filterName, range)
  }

  const handleCancel = () => {
    setLocalDateTimeFrom(
      selectedDateTimeRange
        ? dayjs(selectedDateTimeRange[0], 'DD/MM/YYYY HH:mm')
        : null
    )
    setLocalDateTimeTo(
      selectedDateTimeRange
        ? dayjs(selectedDateTimeRange[1], 'DD/MM/YYYY HH:mm')
        : null
    )

    setIsFromOpen(false)
    setIsToOpen(false)
  }

  useEffect(() => {
    !selectedDateTimeRange && setLocalDateTimeFrom(null)
    !selectedDateTimeRange && setLocalDateTimeTo(null)
  }, [selectedDateTimeRange])
  return (
    <Stack direction="row" gap={3}>
      <LocalizationProvider dateAdapter={AdapterDayjs}>
        <DesktopDateTimePicker
          label="Date From"
          open={isFromOpen}
          slotProps={{
            textField: {
              sx: {
                '& .MuiInputBase-root': {
                  height: '40px',
                },
              },
            },
          }}
          onOpen={() => setIsFromOpen(true)}
          formatDensity="dense"
          views={['year', 'month', 'day', 'hours', 'minutes']}
          ampm={false}
          onChange={(newDate) =>
            handleLocalDateTimeChange(newDate as Dayjs, 'from')
          }
          value={localDateTimeFrom}
          showDaysOutsideCurrentMonth
          slots={{
            layout: (props) => {
              return (
                <PickersLayout
                  sx={{
                    display: 'flex',
                    flexDirection: 'column',
                    '.MuiPickersLayout-contentWrapper': {
                      display: 'flex',
                      flexDirection: 'row',
                    },
                    '.MuiMultiSectionDigitalClock-root': {
                      width: '5vw',
                      height: '260px',
                    },
                    '.MuiPickersCalendarHeader-root': {
                      marginTop: 0,
                      marginBottom: 0,
                    },
                    '.MuiMultiSectionDigitalClockSection-item': {
                      padding: '0px',
                      paddingTop: '3px',
                      paddingBottom: '2px',
                      margin: '0px',
                      width: 'auto',
                      fontWeight: 400,
                      fontSize: '0.75rem',
                    },
                    '.MuiDateCalendar-root': {
                      width: 'auto',
                      height: '260px',
                    },
                    '.MuiDialogActions-root': {
                      padding: 0,
                    },
                    '.MuiButton-root': {
                      lineHeight: '20px',
                      fontWeight: 550,
                      padding: '8px 8px',
                    },
                  }}
                  isRtl={false}
                  views={[]}
                  onChange={(newValue) =>
                    handleLocalDateTimeChange(newValue as Dayjs, 'from')
                  }
                  onViewChange={() => {}}
                  view={null}
                  onAccept={() => handleApply('from')}
                  onClear={() => {}}
                  onCancel={handleCancel}
                  onSetToday={handleApply}
                  isLandscape={false}
                  wrapperVariant={null}
                  isValid={(value) => true}
                  onSelectShortcut={() => {}}
                  onDismiss={() => {}}
                  onOpen={() => setIsFromOpen(true)}
                  onClose={() => setIsFromOpen(false)}
                >
                  {props.children}
                </PickersLayout>
              )
            },
          }}
        />
      </LocalizationProvider>
      <LocalizationProvider dateAdapter={AdapterDayjs}>
        <DesktopDateTimePicker
          label="Date To"
          open={isToOpen}
          onOpen={() => setIsToOpen(true)}
          formatDensity="dense"
          ampm={false}
          slotProps={{
            textField: {
              sx: {
                '& .MuiInputBase-root': {
                  height: '40px',
                },
              },
            },
          }}
          views={['year', 'month', 'day', 'hours', 'minutes']}
          onChange={(newDate) =>
            handleLocalDateTimeChange(newDate as Dayjs, 'to')
          }
          value={localDateTimeTo}
          showDaysOutsideCurrentMonth
          slots={{
            layout: (props) => {
              return (
                <PickersLayout
                  sx={{
                    display: 'flex',
                    flexDirection: 'column',
                    '.MuiPickersLayout-contentWrapper': {
                      display: 'flex',
                      flexDirection: 'row',
                    },
                    '.MuiMultiSectionDigitalClock-root': {
                      width: '5vw',
                      height: '260px',
                    },
                    '.MuiPickersCalendarHeader-root': {
                      marginTop: 0,
                      marginBottom: 0,
                    },
                    '.MuiMultiSectionDigitalClockSection-item': {
                      padding: '0px',
                      paddingTop: '3px',
                      paddingBottom: '2px',
                      margin: '0px',
                      width: 'auto',
                      fontWeight: 400,
                      fontSize: '0.75rem',
                    },
                    '.MuiDateCalendar-root': {
                      width: 'auto',
                      height: '260px',
                    },
                    '.MuiDialogActions-root': {
                      padding: 0,
                    },
                    '.MuiButton-root': {
                      lineHeight: '20px',
                      fontWeight: 550,
                      padding: '8px 8px',
                    },
                  }}
                  isRtl={false}
                  views={[]}
                  onChange={(newValue) =>
                    handleLocalDateTimeChange(newValue as Dayjs, 'to')
                  }
                  onViewChange={() => {}}
                  view={null}
                  onAccept={() => handleApply('to')}
                  onClear={() => {}}
                  onCancel={handleCancel}
                  onSetToday={handleApply}
                  isLandscape={false}
                  wrapperVariant={null}
                  isValid={(value) => true}
                  onSelectShortcut={() => {}}
                  onDismiss={() => {}}
                  onOpen={() => setIsToOpen(true)}
                  onClose={() => setIsToOpen(false)}
                >
                  {props.children}
                </PickersLayout>
              )
            },
          }}
        />
      </LocalizationProvider>
    </Stack>
  )
}
