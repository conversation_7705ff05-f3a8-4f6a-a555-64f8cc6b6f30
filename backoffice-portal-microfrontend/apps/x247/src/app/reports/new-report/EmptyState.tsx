import { Box, Stack, Typography } from '@mui/material'
import Image from 'next/image'
import React from 'react'

export const EmptyStateReports = ({
  subtitle,
  description,
}: {
  subtitle?: string
  description?: string
}) => {
  return (
    <Stack
      sx={{
        width: '100%',
        justifyContent: 'center',
        alignItems: 'center',
      }}
    >
      {' '}
      <Stack
        sx={{
          width: '884px',
          height: '479px',
          backgroundImage: 'url("/dashboard/background-pattern.svg")',
          backgroundRepeat: 'no-repeat',
          backgroundSize: '480px 480px',
          backgroundPosition: 'center',
          backgroundPositionX: '',
          flexShrink: 0,
          justifyContent: 'center',
          alignItems: 'center',
        }}
      >
        <Stack
          sx={{
            flexDirection: 'column',
            alignItems: 'center',
            flex: '1 0 0',
            gap: '20vh',
            paddingTop: '17vh',
          }}
        >
          {/* text box */}
          <Stack
            sx={{
              flexDirection: 'column',
              alignItems: 'center',
              alignSelf: 'stretch',
            }}
          >
            <Stack
              sx={{
                width: '56px',
                height: '56px',
                borderRadius: '12px',
                padding: '14px',
                justifyContent: 'center',
                alignItems: 'center',
                border: '1px solid #EAECF0',
              }}
            >
              <Image
                src={'/dashboard/icons/search-lg-2-x.svg'}
                alt="search"
                width={28}
                height={28}
              />
            </Stack>
            <Stack
              sx={{
                flexDirection: 'column',
                justifyContent: 'center',
                alignItems: 'center',
                alignContent: 'center',
                gap: '20px',
                pt: '6vh',
              }}
            >
              <Typography
                variant="h6"
                sx={{
                  textAlign: 'center',
                  width: '573px',
                }}
              >
                {subtitle || `No saved filters`}
              </Typography>
              <Typography
                variant="body1"
                sx={{
                  textWrap: 'wrap',
                  textAlign: 'center',
                  width: '20vw',
                }}
              >
                {description ||
                  'Once you save a set of filters for a report, it will appear here for quick access. You can adjust the report filters or generate a report with the existing filters.'}
              </Typography>
            </Stack>
          </Stack>
        </Stack>
      </Stack>
    </Stack>
  )
}
