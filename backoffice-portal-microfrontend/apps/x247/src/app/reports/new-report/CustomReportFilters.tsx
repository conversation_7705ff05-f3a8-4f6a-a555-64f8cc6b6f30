'use client'
import { DropdownMenu } from '@/app/approval-requests/CustomFilterBox'
import React from 'react'
import { IReportFilter } from './filters'
import { CustomDateTimeRangePicker } from './CustomDateRangePicker'
import ApiAutocomplete from '@/app/reports/new-report/AutoCompleteFilter'
import { getTransactionChannels } from '@/store/actions'
import { useAppDispatch } from '@/store'
import { setSelectedReportFilters } from '@/store/reducers'

function CustomReportFilters({
  filters,
  selectedFilters,
  setSelectedFilters,
}: {
  filters: IReportFilter[] | undefined
  selectedFilters: Record<string, string | string[]>
  setSelectedFilters: (filters: Record<string, string | string[]>) => void
}) {
  const dispatch = useAppDispatch()
  const handleFilterChange = (
    filterName: string,
    selectedOption: string | string[]
  ) => {
    const newFilters = {
      ...selectedFilters,
      [filterName]: selectedOption,
    }
    setSelectedFilters(newFilters)
    dispatch(setSelectedReportFilters(newFilters))
  }
  return (
    <>
      {filters &&
        filters.map((filter) => {
          if (filter.type === 'dropdown/single') {
            return (
              <DropdownMenu
                key={filter.filterName}
                filter={filter}
                size={'small'}
                onSelected={(str: string) => {
                  handleFilterChange(filter.filterName, str)
                }}
                selectedFilterValue={
                  selectedFilters[filter.filterName] as string
                }
              />
            )
          }
          if (filter.type === 'datetime') {
            return (
              <CustomDateTimeRangePicker
                key={filter.filterName}
                filterName={filter.filterName}
                handleDateTimeChange={handleFilterChange}
                selectedDateTimeRange={
                  selectedFilters[filter.filterName] as string[]
                }
              />
            )
          }
          if (
            filter.type === 'autocomplete' &&
            filter.filterName === 'channel'
          ) {
            return (
              <ApiAutocomplete
                label={filter.filterName}
                onSelect={handleFilterChange}
                onInputChange={(value: string) =>
                  getTransactionChannels(dispatch, value)
                }
                value={selectedFilters[filter.filterName] as string}
                options={filter.options}
              />
            )
          }
        })}
    </>
  )
}

export default CustomReportFilters
