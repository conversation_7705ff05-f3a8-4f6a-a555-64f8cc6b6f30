import {
  <PERSON><PERSON>,
  ClickAwayListener,
  <PERSON>row,
  IconButton,
  InputAdornment,
  List,
  ListItem,
  ListItemText,
  <PERSON><PERSON>,
  Stack,
  TextField,
  Tooltip,
} from '@mui/material'
import {
  BookmarkBorderOutlined,
  Clear,
  KeyboardArrowDown,
} from '@mui/icons-material'
import React, { useEffect, useRef, useState } from 'react'
import {
  exportReportByType,
  getCustomerReports,
  getReports,
  getTransactionChannels,
  postReportFilters,
} from '@/store/actions/reports'
import { useAppDispatch, useAppSelector } from '@/store'
import {
  setSelectedReportFilters,
  setSelectedReportType,
} from '@/store/reducers'
import { CustomerReportTypes, TransactionReportTypes } from '@/store/interfaces'
import { LoadingButton } from '@dtbx/ui/components'
import CustomReportFilters from './CustomReportFilters'
import { reportCategories } from '@/app/reports/new-report/reportOptions'

export type FilterType =
  | 'select'
  | 'dropdown/checkbox'
  | 'dropdown/single'
  | 'date'
  | 'datetime'
  | 'autocomplete'

export interface IReportFilter {
  filterName: string
  options: IReportFilterOption[]
  type: FilterType
  apiAction?: (query: string) => void
}
export interface IReportFilterOption {
  label: string
  value: string
  key: string
}
export interface IReportCategory {
  title: string
  category: string
  icon: React.ReactNode
  items: IReportItem[]
}
export interface IReportItem {
  title: string
  category: string
  key: string
  filters: IReportFilter[]
}

export const NewReportFilters = ({
  buildQueryParams,
  selectedFilters,
  setSelectedFilters,
  selectedCategory,
  setSelectedCategory,
}: {
  buildQueryParams: (page?: number, size?: number) => string
  selectedFilters: Record<string, string | string[]>
  setSelectedFilters: React.Dispatch<
    React.SetStateAction<Record<string, string | string[]>>
  >
  selectedCategory: string
  setSelectedCategory: React.Dispatch<React.SetStateAction<string>>
}) => {
  const [selectedReport, setSelectedReport] = useState<string>('')
  const [firstGeneration, setFirstGeneration] = useState<boolean>(true)
  const [isSearchOpen, setIsSearchOpen] = useState<boolean>(true)
  const [loadedFilters, setLoadedFilters] = useState<IReportFilter[]>()
  const searchRef = useRef<HTMLDivElement>(null)
  const [reportOptions, setReportOptions] = useState(reportCategories)
  const [reportTypeOptions, setReportTypeOptions] = useState<IReportItem[]>([])
  const dispatch = useAppDispatch()
  const {
    selectedReportType,
    isLoadingReports,
    isLoadingExportReport,
    clientReportChannels,
  } = useAppSelector((state) => state.reports)
  const handleTextFieldChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const searchValue = e.target.value
    if (!searchValue) {
      return handleClearClick(true)
    }
    setSelectedReport(searchValue)
    setReportOptions(
      reportCategories.filter((category) =>
        category.title.toLowerCase().includes(searchValue.toLowerCase())
      )
    )
    const selectedTypes = reportCategories.map((category) =>
      category.items.filter((item) =>
        item.title.toLowerCase().includes(searchValue.toLowerCase())
      )
    )
    setReportTypeOptions(selectedTypes?.find((type) => type.length > 0) || [])
  }

  const handleGenerate = async () => {
    if (firstGeneration) setFirstGeneration(false)

    const filters = reportCategories
      .filter((cat) => cat.category === selectedCategory)[0]
      ?.items.find((item) => item.key === selectedReportType)?.filters

    setLoadedFilters(filters)

    const params = buildQueryParams()

    if (selectedCategory === 'Transaction Reports') {
      await getReports(
        dispatch,
        selectedReportType as TransactionReportTypes,
        params
      )
    }

    if (selectedCategory === 'Customer Details Reports') {
      await getCustomerReports(
        dispatch,
        selectedReportType as CustomerReportTypes,
        params
      )
    }
  }
  const handleExport = async () => {
    const params = buildQueryParams()
    await exportReportByType(dispatch, selectedReportType, params)
  }
  const handleClickAway = () => {
    setIsSearchOpen(false)
    setReportOptions(reportCategories)
  }
  const handleSelectReportType = (item: {
    title: string
    key: string
    category: string
  }) => {
    setSelectedReport(item.title)
    if (!selectedCategory) setSelectedCategory(item.category)
    dispatch(
      setSelectedReportType(
        item.key as TransactionReportTypes | CustomerReportTypes
      )
    )
    setSelectedFilters({})
    setLoadedFilters(
      reportCategories
        .filter((cat) => cat.category === item.category)[0]
        ?.items.find((type) => type.key === item.key)?.filters
    )
    handleClickAway()
  }
  const handleClearClick = (open?: boolean) => {
    setSelectedReport('')
    setSelectedCategory('')
    setSelectedFilters({})
    setReportTypeOptions([])
    setIsSearchOpen(open || false)
  }
  useEffect(() => {
    const params = buildQueryParams()
    if (selectedCategory === 'Transaction Reports') {
      getReports(dispatch, selectedReportType as TransactionReportTypes, params)
    }

    if (selectedCategory === 'Customer Details Reports') {
      getCustomerReports(
        dispatch,
        selectedReportType as CustomerReportTypes,
        params
      )
    }
  }, [selectedFilters])
  useEffect(() => {
    if (selectedCategory === 'Transaction Reports') {
      getTransactionChannels(dispatch, '')
    }
  }, [selectedReportType])
  useEffect(() => {
    setLoadedFilters((prevFilters) => {
      if (!prevFilters) return prevFilters

      return prevFilters.map((filter) => {
        if (filter.filterName === 'channel') {
          return {
            ...filter,
            options: clientReportChannels.map((channel) => ({
              label: channel.clientName,
              value: channel.clientId,
              key: channel.clientId,
            })),
          }
        }
        return filter
      })
    })
  }, [clientReportChannels])
  return (
    <Stack sx={{ my: '1vh' }}>
      <Stack direction="row" gap={'4px'}>
        <ClickAwayListener onClickAway={handleClickAway}>
          <Stack ref={searchRef} sx={{ width: '55vw' }}>
            <TextField
              fullWidth
              size="small"
              placeholder="Type to search or click to select a report"
              variant="outlined"
              sx={{ mb: 3 }}
              value={selectedReport}
              onChange={handleTextFieldChange}
              onClick={() => setIsSearchOpen(true)}
              slotProps={{
                input: {
                  endAdornment: (
                    <InputAdornment position="end">
                      {selectedReport && (
                        <IconButton
                          aria-label="clear search"
                          onClick={() => handleClearClick(false)}
                          edge="end"
                          size="small"
                        >
                          <Clear />
                        </IconButton>
                      )}
                      <KeyboardArrowDown
                        sx={{
                          transform: isSearchOpen ? 'rotate(180deg)' : 'none',
                          transition: 'transform 0.2s',
                        }}
                      />
                    </InputAdornment>
                  ),
                },
              }}
            />
            <Popper
              open={isSearchOpen}
              anchorEl={searchRef.current}
              placement="bottom-start"
              transition
              style={{ width: searchRef.current?.clientWidth, zIndex: 1000 }}
            >
              {({ TransitionProps }) => (
                <Grow {...TransitionProps}>
                  <Stack
                    sx={{
                      overflowY: 'auto',
                      width: '100%',
                      flexDirection: 'row',
                      border: '1px solid #E3E4E4',
                      background: '#FFFFFF',
                      borderRadius: '4px',
                      boxShadow:
                        '0px 4px 8px -2px var(--Colors-Effects-Shadows-shadow-md_01, rgba(16, 24, 40, 0.10)), 0px 2px 4px -2px var(--Colors-Effects-Shadows-shadow-md_02, rgba(16, 24, 40, 0.06))',
                    }}
                  >
                    {reportTypeOptions.length > 0 ? (
                      <List
                        sx={{
                          width: selectedCategory ? '20%' : '100%',
                          borderRight: selectedCategory
                            ? '1px solid #E3E4E4'
                            : 'none',
                        }}
                      >
                        {reportTypeOptions.map((item) => (
                          <ListItem
                            key={item.title}
                            component="ul"
                            onClick={() => {
                              handleSelectReportType(item)
                            }}
                            sx={{
                              border: '1px solid #E3E4E4',
                              borderRadius: '4px',
                              cursor: 'pointer',
                              mb: 1,
                              background:
                                selectedReport === item.title
                                  ? '#F0F3F3'
                                  : 'none',
                            }}
                          >
                            <ListItemText
                              primary={item.title}
                              slotProps={{
                                primary: {
                                  fontSize: '0.9rem',
                                },
                              }}
                            />
                          </ListItem>
                        ))}
                      </List>
                    ) : (
                      <List
                        sx={{
                          width: selectedCategory ? '20%' : '100%',
                          borderRight: selectedCategory
                            ? '1px solid #E3E4E4'
                            : 'none',
                        }}
                      >
                        {reportOptions.map((cat) => (
                          <ListItem
                            key={cat.category}
                            component="ul"
                            onClick={() => {
                              setSelectedCategory(cat.category)
                            }}
                            sx={{
                              py: 1,
                              cursor: 'pointer',
                              background:
                                selectedCategory === cat.category
                                  ? '#F0F3F3'
                                  : 'none',
                            }}
                          >
                            <ListItemText
                              primary={cat.category}
                              slotProps={{
                                primary: {
                                  fontSize: '0.9rem',
                                },
                              }}
                            />
                          </ListItem>
                        ))}
                      </List>
                    )}
                    {selectedCategory !== '' && (
                      <List sx={{ width: '80%', p: '1vw' }}>
                        {reportCategories
                          .find((cat) => cat.category === selectedCategory)
                          ?.items.map((item) => (
                            <ListItem
                              key={item.title}
                              component="ul"
                              onClick={() => {
                                handleSelectReportType(item)
                              }}
                              sx={{
                                border: '1px solid #E3E4E4',
                                borderRadius: '4px',
                                cursor: 'pointer',
                                mb: 1,
                                background:
                                  selectedReport === item.title
                                    ? '#F0F3F3'
                                    : 'none',
                              }}
                            >
                              <ListItemText
                                primary={item.title}
                                slotProps={{
                                  primary: {
                                    fontSize: '0.9rem',
                                  },
                                }}
                              />
                            </ListItem>
                          ))}
                      </List>
                    )}
                  </Stack>
                </Grow>
              )}
            </Popper>
          </Stack>
        </ClickAwayListener>
        {isLoadingReports ? (
          <LoadingButton width={'10vw'} />
        ) : (
          <Button
            variant="contained"
            disabled={!selectedReport}
            onClick={handleGenerate}
            sx={{ textWrap: 'nowrap' }}
          >
            {firstGeneration ? 'Generate Report' : 'Update Report'}
          </Button>
        )}
        {isLoadingExportReport ? (
          <LoadingButton width={'10vw'} />
        ) : (
          <Button
            variant="outlined"
            disabled={!selectedReport}
            onClick={handleExport}
          >
            Export
          </Button>
        )}
      </Stack>
      {/*filters open conditionally on select of report*/}
      {selectedReport && (
        <FilterBox
          reportName={selectedReportType}
          loadedFilters={loadedFilters}
          selectedFilters={selectedFilters}
          setSelectedFilters={setSelectedFilters}
        />
      )}
    </Stack>
  )
}

const FilterBox = ({
  reportName,
  loadedFilters,
  selectedFilters,
  setSelectedFilters,
}: {
  reportName: string
  loadedFilters: IReportFilter[] | undefined
  selectedFilters: Record<string, string | string[]>
  setSelectedFilters: React.Dispatch<
    React.SetStateAction<Record<string, string | string[]>>
  >
}) => {
  const { isLoadingSaveReport } = useAppSelector((state) => state.reports)
  const dispatch = useAppDispatch()

  const handleClickSave = async () => {
    let filters: Record<string, string | string[]>[] = []

    Object.entries(selectedFilters).forEach(([key, value]) => {
      if (key === 'date' || key === 'timestamp') {
        filters.push({ dateCreatedFrom: value[0] })
        filters.push({ dateCreatedTo: value[1] })
      } else {
        filters.push({ [key]: value })
      }
    })

    const payload = {
      reportName,
      reportFilters: filters,
    }
    await postReportFilters(dispatch, payload)
  }

  const handleClickClear = () => {
    setSelectedFilters((prevFilters) =>
      Object.fromEntries(Object.keys(prevFilters).map((key) => [key, '']))
    )
    dispatch(setSelectedReportFilters({}))
  }

  return (
    <Stack direction="row" gap="8px">
      <Tooltip title="Save this group of filters for quick access in the future">
        <IconButton
          onClick={handleClickSave}
          loading={isLoadingSaveReport}
          sx={{
            border: '1px solid #AAADB0',
            borderRadius: '4px',
            width: '50px',
          }}
        >
          <BookmarkBorderOutlined />
        </IconButton>
      </Tooltip>

      <Tooltip title="Clear all filters">
        <IconButton
          onClick={handleClickClear}
          sx={{
            border: '1px solid #AAADB0',
            borderRadius: '4px',
            width: '50px',
          }}
        >
          <Clear />
        </IconButton>
      </Tooltip>

      <CustomReportFilters
        filters={loadedFilters}
        selectedFilters={selectedFilters}
        setSelectedFilters={setSelectedFilters}
      />
    </Stack>
  )
}
