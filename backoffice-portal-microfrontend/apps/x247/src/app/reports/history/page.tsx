'use client'
import { InputAdornment, Stack, Typography } from '@mui/material'
import { CustomSearchInput } from '@dtbx/ui/components/Input'
import SearchOutlinedIcon from '@mui/icons-material/SearchOutlined'
import React, { useEffect, useState } from 'react'
import { ReportsHistoryList } from '@/app/reports/history/List'
import { getReportsHistory } from '@/store/actions/reports'
import { useAppDispatch, useAppSelector } from '@/store'
import { CustomSkeleton } from '@dtbx/ui/components'
import { EmptyStateReports } from '../new-report/EmptyState'
import { clearReports } from '@/store/reducers'

export default function ReportGenerationHistoryPage() {
  const [searchValue, setSearchValue] = useState<string>('')
  const {
    isLoadingReportsHistoryList,
    isSearchingReportsHistory,
    reportHistoryPaginationData,
  } = useAppSelector((state) => state.reports)
  const dispatch = useAppDispatch()
  const handleSearch = async (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchValue(e.target.value)
    await getReportsHistory(dispatch, e.target.value)
  }

  useEffect(() => {
    dispatch(clearReports())
    getReportsHistory(dispatch)
  }, [])
  return (
    <Stack sx={{ p: '2%', gap: '2vh' }}>
      {isLoadingReportsHistoryList ? (
        <>
          <Stack>
            <CustomSkeleton animation="wave" variant="rectangular" width="100%">
              <CustomSearchInput />
            </CustomSkeleton>
          </Stack>

          <CustomSkeleton animation="wave" variant="rectangular" width="100%">
            <ReportsHistoryList searchValue={searchValue} />
          </CustomSkeleton>
        </>
      ) : isSearchingReportsHistory ? (
        <>
          <Stack>
            <CustomSearchInput
              sx={{ width: '100%' }}
              value={searchValue}
              onChange={handleSearch}
              placeholder={'Search by Report Type'}
              endAdornment={
                <InputAdornment position="end">
                  <SearchOutlinedIcon sx={{ color: 'text.disabled' }} />
                </InputAdornment>
              }
            />
          </Stack>

          <CustomSkeleton animation="wave" variant="rectangular" width="100%">
            <ReportsHistoryList searchValue={searchValue} />
          </CustomSkeleton>
        </>
      ) : reportHistoryPaginationData?.totalElements > 0 ? (
        <>
          <Stack>
            <CustomSearchInput
              sx={{ width: '100%' }}
              value={searchValue}
              onChange={handleSearch}
              placeholder={'Search by Report Type'}
              endAdornment={
                <InputAdornment position="end">
                  <SearchOutlinedIcon sx={{ color: 'text.disabled' }} />
                </InputAdornment>
              }
            />
          </Stack>

          <ReportsHistoryList searchValue={searchValue} />
        </>
      ) : (
        <EmptyStateReports />
      )}
    </Stack>
  )
}
