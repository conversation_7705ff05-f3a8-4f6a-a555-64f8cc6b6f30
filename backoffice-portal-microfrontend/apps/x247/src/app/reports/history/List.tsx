'use client'
import {
  Button,
  <PERSON>,
  Stack,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableRow,
  Typography,
} from '@mui/material'
import { useAppDispatch, useAppSelector } from '@/store'
import {
  CustomPagination,
  CustomTableHeader,
  PaginationOptions,
} from '@dtbx/ui/components/Table'
import React, { useState } from 'react'
import { formatTimestamp } from '@dtbx/store/utils'
import { exportReportByType, getReportsHistory } from '@/store/actions/reports'
import { IReportFilters, TransactionReportTypes } from '@/store/interfaces'
import { IReportFilter } from '@/app/reports/new-report/filters'
import { isObject } from 'lodash'
const HeaderRow = [
  { id: 'reportName', label: 'Report', alignRight: false },
  { id: 'timestamp', label: 'Report Created On', alignRight: false },
  { id: 'userName', label: 'Generated By', alignRight: false },
  { id: 'filters', label: 'Filters', alignRight: false },
  { id: '', label: 'Actions', alignRight: false },
]
export const ReportsHistoryList = ({
  searchValue,
}: {
  searchValue: string
}) => {
  const [selected, setSelected] = useState<readonly string[]>([])
  const dispatch = useAppDispatch()
  const { reportHistoryList, reportHistoryPaginationData } = useAppSelector(
    (state) => state.reports
  )
  const [paginationOptions, setPaginationOptions] = useState({
    page: reportHistoryPaginationData.pageNumber,
    size: reportHistoryPaginationData.pageSize,
    totalPages: reportHistoryPaginationData.totalNumberOfPages,
  })

  /*************************start pagination handlers***************************/
  const handlePagination = async (newOptions: PaginationOptions) => {
    setPaginationOptions(newOptions) // Update parent state
    await getReportsHistory(
      dispatch,
      searchValue,
      newOptions.page,
      newOptions.size
    )
  }
  const handleSelectAll = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.checked) {
      const newSelected = reportHistoryList.map((n) => n.id)
      setSelected(newSelected)
      return
    }
    setSelected([])
  }
  const handleSelectOne = (event: React.MouseEvent<unknown>, id: string) => {
    const selectedIndex = selected.indexOf(id)
    let newSelected: readonly string[] = []

    if (selectedIndex === -1) {
      newSelected = newSelected.concat(selected, id)
    } else if (selectedIndex === 0) {
      newSelected = newSelected.concat(selected.slice(1))
    } else if (selectedIndex === selected.length - 1) {
      newSelected = newSelected.concat(selected.slice(0, -1))
    } else if (selectedIndex > 0) {
      newSelected = newSelected.concat(
        selected.slice(0, selectedIndex),
        selected.slice(selectedIndex + 1)
      )
    }
    setSelected(newSelected)
  }

  const buildQueryParams = (
    reportFilters: Record<string, string>,
    page: number = 1,
    size: number = 10
  ) => {
    const params = new URLSearchParams()

    params.append('page', String(page))
    params.append('size', String(size))

    Object.entries(reportFilters).forEach(([key, value]) => {
      if (reportFilters[key]) {
        params.append(key, String(value))
      }
    })

    return `?${params.toString()}`
  }

  const handleExport = async (
    reportName: TransactionReportTypes,
    reportFilters: Record<string, string>
  ) => {
    const params = buildQueryParams(reportFilters)
    await exportReportByType(dispatch, reportName, params)
  }

  return (
    <Stack>
      <Paper
        sx={{
          width: '100%',
          overflow: 'hidden',
          borderRadius: '4px',
          border: '1px solid #EAECF0',
          background: '#FEFEFE',
          boxShadow:
            '0px 1px 2px 0px rgba(16, 24, 40, 0.06), 0px 1px 3px 0px rgba(16, 24, 40, 0.10)',
        }}
      >
        <Stack sx={{ px: '1vw' }}>
          <Typography variant="body2">Selected Saved Reports</Typography>
          <Typography variant="label2">
            Showing {reportHistoryList?.length} of{' '}
            {reportHistoryPaginationData.totalElements} records
          </Typography>
        </Stack>
        <TableContainer
          component={Paper}
          sx={{
            boxShadow: 'none',
          }}
        >
          <Table
            sx={{ minWidth: 650 }}
            aria-label="designations table"
            size="small"
          >
            <CustomTableHeader
              order={'asc'}
              orderBy={'id'}
              headLabel={HeaderRow}
              showCheckbox={false}
              rowCount={reportHistoryList?.length}
              numSelected={selected.length}
              onRequestSort={() => {}}
              onSelectAllClick={handleSelectAll}
            />
            <TableBody>
              {reportHistoryList &&
                reportHistoryList.map((row) => {
                  const {
                    id,
                    userName,
                    reportName,
                    dateCreated,
                    reportFilters,
                  } = row
                  const isItemSelected = selected.indexOf(id) !== -1
                  return (
                    <TableRow
                      hover
                      key={id}
                      tabIndex={-1}
                      role="checkbox"
                      onClick={(event) => handleSelectOne(event, row.id)}
                      selected={isItemSelected}
                      aria-checked={isItemSelected}
                    >
                      <TableCell>{reportName}</TableCell>
                      <TableCell>{formatTimestamp(dateCreated)}</TableCell>
                      <TableCell>{userName}</TableCell>
                      <TableCell>
                        <FiltersCell filter={reportFilters} />
                      </TableCell>
                      <TableCell>
                        <Button
                          variant="outlined"
                          onClick={() =>
                            handleExport(
                              reportName as TransactionReportTypes,
                              reportFilters as Record<string, string>
                            )
                          }
                          sx={{
                            border: '1px solid #AAADB0',
                            fontSize: '12px',
                            padding: '5px 8px',
                          }}
                        >
                          Export
                        </Button>
                      </TableCell>
                    </TableRow>
                  )
                })}
            </TableBody>
          </Table>
        </TableContainer>
        <CustomPagination
          options={{
            ...paginationOptions,
            totalPages: reportHistoryPaginationData.totalNumberOfPages,
          }}
          handlePagination={handlePagination}
        />
      </Paper>
    </Stack>
  )
}

export const FiltersCell = ({ filter }: { filter: IReportFilters }) => {
  return (
    <Stack>
      {Object.entries(filter).map(([key, value]) => (
        <Stack
          key={key}
          direction="row"
          alignContent="center"
          alignItems="center"
        >
          <Typography variant="label1" sx={{ fontWeight: 600 }}>
            {key}
          </Typography>
          <Typography variant="label2">:</Typography>
          <Typography variant="label1">
            {isObject(value) ? JSON.stringify(value) : value}
          </Typography>
        </Stack>
      ))}
    </Stack>
  )
}
