'use client'
import { Stack, Typography } from '@mui/material'
import React, { useEffect, useState } from 'react'
import { ReportsIcon } from '@dtbx/ui/icons'
import { AntTabs, AntTab, TabPanel } from '@dtbx/ui/components/Tabs'
import NewReportPage from '@/app/reports/new-report/page'
import ReportGenerationHistoryPage from '@/app/reports/history/page'
import { useAppDispatch } from '@/store'
import { setReportPaginationData } from '@/store/reducers'
import { IPagination } from '@/store/interfaces'

const ReportsPage = () => {
  const [value, setValue] = useState<number>(0)
  const dispatch = useAppDispatch()
  const handleChange = (event: React.SyntheticEvent, newValue: number) => {
    setValue(newValue)
  }
  useEffect(() => {
    dispatch(setReportPaginationData({} as IPagination))
  }, [])
  return (
    <Stack
      sx={{
        background: '#FFFFFF',
      }}
    >
      <Stack
        sx={{
          marginLeft: '1%',
          marginTop: '0.2%',
          flexDirection: 'row',
          justifyContent: 'flex-start',
          alignItems: 'center',
          gap: '8px',
          padding: '8px',
        }}
      >
        <ReportsIcon width="28" height="26" />
        <Typography variant="h5">Reports</Typography>
      </Stack>
      <AntTabs
        sx={{
          marginLeft: '1%',
        }}
        onChange={handleChange}
        value={value}
      >
        <AntTab label={'New Report'} />
        <AntTab label={'Saved Report Filters'} />
      </AntTabs>
      <TabPanel value={value} index={0}>
        <NewReportPage />
      </TabPanel>
      <TabPanel value={value} index={1}>
        <ReportGenerationHistoryPage />
      </TabPanel>
    </Stack>
  )
}
export default ReportsPage
