'use client'
import { FilterListRounded } from '@mui/icons-material'
import {
  Box,
  FormControl,
  IconButton,
  InputLabel,
  MenuItem,
  Select,
  SelectChangeEvent,
  Stack,
  Typography,
} from '@mui/material'
import React, { useState } from 'react'
import { useAppDispatch } from '@/store'
import { ICustomer } from '@/store/interfaces'
import { setCustomerSearch, setSelectedFilters } from '@/store/reducers'
import { getAllCustomers } from '@/store/actions'
import { CustomSearchByInput } from '@dtbx/ui/components/Input'
import { GrowProvider } from '@dtbx/ui/components/Transitions'
import { DateRangePicker } from '@dtbx/ui/components/DropDownMenus'

import { CreateCustomerDialog } from '@/app/customers/customers/Create'

export const customerSearchByItems: {
  label: string
  value: Array<keyof ICustomer>
}[] = [
  { label: 'First Name', value: ['firstName'] },
  { label: 'Last Name', value: ['lastName'] },
  { label: 'Other Names', value: ['otherNames'] },
  { label: 'ID Number', value: ['idNumber'] },
  { label: 'Email', value: ['email'] },
  { label: 'Phone Number', value: ['phoneNumber'] },
  { label: 'Account Number', value: ['accountNumber'] },
]
interface PageHeaderProps {
  setDateCreatedFrom: React.Dispatch<React.SetStateAction<string>>
  setDateCreatedTo: React.Dispatch<React.SetStateAction<string>>
}
const PageHeader: React.FC<PageHeaderProps> = ({
  setDateCreatedFrom,
  setDateCreatedTo,
}) => {
  const dispatch = useAppDispatch()
  const [openFilterBar, setOpenFilterBar] = useState(false)
  const [searchTerm, setSearchTerm] = useState<string>('')
  const [selectedStatus, setSelectedStatus] = useState<string>('')
  const handleStatusChange = (e: SelectChangeEvent) => {
    setSelectedStatus(e.target.value as string)
    getAllCustomers(
      {
        isBlocked: e.target.value !== 'Active' ? 'true' : 'false',
        page: 1,
        size: 10,
      },
      dispatch
    )
  }
  const [searchBy, setSearchBy] = useState<{
    label: string
    value: Array<keyof ICustomer>
  }>(customerSearchByItems[0])

  return (
    <Stack sx={{ flexDirection: 'column', gap: '17px' }}>
      <Stack
        sx={{
          justifyContent: 'space-between',
          flexDirection: 'row',
        }}
      >
        <Stack
          sx={{
            flexDirection: 'row',
            justifyContent: 'space-between',
            width: '70%',
            gap: '5%',
          }}
        >
          <CustomSearchByInput
            searchByDropDownItems={customerSearchByItems}
            onChange={(value: string) => {
              setSearchTerm(value)
              dispatch(
                setCustomerSearch({
                  searchBy: searchBy.value,
                  searchValue: value,
                })
              )
              getAllCustomers(
                {
                  page: 1,
                  size: 7,
                  [searchBy.value[0]]: value,
                },
                dispatch
              )
            }}
            value={searchTerm}
            onSearchBySelect={(value: {
              label: string
              value: Array<keyof ICustomer>
            }) => {
              setSearchBy(value)
            }}
            searchByValue={searchBy}
            onKeyDown={() => {
              getAllCustomers(
                {
                  page: 1,
                  size: 7,
                  [searchBy.value[0]]: searchTerm,
                },
                dispatch
              )
            }}
            width={'100%'}
          />
          <IconButton
            sx={{
              height: '42px',
              width: '130px',
              backgroundColor: '#FFF',
              display: 'flex',
              borderRadius: '4px',
              border: '1.5px solid #D0D5DD',
            }}
            onClick={() => {
              setOpenFilterBar(!openFilterBar)
            }}
          >
            <FilterListRounded />
            <Typography>Filter</Typography>
          </IconButton>
        </Stack>
        <CreateCustomerDialog />
      </Stack>
      {openFilterBar && (
        <GrowProvider in={openFilterBar}>
          <Box
            sx={{
              display: 'flex',
              gap: '8px',
            }}
          >
            <DateRangePicker
              buttonText="Date Created"
              onApplyDateRange={(date) => {
                setDateCreatedFrom(date.start.format('YYYY-MM-DD'))
                setDateCreatedTo(date.end.format('YYYY-MM-DD'))
                getAllCustomers(
                  {
                    page: 1,
                    size: 10,
                    dateCreatedFrom: date.start.format('YYYY-MM-DD'),
                    dateCreatedTo: date.end.format('YYYY-MM-DD'),
                  },
                  dispatch
                )
                dispatch(
                  setSelectedFilters([
                    {
                      filterName: 'Date Created',
                      options: [
                        {
                          key: 'start',
                          label: 'start',
                          value: date.start.format('YYYY-MM-DD'),
                        },
                        {
                          key: 'end',
                          label: 'end',
                          value: date.end.format('YYYY-MM-DD'),
                        },
                      ],
                      type: 'date',
                    },
                  ])
                )
              }}
            />
            <FormControl sx={{ width: '10vw' }}>
              <InputLabel id="demo-simple-select-label">
                Customer Status
              </InputLabel>
              <Select
                size="small"
                value={selectedStatus}
                labelId="demo-simple-select-label"
                id="demo-simple-select"
                label={'Customer Status'}
                onChange={handleStatusChange}
              >
                {['Active', 'Inactive'].map((item) => (
                  <MenuItem key={item} value={item}>
                    {item}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Box>
        </GrowProvider>
      )}
    </Stack>
  )
}

export default PageHeader
