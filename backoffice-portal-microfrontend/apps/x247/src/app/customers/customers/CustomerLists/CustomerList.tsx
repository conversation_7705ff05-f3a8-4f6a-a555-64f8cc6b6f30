'use client'
import {
  Avatar,
  Box,
  Paper,
  Stack,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableRow,
  Typography,
} from '@mui/material'
import React, { useEffect, useState } from 'react'
import { sentenceCase } from 'tiny-case'
import { useCustomRouter } from '@dtbx/ui/hooks'
import { ICustomer, IProfileApprovalRequests } from '@/store/interfaces'
import { IHeadCell } from '@dtbx/store/interfaces'
import { useAppDispatch, useAppSelector } from '@/store'
import {
  fetchPendingSingleCustomerApprovals,
  getAllCustomers,
  getCustomerProfileById,
} from '@/store/actions'
import {
  setCustomerApprovalBarOpen,
  setCustomerIdToView,
  setCustomersWithPendingApprovals,
  setPendingSingleCustomerApprovals,
} from '@/store/reducers'
import { CustomSkeleton } from '@dtbx/ui/components/Loading'
import {
  CustomPagination,
  CustomTableHeader,
  PaginationOptions,
} from '@dtbx/ui/components/Table'
import { CustomerStatusChip } from '@dtbx/ui/components/Chip'

import NoCustomerFound from './NoCustomerFound'
import PageHeader from './pageHeader'
import { CustomerMoreMenu } from './customerMoreMenu'
import { formatTimestamp } from '@dtbx/store/utils'
const headCellItems: IHeadCell[] = [
  {
    id: 'name',
    label: 'Name',
    alignCenter: false,
    alignRight: false,
  },
  {
    id: 'status',
    label: 'Status',
    alignCenter: false,
    alignRight: false,
  },
  {
    id: 'pinStatus',
    label: 'Pin Status',
    alignCenter: false,
    alignRight: false,
  },
  {
    id: 'phone',
    label: 'Phone Number',
    alignCenter: false,
    alignRight: false,
  },
  {
    id: 'idNumber',
    label: 'ID Number',
    alignCenter: false,
    alignRight: false,
  },
  {
    id: 'createdOn',
    label: 'Date Created',
    alignCenter: false,
    alignRight: false,
  },
  {
    id: 'actions',
    label: 'Actions',
    alignCenter: false,
    alignRight: false,
  },
]
const List = () => {
  const dispatch = useAppDispatch()
  const router = useCustomRouter()
  const { customersResponse, isCustomersLoading, isCustomersSuccess } =
    useAppSelector((state) => state.customers)
  const { customersWithPendingApprovals } = useAppSelector(
    (state) => state.approvalRequests
  )
  const [dateCreatedFrom, setDateCreatedFrom] = useState<string>('')
  const [dateCreatedTo, setDateCreatedTo] = useState<string>('')
  const [paginationOptions, setPaginationOptions] = useState({
    page: customersResponse.pageNumber,
    size: 10,
    totalPages: customersResponse.totalNumberOfPages,
  })
  /*************************start pagination handlers***************************/
  const handlePagination = async (newOptions: PaginationOptions) => {
    setPaginationOptions(newOptions) // Update parent state
    await getAllCustomers(
      {
        ...newOptions,
        dateCreatedFrom,
        dateCreatedTo,
      },
      dispatch
    )
  }
  /*************************end pagination handlers**************************/
  const handleRowClick = async (profileId: string) => {
    await getCustomerProfileById(profileId, dispatch)
    router.push(`/customers/customer`)
  }
  const fetchProfilesWithPendingApprovals = () => {
    let customers: IProfileApprovalRequests[] = []
    if (!customersWithPendingApprovals) {
      return
    } else {
      customers = [...customersWithPendingApprovals]
    }

    customersResponse?.data?.forEach(async (customer) => {
      if (
        customersWithPendingApprovals.filter(
          (approval) => approval.profileId === customer.id
        )?.length === 0
      ) {
        const approvals = await fetchPendingSingleCustomerApprovals(
          dispatch,
          customer.id || ''
        )
        customers = [
          ...customers,
          { requests: approvals, profileId: customer.id || '' },
        ]
        dispatch(setCustomersWithPendingApprovals(customers))
      }
    })
  }
  useEffect(() => {
    customersResponse?.data?.length > 0 && fetchProfilesWithPendingApprovals()
  }, [customersResponse])

  useEffect(() => {
    localStorage.removeItem('tab')
    localStorage.removeItem('customerId')
    dispatch(setCustomerApprovalBarOpen(false))
    dispatch(setPendingSingleCustomerApprovals([]))
  }, [])
  return (
    <Stack
      sx={{
        padding: '1.5% 2% 1.5% 2%',
        flexDirection: 'column',
        gap: '16px',
      }}
      data-testid="customer-list"
    >
      <PageHeader
        setDateCreatedFrom={setDateCreatedFrom}
        setDateCreatedTo={setDateCreatedTo}
      />
      {isCustomersLoading ? (
        <CustomSkeleton
          animation="pulse"
          variant="rectangular"
          width={'100%'}
          height={'60vh'}
        />
      ) : customersResponse.data?.length <= 0 ? (
        <NoCustomerFound />
      ) : (
        <Paper
          elevation={0}
          sx={{
            boxShadow:
              '0px 1px 3px 0px rgba(16, 24, 40, 0.10), 0px 1px 2px 0px rgba(16, 24, 40, 0.06)',
            borderRadius: '4px',
            border: '1px solid #EAECF0',
            background: '#FFFFFF',
          }}
        >
          <TableContainer component={Paper} elevation={0}>
            <Table stickyHeader sx={{}}>
              <CustomTableHeader
                order={'desc'}
                orderBy={''}
                rowCount={0}
                headLabel={headCellItems}
                numSelected={0}
              />
              <TableBody>
                {isCustomersSuccess &&
                  !isCustomersLoading &&
                  customersResponse.data.map(
                    (row: ICustomer, index: number) => {
                      const hasPendingRequest =
                        customersWithPendingApprovals &&
                        customersWithPendingApprovals.filter(
                          (approval) => approval.profileId === row.id
                        )[0]?.requests?.length > 0
                      return (
                        <TableRow
                          key={index || row.id}
                          sx={{
                            background: hasPendingRequest ? '#FFF4F4' : 'none',
                          }}
                        >
                          <TableCell
                            onClick={() => handleRowClick(row.id as string)}
                            sx={{
                              padding: '10px 24px 10px 16px',
                              display: 'flex',
                              gap: '12px',
                              cursor: 'pointer',
                            }}
                          >
                            <Avatar>
                              {(row.firstName ? row.firstName[0] : 'O') +
                                (row.lastName ? row.lastName[0] : 'O')}
                            </Avatar>
                            <Box>
                              <Typography
                                variant="body2"
                                sx={{
                                  color: '#000A12',
                                }}
                              >
                                {(row.firstName
                                  ? sentenceCase(row.firstName)
                                  : 'No-FirstName') +
                                  ' ' +
                                  (row.lastName
                                    ? sentenceCase(row.lastName)
                                    : 'No-LastName')}
                                {row.otherNames ? (
                                  <Typography component={'span'}>
                                    {', ' + sentenceCase(row.otherNames)}
                                  </Typography>
                                ) : null}
                              </Typography>
                              <Typography
                                variant="body2"
                                sx={{
                                  fontWeight: 400,
                                }}
                              >
                                {row.email ? row.email : 'No-email'}
                              </Typography>
                            </Box>
                          </TableCell>
                          <TableCell
                            sx={{
                              padding: '10px 24px 10px 16px',
                            }}
                          >
                            <CustomerStatusChip
                              label={row.isBlocked ? 'INACTIVE' : 'ACTIVE'}
                            />
                          </TableCell>
                          <TableCell
                            sx={{
                              padding: '10px 24px 10px 16px',
                            }}
                          >
                            <CustomerStatusChip
                              label={
                                row.pinStatus === 'One_Time_PIN'
                                  ? 'One Time Pin'
                                  : row.pinStatus === 'Active_PIN'
                                    ? 'ACTIVE'
                                    : row.pinStatus
                                      ? row.pinStatus
                                      : 'Not Set'
                              }
                            />
                          </TableCell>
                          <TableCell sx={{ textWrap: 'nowrap' }}>
                            {row.phoneNumber}
                          </TableCell>
                          <TableCell>
                            {row.idNumber ? row.idNumber : 'No-ID Number'}
                          </TableCell>
                          <TableCell>
                            {formatTimestamp(row.dateCreated)}
                          </TableCell>
                          <TableCell
                            sx={{
                              padding: '0px 24px',
                            }}
                          >
                            <CustomerMoreMenu
                              customer={row}
                              menuItems={[
                                {
                                  label: 'See more Details',
                                  onClick: async () => {
                                    dispatch(setCustomerIdToView(row.id))
                                    await getCustomerProfileById(
                                      row.id,
                                      dispatch
                                    )
                                    router.push(`/customers/customer`)
                                  },
                                  disabled: false,
                                  visible: true,
                                },
                                {
                                  label: 'See pending requests',
                                  onClick: async () => {
                                    await fetchPendingSingleCustomerApprovals(
                                      dispatch,
                                      row.id || '',
                                      {},
                                      customersWithPendingApprovals
                                    )
                                    await getCustomerProfileById(
                                      row.id,
                                      dispatch
                                    )
                                    router.push(`/customers/customer`)
                                  },
                                  disabled: false,
                                  visible:
                                    customersWithPendingApprovals &&
                                    customersWithPendingApprovals.filter(
                                      (approval) =>
                                        approval.profileId === row.id
                                    )[0]?.requests?.length > 0,
                                },
                              ]}
                            />
                          </TableCell>
                        </TableRow>
                      )
                    }
                  )}
              </TableBody>
            </Table>
          </TableContainer>
          {customersResponse.totalNumberOfPages > 0 && (
            <CustomPagination
              options={{
                ...paginationOptions,
                totalPages: customersResponse.totalNumberOfPages,
              }}
              handlePagination={handlePagination}
            />
          )}
        </Paper>
      )}
    </Stack>
  )
}

export default List
