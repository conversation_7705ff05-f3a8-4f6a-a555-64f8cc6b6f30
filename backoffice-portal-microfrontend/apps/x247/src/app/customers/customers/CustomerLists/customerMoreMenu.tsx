import { KeyboardArrowDownRounded } from '@mui/icons-material'
import { Box, Button, Menu, MenuItem, styled } from '@mui/material'
import React, { useState } from 'react'
import { ICustomer } from '@/store/interfaces'
import { useAppDispatch, useAppSelector } from '@/store'
import {
  activateCustomerProfile,
  deactivateCustomer,
  deleteCustomerProfile,
  fetchPendingSingleCustomerApprovals,
} from '@/store/actions'
import {
  ACCESS_CONTROLS,
  AccessControlWrapper,
  HasAccessToRights,
} from '@dtbx/store/utils'
import { Dialog } from '@dtbx/ui/components/Overlay'

const CellButton = styled(Button)({
  color: '#2D2D2D',
  fontSize: '14px',
  fontWeight: 500,
  textTransform: 'none',
  '&:hover': {
    background: 'none',
    color: '#2D2D2D',
  },
})

interface ICustomerMoreMenuProps {
  customer: ICustomer
  menuItems: Array<{
    label: string
    onClick: () => void
    disabled: boolean
    visible: boolean
  }>
}

export const CustomerMoreMenu: React.FC<ICustomerMoreMenuProps> = ({
  menuItems,
  customer,
}) => {
  const [anchorEl, setAnchorEl] = React.useState<null | HTMLElement>(null)
  const open = Boolean(anchorEl)

  const [buttonWidth, setButtonWidth] = React.useState<number>(0)

  const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    setAnchorEl(event.currentTarget)
    setButtonWidth(event.currentTarget.offsetWidth)
  }
  const handleClose = () => {
    setAnchorEl(null)
  }

  return (
    <Box>
      <CellButton
        variant="outlined"
        sx={{
          width: '107px',
          padding: '9px 28px',
          justifyContent: 'center',
          borderRadius: '6px',
          border: '1px solid #AAADB0',
          boxShadow: '0px 1px 2px 0px rgba(16, 24, 40, 0.05)',
        }}
        endIcon={<KeyboardArrowDownRounded />}
        onClick={handleClick}
      >
        Action
      </CellButton>
      <Menu
        open={open}
        anchorEl={anchorEl}
        onClose={handleClose}
        slotProps={{
          root: {},
          paper: {
            elevation: 0,
            sx: {
              minWidth: buttonWidth,
              mt: 1,
              borderRadius: '1px solid ligthgray',
              boxShadow:
                '0px 1px 2px 0px rgba(16, 24, 40, 0.06), 0px 1px 3px 0px rgba(16, 24, 40, 0.10)',
              '&::before': {
                content: '""',
                display: 'block',
                position: 'absolute',
                top: 0,
                transform: 'translateY(-50%) rotate(45deg)',
                zIndex: 4,
              },
            },
          },
        }}
      >
        {menuItems
          .filter((item) => item.visible)
          .map((item, index) => (
            <MenuItem
              key={index}
              onClick={() => {
                item.onClick()
              }}
              disabled={item.disabled}
            >
              {item.label}
            </MenuItem>
          ))}
        <ActivateCustomer customer={customer} />
        <DeleteCustomer customer={customer} />
      </Menu>
    </Box>
  )
}
//Reasons props
const reasonsForDeactivating: string[] = [
  'User Request',
  'Temporary Suspension',
  'Fraudulent Activity',
  'Security Concerns',
  'Other',
]

const reasonsForActivating: string[] = [
  'Reinstatement',
  'Resolved Issues',
  'Other',
]
export const ActivateCustomer = ({
  customer,
  origin,
}: {
  customer: ICustomer
  origin?: string
}) => {
  const [open, setOpen] = useState<boolean>(false)
  const dispatch = useAppDispatch()
  const { customersWithPendingApprovals } = useAppSelector(
    (state) => state.approvalRequests
  )

  const handleChange = async (reasons: string[]) => {
    const reason = reasons.join(', ')
    try {
      if (customer.isBlocked) {
        // Activate customer profile
        await activateCustomerProfile({
          profileId: customer.id ? customer.id : '',
          comments: reason,
          dispatch,
        })
        setOpen(false) // Close dialog on success
      } else {
        await deactivateCustomer({
          profileID: customer.id ? customer.id : '',
          reason,
          dispatch,
        })
        setOpen(false) // Close dialog on success
      }
    } catch (error) {
      console.error('Error activating customer profile:', error)
    } finally {
      await fetchPendingSingleCustomerApprovals(
        dispatch,
        customer?.id || '',
        {},
        customersWithPendingApprovals
      )
    }
  }

  return (
    <>
      <AccessControlWrapper
        rights={
          customer.isBlocked
            ? [...ACCESS_CONTROLS.ACTIVATE_CUSTOMERS]
            : [...ACCESS_CONTROLS.DEACTIVATE_CUSTOMERS]
        }
      >
        {origin === 'view' ? (
          <Button
            onClick={() => setOpen(!open)}
            variant="outlined"
            sx={{
              backgroundColor: '#FFFFFF',
              border: '1px solid  #AAADB0',
              borderRadius: '4px',
              boxShadow: '0px 1px 2px 0px rgba(16, 24, 40, 0.05)',
              height: '34px',
              color: '#555C61',
              fontSize: '13px',
              fontStyle: 'normal',
              fontWeight: '500',
              lineHeight: '16px',
              textAlign: 'center',
            }}
          >
            {customer.isBlocked ? 'Activate' : 'Deactivate'}
          </Button>
        ) : (
          <MenuItem onClick={() => setOpen(!open)}>
            {customer.isBlocked ? 'Activate' : 'Deactivate'}
          </MenuItem>
        )}
      </AccessControlWrapper>
      <Dialog
        open={open}
        setOpen={setOpen}
        concatReason
        title={`${customer.isBlocked ? 'Activate customer' : 'Deactivate Customer'}`}
        buttonText={`${customer.isBlocked ? 'Activate' : 'Deactivate'}`}
        descriptionText={`Please let us know why you are ${customer.isBlocked ? 'activating' : 'deactivating'} this customer`}
        onClick={async (reason) => {
          await handleChange(reason)
        }}
        buttonProps={{ color: customer.isBlocked ? '#027A48' : '' }}
        reasons={
          customer.isBlocked ? reasonsForActivating : reasonsForDeactivating
        }
      />
    </>
  )
}

export const DeleteCustomer = ({
  customer,
  origin,
}: {
  customer: ICustomer
  origin?: string
}) => {
  const [open, setOpen] = useState<boolean>(false)
  const dispatch = useAppDispatch()
  const { customersWithPendingApprovals } = useAppSelector(
    (state) => state.approvalRequests
  )

  const handleChange = async (reasons: string[]) => {
    const reason = reasons.join(', ')
    try {
      if (HasAccessToRights(['SUPER_DELETE_CUSTOMERS'])) {
        // Delete customer profile
        await deleteCustomerProfile({
          profileID: customer.id ? customer.id : '',
          comments: reason,
          type: 'super',
          dispatch,
        })
        setOpen(false)
      } else {
        await deleteCustomerProfile({
          profileID: customer.id ? customer.id : '',
          comments: reason,
          type: 'make',
          dispatch,
        })
        setOpen(false)
      }
    } catch (error) {
      console.error('Error Deleting customer profile:', error)
    } finally {
      fetchPendingSingleCustomerApprovals(
        dispatch,
        customer?.id || '',
        {},
        customersWithPendingApprovals
      )
    }
  }

  return (
    <>
      <AccessControlWrapper rights={[...ACCESS_CONTROLS.DELETE_CUSTOMERS]}>
        {origin === 'view' ? (
          <Button
            onClick={() => setOpen(!open)}
            variant="outlined"
            sx={{
              backgroundColor: '#FFFFFF',
              border: '1px solid  #AAADB0',
              borderRadius: '4px',
              boxShadow: '0px 1px 2px 0px rgba(16, 24, 40, 0.05)',
              height: '34px',
              color: '#555C61',
              fontSize: '13px',
              fontStyle: 'normal',
              fontWeight: '500',
              lineHeight: '16px',
              textAlign: 'center',
            }}
          >
            Delete
          </Button>
        ) : (
          <MenuItem onClick={() => setOpen(!open)}>{'Delete'}</MenuItem>
        )}
      </AccessControlWrapper>
      <Dialog
        open={open}
        setOpen={setOpen}
        title={`Delete Customer Profile`}
        buttonText={`Delete`}
        descriptionText={`Please let us know why you are deleting this customer profile`}
        onClick={async (reason) => {
          await handleChange(reason)
        }}
        buttonProps={{ color: customer.isBlocked ? '#027A48' : '' }}
        reasons={reasonsForDeactivating}
      />
    </>
  )
}
