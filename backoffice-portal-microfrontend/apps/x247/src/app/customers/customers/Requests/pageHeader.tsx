import { Stack } from '@mui/material'
import React, { useEffect, useState } from 'react'
import dayjs from 'dayjs'
import { useAppDispatch, useAppSelector } from '@/store'
import { getApprovalRequestTypes, getApprovals } from '@/store/actions'
import { IFilter } from '@dtbx/store/interfaces'
import { CustomFilterBox } from '@/app/approval-requests/CustomFilterBox'
import { setUserSearchValue } from '@/store/reducers'

interface PageHeaderProps {
  page: number
  setPage: (value: number) => void
  selectedFilters: Record<string, string | string[]>
  setSelectedFilters: React.Dispatch<
    React.SetStateAction<Record<string, string | string[]>>
  >
  dateRange: { start: dayjs.Dayjs; end: dayjs.Dayjs } | null
  setDateRange: React.Dispatch<
    React.SetStateAction<{ start: dayjs.Dayjs; end: dayjs.Dayjs } | null>
  >
}

const PageHeader: React.FC<PageHeaderProps> = ({
  page,
  setPage,
  selectedFilters,
  dateRange,
  setDateRange,
}) => {
  const dispatch = useAppDispatch()

  const [openFilterBar, setOpenFilterBar] = useState<boolean>(false)
  const { searchUserValue } = useAppSelector((state) => state.users)
  const [searchValue, setSearchValue] = useState<string>(searchUserValue)
  const { requestTypes } = useAppSelector((state) => state.approvalRequests)
  const { search } = useAppSelector((state) => state.customers)

  const handleDateRangeFilterApply = (
    date: {
      start: dayjs.Dayjs
      end: dayjs.Dayjs
    } | null
  ) => {
    setDateRange(date)
    filterRequests(selectedFilters, date)
  }

  const handleSearch = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const search = e.target.value
    setSearchValue(search)
    filterRequests(selectedFilters, dateRange)
  }

  const setMakerNameValue = async (makerName: string) => {
    filterRequests(
      {
        ...selectedFilters,
        makerName,
      },
      dateRange
    )
  }

  const filterRequests = async (
    filter: Record<string, string | string[]>,
    date: {
      start: dayjs.Dayjs
      end: dayjs.Dayjs
    } | null
  ) => {
    const paramString = [
      `page=${page ?? 1}`,
      `size=${10}`,
      `channel=DBP`,

      `status=PENDING`,
      filter['Request type'] && `requestType=${filter['Request type']}`,
      filter.Module && `module=${filter.Module}`,
      (searchValue || filter.makerName) &&
        `${search?.searchBy[0] === 'firstName' ? 'makerFirstName' : search?.searchBy[0] === 'lastName' ? 'makerLastName' : ''}=${filter.makerName || searchValue}`,
      date &&
        `&createDateFrom=${date.start.format('YYYY-MM-DD')}&createDateTo=${date.end.format('YYYY-MM-DD')}`,
    ].filter(Boolean)
    const url = '?' + paramString.join('&')
    await getApprovals(dispatch, url)
  }

  const { channelModules } = useAppSelector((state) => state.auth)
  // filters
  useEffect(() => {
    filterRequests(selectedFilters, dateRange)
    console.log("Request Types>>", requestTypes)
  }, [selectedFilters, dateRange])

  useEffect(() => {
    if (!requestTypes.length) {
      getApprovalRequestTypes(dispatch, 'DBP')
    }
    dispatch(setUserSearchValue(''))
  }, [])

  const generateModuleOptions = () => {
    const dbpChannel = channelModules.find((module) => module.channel === 'DBP')
    if (!dbpChannel || !dbpChannel.modules) return []

    const excludedModules = ['X247 Reports', 'Tariffs']
    const filteredModules = dbpChannel.modules.filter(
      (module) => !excludedModules.includes(module)
    )

    return filteredModules.map((module) => ({
      key: module.toLowerCase(),
      value: module,
      label: module,
    }))
  }

  const filters: IFilter[] = [
    {
      filterName: 'Date created',
      options: [
        {
          label: 'Date created',
          value: 'dateCreated',
          key: '',
        },
      ],
      type: 'date',
    },
    {
      filterName: 'Module',
      options: generateModuleOptions(),
      type: 'dropdown/single',
    },
    // {
    //   filterName: 'Request type',
    //   options: requestTypes.length
    //     ? [
    //         ...requestTypes
    //           .filter((item) => item.name.toLowerCase().includes('customer'))
    //           .map((item) => {
    //             return { label: item.name, value: item.id, key: item.id }
    //           }),
    //       ]
    //     : [],
    //   type: 'dropdown/single',
    // },
    {
      filterName: 'Request type',
      options: requestTypes.length
        ? [
            ...requestTypes.map((item) => {
              return { label: item.name, value: item.id, key: item.id }
            }),
          ]
        : [],
      type: 'dropdown/single',
    },
  ]

  return (
    <Stack
      sx={{
        flexDirection: 'column',
        justifyContent: 'space-between',
        gap: '16px',
      }}
    >
      <Stack
        sx={{
          flexDirection: 'row',
          justifyContent: 'flex-start',
          alignItems: 'flex-start',
          gap: '18px',
          flex: 1,
        }}
      >
        <CustomFilterBox
          openFilter={openFilterBar}
          setOpenFilter={setOpenFilterBar}
          searchValue={searchValue}
          searchByValues={['First name', 'Last name', 'email']}
          handleSearch={handleSearch}
          filters={filters}
          onFilterChange={(filter) => {
            setPage(1)
            filterRequests(
              Object.keys(filter).length
                ? {
                    ...selectedFilters,
                    ...filter,
                    makerName: selectedFilters?.makerName,
                  }
                : {},
              dateRange
            )
          }}
          setDate={handleDateRangeFilterApply}
          searchPlaceHolder="Search by maker name"
          setMakerName={setMakerNameValue}
        />
      </Stack>
    </Stack>
  )
}

export default PageHeader
