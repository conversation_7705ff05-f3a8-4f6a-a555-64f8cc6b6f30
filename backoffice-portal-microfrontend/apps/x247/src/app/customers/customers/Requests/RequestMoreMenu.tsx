import { CloseRounded } from '@mui/icons-material'
import KeyboardArrowDownIcon from '@mui/icons-material/KeyboardArrowDown'
import {
  Button,
  Dialog,
  DialogContent,
  IconButton,
  Menu,
  MenuItem,
  Stack,
  Typography,
} from '@mui/material'
import React, { useState } from 'react'
import { sentenceCase } from 'tiny-case'
import { ReadOnlyTypography } from '@dtbx/ui/components'
import { IApprovalRequest } from '@/store/interfaces'
import { useAppDispatch } from '@/store'
import { RequestsApprovalIcon } from '@dtbx/ui/icons'

import { ApprovalRequestRouting } from '@/app/approval-requests/RequestRouting'
import { formatTimestamp } from '@dtbx/store/utils'
import { useCustomRouter } from '@dtbx/ui/hooks'

export const RequestMoreMenu = ({ request }: { request: IApprovalRequest }) => {
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null)
  const open = Boolean(anchorEl)
  const dispatch = useAppDispatch()
  const router = useCustomRouter()
  const handleClick = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget)
  }
  const handleClose = () => {
    setAnchorEl(null)
  }
  const handleReviewRequest = async () => {
    await ApprovalRequestRouting(request, dispatch, router)
    handleClose()
  }
  return (
    <>
      <Button
        id="demo-customized-button"
        aria-controls={open ? 'demo-customized-menu' : undefined}
        aria-haspopup="true"
        aria-expanded={open ? 'true' : undefined}
        variant="outlined"
        disableElevation
        onClick={handleClick}
        sx={{
          border: '1px solid #D0D5DD',
          padding: '8px 14px',
          fontWeight: '500',
          gap: 0,
        }}
        endIcon={<KeyboardArrowDownIcon />}
      >
        Actions
      </Button>
      <Menu
        open={open}
        id="demo-customized-menu"
        MenuListProps={{
          'aria-labelledby': 'demo-customized-button',
        }}
        anchorEl={anchorEl}
        onClose={handleClose}
      >
        <MenuItem
          onClick={handleReviewRequest}
          sx={{ display: `${request.status !== 'PENDING' ? 'none' : 'block'}` }}
        >
          Review Approval request
        </MenuItem>
        <RequestDetailsDialog request={request} />
      </Menu>
    </>
  )
}

export const RequestDetailsDialog = ({
  request,
}: {
  request: IApprovalRequest
}) => {
  const [open, setOpen] = useState<boolean>(false)

  const handleClose = (
    e: React.MouseEvent<HTMLButtonElement> | null,
    action: string
  ) => {
    if (action !== 'backdropClick') {
      setOpen(false)
    }
  }
  return (
    <>
      <MenuItem
        onClick={() => setOpen(!open)}
        sx={{ display: `${request.status !== 'PENDING' ? 'block' : 'none'}` }}
      >
        <Typography>See request summary</Typography>
      </MenuItem>
      <Dialog open={open} maxWidth="xs" fullWidth>
        <Stack sx={{ paddingLeft: '5%' }}>
          {/*header icons */}
          <Stack
            sx={{
              justifyContent: 'space-between',
              alignItems: 'center',
              flexDirection: 'row',
            }}
          >
            <RequestsApprovalIcon />
            <IconButton onClick={() => handleClose(null, 'close')}>
              <CloseRounded />
            </IconButton>
          </Stack>
          <Typography
            variant="subtitle1"
            sx={{
              fontSize: '18px',
            }}
          >
            Approval request details
          </Typography>
        </Stack>
        <DialogContent
          sx={{
            flexDirection: 'column',
          }}
        >
          <Stack
            sx={{
              flexDirection: 'column',
              gap: '1.5vh',
            }}
          >
            <ReadOnlyTypography
              label="Approval request type"
              value={
                request?.makerCheckerType.type
                  ? sentenceCase(request?.makerCheckerType.type as string)
                  : 'No type'
              }
            />
            <ReadOnlyTypography
              label="Module"
              value={request?.makerCheckerType.module}
            />
            <ReadOnlyTypography label="Maker" value={request?.maker} />
            <ReadOnlyTypography
              label="Maker timestamp"
              value={formatTimestamp(request?.dateCreated)}
            />
            <ReadOnlyTypography
              label="Maker comment"
              value={request?.makerComments || 'No comment'}
            />
            <ReadOnlyTypography label="Checker" value={request?.checker} />
            <ReadOnlyTypography
              label="Checker timestamp"
              value={formatTimestamp(request?.dateModified)}
            />
            <ReadOnlyTypography
              label="Checker comment"
              value={request?.checkerComments || 'No comment'}
            />
          </Stack>
        </DialogContent>
      </Dialog>
    </>
  )
}
