import {
  Paper,
  Stack,
  styled,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableRow,
} from '@mui/material'
import React, { useEffect, useState } from 'react'
import dayjs from 'dayjs'
import { IHeadCell } from '@dtbx/store/interfaces'
import { useAppDispatch, useAppSelector } from '@/store'
import { CustomSkeleton } from '@dtbx/ui/components/Loading'
import {
  CustomPagination,
  CustomTableHeader,
  PaginationOptions,
} from '@dtbx/ui/components/Table'
import { CustomerStatusChip } from '@dtbx/ui/components/Chip'
import { EmptySearchAndFilter } from '@dtbx/ui/components/EmptyPage'

import { RequestMoreMenu } from '@/app/customers/customers/Requests/RequestMoreMenu'
import { RequestChip } from '@/app/approval-requests/Pending'

import PageHeader from './pageHeader'
import { getApprovals } from '@/store/actions'

export const CustomTableCell = styled(TableCell)(() => ({
  color: '#667085',
}))
const tableHeaders: IHeadCell[] = [
  {
    id: 'requestType',
    label: 'Request Type',
    alignCenter: false,
    alignRight: false,
  },
  {
    id: 'module',
    label: 'Module',
    alignCenter: false,
    alignRight: false,
  },
  {
    id: 'makerTimestamp',
    label: 'Maker Timestamp',
    alignCenter: false,
    alignRight: false,
  },
  {
    id: 'status',
    label: 'Status',
    alignCenter: false,
    alignRight: false,
  },
  {
    id: 'maker',
    label: 'Maker',
    alignCenter: false,
    alignRight: false,
  },
  {
    id: 'actions',
    label: 'Action',
    alignCenter: false,
    alignRight: false,
  },
]
const Requests = () => {
  const dispatch = useAppDispatch()
  const [page, setPage] = useState(1)

  const { isLoadingRequests, approvalRequestResponse } = useAppSelector(
    (state) => state.approvalRequests
  )

  const [selectedFilters, setSelectedFilters] = useState<
    Record<string, string | string[]>
  >({ Module: 'Customers' })
  const [dateRange, setDateRange] = useState<{
    start: dayjs.Dayjs
    end: dayjs.Dayjs
  } | null>(null)
  const approvalRequests = approvalRequestResponse?.data || []

  const paginationOptions = {
    page: page,
    size: 10,
    totalPages: approvalRequestResponse?.totalNumberOfPages || 0,
  }
  /*************************Query params handlers***************************/
  const buildQueryParams = (
    filters: Record<string, any>,
    page: number,
    size: number = 10,
    channel: string = 'DBP',
    status: string = 'PENDING',
    dateRange: { start: dayjs.Dayjs; end: dayjs.Dayjs } | null = null
  ): string => {
    const filterParams = Object.entries(filters)
      .filter(([_, value]) => value !== undefined && value !== '')
      .map(
        ([key, value]) =>
          `${encodeURIComponent(key)}=${encodeURIComponent(value)}`
      )
      .join('&')

    const baseParams = `channel=${channel}&page=${page}&size=${size}&status=${status}`
    const dateParams = dateRange
      ? `&createDateFrom=${dateRange.start.format('YYYY-MM-DD')}&createDateTo=${dateRange.end.format('YYYY-MM-DD')}`
      : ''

    return `?${baseParams}${filterParams ? `&${filterParams}` : ''}${dateParams}`
  }

  const fetchApprovals = async (newPage: number = page) => {
    const params = buildQueryParams(
      selectedFilters,
      newPage,
      paginationOptions.size,
      'DBP',
      'PENDING',
      dateRange
    )
    await getApprovals(dispatch, params)
  }

  /*************************start pagination handlers***************************/
  const handlePagination = async (newOptions: PaginationOptions) => {
    setPage(newOptions.page)
    await fetchApprovals(newOptions.page)
  }

  useEffect(() => {
    fetchApprovals()
  }, [page, selectedFilters, dateRange])
  /*************************end pagination handlers**************************/
  return (
    <Stack
      sx={{
        padding: '1.5% 2% 1.5% 2%',
      }}
      gap={'17px'}
    >
      <PageHeader
        page={page}
        setPage={setPage}
        selectedFilters={selectedFilters}
        setSelectedFilters={setSelectedFilters}
        dateRange={dateRange}
        setDateRange={setDateRange}
      />
      {isLoadingRequests ? (
        <CustomSkeleton
          variant="rectangular"
          sx={{
            width: '100%',
            height: '60vh',
            margin: 'auto',
          }}
        />
      ) : (
        <Paper
          elevation={0}
          sx={{
            boxShadow:
              '0px 1px 3px 0px rgba(16, 24, 40, 0.10), 0px 1px 2px 0px rgba(16, 24, 40, 0.06)',
            borderRadius: '4px',
            border: '1px solid #EAECF0',
            background: '#FFFFFF',
          }}
        >
          <TableContainer component={Paper} elevation={0}>
            <Table stickyHeader sx={{}}>
              <CustomTableHeader
                order={'desc'}
                orderBy={''}
                rowCount={0}
                headLabel={tableHeaders}
                numSelected={0}
              />
              <TableBody>
                {approvalRequests.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={6} align="center">
                      <EmptySearchAndFilter
                        message="No requests match your filters"
                        additionalText="No records found. Please try again with different filters."
                        onClick={() => {
                          getApprovals(
                            dispatch,
                            `?channel=DBP&module=Customers&page=1&size=10`
                          )
                        }}
                      />
                    </TableCell>
                  </TableRow>
                ) : (
                  approvalRequests.map((row) => (
                    <TableRow key={row.id}>
                      <CustomTableCell
                        sx={{
                          padding: '10px 24px 10px 16px',
                        }}
                      >
                        <RequestChip
                          label={row.makerCheckerType.name}
                          sx={{ width: 'auto' }}
                        />
                      </CustomTableCell>
                      <CustomTableCell>
                        {row.makerCheckerType.module}
                      </CustomTableCell>
                      <CustomTableCell>
                        {dayjs(row.dateCreated).format('MMMM D, YYYY')}
                      </CustomTableCell>
                      <CustomTableCell>
                        <CustomerStatusChip label={row.status} />
                      </CustomTableCell>
                      <CustomTableCell>{row.maker}</CustomTableCell>
                      <TableCell
                        padding="none"
                        sx={{
                          padding: '0px 24px',
                        }}
                      >
                        <RequestMoreMenu request={row} />
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </TableContainer>
          {approvalRequestResponse.totalNumberOfPages > 0 && (
            <CustomPagination
              options={{
                page: paginationOptions.page,
                size: paginationOptions.size,
                totalPages: approvalRequestResponse.totalNumberOfPages,
              }}
              handlePagination={handlePagination}
            />
          )}
        </Paper>
      )}
    </Stack>
  )
}

export default Requests
