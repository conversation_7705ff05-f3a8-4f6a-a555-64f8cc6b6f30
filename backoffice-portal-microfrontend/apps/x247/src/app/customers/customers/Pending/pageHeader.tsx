import { Stack } from '@mui/material'
import React, { useEffect, useState } from 'react'
import dayjs from 'dayjs'
import { useAppDispatch, useAppSelector } from '@/store'
import { fetchPendingCustomerApprovals } from '@/store/actions'
import { CustomFilterBox } from '@/app/approval-requests/CustomFilterBox'
import { IFilter } from '@dtbx/store/interfaces'

const PageHeader = ({
  page,
  setPage,
}: {
  page: number
  setPage: (value: number) => void
}) => {
  const dispatch = useAppDispatch()

  const [selectedFilters, setSelectedFilters] = useState<
    Record<string, string | string[]>
  >({ Module: 'Customers' })
  const [openFilterBar, setOpenFilterBar] = useState<boolean>(false)
  const { searchUserValue } = useAppSelector((state) => state.users)
  const [searchValue, setSearchValue] = useState<string>(searchUserValue)

  const { search } = useAppSelector((state) => state.customers)

  const handleDateRangeFilterApply = (date: {
    start: dayjs.Dayjs
    end: dayjs.Dayjs
  } | null) => {
    setDateRange(date)
    filterRequests(selectedFilters, date)
  }

  const handleSearch = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const search = e.target.value
    setSearchValue(search)
    filterRequests(selectedFilters, dateRange)
  }

  const setMakerNameValue = async (makerName: string) => {
    setSelectedFilters({
      ...selectedFilters,
      makerName,
    })
    filterRequests(
      {
        ...selectedFilters,
        makerName,
      },
      dateRange
    )
  }

  const filterRequests = async (
    filter: Record<string, string | string[]>,
    date: {
      start: dayjs.Dayjs
      end: dayjs.Dayjs
    } | null
  ) => {
    fetchPendingCustomerApprovals(dispatch, {
      page,
      size: 10,
      startDate: date ? date.start.format('YYYY-MM-DD') : '',
      endDate: date ? date.end.format('YYYY-MM-DD') : '',
      maker: filter?.makerName,
      makerSearchType: search?.searchBy[0] === 'firstName' ? 'makerFirstName' : search?.searchBy[0] === 'lastName' ? 'makerLastName' : ''
    })
  }

  // filters
  useEffect(() => {
    filterRequests(selectedFilters, dateRange)
  }, [page])

  useEffect(() => {
    fetchPendingCustomerApprovals(dispatch, {
      page: 1,
      size: 10,
    })
  }, [])

  const [dateRange, setDateRange] = useState<{
    start: dayjs.Dayjs
    end: dayjs.Dayjs
  } | null>(null)
  const filters: IFilter[] = [
    {
      filterName: 'Date created',
      options: [
        {
          label: 'Date created',
          value: 'dateCreated',
          key: '',
        },
      ],
      type: 'date',
    },
  ]

  return (
    <Stack
      sx={{
        flexDirection: 'column',
        justifyContent: 'space-between',
        gap: '16px',
      }}
    >
      <Stack
        sx={{
          flexDirection: 'row',
          justifyContent: 'flex-start',
          alignItems: 'flex-start',
          gap: '18px',
          flex: 1,
        }}
      >
        <CustomFilterBox
          openFilter={openFilterBar}
          setOpenFilter={setOpenFilterBar}
          searchValue={searchValue}
          searchByValues={['First name', 'Last name', 'email']}
          handleSearch={handleSearch}
          filters={filters}
          onFilterChange={(filter) => {
            setPage(1)
            filterRequests(
              Object.keys(filter).length
                ? {
                    ...selectedFilters,
                    ...filter,
                    makerID: selectedFilters?.makerID,
                  }
                : {},
              dateRange
            )
          }}
          setDate={handleDateRangeFilterApply}
          setMakerName={setMakerNameValue}
        />
      </Stack>
    </Stack>
  )
}

export default PageHeader
