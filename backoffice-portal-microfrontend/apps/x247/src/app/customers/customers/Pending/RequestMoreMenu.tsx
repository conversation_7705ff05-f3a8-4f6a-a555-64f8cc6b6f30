import KeyboardArrowDownIcon from '@mui/icons-material/KeyboardArrowDown'
import { Button, Menu, MenuItem } from '@mui/material'
import { useRouter } from 'next/navigation'
import React, { useState } from 'react'
import { IApprovalRequest } from '@/store/interfaces'
import { useAppDispatch } from '@/store'

import { ApprovalRequestRouting } from '@/app/approval-requests/RequestRouting'
import { setApprovalDrawerOpen } from '@/store/reducers'
import { useCustomRouter } from '@dtbx/ui/hooks'

export const RequestMoreMenu = ({ request }: { request: IApprovalRequest }) => {
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null)
  const open = Boolean(anchorEl)
  const dispatch = useAppDispatch()
  const router = useCustomRouter()
  const handleClick = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget)
  }
  const handleClose = () => {
    setAnchorEl(null)
  }
  const handleReviewRequest = async () => {
    await ApprovalRequestRouting(request, dispatch, router)
    dispatch(setApprovalDrawerOpen(true))
    handleClose()
  }
  return (
    <>
      <Button
        id="demo-customized-button"
        aria-controls={open ? 'demo-customized-menu' : undefined}
        aria-haspopup="true"
        aria-expanded={open ? 'true' : undefined}
        variant="outlined"
        disableElevation
        onClick={handleClick}
        sx={{
          border: '1px solid #D0D5DD',
          padding: '8px 14px',
          fontWeight: '500',
          gap: 0,
        }}
        endIcon={<KeyboardArrowDownIcon />}
      >
        Actions
      </Button>
      <Menu
        open={open}
        id="demo-customized-menu"
        slotProps={{
          list: {
            'aria-labelledby': 'demo-customized-button',
          },
        }}
        anchorEl={anchorEl}
        onClose={handleClose}
      >
        <MenuItem
          onClick={handleReviewRequest}
          sx={{ display: `${request.status !== 'PENDING' ? 'none' : 'block'}` }}
        >
          Review Approval request
        </MenuItem>
      </Menu>
    </>
  )
}
