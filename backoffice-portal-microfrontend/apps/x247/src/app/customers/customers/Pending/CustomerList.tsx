'use client'
import {
  Avatar,
  Box,
  Button,
  Paper,
  Stack,
  styled,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableRow,
  Typography,
} from '@mui/material'
import React, { useEffect, useState } from 'react'
import { sentenceCase } from 'tiny-case'
import { setCustomerApprovalBarOpen } from '@/store/reducers'
import {
  CustomPagination,
  CustomTableHeader,
  PaginationOptions,
} from '@dtbx/ui/components/Table'
import { useAppDispatch, useAppSelector } from '@/store'
import { IHeadCell } from '@dtbx/store/interfaces'
import { ICustomerAccountDetails } from '@/store/interfaces'
import { CustomerStatusChip } from '@dtbx/ui/components/Chip'
import { CustomSkeleton } from '@dtbx/ui/components/Loading'

import { RequestMoreMenu } from './RequestMoreMenu'
import PageHeader from './pageHeader'
import NoCustomerFound from './NoCustomerFound'
import { formatTimestamp } from '@dtbx/store/utils'

export const CellButton = styled(Button)(
  () => `
  width: auto;
  height: auto;
  padding: 10px 28px;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 8px;
  border-radius: 4px;
  border: 1px solid #AAADB0;
  background: #FFF;
  .& M
`
)

const headCellItems: IHeadCell[] = [
  {
    id: 'name',
    label: 'Name',
    alignCenter: false,
    alignRight: false,
  },
  {
    id: 'status',
    label: 'Status',
    alignCenter: false,
    alignRight: false,
  },
  {
    id: 'phone',
    label: 'Phone Number',
    alignCenter: false,
    alignRight: false,
  },
  {
    id: 'idNumber',
    label: 'ID Number',
    alignCenter: false,
    alignRight: false,
  },
  {
    id: 'maker',
    label: 'Maker',
    alignCenter: false,
    alignRight: false,
  },
  {
    id: 'createdOn',
    label: 'Date Created',
    alignCenter: false,
    alignRight: false,
  },
  {
    id: 'actions',
    label: 'Actions',
    alignCenter: false,
    alignRight: false,
  },
]
const List = () => {
  const dispatch = useAppDispatch()
  const { pendingCustomerApprovalRequestResponse, isLoadingRequests } =
    useAppSelector((state) => state.approvalRequests)

  const [page, setPage] = useState(1)
  const [paginationOptions, setPaginationOptions] = useState({
    page: pendingCustomerApprovalRequestResponse.pageNumber,
    size: 10,
    totalPages: pendingCustomerApprovalRequestResponse.totalNumberOfPages,
  })
  /*************************start pagination handlers***************************/
  const handlePagination = async (newOptions: PaginationOptions) => {
    setPaginationOptions(newOptions) // Update parent state
    setPage(newOptions.page)
  }
  /*************************end pagination handlers**************************/
  const pendingCustomerData = () => {
    const requests: ICustomerAccountDetails[] = []
    pendingCustomerApprovalRequestResponse?.data?.map((request) => {
      requests.push({ ...JSON.parse(request?.entity || '') })
    })
    return requests
  }
  const requestRow = (index: number) => {
    return pendingCustomerApprovalRequestResponse?.data[index]
  }
  useEffect(() => {
    localStorage.removeItem('tab')
    localStorage.removeItem('customerId')
    dispatch(setCustomerApprovalBarOpen(false))
  }, [])
  return (
    <Stack
      sx={{
        padding: '1.5% 2% 1.5% 2%',
        flexDirection: 'column',
        gap: '16px',
      }}
    >
      <PageHeader page={page} setPage={setPage} />
      {isLoadingRequests ? (
        <CustomSkeleton
          animation="pulse"
          variant="rectangular"
          width={'100%'}
          height={'60vh'}
        />
      ) : pendingCustomerApprovalRequestResponse?.data?.length <= 0 ? (
        <NoCustomerFound />
      ) : (
        <Paper
          elevation={0}
          sx={{
            boxShadow:
              '0px 1px 3px 0px rgba(16, 24, 40, 0.10), 0px 1px 2px 0px rgba(16, 24, 40, 0.06)',
            borderRadius: '4px',
            border: '1px solid #EAECF0',
            background: '#FFFFFF',
          }}
        >
          <TableContainer component={Paper} elevation={0}>
            <Table stickyHeader sx={{}}>
              <CustomTableHeader
                order={'desc'}
                orderBy={''}
                rowCount={0}
                headLabel={headCellItems}
                numSelected={0}
              />
              <TableBody>
                {!isLoadingRequests &&
                  pendingCustomerApprovalRequestResponse?.data &&
                  pendingCustomerData().map(
                    (row: ICustomerAccountDetails, index: number) => {
                      return (
                        <TableRow key={index || row.cif}>
                          <TableCell
                            sx={{
                              padding: '10px 24px 10px 16px',
                              display: 'flex',
                              gap: '12px',
                            }}
                          >
                            <Avatar>
                              {(row.firstName ? row.firstName[0] : 'O') +
                                (row.lastName ? row.lastName[0] : 'O')}
                            </Avatar>
                            <Box>
                              <Typography
                                variant="body2"
                                sx={{
                                  color: '#000A12',
                                }}
                              >
                                {(row.firstName
                                  ? sentenceCase(row.firstName)
                                  : 'No-FirstName') +
                                  ' ' +
                                  (row.lastName
                                    ? sentenceCase(row.lastName)
                                    : 'No-LastName')}
                              </Typography>
                              <Typography
                                variant="body2"
                                sx={{
                                  fontWeight: 400,
                                }}
                              >
                                {row.email ? row.email : 'No-email'}
                              </Typography>
                            </Box>
                          </TableCell>
                          <TableCell
                            sx={{
                              padding: '10px 24px 10px 16px',
                            }}
                          >
                            <CustomerStatusChip label={'PENDING'} />
                          </TableCell>
                          <TableCell>{row.phoneNumber}</TableCell>
                          <TableCell>
                            {row.idValue ? row.idValue : 'No-ID Number'}
                          </TableCell>
                          <TableCell>{requestRow(index)?.maker}</TableCell>
                          <TableCell>
                            <Typography
                              variant="body2"
                              sx={{
                                fontWeight: 400,
                              }}
                            >
                              {formatTimestamp(requestRow(index)?.dateCreated)}
                            </Typography>
                          </TableCell>
                          <TableCell
                            // padding="none"
                            sx={{
                              padding: '0px 24px',
                            }}
                          >
                            <RequestMoreMenu request={requestRow(index)} />
                          </TableCell>
                        </TableRow>
                      )
                    }
                  )}
              </TableBody>
            </Table>
          </TableContainer>
          {pendingCustomerApprovalRequestResponse.totalNumberOfPages > 0 && (
            <CustomPagination
              options={{
                ...paginationOptions,
                totalPages:
                  pendingCustomerApprovalRequestResponse.totalNumberOfPages,
              }}
              handlePagination={handlePagination}
            />
          )}
        </Paper>
      )}
    </Stack>
  )
}

export default List
