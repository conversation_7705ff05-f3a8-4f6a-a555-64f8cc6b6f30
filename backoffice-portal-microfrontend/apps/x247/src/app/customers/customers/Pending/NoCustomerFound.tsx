import { sentenceCase } from 'tiny-case'
import { Box, Button, Stack, Typography } from '@mui/material'
import Image from 'next/image'
import React from 'react'
import { useAppSelector } from '@/store'

import { CreateCustomerDialog } from '@/app/customers/customers/Create'

const NoCustomerFound = () => {
  const { search, selectedFilters } = useAppSelector((state) => state.customers)

  return (
    <Stack
      sx={{
        height: '100%',
        width: '100%',
        justifyContent: 'center',
        alignItems: 'center',
        padding: '10px',
      }}
    >
      <Stack
        sx={{
          width: '480px',
          height: '480px',
          backgroundImage: 'url(/dashboard/background-pattern.svg)',
          justifyContent: 'flex-end',

          gap: '45px',
        }}
      >
        <Stack
          sx={{
            justifyContent: 'center',
            alignItems: 'center',
          }}
        >
          <Box
            sx={{
              width: '56px',
              height: '56px',
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
              borderRadius: '12px',
              border: '1px solid #EAECF0',
            }}
          >
            <Image
              src={'/dashboard/icons/search-lg-2-x.svg'}
              alt="search"
              width={28}
              height={28}
            />
          </Box>
        </Stack>
        <Stack
          sx={{
            justifyContent: 'center',
            alignItems: 'center',
            gap: '32px',
          }}
        >
          {/*header  */}
          <Stack
            sx={{
              justifyContent: 'center',
              alignItems: 'center',
              gap: '8px',
              width: '65%',
            }}
          >
            <Typography variant="h6">No pending customers found</Typography>

            <Typography
              variant="subtitle3"
              sx={{
                textAlign: 'center',
              }}
            >
              Your{' '}
              {search && search.searchValue.length > 0
                ? `search by ${search.searchBy && sentenceCase(search.searchBy[0])} for "${search.searchValue}"  `
                : search.searchValue.length > 0 && selectedFilters.length > 0
                  ? `search by ${search.searchBy} for "${search.searchValue}" and filter criteria`
                  : 'filter criteria'}{' '}
              did not match any customers. Please try again or create a new
              customer profile.
            </Typography>
          </Stack>

          {/* buttons */}
          <Stack
            sx={{
              justifyContent: 'center',
              flexDirection: 'row',
              gap: '20px',
              marginTop: '20px',
            }}
          >
            <Button variant="outlined">Clear search</Button>

            <CreateCustomerDialog />
          </Stack>
        </Stack>
      </Stack>
    </Stack>
  )
}

export default NoCustomerFound
