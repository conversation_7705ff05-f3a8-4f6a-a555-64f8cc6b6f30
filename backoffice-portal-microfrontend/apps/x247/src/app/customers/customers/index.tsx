'use client'
import { <PERSON>, Divider, Stack, Typography } from '@mui/material'
import React, { useState } from 'react'
import { AntTab, AntTabs, TabPanel } from '@dtbx/ui/components/Tabs'
import { useAppSelector } from '@/store'

import List from './CustomerLists/CustomerList'
import Requests from './Requests/Requests'
import Pending from './Pending/CustomerList'

const CustomersListPage = () => {
  const { approvalRequestResponse, pendingCustomerApprovalRequestResponse } =
    useAppSelector((state) => state.approvalRequests)

  const approvalCounts = approvalRequestResponse?.totalElements
    ? approvalRequestResponse?.totalElements
    : ''
  const pendingCustomerApprovalCounts =
    pendingCustomerApprovalRequestResponse?.totalElements
      ? pendingCustomerApprovalRequestResponse?.totalElements
      : ''
  const [value, setValue] = useState<number>(0)
  const handleChange = (event: React.SyntheticEvent, newValue: number) => {
    setValue(newValue)
  }

  return (
    <Stack
      sx={{
        backgroundColor: '#FFFFFF',
      }}
    >
      <AntTabs
        value={value}
        onChange={handleChange}
        data-testid="ant-tabs"
        aria-label="ant example"
        sx={{
          marginLeft: '2%',
        }}
      >
        <AntTab
          label={`All Customers`}
          data-testid="tab-All Customers"
          sx={{
            marginBottom: '-10px',
          }}
        />
        <AntTab
          label={`Pending Creation`}
          data-testid="tab-Pending Creation"
          sx={{
            marginBottom: '-10px',
          }}
          icon={
            <Chip
              label={
                <Typography
                  variant="label3"
                  sx={{
                    textAlign: 'center',
                    fontWeight: 500,
                  }}
                >
                  {pendingCustomerApprovalCounts}
                </Typography>
              }
              sx={{
                borderRadius: '12px',
                minWidth: '39px',
                height: '22px',
                border: '1px solid  #EAECF0',
                background: ' #F9FAFB',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                padding: '2px 8px',
              }}
            />
          }
          iconPosition="end"
        />
        <AntTab
          label={`Approval Requests`}
          data-testid="tab-Approval Requests"
          sx={{
            marginBottom: '-10px',
            // display: 'none',
          }}
          icon={
            <Chip
              label={
                <Typography
                  variant="label3"
                  sx={{
                    textAlign: 'center',
                    fontWeight: 500,
                  }}
                >
                  {approvalCounts}
                </Typography>
              }
              sx={{
                borderRadius: '12px',
                minWidth: '39px',
                height: '22px',
                border: '1px solid  #EAECF0',
                background: ' #F9FAFB',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',

                padding: '2px 8px',
              }}
            />
          }
          iconPosition="end"
        />
      </AntTabs>
      <Divider />
      <TabPanel value={value} index={0} data-testid="tab-panel-0">
        <List />
      </TabPanel>
      <TabPanel value={value} index={1} data-testid="tab-panel-1">
        <Pending />
      </TabPanel>
      <TabPanel value={value} index={2} data-testid="tab-panel-2">
        <Requests />
      </TabPanel>
    </Stack>
  )
}

export default CustomersListPage
