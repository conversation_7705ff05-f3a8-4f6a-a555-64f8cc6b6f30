import { ArrowOut<PERSON>, Edit } from '@mui/icons-material'
import ArrowBackIcon from '@mui/icons-material/ArrowBack'
import CircularProgress from '@mui/material/CircularProgress'
import ArrowForwardIcon from '@mui/icons-material/ArrowForward'
import {
  Button,
  Chip,
  Divider,
  Paper,
  Stack,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TextField,
  Typography,
} from '@mui/material'
import React, { useState } from 'react'
import { useAppDispatch, useAppSelector } from '@/store'
import { ICustomerAccount } from '@/store/interfaces'
import { setSelectedCustomerSummaryAccount } from '@/store/reducers'
import { accountType, createCustomerAccount } from '@/store/actions'
import { AccountsSummaryIcon, CustomerSettingsIcon } from '@dtbx/ui/icons'

import { IPersonalDetailsProps } from '@/app/customers/customers/Create/PersonalDetails'
import { HasAccessToRights } from '@dtbx/store/utils'

export const CustomChip = ({ prop }: { prop: string }) => {
  return (
    <Chip
      sx={{
        fontWeight: '500',
        padding: '2px 4px 2px 6px',
        justifyContent: 'center',
        alignItems: 'center',
        gap: '6px',
        background: '#FFF',
        backgroundColor: '#F3F5F5',
        color: '#2A3339',
        fontStyle: 'normal',
        textAlign: 'center',
        minWidth: '56px',
        height: '22px',
        marginRight: '10px',
      }}
      label={prop}
    />
  )
}

export const Summary = ({ formik, setStep }: IPersonalDetailsProps) => {
  const dispatch = useAppDispatch()
  const [currentView, setCurrentView] = useState<string>('')
  const accountDetails = useAppSelector(
    (state) => state.customers.customerAccountDetails
  )
  const loading = useAppSelector((state) => state.customers.isCustomerLoading)
  const changeAccountView = (account: ICustomerAccount) => {
    setCurrentView('Accounts')
    dispatch(setSelectedCustomerSummaryAccount(account))
  }
  return (
    <Stack
      sx={{
        justifyContent: 'center',
        alignItems: 'center',
      }}
      direction="column"
      data-testid="summary-step"
    >
      <Stack
        sx={{
          alignContent: 'center',
          alignItems: 'center',
          py: '2%',
          gap: '1vh',
          width: '60%',
        }}
      >
        <Typography variant="body1" color="primary.main">
          STEP 3 OF 3
        </Typography>
        <Typography variant="h6">Summary</Typography>
        <Typography
          variant="label1"
          textAlign="center"
          color="primary.primary3"
        >
          Review, edit if need be then submit to checker.
        </Typography>
      </Stack>
      <Stack
        sx={{
          width: '100%',
          px: '3%',
        }}
        direction="row"
        gap={'2vh'}
      >
        <Stack
          sx={{
            width: '50%',
            gap: '2vh',
          }}
        >
          <Stack
            sx={{
              py: '2%',
              px: '3%',
              border: '1px solid #E0E0E0',
              borderRadius: '4px',
              background: '#F9FAFB',
              flexDirection: 'row',
              justifyContent: 'space-between',
            }}
          >
            <Stack
              sx={{
                flexDirection: 'row',
                justifyContent: 'space-between',
                alignContent: 'center',
                alignItems: 'center',
                gap: '10px',
              }}
            >
              <CustomerSettingsIcon />
              <Typography
                variant="subtitle2"
                color={'text.primary'}
                sx={{
                  textWrap: 'noWrap',
                }}
              >
                {accountDetails.firstName} {accountDetails.lastName}
              </Typography>
            </Stack>
            <Button
              variant="outlined"
              size="small"
              sx={{
                py: '2px',
                borderRadius: '8px',
                borderColor: '#E3E4E4',
              }}
              onClick={() => setCurrentView('Personal')}
              endIcon={<ArrowOutward />}
            >
              Profile Details
            </Button>
          </Stack>
          {formik.values.customerAccounts.map((account) => (
            <Stack
              key={account.accNumber}
              sx={{
                py: '2%',
                px: '3%',
                border: '1px solid #E0E0E0',
                borderRadius: '4px',
                background: '#F9FAFB',
                flexDirection: 'row',
                justifyContent: 'space-between',
              }}
            >
              <Stack
                sx={{
                  flexDirection: 'row',
                  justifyContent: 'space-between',
                  alignContent: 'center',
                  alignItems: 'center',
                  gap: '10px',
                }}
              >
                <AccountsSummaryIcon />
                <Typography
                  variant="subtitle2"
                  color={'text.primary'}
                  sx={{
                    textWrap: 'noWrap',
                  }}
                >
                  Account {account.accNumber}
                </Typography>
              </Stack>
              <Button
                variant="outlined"
                size="small"
                sx={{
                  py: '2px',
                  borderRadius: '8px',
                  borderColor: '#E3E4E4',
                }}
                endIcon={<ArrowOutward />}
                onClick={() => changeAccountView(account)}
              >
                Account Details
              </Button>
            </Stack>
          ))}
          <Stack>
            <TextField
              {...formik.getFieldProps('comments')}
              label={'Comments'}
              multiline
              rows={5}
              error={Boolean(formik.touched.comments && formik.errors.comments)}
              helperText={formik.touched.comments && formik.errors.comments}
            />
          </Stack>
        </Stack>
        <Divider
          orientation="vertical"
          variant="middle"
          sx={{
            height: 'auto',
          }}
        />
        <Stack
          sx={{
            width: '50%',
          }}
        >
          {(() => {
            switch (currentView) {
              case 'Personal':
                return <ProfileView setStep={setStep} />
              case 'Accounts':
                return <AccountsView setStep={setStep} />
              default:
                return <ProfileView setStep={setStep} />
            }
          })()}
        </Stack>
      </Stack>
      <Stack
        sx={{ width: '70%', py: '3vh' }}
        direction="row"
        justifyContent="space-between"
        gap="4%"
      >
        <Button
          variant="outlined"
          fullWidth
          onClick={() => setStep('Link and setup accounts')}
          startIcon={<ArrowBackIcon />}
        >
          Back
        </Button>
        <Button
          variant="contained"
          fullWidth
          type={'submit'}
          endIcon={<ArrowForwardIcon />}
          disabled={loading || Object.keys(formik.errors).length > 0}
        >
          {loading ? (
            <CircularProgress
              size={20}
              sx={{
                marginRight: 1,
              }}
            />
          ) : HasAccessToRights(['SUPER_CREATE_CUSTOMERS']) ? (
            'Submit'
          ) : (
            'Submit to checker'
          )}
        </Button>
      </Stack>
    </Stack>
  )
}

export const ProfileView = ({
  setStep,
}: {
  setStep: (step: string) => void
}) => {
  const accountDetails = useAppSelector(
    (state) => state.customers.customerAccountDetails
  )
  return (
    <Stack>
      <Stack direction="row" justifyContent={'space-between'}>
        <Typography>Profile Details</Typography>
        <Stack
          sx={{
            flexDirection: 'row',
            marginBottom: '5px',
          }}
        >
          <Button
            variant="outlined"
            endIcon={<Edit />}
            size="small"
            sx={{
              borderColor: '#E3E4E4',
              height: '30px',
            }}
            onClick={() => setStep('Personal Details')}
          >
            Edit
          </Button>
        </Stack>
      </Stack>
      <TableContainer
        component={Paper}
        sx={{
          border: '1px solid #EAECF0',
          borderRadius: '4px',
        }}
        elevation={0}
      >
        <Table sx={{}} aria-label="simple table">
          <TableHead
            sx={{
              background: '#F9FAFB',
            }}
          >
            <TableRow>
              <TableCell>Field</TableCell>
              <TableCell>Data</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            <TableCell>First Name</TableCell>
            <TableCell>{accountDetails.firstName}</TableCell>
          </TableBody>
          <TableBody>
            <TableCell>Last Name</TableCell>
            <TableCell>{accountDetails.lastName}</TableCell>
          </TableBody>
          <TableBody>
            <TableCell>Gender</TableCell>
            <TableCell>
              {accountDetails.gender === 'F' ? 'Female' : 'Male'}
            </TableCell>
          </TableBody>
          <TableBody>
            <TableCell>Phone number</TableCell>
            <TableCell>{accountDetails.phoneNumber}</TableCell>
          </TableBody>
          <TableBody>
            <TableCell>Email</TableCell>
            <TableCell>{accountDetails.email}</TableCell>
          </TableBody>
          <TableBody>
            <TableCell>ID type</TableCell>
            <TableCell>{accountDetails.idType}</TableCell>
          </TableBody>
          <TableBody>
            <TableCell>ID number</TableCell>
            <TableCell>{accountDetails.idValue}</TableCell>
          </TableBody>
          <TableBody>
            <TableCell>CIF number</TableCell>
            <TableCell>{accountDetails.cif}</TableCell>
          </TableBody>
          <TableBody>
            <TableCell>Postal address</TableCell>
            <TableCell>{accountDetails.postalAddress}</TableCell>
          </TableBody>
          <TableBody>
            <TableCell>Physical address</TableCell>
            <TableCell>{accountDetails.physicalAddress}</TableCell>
          </TableBody>
        </Table>
      </TableContainer>
    </Stack>
  )
}

export const AccountsView = ({
  setStep,
}: {
  setStep: (step: string) => void
}) => {
  const accountDetails = useAppSelector(
    (state) => state.customers.customerAccountDetails
  )
  const summaryCustomerAccount = useAppSelector(
    (state) => state.customers.selectedCustomerSummaryAccount
  )
  const { bankBranches } = useAppSelector((state) => state.loans)
  const bankBranch = (code: string) => {
    const branch = bankBranches.find((branch) => branch.branchCode === code)
    return branch?.branchName ? branch.branchName : 'No branch'
  }
  return (
    <Stack direction="column" gap="2vh">
      <>
        <Stack direction="row" justifyContent={'space-between'}>
          <Typography>Accounts Details</Typography>
          <Stack
            sx={{
              flexDirection: 'row',
              marginBottom: '5px',
            }}
          >
            <Button
              variant="outlined"
              endIcon={<Edit />}
              size="small"
              sx={{
                borderColor: '#E3E4E4',
                height: '30px',
              }}
              onClick={() => setStep('Link and setup accounts')}
            >
              Edit
            </Button>
          </Stack>
        </Stack>
        <TableContainer
          component={Paper}
          sx={{
            border: '1px solid #EAECF0',
            borderRadius: '4px',
          }}
          elevation={0}
        >
          <Table sx={{}} aria-label="simple table">
            <TableHead
              sx={{
                background: '#F9FAFB',
              }}
            >
              <TableRow>
                <TableCell>Field</TableCell>
                <TableCell>Data</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              <TableCell>Account name</TableCell>
              <TableCell>{summaryCustomerAccount.accClassDesc}</TableCell>
            </TableBody>
            <TableBody>
              <TableCell>Account number</TableCell>
              <TableCell>{summaryCustomerAccount.accNumber}</TableCell>
            </TableBody>
            <TableBody>
              <TableCell>Type</TableCell>
              <TableCell>
                {accountType(summaryCustomerAccount.customerType)}
              </TableCell>
            </TableBody>
            <TableBody>
              <TableCell>Tariff</TableCell>
              <TableCell>{summaryCustomerAccount.tariffName}</TableCell>
            </TableBody>
            <TableBody>
              <TableCell>Currency</TableCell>
              <TableCell>{summaryCustomerAccount.accCurrency}</TableCell>
            </TableBody>
            <TableBody>
              <TableCell>Branch</TableCell>
              <TableCell>
                {bankBranch(summaryCustomerAccount.accBranchCode)}
              </TableCell>
            </TableBody>
            <TableBody>
              <TableCell>Phone number</TableCell>
              <TableCell>{accountDetails.phoneNumber}</TableCell>
            </TableBody>
          </Table>
        </TableContainer>
      </>
      <>
        <Stack direction="row" justifyContent={'space-between'}>
          <Typography>Subscriptions (E-statements)</Typography>
        </Stack>
        <TableContainer
          component={Paper}
          sx={{
            border: '1px solid #EAECF0',
            borderRadius: '4px',
          }}
          elevation={0}
        >
          <Table sx={{}} aria-label="simple table">
            <TableHead
              sx={{
                background: '#F9FAFB',
              }}
            >
              <TableRow>
                <TableCell>Type</TableCell>
                <TableCell>Email</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              <TableCell>Daily</TableCell>
              <TableCell>
                <CustomChip prop={accountDetails.email} />
              </TableCell>
            </TableBody>
            <TableBody>
              <TableCell>Loan Installment Due</TableCell>
              <TableCell>
                <CustomChip prop={accountDetails.email} />
              </TableCell>
            </TableBody>
          </Table>
        </TableContainer>
      </>
      <>
        <Stack direction="row" justifyContent={'space-between'}>
          <Typography>Notifications</Typography>
        </Stack>
        <TableContainer
          component={Paper}
          sx={{
            border: '1px solid #EAECF0',
            borderRadius: '4px',
          }}
          elevation={0}
        >
          <Table sx={{}} aria-label="simple table">
            <TableHead
              sx={{
                background: '#F9FAFB',
              }}
            >
              <TableRow>
                <TableCell>Type</TableCell>
                <TableCell>Channels</TableCell>
                <TableCell>Phone Numbers</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              <TableCell>Credit & Debit</TableCell>
              <TableCell>
                <CustomChip prop={'SMS'} />
                <CustomChip prop={'Email'} />
              </TableCell>
              <TableCell>
                <CustomChip prop={accountDetails.phoneNumber} />
              </TableCell>
            </TableBody>
            <TableBody>
              <TableCell>Loan Installment Due</TableCell>
              <TableCell>
                <CustomChip prop={'SMS'} />
              </TableCell>
              <TableCell>
                <CustomChip prop={accountDetails.phoneNumber} />
              </TableCell>
            </TableBody>
          </Table>
        </TableContainer>
      </>
      <>
        <Stack direction="row" justifyContent={'space-between'}>
          <Typography>Alerts</Typography>
        </Stack>
        <TableContainer
          component={Paper}
          sx={{
            border: '1px solid #EAECF0',
            borderRadius: '4px',
          }}
          elevation={0}
        >
          <Table sx={{}} aria-label="simple table">
            <TableHead
              sx={{
                background: '#F9FAFB',
              }}
            >
              <TableRow>
                <TableCell>Type</TableCell>
                <TableCell>Channels</TableCell>
                <TableCell>Phone Number</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              <TableCell>Daily</TableCell>
              <TableCell>
                <CustomChip prop={'SMS'} />
              </TableCell>
              <TableCell>
                <CustomChip prop={accountDetails.phoneNumber} />
              </TableCell>
            </TableBody>
            <TableBody>
              <TableCell>Weekly</TableCell>
              <TableCell>
                <CustomChip prop={'SMS'} />
              </TableCell>
              <TableCell>
                <CustomChip prop={accountDetails.phoneNumber} />
              </TableCell>
            </TableBody>
          </Table>
        </TableContainer>
      </>
    </Stack>
  )
}
