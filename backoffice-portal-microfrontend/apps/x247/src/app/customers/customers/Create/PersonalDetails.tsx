import ArrowForwardIcon from '@mui/icons-material/ArrowForward'
import SearchOutlinedIcon from '@mui/icons-material/SearchOutlined'
import InfoOutlinedIcon from '@mui/icons-material/InfoOutlined'
import CircularProgress from '@mui/material/CircularProgress'
import { FormikProps, useFormik } from 'formik'
import {
  Autocomplete,
  Button,
  InputAdornment,
  Paper,
  Stack,
  TextField,
  Typography,
} from '@mui/material'
import React, { useCallback, useEffect } from 'react'
import _ from 'lodash'
import { useAppDispatch, useAppSelector } from '@/store'
import { ICustomerAccountDetails } from '@/store/interfaces'
import { useCustomRouter } from '@dtbx/ui/hooks'
import {
  setChangeTab,
  setCustomerAccountSearch,
  setCustomerProfileAccount,
  setIsViewAccountOpen,
} from '@/store/reducers'
import {
  fetchCustomerAccount,
  getCustomerProfile,
  resetCustomerAccountDetails,
} from '@/store/actions'
import { CustomSearchInput } from '@dtbx/ui/components/Input'

export interface IPersonalDetailsProps {
  formik: FormikProps<ICustomerAccountDetails>
  setStep: (step: string) => void
  setOpen: (val: boolean) => void
}
export const PersonalDetails = ({
  formik,
  setStep,
  setOpen,
}: IPersonalDetailsProps) => {
  const dispatch = useAppDispatch()
  const router = useCustomRouter()
  const { bankBranches } = useAppSelector((state) => state.loans)
  const accountDetails = useAppSelector(
    (state) => state.customers.customerAccountDetails
  )
  const searchAccountNumber = useAppSelector(
    (state) => state.customers.customerAccountSearch.searchValue
  )
  const profileExists = useAppSelector(
    (state) => state.customers.customerProfileExists
  )
  const profileDetails = useAppSelector((state) => state.customers.customer)
  const customerLinkedAccounts = useAppSelector(
    (state) => state.customers.customerLinkedAccountsList
  )
  const loading = useAppSelector((state) => state.customers.isCustomerLoading)
  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    dispatch(
      setCustomerAccountSearch({
        searchValue: e.target.value.replaceAll(' ', ''),
      })
    )
    e.target.value.replaceAll(' ', '').length >= 8 &&
      debouncedSearch(e.target.value.replaceAll(' ', ''))
  }
  const fetchAccount = async (value: string) => {
    await fetchCustomerAccount({ account: value, dispatch })
  }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  const debouncedSearch = useCallback(
    _.debounce((value: string) => fetchAccount(value), 1000),
    []
  )

  const accountFetched = () => {
    return Object.keys(accountDetails).length > 0 && accountDetails.phoneNumber
  }

  const accountsLinked = () => {
    return customerLinkedAccounts.length > 0
  }

  const linkedAccount = () => {
    return customerLinkedAccounts[0]
  }

  const goToProfile = async () => {
    if (profileDetails.id) {
      setOpen(false)
      await getCustomerProfile(profileDetails.id, dispatch)
      router.push(`/customers/customer`)
      dispatch(setChangeTab(0))
    }
  }

  const goToAccount = async () => {
    await goToProfile()
    dispatch(setChangeTab(3))
    dispatch(setIsViewAccountOpen(true))
    dispatch(setCustomerProfileAccount(linkedAccount()))
  }
  useEffect(() => {
    const values = {
      ...accountDetails,
      gender: accountDetails.gender
        ? accountDetails.gender === 'F'
          ? 'Female'
          : 'Male'
        : '',
      idType: accountDetails.idType === 'ID_NO' ? 'NationalId' : 'Passport',
      customerAccounts: formik.values.customerAccounts
        ? formik.values.customerAccounts
        : [],
    }

    if (Object.keys(accountDetails).length > 0) {
      formik.setValues(values)
    }
  }, [accountDetails])
  return (
    <Stack
      sx={{
        justifyContent: 'center',
        alignItems: 'center',
      }}
      data-testid="personal-details-step"
    >
      <Stack
        sx={{
          alignContent: 'center',
          alignItems: 'center',
          py: '2%',
          gap: '1vh',
        }}
      >
        <Typography variant="body1" color="primary.main">
          STEP 1 OF 3
        </Typography>
        <Typography variant="h6">Personal Details</Typography>
        <Typography>
          Search for customer by any of their account numbers to populate their
          details.
        </Typography>
      </Stack>
      <Stack
        sx={{
          width: '70%',
        }}
      >
        <Stack direction="row">
          <Autocomplete
            disablePortal
            size="small"
            id="customer-account-branch"
            options={bankBranches}
            sx={{
              width: '60%',
              display: 'none',
            }}
            getOptionLabel={(option) => option.branchName}
            getOptionKey={(option) => option.branchCode}
            renderInput={(params) => <TextField {...params} label="Branch" />}
          />
          <CustomSearchInput
            value={searchAccountNumber}
            onChange={handleSearch}
            placeholder="Search by account number"
            sx={{
              width: '100%',
            }}
            endAdornment={
              <InputAdornment
                sx={{
                  height: '100%',
                  width: '60px',
                  justifyContent: 'center',
                  display: 'flex',
                  marginRight: '-14px',
                  maxHeight: '100%',
                  backgroundColor: '#EDEEEE',
                }}
                position="end"
              >
                {loading ? (
                  <CircularProgress
                    size={20}
                    sx={{
                      marginRight: 1,
                    }}
                  />
                ) : (
                  <SearchOutlinedIcon
                    sx={{
                      color: 'primary.main',
                      cursor: 'pointer',
                    }}
                    onClick={() => fetchAccount(searchAccountNumber)}
                  />
                )}
              </InputAdornment>
            }
          />
        </Stack>
        {accountDetails?.isM247Customer && (
          <Paper
            sx={{
              display: 'flex',
              flexDirection: 'row',
              alignItems: 'center',
              p: '5px',
            }}
          >
            <InfoOutlinedIcon
              sx={{ color: '#EB0045', fontSize: '17px', marginRight: '5px' }}
            />
            <Typography sx={{ color: '#667085' }}>
              {`${accountDetails.firstName} ${accountDetails.lastName} has an existing m24/7 profile, kindly advice the customer to use the x247 mobile application to onboard.`}
            </Typography>
          </Paper>
        )}
        {profileExists && (
          <Paper
            elevation={0}
            sx={{
              borderRadius: '4px',
              border: '1px solid #d0d5dd',
              background: '#FFFFFF',
              marginTop: '15px',
              marginBottom: '5px',
              padding: '10px 25px',
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'flex-start',
            }}
          >
            <Stack
              sx={{
                display: 'flex',
                flexDirection: 'row',
                alignItems: 'center',
              }}
            >
              <InfoOutlinedIcon
                sx={{ color: '#EB0045', fontSize: '17px', marginRight: '5px' }}
              />
              <Typography sx={{ color: '#667085' }}>
                {`${accountDetails.firstName} ${accountDetails.lastName} already has an x24/7 profile ${accountsLinked() ? 'and has already linked this account.' : 'but has not linked an account.'}`}
              </Typography>
            </Stack>
            <Stack
              sx={{
                display: 'flex',
                flexDirection: 'row',
                alignItems: 'center',
                width: '100%',
                marginTop: '10px',
              }}
            >
              <Button
                variant="outlined"
                fullWidth
                sx={{
                  border: '1px solid #d0d5dd',
                  width: 'max-content',
                  minWidth: '45%',
                  padding: '10px 20px',
                  fontWeight: '500',
                }}
                onClick={() => goToProfile()}
              >
                Go to customer profile
              </Button>
              {accountsLinked() ? (
                <Button
                  variant="outlined"
                  fullWidth
                  sx={{
                    border: '1px solid #d0d5dd',
                    width: 'max-content',
                    minWidth: '45%',
                    padding: '10px 20px',
                    marginLeft: 'auto',
                    fontWeight: '500',
                  }}
                  onClick={() => goToAccount()}
                >
                  Go to account {linkedAccount()?.id?.accountNo}
                </Button>
              ) : (
                <Button
                  variant="outlined"
                  fullWidth
                  sx={{
                    border: '1px solid #d0d5dd',
                    width: 'max-content',
                    minWidth: '45%',
                    padding: '10px 20px',
                    marginLeft: 'auto',
                    fontWeight: '500',
                  }}
                  onClick={() => setStep('Link and setup accounts')}
                >
                  Link account {linkedAccount()?.id?.accountNo}
                </Button>
              )}
            </Stack>
          </Paper>
        )}
        <Stack direction="row" gap={'2%'}>
          <TextField
            fullWidth
            slotProps={{
              input: {
                readOnly: true,
              },
            }}
            size="small"
            margin="normal"
            label="First name"
            {...formik.getFieldProps('firstName')}
            name="firstName"
          />
          <TextField
            fullWidth
            slotProps={{
              input: {
                readOnly: true,
              },
            }}
            size="small"
            margin="normal"
            label="Last name"
            {...formik.getFieldProps('lastName')}
            name="lastName"
          />
        </Stack>
        <TextField
          margin="normal"
          slotProps={{
            input: {
              readOnly: true,
            },
          }}
          fullWidth
          size="small"
          {...formik.getFieldProps('gender')}
          label="Gender"
        />
        <TextField
          margin="normal"
          slotProps={{
            input: {
              readOnly: true,
            },
          }}
          fullWidth
          size="small"
          label="Phone Number"
          {...formik.getFieldProps('phoneNumber')}
        />
        <TextField
          margin="normal"
          slotProps={{
            input: {
              readOnly: true,
            },
          }}
          fullWidth
          size="small"
          label="Email"
          {...formik.getFieldProps('email')}
        />
        <Stack direction="row" gap={'2%'}>
          <TextField
            margin="normal"
            slotProps={{
              input: {
                readOnly: true,
              },
            }}
            fullWidth
            size="small"
            label="ID Type"
            {...formik.getFieldProps('idType')}
          />
          <TextField
            margin="normal"
            slotProps={{
              input: {
                readOnly: true,
              },
            }}
            fullWidth
            size="small"
            label="ID Number"
            {...formik.getFieldProps('idValue')}
          />
        </Stack>
        <TextField
          margin="normal"
          slotProps={{
            input: {
              readOnly: true,
            },
          }}
          fullWidth
          size="small"
          label="CIF Number"
          {...formik.getFieldProps('cif')}
        />

        <TextField
          margin="normal"
          slotProps={{
            input: {
              readOnly: true,
            },
          }}
          fullWidth
          size="small"
          label="Postal Address"
          {...formik.getFieldProps('postalAddress')}
        />
        <TextField
          margin="normal"
          slotProps={{
            input: {
              readOnly: true,
            },
          }}
          fullWidth
          size="small"
          label="Physical Address"
          {...formik.getFieldProps('physicalAddress')}
        />
      </Stack>
      <Stack
        sx={{ width: '70%', py: '3vh' }}
        direction="row"
        justifyContent="space-between"
        gap="4%"
      >
        <Button
          variant="outlined"
          fullWidth
          onClick={() => {
            setOpen(false)
            resetCustomerAccountDetails({ dispatch })
            dispatch(setCustomerAccountSearch({ searchValue: '' }))
          }}
        >
          Cancel
        </Button>
        <Button
          variant="contained"
          fullWidth
          endIcon={<ArrowForwardIcon />}
          onClick={() => setStep('Link and setup accounts')}
          disabled={
            (profileExists && accountsLinked()) ||
            loading ||
            !accountFetched() ||
            accountDetails.isM247Customer
          }
        >
          {loading ? (
            <CircularProgress
              size={20}
              sx={{
                marginRight: 1,
                color: 'black',
              }}
            />
          ) : (
            'Next'
          )}
        </Button>
      </Stack>
    </Stack>
  )
}
