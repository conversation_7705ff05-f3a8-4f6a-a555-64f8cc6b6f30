'use client'

import ArrowBackI<PERSON> from '@mui/icons-material/ArrowBack'
import ArrowF<PERSON>wardIcon from '@mui/icons-material/ArrowForward'
import { Button, Stack, Typography } from '@mui/material'
import React, { useCallback, useState } from 'react'

import { useAppSelector } from '@/store'
import { ICustomerAccount } from '@/store/interfaces'
import { AccountsCard } from '@/app/customers/customer/Details/Accounts/Create/AccountsCard'
import { IPersonalDetailsProps } from '@/app/customers/customers/Create/PersonalDetails'

export type ExpandableView =
  | 'tariffs'
  | 'notifications'
  | 'statements'
  | 'balanceAlerts'

export const Accounts = ({ formik, setStep }: IPersonalDetailsProps) => {
  const [expanded, setExpanded] = useState<string[]>([])
  const [expandedCard, setExpandedCard] = useState<string[]>([])

  const [expandedTariffs, setExpandedTariffs] = useState<string[]>([])

  const [expandedNotifications, setExpandedNotifications] = useState<string[]>(
    []
  )
  const [selectedAccounts, setSelectedAccounts] = useState<ICustomerAccount[]>(
    formik.values.customerAccounts
  )
  const [expandedStatements, setExpandedStatements] = useState<string[]>([])
  const [expandedBalanceAlerts, setExpandedBalanceAlerts] = useState<string[]>(
    []
  )
  const accountDetails = useAppSelector(
    (state) => state.customers.customerAccountDetails
  )

  const handleExpand = useCallback(
    (accNumber: string, type: ExpandableView) => {
      // Reset all expanded states to an empty array except the target view. Target view is the one we are looking to expand
      setExpanded([])
      setExpandedTariffs([])
      setExpandedNotifications([])
      setExpandedStatements([])
      setExpandedBalanceAlerts([])

      switch (type) {
        case 'tariffs':
          const isExpandedTariffs = expandedTariffs.includes(accNumber)
          setExpandedTariffs(isExpandedTariffs ? [] : [accNumber])
          break
        case 'notifications':
          const isExpandedNotifications =
            expandedNotifications.includes(accNumber)
          setExpandedNotifications(isExpandedNotifications ? [] : [accNumber])
          break
        case 'statements':
          const isExpandedStatements = expandedStatements.includes(accNumber)
          setExpandedStatements(isExpandedStatements ? [] : [accNumber])
          break
        case 'balanceAlerts':
          const isExpandedBalanceAlerts =
            expandedBalanceAlerts.includes(accNumber)
          setExpandedBalanceAlerts(isExpandedBalanceAlerts ? [] : [accNumber])
          break
      }
    },
    [
      expandedTariffs,
      expandedNotifications,
      expandedStatements,
      expandedBalanceAlerts,
    ]
  )

  const handleExpandCard = (accNumber: string) => {
    if (expandedCard.includes(accNumber)) {
      setExpandedCard(expandedCard.filter((acc) => acc !== accNumber))
      setExpanded([])
      setExpandedTariffs([])
      setExpandedNotifications([])
      setExpandedStatements([])
      setExpandedBalanceAlerts([])
    } else {
      setExpandedCard([...expandedCard, accNumber])
    }
  }

  const onUpdateAccount = (updatedAcc: ICustomerAccount, remove?: boolean) => {
    if (selectedAccounts.length === 0) {
      setSelectedAccounts([...selectedAccounts, updatedAcc])
    } else if (
      selectedAccounts.find((acc) => acc.accNumber === updatedAcc.accNumber)
    ) {
      setSelectedAccounts((prevAccounts) =>
        prevAccounts.map((acc) =>
          acc.accNumber === updatedAcc.accNumber ? updatedAcc : acc
        )
      )
    } else {
      setSelectedAccounts([...selectedAccounts, updatedAcc])
    }
  }
  const changeLinkedAccountStatus = (accNumber: string) => {
    if (selectedAccounts.find((acc) => acc.accNumber === accNumber)) {
      setSelectedAccounts(
        selectedAccounts.filter((acc) => acc.accNumber !== accNumber)
      )
    } else {
      const account = accountDetails.customerAccounts.find(
        (acc) => acc.accNumber === accNumber
      )
      if (account) {
        setSelectedAccounts([...selectedAccounts, account])
      }
    }
  }
  const activeAccounts = () => {
    const accounts: ICustomerAccount[] = []
    accountDetails.customerAccounts.forEach((account) => {
      if (account.accDormant === 'N') {
        accounts.push(account)
      }
    })
    return accounts
  }
  const dormantAccounts = () => {
    const accounts: ICustomerAccount[] = []
    accountDetails.customerAccounts.forEach((account) => {
      if (account.accDormant !== 'N') {
        accounts.push(account)
      }
    })
    return accounts
  }
  const handleNext = () => {
    formik.setFieldValue('customerAccounts', selectedAccounts)
    setStep('Summary')
  }
  return (
    <Stack
      sx={{
        justifyContent: 'center',
        alignItems: 'center',
      }}
      direction="column"
      data-testid="accounts-step"
    >
      <Stack
        sx={{
          alignContent: 'center',
          alignItems: 'center',
          py: '2%',
          gap: '1vh',
          width: '60%',
        }}
      >
        <Typography variant="body1" color="primary.main">
          STEP 2 OF 3
        </Typography>
        <Typography variant="h6">Link Accounts</Typography>
        <Typography
          variant="label1"
          textAlign="center"
          color="primary.primary3"
        >
          Click on the checkbox of the accounts you want to link, then on the
          dropdown icon to setup preferences for each account.
        </Typography>
      </Stack>
      <Stack
        flexDirection={'row'}
        gap={'2vh'}
        sx={{
          width: '90%',
        }}
      >
        <Stack
          sx={{
            width: '100%',
          }}
          gap={'2vh'}
        >
          <Typography color="primary.primary3">Active</Typography>
          {(activeAccounts() as ICustomerAccount[]).map((account) => (
            <AccountsCard
              key={account.accNumber}
              handleLinkedStatus={changeLinkedAccountStatus}
              account={account}
              selectedAccounts={selectedAccounts}
              onUpdateAccount={onUpdateAccount}
              handleExpand={handleExpand}
              handleExpandCard={handleExpandCard}
              expanded={expanded}
              expandedCard={expandedCard}
              expandedTariffs={expandedTariffs}
              expandedNotifications={expandedNotifications}
              expandedStatements={expandedStatements}
              expandedBalanceAlerts={expandedBalanceAlerts}
            />
          ))}
          {dormantAccounts().length > 0 && (
            <Stack>
              <Typography color="primary.primary3">Dormant</Typography>
              {(dormantAccounts() as ICustomerAccount[]).map((account) => (
                <AccountsCard
                  key={account.accNumber}
                  handleLinkedStatus={changeLinkedAccountStatus}
                  account={account}
                  selectedAccounts={selectedAccounts}
                  handleExpand={handleExpand}
                  handleExpandCard={handleExpandCard}
                  onUpdateAccount={onUpdateAccount}
                  expanded={expanded}
                  expandedCard={expandedCard}
                  expandedTariffs={expandedTariffs}
                  expandedNotifications={expandedNotifications}
                  expandedStatements={expandedStatements}
                  expandedBalanceAlerts={expandedBalanceAlerts}
                />
              ))}
            </Stack>
          )}
        </Stack>
      </Stack>
      <Stack
        sx={{ width: '70%', py: '3vh' }}
        direction="row"
        justifyContent="space-between"
        gap="4%"
      >
        <Button
          variant="outlined"
          fullWidth
          startIcon={<ArrowBackIcon />}
          onClick={() => setStep('Personal Details')}
        >
          Back
        </Button>
        <Button
          variant="contained"
          fullWidth
          endIcon={<ArrowForwardIcon />}
          disabled={selectedAccounts.length === 0}
          onClick={() => handleNext()}
        >
          Next
        </Button>
      </Stack>
    </Stack>
  )
}
