'use client'

import AddOutlinedIcon from '@mui/icons-material/AddOutlined'
import ArrowBackIcon from '@mui/icons-material/ArrowBack'
import CloseIcon from '@mui/icons-material/Close'
import {
  Box,
  Button,
  DialogTitle,
  Divider,
  Drawer,
  Icon<PERSON>utton,
  Stack,
  Typography,
} from '@mui/material'
import { Form, FormikProvider, useFormik } from 'formik'
import React, { useState } from 'react'
import {
  AccountsIcon,
  ConnectorIcon,
  PersonalDetailsIcon,
  SummaryIcon,
} from '@dtbx/ui/icons'
import { useAppDispatch, useAppSelector } from '@/store'
import { ICustomerAccountDetails } from '@/store/interfaces'
import { ACCESS_CONTROLS, AccessControlWrapper } from '@dtbx/store/utils'
import { LocalNotification } from '@dtbx/ui/components'

import { Summary } from '@/app/customers/customers/Create/Summary'
import { PersonalDetails } from '@/app/customers/customers/Create/PersonalDetails'
import { Accounts } from '@/app/customers/customers/Create/Accounts'
import { createCustomerAccount } from '@/store/actions'
import { clearNotification } from '@dtbx/store/reducers'
import * as Yup from 'yup'

const steps = [
  {
    icon: <PersonalDetailsIcon />,
    title: 'Personal Details',
    description: 'Search for and populate details',
  },
  {
    icon: <AccountsIcon />,
    title: 'Link and setup accounts',
    description: "Select customer's accounts to link",
  },
  {
    icon: <SummaryIcon />,
    title: 'Summary',
    description: 'Review and submit to checker',
  },
]
export const CreateCustomerDialog = () => {
  const [open, setOpen] = useState<boolean>(false)
  const dispatch = useAppDispatch()
  const handleClose = (e: React.SyntheticEvent, reason: string) => {
    if (reason === 'backdropClick') {
      return
    }
    setOpen(false)
  }
  const notification = useAppSelector(
    (state) => state.notifications.localNotification
  )
  const notificationType =
    useAppSelector((state) => state.notifications.localNotificationType) ||
    'info'
  const customerSchema = Yup.object().shape({
    comments: Yup.string().required('Comments are required'),
  })
  const [currentStep, setCurrentStep] = useState<string>('Personal Details')
  const formik = useFormik<ICustomerAccountDetails>({
    initialValues: {
      firstName: '',
      lastName: '',
      gender: '',
      phoneNumber: '',
      email: '',
      idType: '',
      idValue: '',
      cif: '',
      postalAddress: '',
      physicalAddress: '',
      customerAccounts: [],
      country: '',
      customerPrefix: '',
      dateOfBirth: '',
      nationality: '',
      customerCategory: '',
      isM247Customer: false,
      customerType: '',
      comments: '',

      // //to confirm if present
      // blockReason: '',
      // branchCode: '',
      // otherNames: '',
      // onboardingType: '',
      // isBlocked: false,
    },
    onSubmit: async (values) => {
      const res = await createCustomerAccount({
        account: values,
        dispatch,
      })
      if (res === 'success') {
        setOpen(false)
      }
    },
    validationSchema: customerSchema,
  })

  return (
    <>
      <AccessControlWrapper rights={[...ACCESS_CONTROLS.CREATE_CUSTOMERS]}>
        <Button
          variant="contained"
          data-testid="create-customer-drawer-button"
          onClick={() => setOpen(!open)}
          sx={{
            textWrap: 'noWrap',
          }}
        >
          <AddOutlinedIcon />
          Create new customer
        </Button>
      </AccessControlWrapper>
      <Drawer
        sx={{
          '.MuiDrawer-paper': {
            width: '95%',
          },
        }}
        open={open}
        variant="persistent"
        anchor={'right'}
        onClose={handleClose}
        slotProps={{
          paper: {
            sx: {
              width: '95%',
            },
          },
        }}
      >
        <Box
          sx={{
            background: '#F9FAFB',
            borderBottom: '2px solid  #F2F4F7',
          }}
        >
          <Stack
            flexDirection="row"
            sx={{
              alignItems: 'center',
              px: '2%',
            }}
          >
            <DialogTitle
              sx={{
                display: 'flex',
                flexDirection: 'row',
                alignItems: 'center',
                alignContent: 'center',
                py: '5px',
              }}
            >
              <IconButton onClick={(e) => handleClose(e, 'close')}>
                <ArrowBackIcon />
              </IconButton>
              <Typography
                variant="subtitle2"
                color={'primary.main'}
                data-testid="create-customer-drawer-header"
              >
                Add new customer
              </Typography>
            </DialogTitle>
          </Stack>
          <IconButton
            aria-label="close-create-customer-drawer"
            data-testid="close-create-customer-drawer-button"
            onClick={(e) => handleClose(e, 'close')}
            sx={{
              position: 'absolute',
              right: 8,
              top: 8,
              color: '',
            }}
          >
            <CloseIcon />
          </IconButton>
        </Box>
        <LocalNotification
          clearNotification={() => dispatch(clearNotification())}
          notification={notification}
          notificationType={notificationType}
        />
        <Stack
          sx={{
            flexDirection: 'row',
          }}
        >
          <Stack
            sx={{
              width: '30%',
              px: '4%',
              alignItems: 'flex-start',
              flexDirection: 'column',
              py: '2%',
            }}
          >
            {steps.map((step, index) => (
              <Stack
                key={step.title}
                sx={{
                  flexDirection: 'column',
                  cursor: 'pointer',
                }}
              >
                <Stack
                  sx={{
                    flexDirection: 'row',
                    gap: '8px',
                  }}
                >
                  {step.icon}
                  <Stack>
                    <Typography variant="body1" color="primary.main">
                      {step.title}
                    </Typography>
                    <Typography>{step.description}</Typography>
                  </Stack>
                </Stack>
                <Stack
                  sx={{
                    px: '9%',
                    py: '2%',
                  }}
                >
                  {index < 2 && <ConnectorIcon />}
                </Stack>
              </Stack>
            ))}
          </Stack>
          <Divider
            orientation="vertical"
            variant="middle"
            sx={{
              height: '90vh',
            }}
          />
          <Stack
            sx={{
              width: '70%',
            }}
          >
            <FormikProvider value={formik}>
              <Form onSubmit={formik.handleSubmit}>
                {(() => {
                  switch (currentStep) {
                    case 'Personal Details':
                      return (
                        <PersonalDetails
                          formik={formik}
                          setStep={setCurrentStep}
                          setOpen={setOpen}
                        />
                      )
                    case 'Link and setup accounts':
                      return (
                        <Accounts
                          formik={formik}
                          setStep={setCurrentStep}
                          setOpen={setOpen}
                        />
                      )
                    case 'Summary':
                      return (
                        <Summary
                          formik={formik}
                          setStep={setCurrentStep}
                          setOpen={setOpen}
                        />
                      )
                    default:
                      return (
                        <PersonalDetails
                          formik={formik}
                          setStep={setCurrentStep}
                          setOpen={setOpen}
                        />
                      )
                  }
                })()}
              </Form>
            </FormikProvider>
          </Stack>
        </Stack>
      </Drawer>
    </>
  )
}
