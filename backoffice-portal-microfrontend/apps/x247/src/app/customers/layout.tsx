'use client'

import { Stack, Typography } from '@mui/material'
import React from 'react'
import { CustomersIcon } from '@dtbx/ui/icons'

export default function CustomersLayout(props: { children: React.ReactNode }) {
  return (
    <Stack>
      <Stack
        sx={{
          marginLeft: '1.5%',
          marginTop: '0.2%',
          flexDirection: 'row',
          justifyContent: 'flex-start',
          alignItems: 'center',
          gap: '8px',
          padding: '8px',
        }}
      >
        <CustomersIcon width="28" height="26" />
        <Typography variant="h5">Customers</Typography>
      </Stack>
      {props.children}
    </Stack>
  )
}
