'use client'
import { Stack } from '@mui/material'
import React, { useEffect } from 'react'
import { useAppDispatch } from '@/store'
import { getAllCustomers } from '@/store/actions'
import CustomersListPage from './customers'
import { CustomerChangeLogDrawer } from '@/app/customers/ChangeLogDrawer'

const CustomersPage = () => {
  const dispatch = useAppDispatch()

  useEffect(() => {
    getAllCustomers(
      {
        page: 1,
        size: 10,
      },
      dispatch
    )
  }, [])
  return (
    <Stack>
      <CustomerChangeLogDrawer data-testid="customer-change-log" />
      <CustomersListPage data-testid="customers-list" />
    </Stack>
  )
}

export default CustomersPage
