import {
  Avatar,
  Button,
  ClickAwayListener,
  Drawer,
  Grow,
  IconButton,
  List,
  ListItem,
  MenuItem,
  MenuList,
  Paper,
  Popper,
  Stack,
  styled,
  Typography,
} from '@mui/material'
import { setOpenChangeLogsDrawer } from '@/store/reducers'
import {
  Close,
  FilterList,
  KeyboardArrowDownRounded,
  SearchRounded,
} from '@mui/icons-material'
import { CustomSearchInput } from '@dtbx/ui/components/Input'
import { Changes, changes } from '@/app/customers/customer/Details/changesList'
import React, { useEffect, useRef, useState } from 'react'
import { useAppDispatch, useAppSelector } from '@/store'

const Span = styled('span')(
  () =>
    `
color: #000A12;
font-size: 14px;
font-style: normal;
font-weight: 500;
line-height: 20px;
text-wrap: wrap;
  `
)
export const CustomerChangeLogDrawer = () => {
  const dispatch = useAppDispatch()

  const openDrawer = useAppSelector(
    (state) => state.customers.openChangeLogsDrawer
  )
  const [openFilter, setOpenFilter] = useState<boolean>(false)

  // filter dropdown
  const [open, setOpen] = useState(false)
  const [openSub, setOpenSub] = useState(false)
  const anchorSubRef = useRef<HTMLButtonElement>(null)
  const anchorRef = useRef<HTMLButtonElement>(null)

  const handleToggle = () => {
    setOpen((prevOpen) => !prevOpen)
    setOpenSub(false)
  }
  const handleToggleSub = () => {
    setOpenSub((prevOpenSub) => !prevOpenSub)
    setOpen(false)
  }

  const handleClose = (event: Event | React.SyntheticEvent) => {
    if (
      anchorRef.current &&
      anchorRef.current.contains(event.target as HTMLElement)
    ) {
      return
    }
    if (
      anchorSubRef.current &&
      anchorSubRef.current.contains(event.target as HTMLElement)
    ) {
      return
    }

    setOpen(false)
    setOpenSub(false)
  }

  function handleListKeyDown(event: React.KeyboardEvent) {
    if (event.key === 'Tab') {
      event.preventDefault()
      setOpen(false)
      setOpenSub(false)
    } else if (event.key === 'Escape') {
      setOpen(false)
      setOpenSub(false)
    }
  }

  // return focus to the button when we transitioned from !open -> open
  const prevOpen = React.useRef(open)
  const prevOpenSub = React.useRef(openSub)
  useEffect(() => {
    if (prevOpen.current && !open) {
      anchorRef.current!.focus()
    }
    if (prevOpenSub.current && !openSub) {
      anchorSubRef.current!.focus()
    }

    prevOpen.current = open
    prevOpenSub.current = openSub
  }, [open, openSub])
  const renderChangeItem = (change: Changes) => {
    return (
      <Stack
        sx={{
          width: '100%',
        }}
      >
        {change.action === 'create' && change.type === 'creation' && (
          <Typography variant="body2" sx={{ whiteSpace: 'normal' }}>
            <Span>{change.actionee}</Span> {'created'}{' '}
            <Span>{change.actionSubject} </Span>
          </Typography>
        )}

        {change.action === 'edit' && change.type === 'approval' && (
          <Typography
            variant="body2"
            sx={{ whiteSpace: 'normal', textWrap: 'nowrap' }}
          >
            <Span>{change.actionee} </Span>approved {change.resource} change
            from <Span>{change.previousState}</Span> to{' '}
            <Span>{change.actionSubject}</Span>
          </Typography>
        )}

        {change.action === 'edit' && change.type === 'edited' && (
          <Typography>
            <Span>{change.actionee} </Span>changed {change.resource} from{' '}
            <Span>{change.previousState}</Span> to{' '}
            <Span>{change.actionSubject}</Span>.
          </Typography>
        )}
        {change.action === 'create' && change.type === 'approval' && (
          <Typography>
            <Span>{change.actionee}</Span> approved creation of{' '}
            <Span>{change.actionSubject}</Span>.
          </Typography>
        )}
        {change.action === 'delete' && change.type === 'deletion' && (
          <Typography>
            <Span>{change.actionee}</Span> deleted{' '}
            <Span>{change.actionSubject}</Span>.
          </Typography>
        )}
        {change.action === 'delete' && change.type === 'approval' && (
          <Typography>
            <Span>{change.actionee}</Span> approved deletion of{' '}
            <Span>{change.actionSubject}</Span>.
          </Typography>
        )}
        {change.comment && (
          <Stack
            sx={{
              width: '20vw',
              display: 'flex',
              flexDirection: 'column',
              padding: '10px 14px',
              border: '1px solid #EEEEEF',
              borderRadius: '4px',
            }}
          >
            <Typography
              variant="body3"
              sx={{
                color: '#6B7280',
                fontWeight: 500,
              }}
            >
              Comment
            </Typography>
            <Typography
              variant="body3"
              sx={{
                fontWeight: 400,
              }}
            >
              {change.comment}{' '}
            </Typography>
          </Stack>
        )}

        <Typography
          variant="body3"
          sx={{
            color: '#6B7280',
            fontWeight: 500,
          }}
        >
          {change.date}
          {' | '}
          {change.time}
        </Typography>
      </Stack>
    )
  }

  return (
    <Drawer
      open={openDrawer}
      anchor="right"
      sx={{
        width: '100%',
      }}
      onClose={() => dispatch(setOpenChangeLogsDrawer(false))}
    >
      <Paper
        elevation={0}
        sx={{
          width: '32vw',
          display: 'flex',
          flexDirection: 'column',
          overflowY: 'hidden',
        }}
      >
        <Stack sx={{}}>
          {/* header */}
          <Stack
            sx={{
              justifyContent: 'space-between',
              flexDirection: 'row',
              padding: '16px 20px 8px 29px',
              borderBottom: '2px solid #F2F4F7',
              background: '#F9FAFB',
            }}
          >
            <Typography variant="h5">Changes Log</Typography>
            <IconButton
              onClick={() => {
                dispatch(setOpenChangeLogsDrawer(false))
              }}
              sx={{
                width: '36px',
                height: '36px',
                borderRadius: '50%',
                border: ' 1px solid #CBD5E1',
                background: '#F1F5F9',
              }}
            >
              <Close
                sx={{
                  width: '20px',
                  height: '20px',
                }}
              />
            </IconButton>
          </Stack>

          {/* search Stack and filter */}
          <Stack
            sx={{
              display: 'flex',
              flexDirection: 'column',
              padding: '0px 20px 0px 29px',
              width: '100%',
            }}
          >
            <Stack
              sx={{
                flexDirection: 'row',
                justifyContent: 'space-between',
                padding: '30px 0px 8px 0px',
                gap: '10px',
              }}
            >
              <CustomSearchInput
                startAdornment={<SearchRounded />}
                placeholder="Search"
                sx={{
                  height: '40px',
                }}
              />
              <Button
                size="small"
                variant="outlined"
                onClick={() => setOpenFilter(!openFilter)}
                sx={{
                  display: 'flex',
                  minWidth: '131px',
                  padding: '8px 42px',
                  justifyContent: 'center',
                  alignItems: 'center',
                  gap: '10px',
                  height: '40px',
                  borderRadius: '4px',
                  border: '1.5px solid #D0D5DD',
                  background: '#FFF',
                  StackShadow: '0px 1px 2px 0px rgba(16, 24, 40, 0.05)',
                }}
              >
                <FilterList />
                <Typography>Filter</Typography>
              </Button>
            </Stack>
            {/* Filter option render conditionally */}
            {openFilter && (
              <Stack
                sx={{
                  width: '100%',
                  flexDirection: 'row',
                  justifyContent: 'flex-start',
                  alignItems: 'center',
                  gap: '12px',
                }}
              >
                <Stack>
                  <Button
                    sx={{
                      height: '34px',
                      width: '156px',
                      textWrap: 'nowrap',
                      padding: '9px 28px',
                      borderRadius: '4px',
                      border: '1px solid  #AAADB0',
                      background: ' #FFF',
                      StackShadow: '0px 1px 2px 0px rgba(16, 24, 40, 0.05)',
                    }}
                    variant="outlined"
                    ref={anchorRef}
                    id="composition-button"
                    aria-controls={open ? 'composition-menu' : undefined}
                    aria-expanded={open ? 'true' : undefined}
                    aria-haspopup="true"
                    onClick={handleToggle}
                    endIcon={<KeyboardArrowDownRounded />}
                  >
                    <Typography variant="label1">Date Modified</Typography>
                  </Button>
                  <Popper
                    open={open}
                    anchorEl={anchorRef.current}
                    role={undefined}
                    placement="bottom-start"
                    transition
                    disablePortal
                    sx={{
                      zIndex: '2000',
                    }}
                  >
                    {({ TransitionProps, placement }) => (
                      <Grow
                        {...TransitionProps}
                        style={{
                          transformOrigin:
                            placement === 'bottom-start'
                              ? 'left top'
                              : 'left bottom',
                        }}
                      >
                        <Paper
                          sx={{
                            minWidth: '156px',
                            marginTop: '22px',
                            padding: '0px 0px 12px 0px',
                            borderRadius: '8px',
                            border: '1px solid #ECECEC',
                            background: '#FFF',
                            StackShadow:
                              '0px 12.514px 15.017px -2.503px rgba(16, 24, 40, 0.08), 0px 5.006px 5.006px -2.503px rgba(16, 24, 40, 0.03)',
                          }}
                        >
                          <ClickAwayListener onClickAway={handleClose}>
                            <MenuList
                              autoFocusItem={open}
                              id="composition-menu"
                              aria-labelledby="composition-button"
                              onKeyDown={handleListKeyDown}
                            >
                              <MenuItem onClick={handleClose}>
                                {'Last 24 hours'}
                              </MenuItem>
                            </MenuList>
                          </ClickAwayListener>
                        </Paper>
                      </Grow>
                    )}
                  </Popper>
                </Stack>
                <Stack>
                  <Button
                    sx={{
                      height: '34px',
                      width: '156px',
                      textWrap: 'nowrap',
                      padding: '9px 28px',
                      borderRadius: '4px',
                      border: '1px solid  #AAADB0',
                      background: ' #FFF',
                      StackShadow: '0px 1px 2px 0px rgba(16, 24, 40, 0.05)',
                    }}
                    variant="outlined"
                    ref={anchorSubRef}
                    id="composition-button"
                    aria-controls={openSub ? 'composition-menu' : undefined}
                    aria-expanded={openSub ? 'true' : undefined}
                    aria-haspopup="true"
                    onClick={handleToggleSub}
                    endIcon={<KeyboardArrowDownRounded />}
                  >
                    <Typography variant="label1">Sub module</Typography>
                  </Button>
                  <Popper
                    open={openSub}
                    anchorEl={anchorSubRef.current}
                    role={undefined}
                    placement="bottom-start"
                    transition
                    disablePortal
                    sx={{
                      zIndex: '2000',
                    }}
                  >
                    {({ TransitionProps, placement }) => (
                      <Grow
                        {...TransitionProps}
                        style={{
                          transformOrigin:
                            placement === 'bottom-start'
                              ? 'left top'
                              : 'left bottom',
                        }}
                      >
                        <Paper
                          sx={{
                            minWidth: '156px',
                            marginTop: '22px',
                            padding: '0px 0px 12px 0px',
                            borderRadius: '8px',
                            border: '1px solid #ECECEC',
                            background: '#FFF',
                            StackShadow:
                              '0px 12.514px 15.017px -2.503px rgba(16, 24, 40, 0.08), 0px 5.006px 5.006px -2.503px rgba(16, 24, 40, 0.03)',
                          }}
                        >
                          <ClickAwayListener onClickAway={handleClose}>
                            <MenuList
                              autoFocusItem={openSub}
                              id="composition-menu"
                              aria-labelledby="composition-button"
                              onKeyDown={handleListKeyDown}
                            >
                              <MenuItem onClick={handleClose}>User</MenuItem>
                            </MenuList>
                          </ClickAwayListener>
                        </Paper>
                      </Grow>
                    )}
                  </Popper>
                </Stack>
              </Stack>
            )}
          </Stack>
        </Stack>

        {/* listStack */}
        <Stack
          sx={{
            flex: 1,
            overflowY: 'auto',
            scrollbarWidth: 'thin',
          }}
        >
          <List
            sx={{
              width: '100%',
            }}
          >
            {changes &&
              changes.map((change: Changes, index) => {
                return (
                  <ListItem
                    key={index}
                    sx={{
                      padding: '16px 29px',
                      display: 'flex',
                      flexDirection: 'row',
                      gap: '17px',
                      justifyContent: 'flex-start',
                      alignItems: 'flex-start',
                      border: '1px solid #E3E4E4',
                      width: '100%',
                    }}
                  >
                    <Avatar>
                      <Typography>
                        {change?.actionee &&
                          change.actionee.split(' ')[0][0] +
                            change.actionee.split(' ')[
                              change.actionee.split(' ').length - 1
                            ][0]}
                      </Typography>
                    </Avatar>
                    {renderChangeItem(change)}
                  </ListItem>
                )
              })}
          </List>
        </Stack>
      </Paper>
    </Drawer>
  )
}
