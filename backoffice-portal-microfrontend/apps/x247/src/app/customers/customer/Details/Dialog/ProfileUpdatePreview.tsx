import { Stack } from '@mui/material'
import React from 'react'
import { ReadOnlyTypography } from '@dtbx/ui/components'
import { IApprovalRequest } from '@/store/interfaces'
import { handleDiff } from '@dtbx/store/utils'

export const ProfileUpdatePreview = ({
  selectedApprovalRequest,
}: {
  selectedApprovalRequest: IApprovalRequest
}) => {
  return (
    <Stack>
      <ReadOnlyTypography
        name="changesMade"
        value={handleDiff(selectedApprovalRequest.diff)}
        id="changesMade"
        minRows={2}
        multiline
      />
    </Stack>
  )
}
