import {
  Stack,
  Tab,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableRow,
  Tabs,
} from '@mui/material'
import React from 'react'
import { useAppSelector } from '@/store'
import { IApprovalRequest } from '@/store/interfaces'
import { ITableData } from '@dtbx/store/interfaces'
import { DotsDropdown } from '@dtbx/ui/components/DropDownMenus'
import { CustomSearchInput } from '@dtbx/ui/components/Input'
import { CustomTableHeader } from '@dtbx/ui/components/Table'
import { SearchRounded } from '@mui/icons-material'

import {
  CustomAntTab,
  CustomTabPanel,
  CustomToggleTabs,
} from '@dtbx/ui/components'
import { formatTimestamp } from '@dtbx/store/utils'

const header = [
  {
    id: 'event',
    label: 'Event Type',
    alignCenter: false,
    alignRight: false,
  },
  {
    id: 'maker',
    label: 'Maker',
    alignCenter: false,
    alignRight: false,
  },
  {
    id: 'maker_timestamp',
    label: 'Maker Timestamp',
    alignCenter: false,
    alignRight: false,
  },
  {
    id: 'makerComments',
    label: 'Maker Comments',
    alignCenter: false,
    alignRight: false,
  },
  {
    id: 'checker',
    label: 'Checker',
    alignCenter: false,
    alignRight: false,
  },
  {
    id: 'checker_timestamp',
    label: 'Checker Timestamp',
    alignCenter: false,
    alignRight: false,
  },
  {
    id: 'checkerComments',
    label: 'Checker Comments',
    alignCenter: false,
    alignRight: false,
  },
  {
    id: 'actions',
    label: 'Actions',
    alignCenter: false,
    alignRight: false,
  },
]
const customerHead = [
  {
    id: 'event',
    label: 'Event Type',
    alignCenter: false,
    alignRight: false,
  },
  {
    id: 'eventDate',
    label: 'Checker Timestamp',
    alignCenter: false,
    alignRight: false,
  },
]

const CustomerAccountsHistory = ({ hideTabs }: { hideTabs?: boolean }) => {
  const [tab, setTab] = React.useState<number>(0)
  const [search, setSearch] = React.useState<string>('')
  const {
    accountLogs,
    isLoadingAccountsLogs,
    accountLogsBackOffice,
    isLoadingAccountsLogsBackOffice,
  } = useAppSelector((state) => state.customers)

  type TableData = IApprovalRequest | ITableData

  const isApprovalRequest = (data: TableData): data is IApprovalRequest => {
    return (data as IApprovalRequest).maker !== undefined
  }

  const isCustomerEvent = (data: TableData): data is ITableData => {
    return (data as ITableData).eventDate !== undefined
  }
  const searchLogs = (
    logs: IApprovalRequest[] | ITableData[],
    search: string
  ) => {
    const strings: string[] = []

    const traverse = (current: IApprovalRequest | ITableData) => {
      for (const value of Object.values(current)) {
        if (typeof value === 'string') {
          strings.push(value)
        } else if (typeof value === 'object' && value !== null) {
          traverse(value)
        }
      }
    }
    return logs?.filter((log) => {
      traverse(log)
      return strings.some((value) =>
        value
          .toString()
          .toLowerCase()
          .replaceAll(' ', '')
          .includes(search.toLowerCase().replaceAll(' ', ''))
      )
    })
  }

  const filteredBackOfficeLogs = React.useMemo(() => {
    return tab === 0 ? searchLogs(accountLogsBackOffice, search) : []
  }, [accountLogsBackOffice, search])

  const filteredCustomerLogs = React.useMemo(() => {
    return tab === 1 ? searchLogs(accountLogs, search) : []
  }, [accountLogs, search])

  const renderTable = (filteredData: ITableData[] | IApprovalRequest[]) => {
    return (
      <TableContainer
        sx={{
          maxHeight: '83vh',
          overflowX: 'auto',
          '&::-webkit-scrollbar': {
            width: '6px',
          },
          '&::-webkit-scrollbar-track': {
            backgroundColor: 'lightgray transparent',
            padding: '0px 4px',
          },
          '&::-webkit-scrollbar-thumb': {
            backgroundColor: 'darkgray',
            borderRadius: '10px',
          },
        }}
      >
        <Table stickyHeader>
          <CustomTableHeader
            order={'desc'}
            orderBy={''}
            rowCount={0}
            headLabel={tab === 0 ? [...header] : [...customerHead]}
            numSelected={0}
          />
          <TableBody>
            {filteredData &&
              filteredData.map((row: IApprovalRequest | ITableData) => {
                isApprovalRequest(row)
                return tab === 0
                  ? !isLoadingAccountsLogsBackOffice &&
                      accountLogsBackOffice &&
                      isApprovalRequest(row) && (
                        <TableRow
                          key={row.id}
                          sx={{
                            background: 'white',
                          }}
                        >
                          <TableCell>{row.makerCheckerType.name}</TableCell>
                          <TableCell>{row.maker}</TableCell>
                          <TableCell>
                            {formatTimestamp(row.dateCreated)}
                          </TableCell>
                          <TableCell>{row.makerComments}</TableCell>
                          <TableCell>{row.checker}</TableCell>
                          <TableCell>
                            {row.checker
                              ? formatTimestamp(row.dateModified)
                              : ''}
                          </TableCell>

                          <TableCell>{row.checkerComments}</TableCell>

                          <TableCell>
                            <DotsDropdown
                              menuItems={[
                                {
                                  label: 'See more Details',
                                  onClick: () => {},
                                },
                              ]}
                            />
                          </TableCell>
                        </TableRow>
                      )
                  : !isLoadingAccountsLogs &&
                      accountLogs &&
                      isCustomerEvent(row) && (
                        <TableRow
                          key={row.id}
                          sx={{
                            background: 'white',
                          }}
                        >
                          <TableCell>{row.event}</TableCell>
                          <TableCell>
                            {formatTimestamp(row.eventDate)}
                          </TableCell>
                        </TableRow>
                      )
              })}
          </TableBody>
        </Table>
      </TableContainer>
    )
  }

  const [tabValue, setTabValue] = React.useState(0)

  const handleChange = (_event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue)
  }
  return (
    <>
      <Stack
        direction={'row'}
        sx={{
          justifyContent: 'space-between',
          alignItems: 'center',
          padding: '2%',
        }}
      >
        <CustomSearchInput
          sx={{
            width: '40%',
            '&.Mui-focused': {
              width: '40%',
            },
          }}
          startAdornment={<SearchRounded />}
          placeholder="Search Event"
          // onChange={handleSearch}
          // value={search}
        />
        {!!hideTabs === false && (
          <CustomToggleTabs
            value={tabValue}
            onChange={handleChange}
            aria-label="ant example"
            // centered
            // variant="fullWidth"
            sx={{
              marginBottom: '1.5%',
              width: '30%',
            }}
          >
            <CustomAntTab label={`Back Office`} />
            <CustomAntTab label={`Customer`} />
          </CustomToggleTabs>
        )}
      </Stack>
      <CustomTabPanel value={tabValue} index={0}>
        {renderTable(filteredBackOfficeLogs as IApprovalRequest[])}
      </CustomTabPanel>
      <CustomTabPanel index={1} value={tabValue}>
        {renderTable(filteredCustomerLogs as ITableData[])}
      </CustomTabPanel>
    </>
  )
}

export default CustomerAccountsHistory
