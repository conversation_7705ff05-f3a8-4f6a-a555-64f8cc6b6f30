import { CloseRounded, SearchRounded } from '@mui/icons-material'
import {
  <PERSON>,
  Button,
  Drawer,
  IconButton,
  Paper,
  Stack,
  Typography,
} from '@mui/material'
import React, { useEffect, useState } from 'react'
import { CustomSearchInput } from '@dtbx/ui/components/Input'
import {
  CustomAntTab,
  CustomTabPanel,
  CustomToggleTabs,
} from '@dtbx/ui/components'
import BackOfficeQuestionsHistory from '@/app/customers/customer/Details/Security/Drawer/BackOfficeQuestionsHistory'
import CustomerQuestionsHistory from '@/app/customers/customer/Details/Security/Drawer/CustomerQuestionsHistory'
import { getCustomerPinLogsBackoffice } from '@/store/actions'
import { useAppDispatch } from '@/store'
import { ICustomer } from '@/store/interfaces'

const SecurityDrawer = ({ customer }: { customer: ICustomer }) => {
  const [open, setOpen] = useState(false)
  const [tabValue, setTabValue] = useState<number>(0)
  const dispatch = useAppDispatch()
  const handleTabChange = (_event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue)
  }
  useEffect(() => {
    if (open) {
      getCustomerPinLogsBackoffice(customer?.id || '', dispatch, 1, 10)
    }
  }, [open])
  return (
    <>
      <Button
        variant={'outlined'}
        onClick={() => setOpen(true)}
        sx={{
          width: '100%',
          height: '36px',
          border: '1px solid #D0D5DD',
        }}
      >
        Security History
      </Button>
      <Drawer
        open={open}
        anchor="right"
        variant="persistent"
        slotProps={{
          paper: {
            sx: {
              maxWidth: '72%',
              minWidth: '30%',
            },
          },
        }}
      >
        <Paper
          elevation={0}
          sx={{
            height: '100vh',
            display: 'flex',
            flexDirection: 'column',
            justifyContent: 'flex-start',
            gap: '10px',
            transition: 'width 3.3s ease-in-out',
          }}
        >
          <Box
            sx={{
              minWidth: '52vw',
              width: '100%',
              maxHeight: '60px',
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
              padding: '16px 20px 8px 24px',
              borderBottom: '1px solid lightgray',
              backgroundColor: '#F9FAFB',
            }}
          >
            {/* DrawerHeader */}
            <Box
              sx={{
                display: 'flex',
                gap: '10px',
                justifyContent: 'flex-start',
                alignItems: 'center',
              }}
            >
              <Typography variant="h6">Security Questions History</Typography>
            </Box>
            <IconButton
              sx={{
                border: '1px solid #CBD5E1',
                backgroundColor: '#F1F5F9',
              }}
              onClick={() => {
                setOpen(false)
              }}
            >
              <CloseRounded
                sx={{
                  fontSize: '20px',
                }}
              />
            </IconButton>
          </Box>
          <Stack
            direction={'row'}
            sx={{ justifyContent: 'space-between', alignItems: 'center' }}
          >
            <CustomSearchInput
              sx={{
                width: '40%',
                '&.Mui-focused': {
                  width: '40%',
                },
              }}
              startAdornment={<SearchRounded />}
              placeholder="Search Event"
            />
            <CustomToggleTabs
              value={tabValue}
              onChange={handleTabChange}
              aria-label="ant example"
              sx={{
                marginBottom: '1.5%',
                width: '30%',
              }}
            >
              <CustomAntTab label={`Back Office`} />
              <CustomAntTab label={`Customer`} />
            </CustomToggleTabs>
          </Stack>
          x
          <CustomTabPanel value={tabValue} index={0}>
            <BackOfficeQuestionsHistory />
          </CustomTabPanel>
          <CustomTabPanel value={tabValue} index={1}>
            <CustomerQuestionsHistory />
          </CustomTabPanel>
        </Paper>
      </Drawer>
    </>
  )
}

export default SecurityDrawer
