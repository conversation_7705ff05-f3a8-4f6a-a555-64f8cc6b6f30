import { useEffect } from 'react'
import { Stack, Typography } from '@mui/material'
import { useAppDispatch, useAppSelector } from '@/store'
import { getLinkedCustomerAccountsByProfileId } from '@/store/actions'
import { LoadingListsSkeleton } from '@dtbx/ui/components/Loading'

import { SingleAccountView } from '@/app/customers/customer/Details/Accounts/SingleAccountView'
import { AccountsEmptyState } from '@/app/customers/customer/Details/Accounts/EmptyState'
import { AccountsList } from '@/app/customers/customer/Details/Accounts/AccountsList'
import { AccountsHeader } from '@/app/customers/customer/Details/Accounts/Header'

const CustomerAccounts = () => {
  const {
    isViewAccountOpen,
    customer,
    isLoadingAccounts,
    customerLinkedAccountsList,
  } = useAppSelector((state) => state.customers)
  const dispatch = useAppDispatch()
  useEffect(() => {
    customer &&
      customer.id &&
      getLinkedCustomerAccountsByProfileId(customer.id, dispatch)
  }, [])
  return (
    <Stack
      direction="column"
      gap={'1vh'}
      sx={{
        px: '1.5%',
      }}
    >
      {!isViewAccountOpen ? (
        <>
          <Typography variant="h6"> Accounts</Typography>
          <AccountsHeader customer={customer} />
        </>
      ) : null}
      {isLoadingAccounts ? (
        <LoadingListsSkeleton />
      ) : isViewAccountOpen ? (
        <SingleAccountView />
      ) : (
        <>
          {customerLinkedAccountsList.length < 1 ? (
            <AccountsEmptyState />
          ) : (
            <AccountsList />
          )}
        </>
      )}
    </Stack>
  )
}
export default CustomerAccounts
