'use client'
import React, { useEffect } from 'react'
import { Box } from '@mui/material'
import { setChangeTab, setIsViewAccountOpen } from '@/store/reducers'
import { useAppDispatch, useAppSelector } from '@/store'
import { getCustomerProfileById } from '@/store/actions'
import { AntTab, AntTabs, TabPanel } from '@dtbx/ui/components/Tabs'

import CustomerAccounts from '@/app/customers/customer/Details/Accounts'

import Profile from './Profile/Profile'
import KYC from './KYC'
import Wallets from './Wallets'
import SecurityQuestions from './Security/SecurityQuestions'
import Notifications from './Notifications'
import Subscriptions from './Subscriptions'
import Devices from './Devices'

const CustomerDetails = () => {
  const dispatch = useAppDispatch()

  const { customer, tabStateValue, customerIdToView } = useAppSelector(
    (state) => state.customers
  )
  const handleChange = (event: React.SyntheticEvent, newValue: number) => {
    dispatch(setChangeTab(newValue))
    localStorage.setItem('tab', JSON.stringify(newValue))
    if (newValue === 3) {
      dispatch(setIsViewAccountOpen(false))
    }
  }
  useEffect(() => {
    if (Object.keys(customer).length === 0) {
      getCustomerProfileById(customerIdToView, dispatch)
    }
  }, [])
  return (
    <Box
      sx={{
        width: '100%',
        height: '100%',
        display: 'flex',
        flexDirection: 'column',
        padding: '1% 1.8% 1.8% 2%',
        borderTop: '1px solid #D0D5DD',
        backgroundColor: '#FFFFFF',
      }}
    >
      <AntTabs
        value={tabStateValue}
        onChange={handleChange}
        aria-label="ant example"
        centered
        variant="fullWidth"
        sx={{
          borderBottom: '1px solid #D0D5DD',
          marginBottom: '1.5%',
        }}
      >
        <AntTab label={`Personal Details`} />
        <AntTab label={`KYC`} sx={{ display: 'none' }} />
        <AntTab label={`Devices`} />
        <AntTab label={`Accounts`} />
        <AntTab label={`Wallets`} sx={{ display: 'none' }} />
        <AntTab label={`Security`} />
        <AntTab label={`Notifications`} sx={{ display: 'none' }} />
        <AntTab label={`Subscriptions`} sx={{ display: 'none' }} />
      </AntTabs>

      <TabPanel value={tabStateValue} index={0}>
        <Profile />
      </TabPanel>
      <TabPanel value={tabStateValue} index={1}>
        <KYC />
      </TabPanel>
      <TabPanel value={tabStateValue} index={2}>
        <Devices />
      </TabPanel>
      <TabPanel value={tabStateValue} index={3}>
        <CustomerAccounts />
      </TabPanel>
      <TabPanel value={tabStateValue} index={4}>
        <Wallets />
      </TabPanel>
      <TabPanel value={tabStateValue} index={5}>
        <SecurityQuestions />
      </TabPanel>
      <TabPanel value={tabStateValue} index={6}>
        <Notifications />
      </TabPanel>
      <TabPanel value={tabStateValue} index={7}>
        <Subscriptions />
      </TabPanel>
    </Box>
  )
}

export default CustomerDetails
