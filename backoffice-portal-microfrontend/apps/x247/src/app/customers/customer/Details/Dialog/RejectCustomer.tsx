import { CloseRounded, KeyboardArrowDownRounded } from '@mui/icons-material'
import {
  Box,
  Button,
  Dialog,
  DialogContent,
  DialogContentText,
  DialogTitle,
  Grow,
  IconButton,
  InputBase,
  MenuItem,
  Select,
  SelectChangeEvent,
  TextField,
  Typography,
} from '@mui/material'
import { useAppDispatch, useAppSelector } from '@/store'
import { setOpenRejectCustomerModal } from '@/store/reducers'
import { rejectUpdateCustomer } from '@/store/actions'
import React from 'react'

const rejectionReasonsList: string[] = [
  'Inconsistent information ',
  'Mismatch between info provided and on record',
  'Expired documents',
  'Documents appear tampered with or fake',
  'Suspicious activity detected',
  'Other',
]
const RejectCustomerDialog = () => {
  // redux state

  const open = useAppSelector(
    (state) => state.customers.openRejectCustomerModal
  )
  const { selectedApprovalRequest } = useAppSelector(
    (state) => state.approvalRequests
  )
  const [openOthersInput, setOpenOthersInput] = React.useState(false)
  const dispatch = useAppDispatch()

  const handleRejectCustomerUpdate = async () => {
    dispatch(setOpenRejectCustomerModal(false))
    setOpenOthersInput(false)
    if (
      selectedApprovalRequest &&
      selectedApprovalRequest.entityId &&
      selectedApprovalRequest.id
    ) {
      await rejectUpdateCustomer({
        approvalID: selectedApprovalRequest.id,
        comments: 'test',
        dispatch,
      })
    }
  }
  return (
    <Dialog
      open={open}
      onClose={() => dispatch(setOpenRejectCustomerModal(false))}
    >
      <Box sx={{ width: '383px' }}>
        <DialogTitle
          sx={{
            borderBottom: '1px solid #CBD5E1',
            padding: '16px 20px 8px 24px',
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
          }}
        >
          <Typography>Reject customer</Typography>
          <IconButton
            onClick={() => dispatch(setOpenRejectCustomerModal(false))}
            sx={{
              width: '36px',
              height: '36px',
              backgroundColor: ' #F1F5F9',
              border: '1px solid #CBD5E1)',
              borderRadius: '50%',
            }}
          >
            <CloseRounded />
          </IconButton>
        </DialogTitle>
        <DialogContent
          sx={{
            display: 'flex',
            flexDirection: 'column',
            gap: '20px',
            padding: '0px 20px 20px 20px',
          }}
        >
          <Box
            sx={{
              display: 'flex',
              flexDirection: 'column',
              gap: '20px',
            }}
          >
            <DialogContentText variant="body2">
              Please let us know why you are rejecting this customer.
            </DialogContentText>
            <Select
              MenuProps={{
                PaperProps: {
                  sx: {
                    padding: '8px 15px',
                  },
                },
              }}
              IconComponent={KeyboardArrowDownRounded}
              input={
                <InputBase
                  sx={{
                    borderRadius: '4px',
                    border: '1px solid #D0D5DD',
                    background: '#FFF',
                    boxShadow: '0px 1px 2px 0px rgba(16, 24, 40, 0.05)',
                    height: '44px',
                    padding: '10px 14px',
                  }}
                />
              }
              fullWidth
              size="small"
              onChange={(event: SelectChangeEvent) => {
                if (event.target.value === 'Other') {
                  setOpenOthersInput(true)
                } else {
                  setOpenOthersInput(false)
                }
              }}
            >
              {rejectionReasonsList.map((reason, index) => {
                if (reason === 'Other') {
                  return (
                    <MenuItem
                      key={index}
                      value={reason}
                      sx={{
                        borderRadius: '4px',
                        border: '1px solid #ACACAC',
                      }}
                    >
                      {reason}
                    </MenuItem>
                  )
                }
                return (
                  <MenuItem key={index} value={reason}>
                    {reason}
                  </MenuItem>
                )
              })}
            </Select>
            {openOthersInput && (
              <Grow
                in={openOthersInput}
                style={{ transformOrigin: '0 0 0' }}
                {...(openOthersInput ? { timeout: 1000 } : {})}
              >
                <TextField
                  fullWidth
                  multiline
                  rows={4}
                  placeholder="Please specify the reason for rejection"
                  variant="outlined"
                  label="Other(Specify)"
                />
              </Grow>
            )}
          </Box>

          <Box
            sx={{
              display: 'flex',
              justifyContent: 'center',
              gap: '20px',
            }}
          >
            {/* footeer */}
            <Button
              variant="outlined"
              sx={{
                width: '153px',
                height: '40px',
                fontSize: '14px',
                textWrap: 'nowrap',
              }}
              onClick={() => {
                dispatch(setOpenRejectCustomerModal(false))
                setOpenOthersInput(false)
              }}
            >
              Cancel
            </Button>
            <Button
              variant="contained"
              sx={{
                width: '153px',
                height: '40px',
                fontSize: '14px',
                textWrap: 'nowrap',
                background: '#EB0045',
              }}
              onClick={() => {
                handleRejectCustomerUpdate()
              }}
            >
              Reject
            </Button>
          </Box>
        </DialogContent>
      </Box>
    </Dialog>
  )
}

export default RejectCustomerDialog
