import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>ield, Typography } from '@mui/material'
import { useFormik } from 'formik'
import React, { useEffect } from 'react'
import { sentenceCase } from 'tiny-case'
import { RootState, useAppDispatch, useAppSelector } from '@/store'
import {
  ACCESS_CONTROLS,
  AccessControlWrapper,
  formatTimestamp,
  HasAccessToRights,
} from '@dtbx/store/utils'
import {
  getCustomerProfileById,
  makerUpdateCustomer,
  updateCustomerDetails,
} from '@/store/actions'
import { setChangeTab } from '@/store/reducers'
import { ConfirmCancelSave } from '@dtbx/ui/components/Overlay'
import { ProfileIcon } from '@dtbx/ui/icons'

import LoadingProfile from './LoadingProfile'

const Profile = () => {
  const [enableEdit, setEnableEdit] = React.useState(false)
  const [isConfirmCancelOpen, setIsConfirmCancelOpen] = React.useState(false)
  const { customer, isCustomerLoading } = useAppSelector(
    (state: RootState) => state.customers
  )
  const dispatch = useAppDispatch()
  const formik = useFormik({
    initialValues: {
      firstName: customer.firstName,
      middleName: customer.otherNames,
      lastName: customer.lastName,
      gender: customer?.sex === 'M' ? 'Male' : 'Female',
      idType: customer.idType,
      idNumber: customer.idNumber,
      phoneNumber: customer.phoneNumber,
      email: customer.email,
      postalAddress: customer.postalAddress,
      cifNumber:
        customer?.profileAccountStoreIds?.length > 0
          ? customer?.profileAccountStoreIds[0]?.customerId
          : '',
      dateCreated: customer.dateCreated,
      customerId: customer.id ? customer.id : '',
    },
    enableReinitialize: true,
    onSubmit: async () => {
      if (HasAccessToRights(['SUPER_UPDATE_CUSTOMERS'])) {
        await updateCustomerDetails({
          email: formik.values.email,
          profileID: customer.id ? customer.id : '',
          dispatch,
        })
      } else {
        await makerUpdateCustomer({
          email: formik.values.email,
          profileID: customer.id ? customer.id : '',
          dispatch,
        })
      }
      await getCustomerProfileById(customer.id ? customer.id : '', dispatch)
    },
  })
  useEffect(() => {
    formik.values.email = customer.email
    formik.resetForm
    if (localStorage.tab && customer.id) {
      dispatch(setChangeTab(JSON.parse(localStorage.tab)))
    }
  }, [customer])
  return isCustomerLoading ? (
    <LoadingProfile />
  ) : (
    <Stack sx={{ py: '1%', px: '2%' }}>
      <Stack
        sx={{
          backgroundColor: '#FFF',
          px: '2%',
          py: '3%',

          border: '1px solid #D0D5DD',
        }}
        direction="column"
        spacing={3}
      >
        {/* header */}
        <Stack direction="row" sx={{ justifyContent: 'space-between' }}>
          <ConfirmCancelSave
            open={isConfirmCancelOpen}
            onConfirmCancel={() => {
              setIsConfirmCancelOpen(false)
              setEnableEdit(false)
            }}
            onClose={() => {
              setIsConfirmCancelOpen(false)
            }}
            onConfirmSubmit={() => {
              setIsConfirmCancelOpen(false)
              setEnableEdit(false)
              formik.handleSubmit()
            }}
          />
          <Stack direction="row" spacing={2}>
            <Avatar sx={{ backgroundColor: '#E7E8E9' }}>
              <ProfileIcon />
            </Avatar>
            <Stack direction="column">
              <Typography sx={{ fontSize: '16px', fontWeight: 600 }}>
                {customer && customer.firstName
                  ? sentenceCase(customer.firstName)
                  : customer && customer.lastName
                    ? sentenceCase(customer.lastName)
                    : ''}
              </Typography>

              <Typography sx={{ fontSize: '14px', fontWeight: 400 }}>
                {formatTimestamp(customer.dateCreated)}
              </Typography>
            </Stack>
          </Stack>
          <Stack direction="row" spacing={2}>
            <AccessControlWrapper
              rights={[...ACCESS_CONTROLS.UPDATE_CUSTOMERS]}
            >
              <Button
                variant="outlined"
                sx={{
                  backgroundColor: '#F8F9FC',
                  border: '1px solid  #AAADB0',
                  borderRadius: '6px',
                  boxShadow: '0px 1px 2px 0px rgba(16, 24, 40, 0.05)',
                  height: '34px',
                  display: 'none',
                }}
                onClick={() => {
                  setEnableEdit(true)
                  if (enableEdit && formik.dirty) {
                    setIsConfirmCancelOpen(true)
                  } else if (enableEdit && !formik.dirty) {
                    setEnableEdit(false)
                  }
                }}
                disabled={customer.isBlocked}
              >
                <Typography>{enableEdit ? 'Cancel' : 'Edit'}</Typography>
              </Button>
            </AccessControlWrapper>
            {enableEdit && (
              <Button
                variant="contained"
                sx={{
                  color: '#fff',
                  borderRadius: '4px',
                  height: '34px',
                  boxShadow: '0px 1px 2px 0px rgba(16, 24, 40, 0.05)',
                }}
                type="submit"
                disabled={!formik.dirty}
                onClick={() => formik.handleSubmit()}
              >
                Save changes
              </Button>
            )}
          </Stack>
        </Stack>
        <Stack direction="column" spacing={3}>
          {/* first row input */}
          <Stack direction="row" spacing={3}>
            <TextField
              value={
                formik.values.firstName
                  ? sentenceCase(formik.values.firstName)
                  : ''
              }
              label="First Name"
              fullWidth
              name=""
              inputProps={{ readOnly: true }}
              disabled={enableEdit}
            />
            <TextField
              value={
                formik.values.middleName
                  ? sentenceCase(formik.values.middleName)
                  : ''
              }
              label="Middle Name"
              fullWidth
              name=""
              inputProps={{ readOnly: true }}
              disabled={enableEdit}
            />
            <TextField
              value={
                formik.values.lastName
                  ? sentenceCase(formik.values.lastName)
                  : ''
              }
              label="Last Name"
              fullWidth
              name=""
              inputProps={{ readOnly: true }}
              disabled={enableEdit}
            />
          </Stack>
          {/* second row input */}
          <Stack direction="row" spacing={3}>
            <TextField
              value={formik.values.gender}
              label="Gender"
              fullWidth
              name=""
              inputProps={{ readOnly: true }}
              disabled={enableEdit}
            />
            <TextField
              value={formik.values.idType}
              label="ID Type"
              fullWidth
              name=""
              inputProps={{ readOnly: true }}
              disabled={enableEdit}
            />
            <TextField
              value={formik.values.idNumber}
              label="ID Number"
              fullWidth
              name=""
              inputProps={{ readOnly: true }}
              disabled={enableEdit}
            />
          </Stack>
          {/* third row input */}
          <Stack direction="row" spacing={3}>
            <TextField
              value={formik.values.phoneNumber}
              label="Phone Number"
              fullWidth
              name=""
              inputProps={{ readOnly: !enableEdit }}
              disabled={enableEdit}
            />
            <TextField
              value={formik.values.email}
              label="Email"
              onChange={formik.handleChange}
              fullWidth
              name="email"
              inputProps={{ readOnly: !enableEdit }}
            />
            <TextField
              value={formik.values.postalAddress}
              label="Postal Address"
              fullWidth
              name=""
              inputProps={{ readOnly: !enableEdit }}
              disabled={enableEdit}
            />
          </Stack>
          {/* forth row input */}
          <Stack
            direction="row"
            spacing={3}
            sx={{
              width: '66%',
            }}
          >
            <TextField
              value={formatTimestamp(formik.values.dateCreated)}
              label="Date created"
              fullWidth
              name=""
              inputProps={{ readOnly: true }}
              disabled={enableEdit}
            />
            <TextField
              value={formik.values.cifNumber}
              label="CIF Number"
              fullWidth
              name=""
              inputProps={{ readOnly: true }}
              disabled={enableEdit}
            />
          </Stack>
        </Stack>
      </Stack>
    </Stack>
  )
}

export default Profile
