import React, { useEffect, useState } from 'react'
import { CloseRounded } from '@mui/icons-material'
import {
  Box,
  Button,
  Dialog,
  DialogContent,
  DialogTitle,
  IconButton,
  TextField,
  Typography,
} from '@mui/material'
import { useAppDispatch, useAppSelector } from '@/store'
import {
  ACCESS_CONTROLS,
  AccessControlWrapper,
  HasAccessToRights,
} from '@dtbx/store/utils'
import {
  getCustomerPinDetails,
  getCustomerProfileById,
  resetCustomerPin,
} from '@/store/actions'
import { CustomCheckBox } from '@dtbx/ui/components/CheckBox'
import { LoadingButton } from '@dtbx/ui/components/Loading'

export const ResetPin = () => {
  const [hasTouched, setHasTouched] = useState(false)
  const [open, setOpen] = useState(false)
  const [selectedOption, setSelectedOptin] = useState<string>()
  const [customReason, setCustomReason] = useState<string>('')
  const [error, setError] = useState<string>('')
  const resetReasons: string[] = [
    'Compromised PIN',
    'Lost or Stolen Phone',
    'Forgotten PIN',
    'PIN Reset Policy',
    'Other',
  ]

  const { customer, isLoadingSecurity } = useAppSelector(
    (state) => state.customers
  )
  const dispatch = useAppDispatch()

  const handleClose = () => {
    if (!isLoadingSecurity) {
      setSelectedOptin('')
      setCustomReason('')
      setOpen(false)
    }
  }
  useEffect(() => {
    if (!open) {
      setSelectedOptin('')
      setCustomReason('')
      setOpen(false)
    }
  }, [open, resetReasons.length])
  const handleReset = async () => {
    const profileID = customer?.id
    const comments = `${selectedOption}: ${customReason}`
    try {
      if (HasAccessToRights(['SUPER_RESET_SECURITY_QUESTIONS'])) {
        await resetCustomerPin({
          profileID,
          dispatch,
          comments,
          role: 'super',
        })
      } else if (HasAccessToRights(['MAKE_RESET_SECURITY_QUESTIONS'])) {
        await resetCustomerPin({
          profileID,
          dispatch,
          comments,
          role: 'maker',
        })
      }
    } finally {
      handleClose()
      await getCustomerPinDetails({
        profileID: profileID || '',
        dispatch,
      })
      await getCustomerProfileById(customer.id ? customer.id : '', dispatch)
    }
  }
  const validateReason = (reason: string) => {
    const trimmedReason = reason.trim()
    return trimmedReason.length >= 10 && /^[\s\S]*$/.test(trimmedReason)
  }
  useEffect(() => {
    if (!validateReason(customReason)) {
      setError(
        'Reason must be at least 10 characters and contain only valid characters.'
      )
    } else {
      setError('')
    }
  }, [customReason])
  return (
    <>
      <AccessControlWrapper
        rights={[...ACCESS_CONTROLS.RESET_SECURITY_QUESTIONS]}
      >
        <Button
          variant="contained"
          onClick={() => {
            setOpen(true)
          }}
          sx={{ height: '35px' }}
        >
          Reset{' '}
        </Button>
      </AccessControlWrapper>
      <Dialog open={open}>
        <Box
          sx={{
            width: '30vw',
            height: 'auto',
            display: 'flex',
            flexDirection: 'column',
            gap: '10px',
          }}
        >
          <DialogTitle
            sx={{
              width: '100%',
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
              padding: '16px 20px 8px 24px',
              borderBottom: '2px solid #F2F4F7',
              background: '#F9FAFB',
            }}
          >
            <Box sx={{ flexDirection: 'column' }}>
              <Typography sx={{ fontWeight: 700 }}>Reset </Typography>
              <Typography sx={{ fontSize: '14px' }}>
                Please note that this action will reset both PIN and security
                questions for the customer.
              </Typography>
            </Box>
            <IconButton
              sx={{
                border: '0.8px solid #CBD5E1',
              }}
              onClick={handleClose}
            >
              <CloseRounded />
            </IconButton>
          </DialogTitle>
          <DialogContent
            sx={{
              padding: '0px 40px 20px 40px',
              display: 'flex',
              flexDirection: 'column',
              gap: '20px',
            }}
          >
            <Typography
              sx={{
                textWrap: 'nowrap',
              }}
            >
              Please give a reason for resetting.
            </Typography>
            {!selectedOption &&
              resetReasons &&
              resetReasons.map((reason, index) => (
                <Box
                  key={reason}
                  sx={{
                    display: 'flex',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                    border:
                      selectedOption === reason
                        ? '2px solid #555C61'
                        : '2px solid #D0D5DD',
                    borderRadius: '6px',
                    padding: '1px',
                  }}
                >
                  <Typography sx={{ padding: '0 4px' }}>{reason}</Typography>
                  <CustomCheckBox
                    checked={selectedOption === reason}
                    onChange={() => setSelectedOptin(reason)}
                  />
                </Box>
              ))}
            {selectedOption && (
              <TextField
                fullWidth
                multiline
                rows={4}
                sx={{}}
                placeholder="Type your reason here"
                value={customReason}
                onChange={(e) => {
                  setHasTouched(true)
                  setCustomReason(e.target.value)
                }}
                error={Boolean(hasTouched && error)}
                helperText={hasTouched && error ? error : ''}
              />
            )}

            <Box
              sx={{
                display: 'flex',
                justifyContent: 'space-between',
                gap: '10px',
              }}
            >
              <Button
                variant="outlined"
                sx={{
                  width: '167px',
                  height: '40px',
                  padding: '8px 42px',
                  border: '1.5px solid #808488',
                  borderRadius: '6px',
                }}
                onClick={handleClose}
              >
                Cancel
              </Button>
              {!isLoadingSecurity ? (
                <Button
                  variant="contained"
                  disabled={!validateReason(customReason)}
                  sx={{
                    width: '167px',
                    height: '40px',
                    padding: '8px 42px',
                    background: '#EB0045',
                    borderRadius: '6px',
                    '&:hover': {
                      background: '#EB0045 ',
                      opacity: '0.5',
                    },
                  }}
                  onClick={handleReset}
                >
                  Reset
                </Button>
              ) : (
                <LoadingButton width="167px" height="40px" />
              )}
            </Box>
          </DialogContent>
        </Box>
      </Dialog>
    </>
  )
}
