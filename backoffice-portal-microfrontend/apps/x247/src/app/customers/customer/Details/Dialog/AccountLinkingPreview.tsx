import { List, ListItem, Stack, Typography } from '@mui/material'
import React from 'react'
import { IApprovalRequest } from '@/store/interfaces'
import { AccountsSummaryIcon } from '@dtbx/ui/icons'

export const AccountLinkingPreview = ({
  selectedApprovalRequest,
}: {
  selectedApprovalRequest: IApprovalRequest
}) => {
  const newValue: string = selectedApprovalRequest.diff.find(
    (val) => val.field === 'accounts'
  )?.newValue as string
  const accountsList: string[] = newValue.split(',')
  return (
    <Stack>
      <Typography>Accounts to be linked:</Typography>
      {accountsList.map((account, index) => (
        <Stack
          key={index}
          sx={{
            py: '2%',
            px: '3%',
            border: '1px solid #E0E0E0',
            borderRadius: '4px',
            background: '#F9FAFB',
            flexDirection: 'row',
            justifyContent: 'space-between',
            marginBottom: '10px',
          }}
        >
          <Stack
            sx={{
              flexDirection: 'row',
              justifyContent: 'space-between',
              alignContent: 'center',
              alignItems: 'flex-start',
              gap: '10px',
            }}
          >
            <AccountsSummaryIcon />
            <Stack>
              <Typography
                variant="subtitle2"
                color={'text.primary'}
                sx={{
                  textWrap: 'noWrap',
                }}
              >
                Account {account}
              </Typography>
              <List
                sx={{
                  listStyleType: 'disc',
                  paddingLeft: '15px',
                  paddingTop: '0px',
                }}
              >
                <ListItem sx={{ display: 'list-item', padding: '0px' }}>
                  <Typography
                    variant="subtitle2"
                    color={'text.primary'}
                    sx={{
                      textWrap: 'noWrap',
                      color: '#555C61',
                      fontSize: '14px',
                    }}
                  >
                    Add account
                  </Typography>
                </ListItem>
                <ListItem sx={{ display: 'list-item', padding: '0px' }}>
                  <Typography
                    variant="subtitle2"
                    color={'text.primary'}
                    sx={{
                      textWrap: 'noWrap',
                      color: '#555C61',
                      fontSize: '14px',
                    }}
                  >
                    Add notifications
                  </Typography>
                </ListItem>
                <ListItem sx={{ display: 'list-item', padding: '0px' }}>
                  <Typography
                    variant="subtitle2"
                    color={'text.primary'}
                    sx={{
                      textWrap: 'noWrap',
                      color: '#555C61',
                      fontSize: '14px',
                    }}
                  >
                    Add subscriptions
                  </Typography>
                </ListItem>
              </List>
            </Stack>
          </Stack>
        </Stack>
      ))}
    </Stack>
  )
}
