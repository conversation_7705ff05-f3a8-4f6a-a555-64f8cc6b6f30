import {
  BlockOutlined,
  CloseRounded,
  DeleteOutline,
  FiberManualRecord,
  History,
  MoreVertOutlined,
} from '@mui/icons-material'
import ArrowBackIcon from '@mui/icons-material/ArrowBack'
import CloseIcon from '@mui/icons-material/Close'
import React, { useState } from 'react'
import KeyboardArrowDownIcon from '@mui/icons-material/KeyboardArrowDown'
import {
  Box,
  Button,
  DialogTitle,
  Drawer,
  IconButton,
  Menu,
  MenuItem,
  Paper,
  Stack,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Typography,
} from '@mui/material'
import { useAppDispatch, useAppSelector } from '@/store'
import {
  ICustomer,
  ICustomerAccountEventHistory,
  ICustomerProfileAccount,
} from '@/store/interfaces'
import {
  activateCustomerAccount,
  deactivateCustomerAccount,
  getCustomerAccountByAccountNo,
  getCustomerAccountsLogsBackoffice,
  getCustomersAccountHistory,
  getLinkedCustomerAccountsByProfileId,
  makerRestrictAccount,
  makeUnlinkCustomerAccounts,
  unlinkCustomerAccount,
} from '@/store/actions'
import { setIsViewAccountOpen } from '@/store/reducers'
import { AccountDetailsIcon, LockIcon } from '@dtbx/ui/icons'
import {
  AccessControlWrapper,
  HasAccessToRights,
  reasonsForActivation,
  reasonsForDeactivating,
  reasonsForDeleting,
  reasonsForUnlinking,
  restrictReasons,
} from '@dtbx/store/utils'
import { Dialog } from '@dtbx/ui/components/Overlay'
import CustomerAccountsHistory from './History/CustomerAccountsHistory'

export const AccountsMoreMenu = ({
  account,
}: {
  account: ICustomerProfileAccount
}) => {
  const dispatch = useAppDispatch()
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null)
  const open = Boolean(anchorEl)
  const handleClick = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget)
  }
  const handleClose = () => {
    setAnchorEl(null)
  }
  const { customer } = useAppSelector((state) => state.customers)
  const handleMoreDetails = async () => {
    const accountDetails = await getCustomerAccountByAccountNo(
      customer.id ? customer.id : '',
      account.accountNo,
      dispatch
    )
    dispatch(setIsViewAccountOpen(!!accountDetails?.profile?.phoneNumber))
  }
  return (
    <>
      <Button
        id="demo-customized-button"
        aria-controls={open ? 'demo-customized-menu' : undefined}
        aria-haspopup="true"
        aria-expanded={open ? 'true' : undefined}
        variant="outlined"
        disableElevation
        onClick={handleClick}
        sx={{
          border: '1px solid #D0D5DD',
          padding: '8px 14px',
          fontWeight: '500',
          gap: 0,
        }}
        endIcon={<KeyboardArrowDownIcon />}
      >
        Actions
      </Button>

      <Menu
        id="demo-customized-menu"
        slotProps={{
          list: {
            'aria-labelledby': 'demo-customized-button',
          },
        }}
        anchorEl={anchorEl}
        onClose={handleClose}
        open={open}
        sx={{
          borderRadius: '4px',
        }}
      >
        <MenuItem onClick={handleMoreDetails}>See More Details</MenuItem>
        <DeleteAccount
          isMainMenu={true}
          account={account}
          customer={customer}
        />
        {account.status === 'INACTIVE' ? (
          <ActivateAccount
            isMainMenu={true}
            account={account}
            customer={customer}
          />
        ) : (
          <DeactivateAccount
            isMainMenu={true}
            account={account}
            customer={customer}
          />
        )}
      </Menu>
    </>
  )
}

const DisabledIcon: React.FC<{
  isDisabled: boolean
  children: React.ReactNode
}> = ({ isDisabled, children }) => {
  return (
    <span
      style={{
        opacity: isDisabled ? 0.5 : 1,
        cursor: isDisabled ? 'not-allowed' : 'pointer',
      }}
    >
      {children}
    </span>
  )
}

export const RestrictAccount = () => {
  const dispatch = useAppDispatch()
  const { customerProfileAccount, customer } = useAppSelector(
    (state) => state.customers
  )

  const handleClose = (e: unknown, action: string) => {
    if (action !== 'backdropClick') {
      setOpen(false)
    }
  }
  const handleStatusChange = async (reasons: string[]) => {
    const reason = reasons.join(', ')
    if (customerProfileAccount.status === 'RESTRICTED') {
      await activateCustomerAccount({
        accountNo: customerProfileAccount.accountNo,
        profileId: customerProfileAccount.profile.id,
        comments: reason,
        dispatch,
        setOpen: () => {},
        setIsLoading: () => {},
        isRestrict: true,
      })
    } else {
      await makerRestrictAccount({
        profileId: customerProfileAccount.profile.id,
        accountNo: customerProfileAccount.accountNo,
        comments: reason,
        dispatch,
      })
    }

    handleClose(null, 'close')
  }

  const isLoading = false
  const [open, setOpen] = useState<boolean>(false)
  return (
    <>
      <Button
        variant="text"
        sx={{
          color: '#555C61',
          fontWeight: '500',
          justifyContent: 'flex-start',
          paddingLeft: '0px',
        }}
        onClick={() => setOpen(!open)}
        startIcon={<LockIcon />}
        disabled={customer.isBlocked} // Disable the button based on the global state
      >
        {customerProfileAccount.status === 'RESTRICTED'
          ? 'UnRestrict'
          : 'Restrict'}
      </Button>
      <Dialog
        open={open}
        setOpen={setOpen}
        concatReason
        title={`${customerProfileAccount.status === 'RESTRICTED' ? 'Un' : ''}Restrict Account ${customerProfileAccount.accountNo}`}
        buttonText={
          customerProfileAccount.status === 'RESTRICTED'
            ? 'Reactivate'
            : 'Restrict'
        }
        buttonProps={{
          color: '#EB0045',
        }}
        descriptionText={`Please give a reason for ${customerProfileAccount.status === 'RESTRICTED' ? 'reactivating' : 'restricting'} this account`}
        isLoading={isLoading}
        onClick={async (reason) => handleStatusChange(reason)}
        reasons={
          customerProfileAccount.status === 'RESTRICTED'
            ? reasonsForActivation
            : restrictReasons
        }
      />
    </>
  )
}
export const DeactivateAccount = ({
  isMainMenu,
  account,
  customer,
}: {
  isMainMenu: boolean
  account: ICustomerProfileAccount
  customer: ICustomer
}) => {
  const dispatch = useAppDispatch()
  const handleClose = (e: unknown, action: string) => {
    if (action !== 'backdropClick') {
      setOpen(false)
    }
  }
  const handleStatusChange = async (reasons: string[]) => {
    const reason = reasons.join(', ')
    await deactivateCustomerAccount({
      accountNo: account.accountNo,
      profileId: account.id.profileId,
      comments: reason,
      setOpen,
      setIsLoading,
      dispatch,
    })
    await getLinkedCustomerAccountsByProfileId(account.profile.id, dispatch)
    handleClose(null, 'close')
  }
  const [isLoading, setIsLoading] = useState<boolean>(false)
  const [open, setOpen] = useState<boolean>(false)
  return (
    <>
      <AccessControlWrapper
        rights={['SUPER_DEACTIVATE_ACCOUNTS', 'MAKE_DEACTIVATE_ACCOUNTS']}
      >
        {isMainMenu ? (
          <MenuItem
            onClick={() => setOpen(!open)}
            disabled={customer.isBlocked}
          >
            Deactivate Account
          </MenuItem>
        ) : (
          <Button
            variant="text"
            sx={{
              color: '#555C61',
              fontWeight: '500',
              justifyContent: 'flex-start',
              paddingLeft: '0px',
            }}
            disabled={customer.isBlocked}
            onClick={() => setOpen(!open)}
            startIcon={<BlockOutlined />}
          >
            Deactivate
          </Button>
        )}
      </AccessControlWrapper>

      <Dialog
        open={open}
        setOpen={setOpen}
        title={`Deactivate Account ${account.accountNo}`}
        buttonText={'Deactivate'}
        buttonProps={{
          color: '#EB0045',
        }}
        descriptionText={'Please give a reason for deactivating this account'}
        isLoading={isLoading}
        onClick={(reasons) => handleStatusChange(reasons)}
        reasons={reasonsForDeactivating}
      />
    </>
  )
}
export const ActivateAccount = ({
  isMainMenu,
  account,
  customer,
}: {
  isMainMenu: boolean
  account: ICustomerProfileAccount
  customer: ICustomer
}) => {
  const dispatch = useAppDispatch()
  const handleClose = (e: unknown, action: string) => {
    if (action !== 'backdropClick') {
      setOpen(false)
    }
  }
  const handleStatusChange = async (reasons: string[]) => {
    const reason = reasons.join(', ')
    await activateCustomerAccount({
      accountNo: account.accountNo,
      profileId: account.id.profileId,
      comments: reason,
      setOpen,
      setIsLoading,
      dispatch,
    })
    await getLinkedCustomerAccountsByProfileId(account.profile.id, dispatch)
    handleClose(null, 'close')
  }
  const [isLoading, setIsLoading] = useState<boolean>(false)
  const [open, setOpen] = useState<boolean>(false)
  return (
    <>
      <AccessControlWrapper
        rights={['SUPER_ACTIVATE_ACCOUNTS', 'MAKE_ACTIVATE_ACCOUNTS']}
      >
        {isMainMenu ? (
          <MenuItem
            onClick={() => setOpen(!open)}
            disabled={customer.isBlocked}
          >
            Activate Account
          </MenuItem>
        ) : (
          <Button
            variant="text"
            sx={{
              color: '#555C61',
              fontWeight: '500',
              justifyContent: 'flex-start',
              paddingLeft: '0px',
            }}
            disabled={customer.isBlocked}
            onClick={() => setOpen(!open)}
            startIcon={<BlockOutlined />}
          >
            Activate
          </Button>
        )}
      </AccessControlWrapper>
      <Dialog
        open={open}
        setOpen={setOpen}
        title={`Activate Account ${account.accountNo}`}
        buttonText={'Activate'}
        buttonProps={{
          color: '#EB0045',
        }}
        descriptionText={'Please give a reason for activating this account'}
        isLoading={isLoading}
        onClick={(reasons) => handleStatusChange(reasons)}
        reasons={reasonsForActivation}
      />
    </>
  )
}
export const DeleteAccount = ({
  isMainMenu,
  account,
  customer,
}: {
  isMainMenu: boolean
  account: ICustomerProfileAccount
  customer: ICustomer
}) => {
  const dispatch = useAppDispatch()
  const handleClose = (e: unknown, action: string) => {
    if (action !== 'backdropClick') {
      setOpen(false)
    }
  }
  const { isUnlinkingLoadingSuper, isUnlinkingLoading } = useAppSelector(
    (state) => state.customers
  )
  const handleUnlinkAccount = async (reasons: string[]) => {
    const reason = reasons.join(',')
    if (HasAccessToRights(['SUPER_DELETE_ACCOUNTS'])) {
      await unlinkCustomerAccount(dispatch, {
        accountNo: account.accountNo,
        profileID: account.id.profileId,
        comments: reason,
      })
      handleClose(null, 'close')
    } else {
      await makeUnlinkCustomerAccounts(dispatch, {
        accountNo: account.accountNo,
        profileID: account.id.profileId,
        comments: reason,
      })
      handleClose(null, 'close')
    }
  }
  const [open, setOpen] = useState<boolean>(false)
  return (
    <>
      <AccessControlWrapper
        rights={['SUPER_DELETE_ACCOUNTS', 'MAKE_DELETE_ACCOUNTS']}
      >
        {isMainMenu ? (
          <MenuItem
            onClick={() => {
              setOpen(!open)
            }}
            disabled={customer.isBlocked}
          >
            Delete Account
          </MenuItem>
        ) : (
          <Button
            variant="text"
            disabled={customer.isBlocked}
            sx={{
              color: '#555C61',
              fontWeight: '500',
              justifyContent: 'flex-start',
              paddingLeft: '0px',
            }}
            onClick={() => setOpen(!open)}
            startIcon={<BlockOutlined />}
          >
            Delete
          </Button>
        )}
      </AccessControlWrapper>

      <Dialog
        open={open}
        setOpen={setOpen}
        title={`Delete Account ${account.accountNo}`}
        buttonText={'Delete'}
        buttonProps={{
          color: '#EB0045',
        }}
        descriptionText={'Please give a reason for deleting this account'}
        isLoading={
          HasAccessToRights(['SUPER_DELETE_ACCOUNTS'])
            ? isUnlinkingLoadingSuper
            : isUnlinkingLoading
        }
        onClick={(reasons) => handleUnlinkAccount(reasons)}
        reasons={reasonsForDeleting}
      />
    </>
  )
}

export const UnlinkAccount = ({
  account,
  customer,
}: {
  account: ICustomerProfileAccount
  customer: ICustomer
}) => {
  const dispatch = useAppDispatch()
  const handleClose = (e: unknown, action: string) => {
    if (action !== 'backdropClick') {
      setOpen(false)
    }
  }
  const handleStatusChange = async (reasons: string[]) => {
    const reason = reasons.join(',')
    if (HasAccessToRights(['SUPER_DELETE_ACCOUNTS'])) {
      await unlinkCustomerAccount(dispatch, {
        accountNo: account.accountNo,
        profileID: account.id.profileId,
        comments: reason,
      })
      handleClose(null, 'close')
    } else {
      await makeUnlinkCustomerAccounts(dispatch, {
        accountNo: account.accountNo,
        profileID: account.id.profileId,
        comments: reason,
      })
      handleClose(null, 'close')
    }
  }
  const isLoading = false
  const [open, setOpen] = useState<boolean>(false)
  return (
    <>
      <Button
        variant="text"
        sx={{
          color: '#555C61',
          fontWeight: '500',
          justifyContent: 'flex-start',
          paddingLeft: '0px',
        }}
        onClick={() => setOpen(!open)}
        disabled={customer.isBlocked}
        startIcon={<DeleteOutline />}
      >
        Unlink
      </Button>
      <Dialog
        open={open}
        setOpen={setOpen}
        title={`Unlink Account ${account.accountNo}`}
        buttonText={'Unlink'}
        buttonProps={{
          color: '#EB0045',
        }}
        descriptionText={'Please give a reason for unlinking this account'}
        isLoading={isLoading}
        onClick={(reasons) => handleStatusChange(reasons)}
        reasons={reasonsForUnlinking}
      />
    </>
  )
}

export const ViewAccountHistory = () => {
  const dispatch = useAppDispatch()
  const { customerProfileAccount } = useAppSelector((state) => state.customers)
  const [open, setOpen] = useState<boolean>(false)

  return (
    <>
      <Button
        variant="text"
        sx={{
          color: '#555C61',
          fontWeight: '500',
          justifyContent: 'flex-start',
          paddingLeft: '0px',
        }}
        onClick={async () => {
          await getCustomerAccountsLogsBackoffice(
            dispatch,
            customerProfileAccount?.accountNo
          )
          await getCustomersAccountHistory({
            dispatch,
            profileID: customerProfileAccount?.profile.id,
            accountNo: customerProfileAccount?.accountNo,
          })
          setOpen(true)

          //
        }}
        startIcon={<History />}
      >
        View History
      </Button>
      <Drawer open={open} anchor="right">
        <Paper
          elevation={0}
          sx={{
            height: '100vh',
            display: 'flex',
            flexDirection: 'column',
            justifyContent: 'flex-start',
            gap: '10px',
            transition: 'width 3.3s ease-in-out',
          }}
        >
          <Box
            sx={{
              minWidth: '52vw',
              width: '100%',
              maxHeight: '60px',
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
              padding: '16px 20px 8px 24px',
              borderBottom: '1px solid lightgray',
              backgroundColor: '#F9FAFB',
            }}
          >
            {/* DrawerHeader */}
            <Box
              sx={{
                display: 'flex',
                gap: '10px',
                justifyContent: 'flex-start',
                alignItems: 'center',
              }}
            >
              <Typography variant="h6">Account History</Typography>
            </Box>
            <IconButton
              sx={{
                border: '1px solid #CBD5E1',
                backgroundColor: '#F1F5F9',
              }}
              onClick={() => {
                setOpen(false)
              }}
            >
              <CloseRounded
                sx={{
                  fontSize: '20px',
                }}
              />
            </IconButton>
          </Box>
          <Stack>
            <CustomerAccountsHistory />
          </Stack>
        </Paper>
      </Drawer>
    </>
  )
}
interface IMoreAccountEventDetails {
  event: ICustomerAccountEventHistory
}

export const MoreAccountEventDetails = ({
  event,
}: IMoreAccountEventDetails) => {
  const [open, setOpen] = useState<boolean>(false)
  const handleClose = (e: unknown, action: string) => {
    if (action !== 'backdropClick') {
      setOpen(false)
    }
  }
  const handleOpen = () => {
    setOpen(true)
    handleCloseMenu()
  }
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null)
  const openMenu = Boolean(anchorEl)
  const handleClickMenu = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget)
  }
  const handleCloseMenu = () => {
    setAnchorEl(null)
  }
  return (
    <>
      <IconButton onClick={handleClickMenu}>
        <MoreVertOutlined />
      </IconButton>
      <Menu
        id="demo-customized-menu"
        MenuListProps={{
          'aria-labelledby': 'demo-customized-button',
        }}
        anchorEl={anchorEl}
        onClose={handleCloseMenu}
        open={openMenu}
        sx={{
          borderRadius: '4px',
        }}
      >
        <MenuItem onClick={handleOpen}>See more details</MenuItem>
      </Menu>
      <Drawer
        sx={{
          '.MuiDrawer-paper': {
            width: '70%',
          },
        }}
        open={open}
        anchor={'right'}
        onClose={handleClose}
        PaperProps={{
          sx: {
            width: '70%',
          },
        }}
      >
        <Box
          sx={{
            background: '#F9FAFB',
            borderBottom: '2px solid  #F2F4F7',
          }}
        >
          <Stack
            flexDirection="row"
            sx={{
              alignItems: 'center',
            }}
          >
            <DialogTitle
              sx={{
                display: 'flex',
                flexDirection: 'row',
                alignItems: 'center',
                alignContent: 'center',
                gap: '5px',
              }}
            >
              <IconButton
                sx={{
                  border: '1px solid #D0D5DD',
                }}
                size="small"
                onClick={(e) => handleClose(e, 'close')}
              >
                <ArrowBackIcon />
              </IconButton>
              <Typography variant="subtitle2" color={'primary.main'}>
                {event.type}
              </Typography>
            </DialogTitle>
          </Stack>
          <IconButton
            aria-label="close"
            onClick={(e) => handleClose(e, 'close')}
            sx={{
              position: 'absolute',
              right: 8,
              top: 8,
              color: '',
            }}
          >
            <CloseIcon />
          </IconButton>
        </Box>
        <Stack
          sx={{
            px: '2%',
          }}
        >
          <Stack
            sx={{
              py: '3%',
              flexDirection: 'row',
              gap: '10px',
            }}
          >
            <AccountDetailsIcon />
            <Stack direction="column">
              <Typography variant="subtitle2">Account 1234566</Typography>
              <Typography variant="label1">
                {' '}
                <FiberManualRecord
                  sx={{
                    fontSize: '10px',
                  }}
                />
                {event.type}
              </Typography>
            </Stack>
          </Stack>
          <Stack>
            <Typography>Account Details</Typography>
            <TableContainer
              component={Paper}
              elevation={0}
              sx={{
                border: '1px solid #EAECF0',
              }}
            >
              <Table>
                <TableHead sx={{ background: '#F9FAFB' }}>
                  <TableRow>
                    <TableCell>Field</TableCell>
                    <TableCell>Data</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody></TableBody>
              </Table>
            </TableContainer>
          </Stack>
          <Stack>
            <Typography>Subscriptions(E-Statements)</Typography>
            <TableContainer
              component={Paper}
              elevation={0}
              sx={{
                border: '1px solid #EAECF0',
              }}
            >
              <Table>
                <TableHead sx={{ background: '#F9FAFB' }}>
                  <TableRow>
                    <TableCell>Type</TableCell>
                    <TableCell>Email</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody></TableBody>
              </Table>
            </TableContainer>
          </Stack>
          <Stack>
            <Typography>Notifications</Typography>
            <TableContainer
              component={Paper}
              elevation={0}
              sx={{
                border: '1px solid #EAECF0',
              }}
            >
              <Table>
                <TableHead sx={{ background: '#F9FAFB' }}>
                  <TableRow>
                    <TableCell>Type</TableCell>
                    <TableCell>Channels</TableCell>
                    <TableCell>Phone numbers</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody></TableBody>
              </Table>
            </TableContainer>
          </Stack>
          <Stack>
            <Typography>Alerts</Typography>
            <TableContainer
              component={Paper}
              elevation={0}
              sx={{
                border: '1px solid #EAECF0',
              }}
            >
              <Table>
                <TableHead sx={{ background: '#F9FAFB' }}>
                  <TableRow>
                    <TableCell>Type</TableCell>
                    <TableCell>Channels</TableCell>
                    <TableCell>Phone numbers</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody></TableBody>
              </Table>
            </TableContainer>
          </Stack>
        </Stack>
      </Drawer>
    </>
  )
}
