import { Button, Stack, Typography } from '@mui/material'
import React from 'react'
import { sentenceCase } from 'tiny-case'
import { useAppDispatch, useAppSelector } from '@/store'
import { getCustomerDevices } from '@/store/actions'

const EmptySearchAndFilter = () => {
  const { deviceFilters, deviceSearchParams, selectedCustomer } =
    useAppSelector((state) => state.customers)
  const dispatch = useAppDispatch()
  return (
    <Stack
      sx={{
        height: '100%',
        width: '100%',
        justifyContent: 'center',
        alignItems: 'center',
        padding: '10px',
      }}
    >
      <Stack
        sx={{
          width: '480px',
          height: '480px',
          backgroundImage: 'url(/dashboard/combo.svg)',
          justifyContent: 'flex-end',
          gap: '45px',
        }}
      >
        <Stack
          sx={{
            justifyContent: 'center',
            alignItems: 'center',
          }}
        ></Stack>
        <Stack
          sx={{
            justifyContent: 'center',
            alignItems: 'center',
            gap: '32px',
          }}
        >
          {/*header  */}
          <Stack
            sx={{
              justifyContent: 'center',
              alignItems: 'center',
              gap: '8px',
              width: '65%',
            }}
          >
            <Typography variant="subtitle1">No device was found</Typography>

            {deviceFilters.filterValue.length > 0 ||
            deviceSearchParams.searchValue.length > 0 ? (
              <Typography
                variant="subtitle3"
                sx={{
                  textAlign: 'center',
                }}
              >
                Your{' '}
                {deviceSearchParams.searchValue.length > 0
                  ? `search "${deviceSearchParams.searchValue}" by ${deviceSearchParams.searchBy[0].length > 0 ? sentenceCase(deviceSearchParams.searchBy[0]) : ''} `
                  : deviceFilters.filterValue.length > 0
                    ? 'filter criteria'
                    : 'search and filter criteria '}
                did not match any devices. Please try again
              </Typography>
            ) : null}
          </Stack>

          {/* buttons */}
          <Stack
            sx={{
              justifyContent: 'center',
              flexDirection: 'row',
              gap: '20px',
              marginTop: '20px',
            }}
          >
            {deviceFilters.filterValue.length > 0 ||
            deviceSearchParams.searchValue.length > 0 ? (
              <Button
                variant="outlined"
                onClick={() => {
                  getCustomerDevices({
                    params: {
                      profileID: selectedCustomer.customer?.id
                        ? selectedCustomer.customer.id
                        : '',
                      page: 0,
                      size: 7,
                      deviceType: '',
                    },
                    dispatch,
                  })
                }}
              >
                Clear search
              </Button>
            ) : null}
          </Stack>
        </Stack>
      </Stack>
    </Stack>
  )
}

export default EmptySearchAndFilter
