import { Box, Stack } from '@mui/material'
import React from 'react'
import { CustomSkeleton } from '@dtbx/ui/components/Loading'

const LoadingDeviceDetails = () => {
  return (
    <Stack
      sx={{
        height: '100%',
        gap: '17px',
        px: '1%',
        py: '1%',
      }}
    >
      <Stack
        sx={{
          width: '100%',
          height: '50vh',
          padding: '20px 40px',
          border: '1px solid #D0D5DD',
          gap: '10px',
          justifyContent: 'space-between',
        }}
      >
        <CustomSkeleton variant="rectangular" width="150px" height="40px" />
        <Stack
          sx={{
            width: '100%',
            height: '30%',
          }}
        >
          <Stack
            sx={{
              flexDirection: 'row',
              width: '100%',
              justifyContent: 'space-between',
              alignItems: 'center',
            }}
          >
            {/* avaatar box */}
            <Box
              sx={{
                display: 'flex',
                flexDirection: 'row',
                gap: '20px',
                justifyContent: 'flex-start',
                alignItems: 'center',
              }}
            >
              <CustomSkeleton
                variant="circular"
                width="60px"
                height="60px"
                sx={{
                  borderRadius: '50%',
                }}
              />
              <Box
                sx={{
                  display: 'flex',
                  flexDirection: 'column',
                  gap: '10px',
                }}
              >
                <CustomSkeleton
                  variant="rectangular"
                  width="150px"
                  height="30px"
                />
                <CustomSkeleton
                  variant="rectangular"
                  width="75px"
                  height="20px"
                />
              </Box>
            </Box>

            {/* buttons */}
            <Box
              sx={{
                display: 'flex',
                gap: '20px',
              }}
            >
              <CustomSkeleton
                variant="rectangular"
                width="150px"
                height="40px"
              />
              <CustomSkeleton
                variant="rectangular"
                width="150px"
                height="40px"
              />
            </Box>
          </Stack>
        </Stack>
        <Stack
          sx={{
            display: 'grid',
            gridTemplateColumns: 'repeat(3, 2fr)',
            width: '100%',
            height: '60%',
            gap: '10px',
          }}
        >
          {Array.from({ length: 12 }).map((_, index) => (
            <CustomSkeleton key={index} variant="rectangular" height={'50px'} />
          ))}
        </Stack>
      </Stack>
    </Stack>
  )
}

export default LoadingDeviceDetails
