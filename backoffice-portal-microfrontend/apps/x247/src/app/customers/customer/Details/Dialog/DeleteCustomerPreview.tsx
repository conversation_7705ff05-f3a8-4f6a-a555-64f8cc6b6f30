import { Stack } from '@mui/material'
import React from 'react'
import { IApprovalRequest } from '@/store/interfaces'
import { ReadOnlyTypography } from '@dtbx/ui/components'
import { handleDiff } from '@dtbx/store/utils'

export const DeleteCustomerPreview = ({
  selectedApprovalRequest,
}: {
  selectedApprovalRequest: IApprovalRequest
}) => {
  return (
    <Stack>
      <ReadOnlyTypography
        name="changesMade"
        value={handleDiff(selectedApprovalRequest.diff)}
        id="changesMade"
        minRows={2}
        multiline
      />
    </Stack>
  )
}
