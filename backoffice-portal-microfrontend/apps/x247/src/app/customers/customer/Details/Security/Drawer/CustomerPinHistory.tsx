import {
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableRow,
} from '@mui/material'

import { PinHistoryEmptyState } from './EmptyState'
import { IHeadCell } from '@dtbx/store/interfaces'
import { useAppDispatch, useAppSelector } from '@/store'
import {
  CustomPagination,
  CustomTableHeader,
  PaginationOptions,
} from '@dtbx/ui/components/Table'
import { CustomSkeleton, LoadingListsSkeleton } from '@dtbx/ui/components'
import { useEffect, useState } from 'react'
import { getCustomerPinHistory } from '@/store/actions'
import { formatTimestamp } from '@dtbx/store/utils'
import { ICustomer } from '@/store/interfaces'

const header: IHeadCell[] = [
  {
    id: 'event',
    label: 'Event Type',
    alignCenter: false,
    alignRight: false,
  },
  {
    id: 'eventTimestamp',
    label: 'Event Timestamp',
    alignCenter: false,
    alignRight: false,
  },
  {
    id: 'eventSource',
    label: 'Event Source',
    alignCenter: false,
    alignRight: false,
  },
  {
    id: 'deviceId',
    label: 'Device Id',
    alignCenter: false,
    alignRight: false,
  },
]

const CustomerPinHistory: React.FC<{
  page: number
  onPageChange: (newPage: number) => void
}> = ({ page, onPageChange }) => {
  const dispatch = useAppDispatch()
  const { customer, customerPinLogResponse, isLoadingCustomerPinHistory } =
    useAppSelector((state) => state.customers)
  const [paginationOptions, setPaginationOptions] = useState({
    page: customerPinLogResponse.pageNumber || page,
    size: customerPinLogResponse.pageSize || 10,
    totalPages: customerPinLogResponse.totalNumberOfPages,
  })
  /*************************start pagination handlers***************************/
  const handlePagination = async (newOptions: PaginationOptions) => {
    onPageChange(newOptions.page) // Update pagination state

    await getCustomerPinHistory({
      profileID: customer?.id || '',
      dispatch,
      ...newOptions,
      pinType: 'PIN',
    })
  }
  /*************************end pagination handlers**************************/
  useEffect(() => {
    getCustomerPinHistory({
      profileID: customer?.id || '',
      dispatch,
      page: 1,
      size: 10,
      pinType: 'PIN',
    })
  }, [])

  return (
    <Paper
      elevation={0}
      sx={{
        border: '1px solid #EAECF0',
        boxShadow:
          '0px 1px 3px 0px rgba(16, 24, 40, 0.10), 0px 1px 2px 0px rgba(16, 24, 40, 0.06)',
      }}
    >
      <TableContainer>
        {isLoadingCustomerPinHistory ? (
          <LoadingListsSkeleton />
        ) : customerPinLogResponse?.data?.length > 0 ? (
          <Table stickyHeader sx={{ minWidth: '70vw' }}>
            <CustomTableHeader
              order={'desc'}
              orderBy={''}
              rowCount={0}
              headLabel={[...header]}
              numSelected={0}
            />
            <TableBody>
              {customerPinLogResponse &&
                customerPinLogResponse.data?.map((row) => {
                  return (
                    <TableRow key={row?.id}>
                      <TableCell sx={{ textWrap: 'nowrap' }}>
                        {row?.event}
                      </TableCell>
                      <TableCell sx={{ textWrap: 'nowrap' }}>
                        {formatTimestamp(row?.eventDate)}
                      </TableCell>
                      <TableCell sx={{ textWrap: 'nowrap' }}>
                        {row?.eventSource}
                      </TableCell>
                      <TableCell>{row?.deviceId ?? '-'}</TableCell>
                    </TableRow>
                  )
                })}
            </TableBody>
          </Table>
        ) : (
          <PinHistoryEmptyState />
        )}
      </TableContainer>
      {customerPinLogResponse.totalNumberOfPages > 0 && (
        <CustomPagination
          options={{
            ...paginationOptions,
            totalPages: customerPinLogResponse.totalNumberOfPages,
          }}
          handlePagination={handlePagination}
          key={'pagination_pending_requests'}
        />
      )}
    </Paper>
  )
}

export default CustomerPinHistory
