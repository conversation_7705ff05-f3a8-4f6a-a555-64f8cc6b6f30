import { But<PERSON>, <PERSON>, ListItem, Stack, Typography } from '@mui/material'
import React from 'react'
import CallMadeIcon from '@mui/icons-material/CallMade'
import {
  fetchNotificationEventsPerAccount,
  getBankBranches,
  getCustomerAccountByAccountNo,
} from '@/store/actions'
import { useAppDispatch, useAppSelector } from '@/store'
import { AccountsSummaryIcon } from '@dtbx/ui/icons'
import { IApprovalRequest, ICustomerAccount } from '@/store/interfaces'
import { setCustomerProfileAccount } from '@/store/reducers'

export const AccountsEditPreview = ({
  selectedApprovalRequest,
  setOpenAccountDetails,
}: {
  selectedApprovalRequest: IApprovalRequest
  setOpenAccountDetails: (val: boolean) => void
}) => {
  const dispatch = useAppDispatch()
  const { customer } = useAppSelector((state) => state.customers)
  const entity = JSON.parse(selectedApprovalRequest?.entity || '')
  const accountNos: string[] =
    entity?.accounts && entity?.accounts.length > 0
      ? entity.accounts.map((acc: ICustomerAccount) => acc.accNumber)
      : entity?.accountNo
        ? [entity?.accountNo]
        : ''
  return (
    <Stack>
      {accountNos.map((account) => (
        <Stack
          sx={{
            py: '2%',
            px: '5%',
            border: '1px solid #E0E0E0',
            borderRadius: '4px',
            flexDirection: 'row',
            marginBottom: '10px',
            alignItems: 'center',
          }}
          key={account}
        >
          <Stack
            sx={{
              flexDirection: 'row',
              alignItems: 'flex-start',
              gap: '10px',
            }}
          >
            <Stack sx={{ marginTop: '2%', zoom: '120%', marginRight: '2%' }}>
              <AccountsSummaryIcon />
            </Stack>
            <Stack>
              <Typography
                variant="subtitle2"
                color={'text.primary'}
                sx={{
                  textWrap: 'noWrap',
                }}
              >
                Account {account}
              </Typography>
              <List
                sx={{
                  listStyleType: 'disc',
                  paddingLeft: '15px',
                  paddingTop: '0px',
                }}
              >
                <ListItem sx={{ display: 'list-item', padding: '0px' }}>
                  <Typography
                    variant="subtitle2"
                    color={'text.primary'}
                    sx={{
                      textWrap: 'noWrap',
                      color: '#555C61',
                      fontSize: '14px',
                    }}
                  >
                    {selectedApprovalRequest?.makerCheckerType?.name}
                  </Typography>
                </ListItem>
              </List>
            </Stack>
          </Stack>
          <Stack sx={{ marginLeft: 'auto' }}>
            <Button
              variant="outlined"
              onClick={async () => {
                let accountDetails = await getCustomerAccountByAccountNo(
                  customer.id || '',
                  account,
                  dispatch
                )
                accountDetails = { ...accountDetails }

                await fetchNotificationEventsPerAccount({
                  accountId: account,
                  dispatch,
                })
                await getBankBranches(dispatch)
                if (
                  entity?.accounts?.find(
                    (acc: ICustomerAccount) => acc.accNumber === account
                  )
                ) {
                  accountDetails.tariffName = entity?.accounts?.find(
                    (acc: ICustomerAccount) => acc.accNumber === account
                  )?.tariffName
                }
                dispatch(setCustomerProfileAccount(accountDetails))
                setOpenAccountDetails(true)
              }}
              sx={{
                border: '1px solid #E3E4E4',
                fontSize: '15px',
                fontWeight: '600',
                padding: '0px 10px',
              }}
              endIcon={<CallMadeIcon style={{ fontSize: 15 }} />}
            >
              See details
            </Button>
          </Stack>
        </Stack>
      ))}
    </Stack>
  )
}
