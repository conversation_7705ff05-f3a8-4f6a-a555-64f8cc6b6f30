import {
  CloseRounded,
  History,
} from '@mui/icons-material'
import { useAppDispatch, useAppSelector } from "@/store"
import { fetchNotificationAlertsHistory } from "@/store/actions"
import { Box, Button, Drawer, IconButton, Paper, Stack, Typography } from "@mui/material"
import { useState } from "react"
import CustomerAccountsHistory from '../History/CustomerAccountsHistory'
import { setAccountLogsBackOffice } from '@/store/reducers'

export const NotificationHistory = ({type} : {type: string}) => {
  const dispatch = useAppDispatch()
  const { accountNotificationLogs, accountSubscriptionLogs } = useAppSelector((state) => state.customers)
  const [open, setOpen] = useState<boolean>(false)

  return (
    <>
      <Button
        variant="outlined"
        sx={{
          height: '35px',
          px: '10%',
          border: '1px solid #E3E4E4',
        }}
        size="small"
        startIcon={<History />}
        onClick={async () => {
          dispatch(setAccountLogsBackOffice(type === 'notification' ? accountNotificationLogs : accountSubscriptionLogs))
          setOpen(true)
        }}
      >
        History
      </Button>
      <Drawer open={open} anchor="right">
        <Paper
          elevation={0}
          sx={{
            height: '100vh',
            display: 'flex',
            flexDirection: 'column',
            justifyContent: 'flex-start',
            gap: '10px',
            transition: 'width 3.3s ease-in-out',
          }}
        >
          <Box
            sx={{
              minWidth: '52vw',
              width: '100%',
              maxHeight: '60px',
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
              padding: '16px 20px 8px 24px',
              borderBottom: '1px solid lightgray',
              backgroundColor: '#F9FAFB',
            }}
          >
            {/* DrawerHeader */}
            <Box
              sx={{
                display: 'flex',
                gap: '10px',
                justifyContent: 'flex-start',
                alignItems: 'center',
              }}
            >
              <Typography variant="h6">Account History</Typography>
            </Box>
            <IconButton
              sx={{
                border: '1px solid #CBD5E1',
                backgroundColor: '#F1F5F9',
              }}
              onClick={() => {
                setOpen(false)
              }}
            >
              <CloseRounded
                sx={{
                  fontSize: '20px',
                }}
              />
            </IconButton>
          </Box>
          <Stack>
            <CustomerAccountsHistory hideTabs={true} />
          </Stack>
        </Paper>
      </Drawer>
    </>
  )
}