import { useAppSelector } from '@/store'
import { useEffect } from 'react'
import {
  TooltipProps,
  Tooltip,
  tooltipClasses,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableRow,
  Typography,
  Paper,
  Stack,
} from '@mui/material'
import { styled } from '@mui/material/styles'
import { accountType } from '@/store/actions'

export const AccountTooltip = styled(
  ({ className, ...props }: TooltipProps) => (
    <Tooltip {...props} classes={{ popper: className }} />
  )
)(({ theme }) => ({
  [`& .${tooltipClasses.tooltip}`]: {
    backgroundColor: 'white',
    borderRadius: '8px',
    color: 'rgba(0, 0, 0, 0.87)',
    fontSize: 11,
    padding: theme.spacing(1),
    minWidth: '20vw',
    border: '1px solid #AAADB0',
    boxShadow:
      '0px 1px 3px 0px var(--Colors-Effects-Shadows-shadow-sm_01, rgba(16, 24, 40, 0.10)), 0px 1px 2px 0px var(--Colors-Effects-Shadows-shadow-sm_02, rgba(16, 24, 40, 0.06))',
    maxWidth: '25vw',
  },
}))

export const AccountInfoTable = ({ selectedAccount }: any) => {
  const { customer } = useAppSelector((state) => state.customers)

  const accountDetails = [
    {
      label: 'Account Name',
      value: `${customer.firstName} ${customer.lastName}`,
    },
    {
      label: 'Phone Number',
      value: customer.phoneNumber,
    },
    { label: 'Account Number', value: selectedAccount.accNumber },
    { label: 'Account Branch Name', value: selectedAccount.accBranchCode },
    { label: 'Type', value: accountType(selectedAccount.customerType) },
    { label: 'Tariff', value: selectedAccount.tariffName ?? 'Not set' },
  ]

  return (
    <Stack sx={{ p: '0.4rem', width: '100%' }}>
      <Typography sx={{ fontSize: '0.7rem', fontWeight: 600 }}>
        Account Info
      </Typography>
      <TableContainer
        component={Paper}
        elevation={0}
        sx={{ boxShadow: 'none' }}
      >
        <Table
          stickyHeader
          size="small"
          sx={{ border: '1px solid #EAECF0', boxShadow: 'none' }}
        >
          <TableBody>
            {accountDetails.map(({ label, value }) => (
              <TableRow key={label}>
                <TableCell>
                  <Typography sx={{ fontSize: '0.7rem' }}>{label}</Typography>
                </TableCell>
                <TableCell>
                  <Typography sx={{ fontSize: '0.7rem' }}>{value}</Typography>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>
    </Stack>
  )
}
