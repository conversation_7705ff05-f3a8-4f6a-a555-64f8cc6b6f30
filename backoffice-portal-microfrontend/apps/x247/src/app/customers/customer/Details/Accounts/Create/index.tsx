'use client'
import AddOutlinedIcon from '@mui/icons-material/AddOutlined'
import ArrowBackIcon from '@mui/icons-material/ArrowBack'
import CloseIcon from '@mui/icons-material/Close'
import {
  Box,
  Button,
  DialogTitle,
  Divider,
  Drawer,
  Icon<PERSON>utton,
  Stack,
  Typography,
} from '@mui/material'
import { Form, FormikProvider, useFormik } from 'formik'
import React, { useState } from 'react'
import { AccountsIcon, ConnectorIcon, SummaryIcon } from '@dtbx/ui/icons'
import { useAppDispatch, useAppSelector } from '@/store'
import { ICustomerAccountLink } from '@/store/interfaces'
import { AccessControlWrapper, HasAccessToRights } from '@dtbx/store/utils'
import { linkCustomerAccounts } from '@/store/actions'

import { AccountsStep } from '@/app/customers/customer/Details/Accounts/Create/AccountsStep'
import { AccountsSummary } from '@/app/customers/customer/Details/Accounts/Create/AccountsSummary'
import * as Yup from 'yup'

const steps = [
  {
    icon: <AccountsIcon />,
    title: 'Link accounts',
    description: 'Link and setup accounts',
  },
  {
    icon: <SummaryIcon />,
    title: 'Summary',
    description: 'Review and submit to checker',
  },
]
export const LinkAccountDialog = () => {
  const [open, setOpen] = useState<boolean>(false)
  const dispatch = useAppDispatch()
  const handleClose = (e: React.SyntheticEvent, reason: string) => {
    if (reason === 'backdropClick') {
      return
    }
    setOpen(false)
    setCurrentStep('Link accounts')
    formik.resetForm()
  }
  const { customer } = useAppSelector((state) => state.customers)
  const [currentStep, setCurrentStep] = useState<string>('Personal Details')
  const formik = useFormik<ICustomerAccountLink>({
    initialValues: {
      accounts: [],
      comments: '',
    },
    onSubmit: async (values, { resetForm }) => {
      if (HasAccessToRights(['SUPER_CREATE_ACCOUNTS'])) {
        await linkCustomerAccounts(
          customer.id ? customer.id : '',
          { accounts: values.accounts, comments: values.comments },
          dispatch,
          'super'
        )
      } else if (HasAccessToRights(['MAKE_CREATE_ACCOUNTS'])) {
        await linkCustomerAccounts(
          customer.id ? customer.id : '',
          { accounts: values.accounts, comments: values.comments },
          dispatch,
          'maker'
        )
      }
      setCurrentStep('Personal Details')
      resetForm()
      setOpen(false)
    },
    validationSchema: Yup.object({
      comments: Yup.string().required('Comments are required'),
    }),
  })
  return (
    <>
      <AccessControlWrapper
        rights={['SUPER_CREATE_ACCOUNTS', 'MAKE_CREATE_ACCOUNTS']}
      >
        <Button
          variant="contained"
          onClick={() => setOpen(!open)}
          sx={{
            textWrap: 'nowrap',
          }}
        >
          Link Account
          <AddOutlinedIcon />
        </Button>
      </AccessControlWrapper>

      <Drawer
        sx={{
          '.MuiDrawer-paper': {
            width: '95%',
          },
        }}
        open={open}
        anchor={'right'}
        onClose={handleClose}
        PaperProps={{
          sx: {
            width: '95%',
          },
        }}
      >
        <Box
          sx={{
            background: '#F9FAFB',
            borderBottom: '2px solid  #F2F4F7',
          }}
        >
          <Stack
            flexDirection="row"
            sx={{
              alignItems: 'center',
              px: '2%',
            }}
          >
            <DialogTitle
              sx={{
                display: 'flex',
                flexDirection: 'row',
                alignItems: 'center',
                alignContent: 'center',
                py: '5px',
              }}
            >
              <IconButton onClick={(e) => handleClose(e, 'close')}>
                <ArrowBackIcon />
              </IconButton>
              <Typography variant="subtitle2" color={'primary.main'}>
                Add account
              </Typography>
            </DialogTitle>
          </Stack>
          <IconButton
            aria-label="close"
            onClick={(e) => handleClose(e, 'close')}
            sx={{
              position: 'absolute',
              right: 8,
              top: 8,
              color: '',
            }}
          >
            <CloseIcon />
          </IconButton>
        </Box>
        <Stack
          sx={{
            flexDirection: 'row',
          }}
        >
          <Stack
            sx={{
              width: '25%',
              px: '4%',
              alignItems: 'flex-start',
              flexDirection: 'column',
              py: '2%',
            }}
          >
            {steps.map((step, index) => (
              <Stack
                key={step.title}
                sx={{
                  flexDirection: 'column',
                  cursor: 'pointer',
                }}
                onClick={() => setCurrentStep(step.title)}
              >
                <Stack
                  sx={{
                    flexDirection: 'row',
                    gap: '8px',
                  }}
                >
                  {step.icon}
                  <Stack>
                    <Typography variant="body1" color="primary.main">
                      {step.title}
                    </Typography>
                    <Typography>{step.description}</Typography>
                  </Stack>
                </Stack>
                <Stack
                  sx={{
                    px: '9%',
                    py: '2%',
                  }}
                >
                  {index < 1 && <ConnectorIcon />}
                </Stack>
              </Stack>
            ))}
          </Stack>
          <Divider
            orientation="vertical"
            variant="middle"
            sx={{
              height: '90vh',
            }}
          />
          <Stack
            sx={{
              width: '75%',
            }}
          >
            <FormikProvider value={formik}>
              <Form onSubmit={formik.handleSubmit}>
                {(() => {
                  switch (currentStep) {
                    case 'Link accounts':
                      return (
                        <AccountsStep
                          formik={formik}
                          setStep={setCurrentStep}
                        />
                      )
                    case 'Summary':
                      return (
                        <AccountsSummary
                          formik={formik}
                          setStep={setCurrentStep}
                        />
                      )
                    default:
                      return (
                        <AccountsStep
                          formik={formik}
                          setStep={setCurrentStep}
                        />
                      )
                  }
                })()}
              </Form>
            </FormikProvider>
          </Stack>
        </Stack>
      </Drawer>
    </>
  )
}
