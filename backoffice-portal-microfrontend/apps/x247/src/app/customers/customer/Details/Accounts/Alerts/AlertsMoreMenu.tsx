import { MoreHoriz } from '@mui/icons-material'
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  IconButton,
  InputAdornment,
  Menu,
  MenuItem,
  Stack,
  TextField,
  Typography,
} from '@mui/material'
import CloseIcon from '@mui/icons-material/Close'
import EditIcon from '@mui/icons-material/Edit'
import Tooltip, { tooltipClasses, TooltipProps } from '@mui/material/Tooltip'
import { styled } from '@mui/material/styles'
import { useEffect, useState } from 'react'
import { useAppDispatch, useAppSelector } from '@/store'
import { CustomCheckBox } from '@dtbx/ui/components/CheckBox'
import { InfoIcon } from '@dtbx/ui/icons'
import { sentenceCase } from 'tiny-case'
import {
  ACCESS_CONTROLS,
  AccessControlWrapper,
  alertTypes,
  formatTimestamp,
  reasonsForUnsubscribing,
} from '@dtbx/store/utils'
import { Dialog } from '@dtbx/ui/components/Overlay'
import {
  INotificationEventSettings,
  INotificationEventsPayload,
  INotificationEventsPerAccount,
  INotificationEventSubscriberPayload,
} from '@/store/interfaces'
import {
  editSubscriptionAlerts,
  fetchNotificationEventsPerAccount,
  unsubscribeToNotificationEvents,
} from '@/store/actions'

export const NotificationsAlertsMoreMenu = ({
  notification,
}: {
  notification: INotificationEventsPerAccount
}) => {
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null)
  const open = Boolean(anchorEl)
  const handleClick = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget)
  }
  const handleClose = () => {
    setAnchorEl(null)
  }
  return (
    <>
      <IconButton
        sx={{
          width: '24px',
          height: '24px',
          borderRadius: '50%',
        }}
        onClick={handleClick}
      >
        <MoreHoriz />
      </IconButton>

      <Menu
        id="demo-customized-menu"
        MenuListProps={{
          'aria-labelledby': 'demo-customized-button',
        }}
        anchorEl={anchorEl}
        onClose={handleClose}
        open={open}
        sx={{
          borderRadius: '4px',
        }}
      >
        <EditNotificationsAlerts editMode={false} notification={notification} />
        <EditNotificationsAlerts editMode={true} notification={notification} />
        <RemoveNotificationAlerts notification={notification} />
      </Menu>
    </>
  )
}

const EditNotificationsAlerts = ({
  editMode,
  notification,
}: {
  editMode: boolean
  notification: INotificationEventsPerAccount
}) => {
  const dispatch = useAppDispatch()
  const handleClose = () => {
    setOpenDrawer(false)
  }
  const { notificationEvents, customerProfileAccount } = useAppSelector(
    (state) => state.customers
  )
  const [openDrawer, setOpenDrawer] = useState(false)
  const [edit, setEdit] = useState<boolean>(editMode)
  const [dialogSize] = useState<string>('25%')
  const [checkedAlerts, setCheckedAlerts] = useState<boolean[]>([])
  const [checkedPhone, setCheckedPhone] = useState<boolean>(true)
  const [transactionLimit, setTransactionLimit] = useState<number>(
    notification?.thresholdAmount
  )

  const eventName = () => {
    return notification?.event?.eventName?.replace('event', '')
  }
  const event = () => {
    return notificationEvents.find(
      (event) => event.eventType === notification?.event?.eventType
    )
  }
  const deliveryMode = (setting: INotificationEventSettings) => {
    switch (setting?.deliveryMode) {
      case 'EMAIL':
        return customerProfileAccount?.profile?.email
      case 'SMS':
        return customerProfileAccount?.profile?.phoneNumber
      default:
        return ''
    }
  }
  const alertSettings = () => {
    const alert = event()
    return alert
      ? alert?.settings.filter((setting) => setting?.deliveryMode !== 'PUSH')
      : []
  }
  const handleSelection = (index: number) => {
    const updatedSelection = [...checkedAlerts]
    updatedSelection[index] = !updatedSelection[index]
    if (alertSettings()[index]?.deliveryMode === 'SMS') {
      setCheckedPhone(updatedSelection[index])
    }
    setCheckedAlerts(updatedSelection)
  }
  const handleSubmit = async () => {
    setOpenDrawer(false)
    const subscribers: INotificationEventSubscriberPayload[] = []
    checkedAlerts.forEach((alert, index) => {
      if (alert) {
        const subscriber = notification?.subscribers[index]
        subscribers.push({
          recipients: [deliveryMode(alertSettings()[index])],
          deliveryMode: alertSettings()[index]?.deliveryMode,
          name: `${customerProfileAccount?.profile?.firstName} ${customerProfileAccount?.profile?.lastName}`,
          recipientType: 'END_USER',
        })
      }
    })
    const payload: INotificationEventsPayload = {
      eventId: notification?.event?.id,
      profileId: customerProfileAccount?.profile?.id,
      accountSource: notification?.accountSource,
      branchCode: customerProfileAccount?.branchCode,
      accountId: notification?.accountId,
      thresholdAmount: `${transactionLimit}`,
      subscribers,
      frequencyId: notification?.alertFrequency?.id,
      comments: 'Edit notification alert',
    }
    await editSubscriptionAlerts({ events: payload, dispatch })
    fetchNotificationEventsPerAccount({
      accountId: notification?.accountId,
      dispatch,
    })
  }
  const handleChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    event.stopPropagation()
    setTransactionLimit(parseInt(event.target.value))
  }
  const selectPhoneNumber = () => {
    setCheckedPhone(!checkedPhone)
    alertSettings().forEach((alert, index) => {
      if (alert?.deliveryMode === 'SMS') {
        const checked = [...checkedAlerts]
        checked[index] = !checkedPhone
        setCheckedAlerts(checked)
      }
    })
  }
  useEffect(() => {
    const alerts: boolean[] = []
    alertSettings().forEach((alert) => {
      alerts.push(
        !!notification?.subscribers?.find(
          (sub) => sub?.deliveryMode === alert?.deliveryMode
        )
      )
    })
    setCheckedAlerts(alerts)
    setCheckedPhone(
      notification?.subscribers?.filter((sub) => sub?.deliveryMode === 'SMS')
        ?.length > 0
    )
  }, [])

  useEffect(() => {
    setEdit(editMode)
  }, [openDrawer])

  const LightTooltip = styled(({ className, ...props }: TooltipProps) => (
    <Tooltip {...props} classes={{ popper: className }} />
  ))(({ theme }) => ({
    [`& .${tooltipClasses.tooltip}`]: {
      backgroundColor: '#ffffff',
      color: theme.palette.text.primary,
      boxShadow: theme.shadows[1],
      fontSize: '13px',
    },
  }))
  return (
    <>
      {editMode ? (
        <AccessControlWrapper
          rights={
            editMode ? [...ACCESS_CONTROLS.UPDATE_ACCOUNT_PREFERENCES] : []
          }
        >
          <MenuItem
            onClick={() => {
              setOpenDrawer(true)
            }}
          >
            <Typography variant="body1">{'Edit Details'}</Typography>
          </MenuItem>
        </AccessControlWrapper>
      ) : (
        <MenuItem
          onClick={() => {
            setOpenDrawer(true)
          }}
        >
          <Typography variant="body1">{'See more details'}</Typography>
        </MenuItem>
      )}
      <Drawer
        open={openDrawer}
        anchor="right"
        variant="persistent"
        sx={{
          '.MuiDrawer-paper': {
            width: dialogSize,
          },
        }}
        onClose={handleClose}
        PaperProps={{
          sx: {
            width: dialogSize,
          },
        }}
      >
        <Stack
          sx={{
            justifyContent: 'space-between',
            alignItems: 'center',
            flexDirection: 'row',
            padding: '10px 16px 10px 16px',
          }}
        >
          {/* tittle */}
          <Stack sx={{ flexDirection: 'row', alignItems: 'center' }}>
            <Typography
              sx={{
                fontSize: '16px',
                fontWeight: '700',
                marginRight: '10px',
              }}
            >
              {`${eventName()} notification`}
            </Typography>
            {!edit && (
              <IconButton
                onClick={() => {
                  setEdit(!edit)
                }}
              >
                <EditIcon sx={{ fontSize: '20px', cursor: 'pointer' }} />
              </IconButton>
            )}
          </Stack>
          {/* close button */}
          <IconButton
            onClick={() => {
              setOpenDrawer(false)
            }}
          >
            <CloseIcon />
          </IconButton>
        </Stack>
        <Stack
          direction="row"
          sx={{
            height: '100%',
          }}
        >
          <Stack
            sx={{
              padding: '0px 16px 0px 16px',
              height: '100%',
              width: '100%',
            }}
          >
            <TextField
              fullWidth
              label="Type"
              margin="normal"
              size="small"
              value={eventName()}
              inputProps={{ readOnly: !edit }}
              disabled={edit}
            />

            <TextField
              fullWidth
              label="Status"
              margin="normal"
              size="small"
              value={notification?.status}
              inputProps={{ readOnly: !edit }}
              disabled={edit}
            />
            <TextField
              fullWidth
              label="Date opted in"
              margin="normal"
              size="small"
              value={formatTimestamp(notification?.optedInDate)}
              inputProps={{ readOnly: !edit }}
              disabled={edit}
            />
            <TextField
              fullWidth
              label={'Notify of payments above'}
              margin={'normal'}
              type="number"
              value={transactionLimit}
              onChange={handleChange}
              inputProps={{ readOnly: !edit }}
              size="small"
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">KES</InputAdornment>
                ),
              }}
            />
            <Stack>
              <Typography
                sx={{
                  p: '5px 0px',
                  fontSize: '15px',
                  textDecoration: 'underline',
                }}
              >
                Channels
              </Typography>
              {edit &&
                alertSettings().map((alert, index) => (
                  <Stack key={alert?.id}>
                    <Stack sx={{ flexDirection: 'row', alignItems: 'center' }}>
                      <CustomCheckBox
                        checked={!!checkedAlerts[index]}
                        onChange={() => handleSelection(index)}
                      />
                      <Typography sx={{ fontSize: '15px' }}>
                        {sentenceCase(alert?.deliveryMode)}
                      </Typography>
                    </Stack>
                  </Stack>
                ))}
              {!edit &&
                notification?.subscribers?.map((sub) => (
                  <Typography sx={{ fontSize: '15px', lineHeight: '30px' }}>
                    {sentenceCase(sub?.deliveryMode)}
                  </Typography>
                ))}
            </Stack>
            <Stack>
              <Stack sx={{ flexDirection: 'row' }}>
                <Typography
                  sx={{
                    p: '5px 0px',
                    fontSize: '15px',
                    textDecoration: 'underline',
                  }}
                >
                  Phone numbers
                </Typography>
                <LightTooltip title="When the SMS option is selected the default phone number is also selected">
                  <IconButton>
                    <InfoIcon />
                  </IconButton>
                </LightTooltip>
              </Stack>
              <Stack sx={{ flexDirection: 'row', alignItems: 'center' }}>
                {edit && (
                  <Stack sx={{ flexDirection: 'row', alignItems: 'center' }}>
                    <CustomCheckBox
                      checked={checkedPhone}
                      onChange={() => selectPhoneNumber()}
                      disabled={!edit}
                    />
                    <Typography sx={{ fontSize: '15px' }}>
                      {customerProfileAccount?.profile?.phoneNumber}
                    </Typography>
                  </Stack>
                )}
                {!edit && (
                  <Typography sx={{ fontSize: '15px', lineHeight: '30px' }}>
                    {customerProfileAccount?.profile?.phoneNumber}
                  </Typography>
                )}
              </Stack>
            </Stack>
            {edit && (
              <Stack direction="row" gap={'2%'} sx={{ marginTop: '30px' }}>
                <Button
                  variant="outlined"
                  fullWidth
                  sx={{
                    maxHeight: '34px',
                    background: '#ffffff',
                    border: '1px solid #AAADB0',
                    boxShadow: ' 0px 1px 2px 0px rgba(16, 24, 40, 0.05)',
                  }}
                  onClick={() => setOpenDrawer(false)}
                >
                  Cancel
                </Button>
                <Button
                  variant="contained"
                  fullWidth
                  sx={{
                    maxHeight: '34px',
                  }}
                  onClick={handleSubmit}
                >
                  Save changes
                </Button>
              </Stack>
            )}
          </Stack>
        </Stack>
      </Drawer>
    </>
  )
}

const RemoveNotificationAlerts = ({
  notification,
}: {
  notification: INotificationEventsPerAccount
}) => {
  const dispatch = useAppDispatch()
  const [open, setOpen] = useState(false)
  const { customerProfileAccount } = useAppSelector((state) => state.customers)
  const sumbit = async (reasons: string[]) => {
    await unsubscribeToNotificationEvents({
      accountId: notification?.accountId,
      eventId: notification?.event?.id,
      profileId: customerProfileAccount?.profile?.id,
      comments: reasons.join(', '),
      dispatch,
    })
    fetchNotificationEventsPerAccount({
      accountId: notification?.accountId,
      dispatch,
    })
  }
  return (
    <>
      <AccessControlWrapper
        rights={[...ACCESS_CONTROLS.UPDATE_ACCOUNT_PREFERENCES]}
      >
        <MenuItem
          onClick={() => {
            setOpen(true)
          }}
        >
          <Typography variant="body1">Remove notification</Typography>
        </MenuItem>
      </AccessControlWrapper>
      <Dialog
        open={open}
        setOpen={setOpen}
        title={'Remove notification'}
        buttonText={'Remove'}
        descriptionText={'Please give a reason for removing notification.'}
        isLoading={false}
        reasons={reasonsForUnsubscribing}
        buttonProps={{
          color: '#EB0045',
        }}
        onClick={sumbit}
      />
    </>
  )
}
