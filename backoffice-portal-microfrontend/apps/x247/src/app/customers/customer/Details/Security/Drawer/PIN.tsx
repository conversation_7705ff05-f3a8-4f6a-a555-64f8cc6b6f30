'use client'
import { CloseRounded, SearchRounded } from '@mui/icons-material'
import {
  Box,
  Button,
  Drawer,
  IconButton,
  Paper,
  Stack,
  Typography,
} from '@mui/material'
import React, { useEffect, useState } from 'react'
import {
  getCustomerPinHistory,
  getCustomerPinLogsBackoffice,
} from '@/store/actions'
import { useAppDispatch } from '@/store'
import { ICustomer } from '@/store/interfaces'
import { CustomSearchInput } from '@dtbx/ui/components/Input'
import {
  CustomAntTab,
  CustomTabPanel,
  CustomToggleTabs,
} from '@dtbx/ui/components'
import BackOfficePinHistory from '@/app/customers/customer/Details/Security/Drawer/BackOfficePinHistory'
import CustomerPinHistory from '@/app/customers/customer/Details/Security/Drawer/CustomerPinHistory'

export const PinDrawer = ({ customer }: { customer: ICustomer }) => {
  const dispatch = useAppDispatch()
  const [open, setOpen] = useState(false)
  const [tabValue, setTabValue] = useState(0)
  const [page, setPage] = useState(1)

  const handleChange = (_event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue)
    setPage(1)
  }

  const handlePageChange = (newPage: number) => {
    setPage(newPage)
  }
  useEffect(() => {
    if (open) {
      getCustomerPinLogsBackoffice(
        customer.id ? customer.id : '',
        dispatch,
        1,
        10
      )
    }
  }, [])
  return (
    <>
      <Button
        variant={'outlined'}
        onClick={() => setOpen(true)}
        sx={{
          width: '100%',
          height: '36px',
          border: '1px solid #D0D5DD',
        }}
      >
        Pin History
      </Button>
      <Drawer
        open={open}
        anchor="right"
        variant="temporary"
        slotProps={{
          paper: {
            sx: {
              maxWidth: '72%',
              minWidth: '30%',
            },
          },
        }}
      >
        <Paper
          elevation={0}
          sx={{
            height: '100vh',
            display: 'flex',
            flexDirection: 'column',
            justifyContent: 'flex-start',
            gap: '10px',
            transition: 'width 3.3s ease-in-out',
          }}
        >
          <Box
            sx={{
              minWidth: '52vw',
              width: '100%',
              maxHeight: '60px',
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
              padding: '16px 20px 8px 24px',
              borderBottom: '1px solid lightgray',
              backgroundColor: '#F9FAFB',
            }}
          >
            {/* DrawerHeader */}
            <Box
              sx={{
                display: 'flex',
                gap: '10px',
                justifyContent: 'flex-start',
                alignItems: 'center',
              }}
            >
              <Typography variant="h6">PIN History</Typography>
            </Box>
            <IconButton
              sx={{
                border: '1px solid #CBD5E1',
                backgroundColor: '#F1F5F9',
              }}
              onClick={() => {
                setOpen(false)
              }}
            >
              <CloseRounded
                sx={{
                  fontSize: '20px',
                }}
              />
            </IconButton>
          </Box>
          <Stack
            sx={{
              padding: '0 3% 0 2%',
              gap: '2vh',
            }}
          >
            <Stack
              direction={'row'}
              sx={{ justifyContent: 'space-between', alignItems: 'center' }}
            >
              <CustomSearchInput
                sx={{
                  width: '40%',
                  '&.Mui-focused': {
                    width: '40%',
                  },
                }}
                startAdornment={<SearchRounded />}
                placeholder="Search Event"
              />
              <CustomToggleTabs
                value={tabValue}
                onChange={handleChange}
                aria-label="ant example"
                sx={{
                  marginBottom: '1.5%',
                  width: '30%',
                }}
              >
                <CustomAntTab label={`Back Office`} />
                <CustomAntTab label={`Customer`} />
              </CustomToggleTabs>
            </Stack>

            <CustomTabPanel value={tabValue} index={0}>
              <BackOfficePinHistory />
            </CustomTabPanel>
            <CustomTabPanel value={tabValue} index={1}>
              <CustomerPinHistory page={page} onPageChange={handlePageChange} />
            </CustomTabPanel>
          </Stack>
        </Paper>
      </Drawer>
    </>
  )
}
