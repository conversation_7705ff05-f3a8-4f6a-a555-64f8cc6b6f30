'use client'
import { Avatar, Box, Button, Stack, Typography } from '@mui/material'
import React, { useEffect, useState } from 'react'
import { useAppDispatch, useAppSelector } from '@/store'
import { getCustomerPinDetails } from '@/store/actions'
import { CheckIcon, KeyIcon, LockIcon, XIcon } from '@dtbx/ui/icons'
import { ReadOnlyTypography } from '@dtbx/ui/components'

import { ResetPin } from './ResetPin'
import { PinDrawer } from './Drawer/PIN'
import SecurityDrawer from './Drawer/Security'
import { formatTimestamp } from '@dtbx/store/utils'
import { LoadingSecurity } from '@/app/customers/customer/Details/Security/LoadingSecurity'

const SecurityQuestions = () => {
  const { customer, customerPinDetails, isLoadingSecurity } = useAppSelector(
    (state) => state.customers
  )

  const dispatch = useAppDispatch()

  const getDetailByType = (type: string) => {
    if (Array.isArray(customerPinDetails)) {
      return customerPinDetails.find((detail) => detail.type === type)
    }
    return null
  }

  const pinDetails = getDetailByType('PIN')
  const securityQuestionDetails = getDetailByType('Security Questions')

  useEffect(() => {
    customer &&
      customer.id &&
      getCustomerPinDetails({ profileID: customer.id, dispatch })
  }, [customer])
  return isLoadingSecurity ? (
    <LoadingSecurity />
  ) : (
    <Stack sx={{ px: '3%', py: '1%', flexDirection: 'column', gap: '5vh' }}>
      <Stack
        sx={{
          backgroundColor: '#FFFFFF',
          border: '1px solid #D0D5DD',
          px: '2%',
          py: '3%',
          gap: '5vh',
          justifyContent: '',
        }}
      >
        <Stack
          sx={{
            flexDirection: 'row',
            justifyContent: 'space-between',
            alignItems: 'center',
          }}
        >
          <Stack
            sx={{
              flexDirection: 'row',
              justifyContent: 'space-between',
              gap: '10px',
              alignItems: 'flex-start',
            }}
          >
            <Avatar sx={{ backgroundColor: '#E7E8E9' }}>
              <KeyIcon />
            </Avatar>
            <Stack sx={{ flexDirection: 'column' }}>
              <Typography>PIN</Typography>
              <Stack
                sx={{ flexDirection: 'row', alignItems: 'center', gap: '4px' }}
              >
                <Box
                  sx={{
                    border: '1px solid #FECDCA',
                    borderRadius: '100%',
                    background: '#FEF3F2',
                    height: '19px',
                    width: '19px',
                    display: 'flex',
                    justifyContent: 'center',
                    alignItems: 'center',
                  }}
                >
                  {' '}
                  {pinDetails?.status === 'Active PIN' ? (
                    <CheckIcon />
                  ) : (
                    <XIcon />
                  )}
                </Box>

                <Typography
                  sx={{
                    color:
                      pinDetails?.status === 'Active PIN'
                        ? '#027A48'
                        : '#FA727B',
                    fontWeight: 500,
                    fontSize: 14,
                  }}
                >
                  {pinDetails?.status || 'Not Set'}
                </Typography>
              </Stack>
            </Stack>
          </Stack>
          <Stack
            sx={{
              flexDirection: 'row',
              gap: '8px',
              justifyContent: 'flex-end',
            }}
          >
            <ResetPin />
            <PinDrawer customer={customer} />
          </Stack>
        </Stack>
        <Stack
          sx={{ flexDirection: 'row', justifyContent: 'space-between' }}
          direction="row"
          spacing={2}
        >
          <ReadOnlyTypography
            label="Pin Status"
            value={pinDetails?.status || 'Not Set'}
          />

          <ReadOnlyTypography
            label="Pin Retries"
            value={pinDetails?.attempts}
          />

          <ReadOnlyTypography
            label="Date First Created"
            value={
              (pinDetails &&
                pinDetails?.dateFirstCreated &&
                formatTimestamp(pinDetails.dateFirstCreated)) ||
              'N/A'
            }
          />

          <ReadOnlyTypography
            label="Date Last Changed"
            value={
              (pinDetails &&
                pinDetails.dateLastChanged !== null &&
                formatTimestamp(pinDetails?.dateLastChanged)) ||
              'N/A'
            }
          />
        </Stack>
      </Stack>
      <Stack
        sx={{
          backgroundColor: '#FFFFFF',
          border: '1px solid #D0D5DD',
          px: '2%',
          py: '3%',
          gap: '5vh',
        }}
      >
        <Stack sx={{ flexDirection: 'row', justifyContent: 'space-between' }}>
          <Stack
            sx={{ flexDirection: 'row', gap: '10px', alignItems: 'flex-start' }}
          >
            <Avatar sx={{ bgcolor: '#E7E8E9' }}>
              <LockIcon />
            </Avatar>
            <Stack sx={{ flexDirection: 'column' }}>
              <Typography>Security Questions</Typography>
              <Stack sx={{ flexDirection: 'row' }}>
                <Stack
                  sx={{
                    alignItems: 'center',
                    flexDirection: 'row',
                    gap: '5px',
                  }}
                >
                  <Box
                    sx={{
                      border: '1px solid #FECDCA',
                      borderRadius: '100%',
                      background: '#FEF3F2',
                      height: '19px',
                      width: '19px',
                      display: 'flex',
                      justifyContent: 'center',
                      alignItems: 'center',
                    }}
                  >
                    {securityQuestionDetails?.status === 'Active Set' ? (
                      <CheckIcon />
                    ) : (
                      <XIcon />
                    )}
                  </Box>
                  <Typography
                    sx={{
                      color:
                        securityQuestionDetails?.status === 'Active Set'
                          ? '#027A48'
                          : '#FA727B',
                      fontWeight: 500,
                      fontSize: 14,
                    }}
                  >
                    {securityQuestionDetails?.status || 'Not Set'}
                  </Typography>
                </Stack>
              </Stack>
            </Stack>
          </Stack>
          <Stack>
            <SecurityDrawer customer={customer} />
          </Stack>
        </Stack>
        <Stack
          sx={{ flexDirection: 'row', justifyContent: 'space-between' }}
          direction="row"
          spacing={2}
        >
          <ReadOnlyTypography
            label="Security Questions Status"
            value={securityQuestionDetails?.status || 'Not Set'}
          />
          <ReadOnlyTypography
            label="Security Questions Retries"
            value={securityQuestionDetails?.attempts}
          />
          <ReadOnlyTypography
            label="Date First Created"
            value={
              (securityQuestionDetails &&
                securityQuestionDetails?.dateFirstCreated &&
                formatTimestamp(securityQuestionDetails?.dateFirstCreated)) ||
              'N/A'
            }
          />
          <ReadOnlyTypography
            label="Date Last Changed"
            value={
              (securityQuestionDetails &&
                securityQuestionDetails?.dateLastChanged &&
                formatTimestamp(securityQuestionDetails?.dateLastChanged)) ||
              'N/A'
            }
          />
        </Stack>
      </Stack>
    </Stack>
  )
}
export default SecurityQuestions
