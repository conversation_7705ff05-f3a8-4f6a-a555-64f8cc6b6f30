import React, { useEffect } from 'react'
import { useAppDispatch, useAppSelector } from '@/store'
import { getCustomerDevices } from '@/store/actions'

import { ListView } from './ListDevices'
import { DeviceDetail } from './DeviceDetails'

const Index = () => {
  const dispatch = useAppDispatch()
  const { openDevice, customer } = useAppSelector((state) => state.customers)
  useEffect(() => {
    getCustomerDevices({
      params: {
        profileID: customer.id ? customer.id : '',
        page: 1,
        size: 7,
      },
      dispatch,
    })
  }, [])
  return <>{openDevice ? <DeviceDetail /> : <ListView />}</>
}

export default Index
