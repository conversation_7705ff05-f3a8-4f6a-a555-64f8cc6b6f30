import { EditOutlined, History, MoreHoriz } from '@mui/icons-material'
import AddOutlinedIcon from '@mui/icons-material/AddOutlined'
import ArrowBackIcon from '@mui/icons-material/ArrowBack'
import {
  Button,
  Chip,
  Divider,
  IconButton,
  Paper,
  Stack,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TextField,
  Typography,
} from '@mui/material'
import React, { useEffect } from 'react'
import { useAppDispatch, useAppSelector } from '@/store'
import { setIsViewAccountOpen, setSelectedAccount } from '@/store/reducers'
import {
  ICustomerAccount,
  INotificationEventsPerAccount,
} from '@/store/interfaces'
import {
  fetchNotificationAlertsHistory,
  fetchNotificationEventFrequencies,
  fetchNotificationEvents,
  fetchNotificationEventsPerAccount,
  getCustomerAccountsLogsBackoffice,
  getCustomersAccountHistory,
} from '@/store/actions'
import {
  AccountDetailsIcon,
  AccountIcon,
  NotificationDetailsIcon,
  StatementDetailsIcon,
} from '@dtbx/ui/icons'
import { CustomerStatusChip } from '@dtbx/ui/components/Chip'

import {
  AddNotificationDialog,
  AddSubscriptionsDialog,
} from '@/app/customers/customer/Details/Accounts/AccountViewDialogs'
import {
  ActivateAccount,
  DeactivateAccount,
  RestrictAccount,
  UnlinkAccount,
  ViewAccountHistory,
} from '@/app/customers/customer/Details/Accounts/AccountsMoreMenu'
import AccountHistory from './History'
import { NotificationsAlertsMoreMenu } from './Alerts/AlertsMoreMenu'
import { SubscriptionAlertsMoreMenu } from './Alerts/SubscriptionsMoreMenu'
import { NotificationHistory } from './Alerts/NotificationHistory'
import { formatTimestamp } from '@dtbx/store/utils'

export const SingleAccountView = () => {
  const {
    customerProfileAccount,
    customer,
    accountLogsBackOffice,
    accountNotificationLogs,
    accountSubscriptionLogs,
  } = useAppSelector((state) => state.customers)

  const buttons = [
    {
      Button: <ViewAccountHistory />,
      key: 'history',
    },
    {
      Button: <RestrictAccount />,
      key: 'restrict',
    },
    {
      Button:
        customerProfileAccount.status === 'INACTIVE' ? (
          <ActivateAccount
            isMainMenu={false}
            account={customerProfileAccount}
            customer={customer}
          />
        ) : (
          <DeactivateAccount
            isMainMenu={false}
            account={customerProfileAccount}
            customer={customer}
          />
        ),
      key: customerProfileAccount.isBlocked ? 'activate' : 'deactivate',
    },
    {
      Button: (
        <UnlinkAccount account={customerProfileAccount} customer={customer} />
      ),
      key: 'unlink',
    },
  ]
  const dispatch = useAppDispatch()
  const handleClose = () => {
    dispatch(setIsViewAccountOpen(false))
    dispatch(setSelectedAccount({} as ICustomerAccount))
  }

  useEffect(() => {
    getCustomerAccountsLogsBackoffice(
      dispatch,
      customerProfileAccount?.accountNo
    )
    getCustomersAccountHistory({
      dispatch,
      profileID: customerProfileAccount?.profile.id,
      accountNo: customerProfileAccount?.accountNo,
    })
    fetchNotificationEvents({ dispatch })
    fetchNotificationEventFrequencies({ dispatch })
    fetchNotificationEventsPerAccount({
      accountId: customerProfileAccount?.accountNo,
      dispatch,
    })
    fetchNotificationAlertsHistory({
      accountNo: customerProfileAccount?.accountNo,
      dispatch,
    })
  }, [])
  return (
    <Stack direction="row" gap={'2vw'}>
      <Stack
        sx={{
          width: '22%',
          gap: '2vh',
        }}
        direction="column"
      >
        <IconButton
          sx={{
            border: '1px solid #AAADB0',
            borderRadius: '8px',
            fontWeight: '500',
            width: '20%',
          }}
          onClick={handleClose}
        >
          <ArrowBackIcon />
        </IconButton>
        <AccountIcon />
        <Typography
          variant="subtitle1"
          sx={{
            textWrap: 'noWrap',
          }}
        >
          Account {customerProfileAccount.accountNo}
        </Typography>
        <CustomerStatusChip
          label={customerProfileAccount.status}
          sx={{
            width: 'fit-content',
          }}
        />
        <Stack>
          {buttons.map((button) => (
            <Stack key={button.key}>
              {button.Button}
              <Divider />
            </Stack>
          ))}
        </Stack>
      </Stack>
      <Stack
        sx={{
          width: '78%',
          gap: '3vh',
        }}
        direction="column"
      >
        <Stack
          sx={{
            border: '1px solid #E3E4E4',
            borderRadius: '8px',
            padding: '2%',
          }}
        >
          <Stack direction="row" justifyContent="space-between">
            <Typography
              variant="subtitle2"
              color="text.primary"
              sx={{
                display: 'flex',
                alignItems: 'center',
                gap: '5px',
              }}
            >
              <AccountDetailsIcon />
              Account Details
            </Typography>
            <Stack direction="row" gap={'5px'}>
              <AccountHistory
                onHistoryButton={async () => {
                  await getCustomerAccountsLogsBackoffice(
                    dispatch,
                    customerProfileAccount?.accountNo
                  )
                  await getCustomersAccountHistory({
                    dispatch,
                    profileID: customerProfileAccount?.profile.id,
                    accountNo: customerProfileAccount?.accountNo,
                  })
                }}
              />
              <Button
                variant="outlined"
                startIcon={<EditOutlined />}
                size="small"
                sx={{
                  height: '35px',
                  border: '1px solid #E3E4E4',
                }}
                disabled={customer.isBlocked}
              >
                Edit
              </Button>
            </Stack>
          </Stack>

          <Stack
            sx={{
              flexDirection: 'row',
              justifyContent: 'space-between',
              gap: '2vw',
            }}
          >
            <TextField
              fullWidth
              label={'Branch'}
              margin={'normal'}
              value={customerProfileAccount.branchCode}
              size="small"
            />
            <TextField
              fullWidth
              label={'Account Name'}
              margin={'normal'}
              value={customerProfileAccount.fullName}
              size="small"
            />
            <TextField
              fullWidth
              label={'Account Alias'}
              margin={'normal'}
              value={customerProfileAccount.shortName}
              size="small"
            />
          </Stack>
          <Stack direction="row" gap={'2vw'}>
            <TextField
              fullWidth
              label={'Account Number'}
              margin={'normal'}
              value={customerProfileAccount.accountNo}
              size="small"
            />
            <TextField
              fullWidth
              label={'Status'}
              margin={'normal'}
              value={customerProfileAccount.status}
              size="small"
            />
            <TextField
              fullWidth
              label={'Currency'}
              margin={'normal'}
              value={customerProfileAccount.currency}
              size="small"
            />
          </Stack>
          <Stack direction="row" gap={'2vw'}>
            <TextField
              fullWidth
              label={'Tariff'}
              margin={'normal'}
              value={customerProfileAccount.tariffName}
              size="small"
            />
            <TextField
              fullWidth
              label={'Type'}
              margin={'normal'}
              value={customerProfileAccount.accountType}
              size="small"
            />
            <TextField
              fullWidth
              label={'Phone Number'}
              margin={'normal'}
              value={
                customerProfileAccount.profile.phoneNumber
                  ? customerProfileAccount.profile.phoneNumber
                  : 'Not provided'
              }
              size="small"
            />
          </Stack>
        </Stack>
        <Stack
          sx={{
            border: '1px solid #E3E4E4',
            borderRadius: '8px',
            padding: '2%',
            gap: '2vh',
          }}
        >
          <Stack direction="row" justifyContent="space-between">
            <Typography
              variant="subtitle2"
              color="text.primary"
              sx={{
                display: 'flex',
                alignItems: 'center',
                gap: '5px',
              }}
            >
              <NotificationDetailsIcon />
              Notifications/Alerts
            </Typography>
            <Stack direction="row" gap={'5px'}>
              {accountNotificationLogs?.length > 0 && (
                <NotificationHistory type={'notification'} />
              )}
              <AddNotificationDialog />
            </Stack>
          </Stack>
          <NotificationsList />
        </Stack>
        <Stack
          sx={{
            border: '1px solid #E3E4E4',
            borderRadius: '8px',
            padding: '2%',
            gap: '2vh',
          }}
        >
          <Stack direction="row" justifyContent="space-between">
            <Typography
              variant="subtitle2"
              color="text.primary"
              sx={{
                display: 'flex',
                alignItems: 'center',
                gap: '5px',
              }}
            >
              <StatementDetailsIcon />
              E-statement subscriptions
            </Typography>
            <Stack direction="row" gap={'5px'}>
              {accountSubscriptionLogs?.length > 0 && (
                <NotificationHistory type={'subscription'} />
              )}
              <AddSubscriptionsDialog />
            </Stack>
          </Stack>
          <SubscriptionsList />
        </Stack>
      </Stack>
    </Stack>
  )
}

const NotificationsList = () => {
  const { accountNotificationPreferences, customerProfileAccount } =
    useAppSelector((state) => state.customers)

  const nofificationPreferences = () => {
    return accountNotificationPreferences?.filter(
      (preference) => preference?.event?.eventType !== 'ACCOUNT_STATEMENT'
    )
  }
  const eventName = (notification: INotificationEventsPerAccount) => {
    return `${
      notification?.event?.eventType === 'BALANCE_ALERT'
        ? notification?.alertFrequency?.name?.replace(' Frequency', ' ')
        : ''
    }${notification?.event?.eventName?.replace(' event', '')?.replace('Account ', '')}`
  }
  return (
    <Stack>
      {nofificationPreferences()?.length > 0 && (
        <TableContainer
          component={Paper}
          elevation={0}
          sx={{
            border: '1px solid #EAECF0',
            boxShadow:
              '0px 1px 3px 0px rgba(16, 24, 40, 0.10), 0px 1px 2px 0px rgba(16, 24, 40, 0.06)',
          }}
        >
          <Table sx={{}} aria-label="simple table" size="small">
            <TableHead
              sx={{
                background: '#F9FAFB',
              }}
            >
              <TableRow>
                <TableCell width={'20%'}>Type</TableCell>
                <TableCell>Status</TableCell>
                <TableCell>Date Opted In</TableCell>
                <TableCell>Channels</TableCell>
                <TableCell>Phone Numbers</TableCell>
                <TableCell></TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {nofificationPreferences().map((notification, index) => (
                <TableRow key={index}>
                  <TableCell>
                    <Typography sx={{ textTransform: 'capitalize' }}>
                      {eventName(notification)}
                    </Typography>
                  </TableCell>
                  <TableCell>
                    <CustomerStatusChip
                      label={notification.status.toUpperCase()}
                    />
                  </TableCell>
                  <TableCell>
                    {formatTimestamp(notification?.optedInDate)}
                  </TableCell>
                  <TableCell>
                    {notification.subscribers.map((sub) => (
                      <Chip
                        key={sub?.id}
                        label={sub?.deliveryMode}
                        sx={{
                          marginRight: '5px',
                        }}
                      />
                    ))}
                  </TableCell>
                  <TableCell>
                    {notification.subscribers.map(
                      (sub) =>
                        sub?.deliveryMode === 'SMS' && (
                          <Chip
                            key={sub?.id}
                            label={sub?.recipient}
                            sx={{
                              marginRight: '5px',
                            }}
                          />
                        )
                    )}
                  </TableCell>
                  <TableCell>
                    <NotificationsAlertsMoreMenu notification={notification} />
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      )}
      {nofificationPreferences()?.length === 0 && (
        <Stack sx={{ textAlign: 'center', p: 5 }}>
          <Typography
            sx={{ fontSize: '18px', fontWeight: '600', marginBottom: '10px' }}
          >
            No notifications
          </Typography>
          <Typography sx={{ width: '40%', margin: 'auto' }}>
            When {customerProfileAccount?.profile?.firstName} opts in to
            notifications, you will
          </Typography>
          <Typography sx={{ width: '40%', margin: 'auto' }}>
            see them here.
          </Typography>
        </Stack>
      )}
    </Stack>
  )
}
const SubscriptionsList = () => {
  const { accountNotificationPreferences, customerProfileAccount } =
    useAppSelector((state) => state.customers)
  const subscriptionPreferences = () => {
    return accountNotificationPreferences?.filter(
      (preference) => preference?.event?.eventType === 'ACCOUNT_STATEMENT'
    )
  }
  return (
    <Stack>
      {subscriptionPreferences()?.length > 0 && (
        <TableContainer
          component={Paper}
          elevation={0}
          sx={{
            border: '1px solid #EAECF0',
            boxShadow:
              '0px 1px 3px 0px rgba(16, 24, 40, 0.10), 0px 1px 2px 0px rgba(16, 24, 40, 0.06)',
          }}
        >
          <Table sx={{}} aria-label="simple table" size="small">
            <TableHead
              sx={{
                background: '#F9FAFB',
              }}
            >
              <TableRow>
                <TableCell width={'20%'}>Type</TableCell>
                <TableCell>Status</TableCell>
                <TableCell>Date Opted In</TableCell>
                <TableCell>Email</TableCell>
                <TableCell>Charge Per Statement</TableCell>
                <TableCell></TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {subscriptionPreferences().map((notification, index) => (
                <TableRow key={index}>
                  <TableCell>
                    <Typography>
                      {notification?.alertFrequency?.name?.replace(
                        ' Frequency',
                        ' Subscription'
                      )}
                    </Typography>
                  </TableCell>
                  <TableCell>
                    <CustomerStatusChip
                      label={notification?.status?.toUpperCase()}
                    />
                  </TableCell>
                  <TableCell>
                    {formatTimestamp(notification?.optedInDate)}
                  </TableCell>
                  <TableCell>
                    {notification.subscribers.map((sub) => (
                      <Chip
                        key={sub?.id}
                        label={sub?.deliveryMode}
                        sx={{
                          marginRight: '5px',
                        }}
                      />
                    ))}
                  </TableCell>
                  <TableCell>--</TableCell>
                  <TableCell>
                    <SubscriptionAlertsMoreMenu notification={notification} />
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      )}
      {subscriptionPreferences()?.length === 0 && (
        <Stack sx={{ textAlign: 'center', p: 5 }}>
          <Typography
            sx={{ fontSize: '18px', fontWeight: '600', marginBottom: '10px' }}
          >
            No subscriptions
          </Typography>
          <Typography sx={{ width: '40%', margin: 'auto' }}>
            When {customerProfileAccount?.profile?.firstName} opts in to
            notifications, you will
          </Typography>
          <Typography sx={{ width: '40%', margin: 'auto' }}>
            see them here.
          </Typography>
        </Stack>
      )}
    </Stack>
  )
}
