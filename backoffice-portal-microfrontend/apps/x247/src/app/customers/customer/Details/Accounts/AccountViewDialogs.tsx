import AddOutlinedIcon from '@mui/icons-material/AddOutlined'
import ArrowBackIcon from '@mui/icons-material/ArrowBack'
import {
  Button,
  FormControl,
  IconButton,
  InputAdornment,
  InputLabel,
  Menu,
  MenuItem,
  Popover,
  Select,
  SelectChangeEvent,
  Stack,
  styled,
  TextField,
  Tooltip,
  tooltipClasses,
  TooltipProps,
  Typography,
} from '@mui/material'
import React, { useState } from 'react'
import { useAppDispatch, useAppSelector } from '@/store'
import {
  subscribeToNotificationEvents,
  fetchNotificationEventsPerAccount,
} from '@/store/actions'
import {
  INotificationEventSettings,
  INotificationEventsPayload,
  INotificationFrequencies,
} from '@/store/interfaces'
import {
  ACCESS_CONTROLS,
  AccessControlWrapper,
  alertTypes,
} from '@dtbx/store/utils'
import { CustomCheckBox } from '@dtbx/ui/components/CheckBox'
import { InfoIcon } from '@dtbx/ui/components/SvgIcons'
import { sentenceCase } from 'tiny-case'

export const AddNotificationDialog = ({}) => {
  const dispatch = useAppDispatch()
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null)
  const [anchorPopEl, setAnchorPopEl] = useState<null | HTMLElement>(null)
  const [selectedAlert, setSelectedAlert] = useState<string>('')
  const [selectedEventName, setSelectedEventName] = useState<string>('')
  const [selectedFrequency, setSelectedFrequency] = useState<string>('')
  const [checkedPhone, setCheckedPhone] = useState<boolean>(false)
  const [transactionLimit, setTransactionLimit] = useState<string>('')
  const [checkedAlerts, setCheckedAlerts] = useState<boolean[]>([])
  const {
    customer,
    notificationEvents,
    notificationFrequencies,
    customerProfileAccount,
  } = useAppSelector((state) => state.customers)
  const open = Boolean(anchorEl)
  const openPop = Boolean(anchorPopEl)
  const handleClick = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget)
  }
  const handleClose = () => {
    setAnchorEl(null)
  }
  const handlePopClose = () => {
    setSelectedAlert('')
    setAnchorPopEl(null)
  }
  const handleMenuItemClick = (
    event: string,
    eventName: string,
    frequency: string
  ) => {
    setSelectedAlert(event)
    setSelectedEventName(eventName)
    setSelectedFrequency(frequency)
    setAnchorPopEl(anchorEl)
    setAnchorEl(null)
  }
  const handleChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    event.stopPropagation()
    setTransactionLimit(event.target.value)
  }
  const event = () => {
    return notificationEvents.find(
      (event) => event.eventType === selectedEventName
    )
  }
  const alertSettings = () => {
    const alert = event()
    return alert
      ? alert?.settings.filter((setting) => setting?.deliveryMode !== 'PUSH')
      : []
  }
  const handleSelection = (index: number) => {
    const updatedSelection = [...checkedAlerts]
    updatedSelection[index] = !updatedSelection[index]
    if (alertSettings()[index]?.deliveryMode === 'SMS') {
      setCheckedPhone(updatedSelection[index])
    }
    setCheckedAlerts(updatedSelection)
  }
  const activeSubmit = () => {
    return (
      checkedAlerts.length === 0 ||
      !checkedAlerts.includes(true) ||
      transactionLimit === '' ||
      parseInt(transactionLimit) <= 0
    )
  }
  const deliveryMode = (setting: INotificationEventSettings) => {
    switch (setting?.deliveryMode) {
      case 'EMAIL':
        return customerProfileAccount?.profile?.email
      case 'SMS':
        return customerProfileAccount?.profile?.phoneNumber
      default:
        return ''
    }
  }
  const selectPhoneNumber = () => {
    setCheckedPhone(!checkedPhone)
    alertSettings().forEach((alert, index) => {
      if (alert?.deliveryMode === 'SMS') {
        const checked = [...checkedAlerts]
        checked[index] = !checkedPhone
        setCheckedAlerts(checked)
      }
    })
  }
  const handleSubmit = async () => {
    const frequency = notificationFrequencies?.find(
      (frequency) => frequency?.name === selectedFrequency
    )
    const payload: INotificationEventsPayload = {
      eventId: event()?.id || '',
      profileId: customerProfileAccount?.profile?.id,
      accountSource: 'CBS',
      branchCode: customerProfileAccount?.branchCode,
      accountId: customerProfileAccount?.accountNo,
      frequencyId: frequency ? frequency?.id : '',
      thresholdAmount: transactionLimit,
      subscribers: [],
      comments: 'Create notification alert',
    }
    checkedAlerts.forEach((alert, index) => {
      if (alert) {
        payload.subscribers.push({
          recipients: [deliveryMode(alertSettings()[index])],
          deliveryMode: alertSettings()[index]?.deliveryMode,
          name: `${customerProfileAccount?.profile?.firstName} ${customerProfileAccount?.profile?.lastName}`,
          recipientType: 'END_USER',
        })
      }
    })
    await subscribeToNotificationEvents({
      events: payload,
      dispatch,
    })
    fetchNotificationEventsPerAccount({
      accountId: customerProfileAccount?.accountNo,
      dispatch,
    })
    handlePopClose()
  }
  const LightTooltip = styled(({ className, ...props }: TooltipProps) => (
    <Tooltip {...props} classes={{ popper: className }} />
  ))(({ theme }) => ({
    [`& .${tooltipClasses.tooltip}`]: {
      backgroundColor: '#ffffff',
      color: theme.palette.text.primary,
      boxShadow: theme.shadows[1],
      fontSize: '13px',
    },
  }))
  return (
    <>
      <AccessControlWrapper
        rights={[...ACCESS_CONTROLS.UPDATE_ACCOUNT_PREFERENCES]}
      >
        <Button
          variant="outlined"
          startIcon={<AddOutlinedIcon />}
          data-testid="add-notification-button"
          size="small"
          onClick={handleClick}
          sx={{
            height: '35px',
            border: '1px solid #E3E4E4',
            textWrap: 'noWrap',
            background: '#F8F9FC',
          }}
          disabled={customer.isBlocked}
        >
          Add Notification
        </Button>
      </AccessControlWrapper>
      <Menu
        id="demo-customized-menu"
        slotProps={{
          list: {
            'aria-labelledby': 'demo-customized-button',
          },
        }}
        anchorEl={anchorEl}
        onClose={handleClose}
        open={open}
        sx={{
          borderRadius: '4px',
        }}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'left',
        }}
      >
        {alertTypes.map((alert, index) => (
          <MenuItem
            key={index}
            onClick={() =>
              handleMenuItemClick(alert.label, alert.type, alert.frequency)
            }
          >
            {alert.label}
          </MenuItem>
        ))}
      </Menu>
      <Popover
        id={selectedAlert}
        open={openPop}
        anchorEl={anchorPopEl}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'left',
        }}
        onClose={handlePopClose}
      >
        <Stack
          sx={{
            border: '1px solid #DDDDDD',
            padding: '10px 15px',
            borderRadius: '5px',
          }}
        >
          <Stack sx={{ flexDirection: 'row', alignItems: 'center' }}>
            <ArrowBackIcon sx={{ fontSize: '15px' }} onClick={handlePopClose} />
            <Typography sx={{ p: '10px', fontSize: '15px' }}>
              {selectedAlert}
            </Typography>
          </Stack>
          <TextField
            fullWidth
            label={'Notify of payments above'}
            margin={'normal'}
            type="number"
            value={transactionLimit}
            onChange={handleChange}
            size="small"
            slotProps={{
              input: {
                startAdornment: (
                  <InputAdornment position="start">KES</InputAdornment>
                ),
                readOnly: false,
              },
            }}
          />
          <Stack>
            <Typography sx={{ p: '5px 0px', fontSize: '15px' }}>
              Channels
            </Typography>
            {alertSettings().map((alert, index) => (
              <Stack
                sx={{ flexDirection: 'row', alignItems: 'center' }}
                key={alert?.id}
              >
                <CustomCheckBox
                  checked={!!checkedAlerts[index]}
                  onChange={() => handleSelection(index)}
                />
                <Typography sx={{ fontSize: '15px' }}>
                  {sentenceCase(alert?.deliveryMode)}
                </Typography>
              </Stack>
            ))}
          </Stack>
          <Stack>
            <Stack sx={{ flexDirection: 'row' }}>
              <Typography sx={{ p: '5px 0px', fontSize: '15px' }}>
                Phone numbers
              </Typography>
              <LightTooltip title="When the SMS option is selected the default phone number is also selected">
                <IconButton>
                  <InfoIcon />
                </IconButton>
              </LightTooltip>
            </Stack>
            <Stack sx={{ flexDirection: 'row', alignItems: 'center' }}>
              <CustomCheckBox
                checked={checkedPhone}
                onChange={() => selectPhoneNumber()}
              />
              <Typography sx={{ fontSize: '15px' }}>
                {customerProfileAccount?.profile?.phoneNumber}
              </Typography>
            </Stack>
          </Stack>
          <Stack sx={{ flexDirection: 'row', marginTop: '10px' }}>
            <Button
              variant="outlined"
              size="small"
              onClick={handlePopClose}
              sx={{
                height: '35px',
                border: '1px solid #E3E4E4',
                textWrap: 'noWrap',
                background: '#F8F9FC',
                marginRight: '10px',
              }}
            >
              Cancel
            </Button>
            <Button
              variant="outlined"
              size="small"
              onClick={handleSubmit}
              sx={{
                height: '35px',
                border: '1px solid #E3E4E4',
                textWrap: 'noWrap',
                background: '#F8F9FC',
              }}
              disabled={activeSubmit()}
            >
              Add Notification
            </Button>
          </Stack>
        </Stack>
      </Popover>
    </>
  )
}
export const AddSubscriptionsDialog = ({}) => {
  const dispatch = useAppDispatch()
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null)
  const [anchorPopEl, setAnchorPopEl] = useState<null | HTMLElement>(null)
  const [selectedSubscription, setSelectedSubscription] =
    useState<INotificationFrequencies>({} as INotificationFrequencies)
  const [subscriptionEmail, setSubscriptionEmail] = useState<string>('')
  const {
    customer,
    notificationFrequencies,
    notificationEvents,
    customerProfileAccount,
  } = useAppSelector((state) => state.customers)
  const open = Boolean(anchorEl)
  const openPop = Boolean(anchorPopEl)
  const handleClick = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget)
  }
  const handleClose = () => {
    setAnchorEl(null)
  }
  const handlePopClose = () => {
    setSubscriptionEmail('')
    setAnchorPopEl(null)
  }
  const handleMenuItemClick = (sub: INotificationFrequencies) => {
    setSelectedSubscription(sub)
    setAnchorPopEl(anchorEl)
    setAnchorEl(null)
  }
  const handleChange = (event: SelectChangeEvent) => {
    event.stopPropagation()
    setSubscriptionEmail(event.target.value)
  }
  const activeSubmit = () => {
    return subscriptionEmail === ''
  }
  const handleSubmit = async () => {
    const event = notificationEvents.find(
      (event) => event.eventType === 'ACCOUNT_STATEMENT'
    )
    const payload: INotificationEventsPayload = {
      eventId: event?.id || '',
      profileId: customerProfileAccount?.profile?.id,
      accountSource: 'CBS',
      branchCode: customerProfileAccount?.branchCode,
      accountId: customerProfileAccount?.accountNo,
      frequencyId: selectedSubscription?.id,
      thresholdAmount: '',
      subscribers: [
        {
          recipients: [customerProfileAccount?.profile?.email],
          deliveryMode: 'EMAIL',
          name: `${customerProfileAccount?.profile?.firstName} ${customerProfileAccount?.profile?.lastName}`,
          recipientType: 'END_USER',
        },
      ],
      comments: 'Create subscription alert',
    }
    await subscribeToNotificationEvents({
      events: payload,
      dispatch,
    })
    fetchNotificationEventsPerAccount({
      accountId: customerProfileAccount?.accountNo,
      dispatch,
    })
    handlePopClose()
  }
  return (
    <>
      <AccessControlWrapper
        rights={[...ACCESS_CONTROLS.UPDATE_ACCOUNT_PREFERENCES]}
      >
        <Button
          variant="outlined"
          startIcon={<AddOutlinedIcon />}
          size="small"
          data-testid="add-subscription-button"
          onClick={handleClick}
          sx={{
            height: '35px',
            border: '1px solid #E3E4E4',
            textWrap: 'noWrap',
            background: '#F8F9FC',
          }}
          disabled={customer.isBlocked}
        >
          Add Subscription
        </Button>
      </AccessControlWrapper>
      <Menu
        id="demo-customized-menu"
        slotProps={{
          list: {
            'aria-labelledby': 'demo-customized-button',
          },
        }}
        anchorEl={anchorEl}
        onClose={handleClose}
        open={open}
        sx={{
          borderRadius: '4px',
        }}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'left',
        }}
      >
        {notificationFrequencies.map((sub, index) => (
          <MenuItem key={index} onClick={() => handleMenuItemClick(sub)}>
            {sub?.name?.split(' ')[0]}
          </MenuItem>
        ))}
      </Menu>
      <Popover
        id={selectedSubscription?.id}
        open={openPop}
        anchorEl={anchorPopEl}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'left',
        }}
        onClose={handlePopClose}
      >
        <Stack
          sx={{
            border: '1px solid #DDDDDD',
            padding: '10px 15px',
            borderRadius: '5px',
          }}
        >
          <Stack sx={{ flexDirection: 'row', alignItems: 'center' }}>
            <ArrowBackIcon sx={{ fontSize: '15px' }} onClick={handlePopClose} />
            <Typography sx={{ p: '10px', fontSize: '15px' }}>
              {`${selectedSubscription?.name?.split(' ')[0]} subscription`}
            </Typography>
          </Stack>
          <FormControl fullWidth margin={'normal'} size="small">
            <InputLabel>Email</InputLabel>
            <Select
              value={subscriptionEmail}
              label="Email"
              onChange={handleChange}
            >
              <MenuItem value={customerProfileAccount?.profile?.email}>
                {customerProfileAccount?.profile?.email}
              </MenuItem>
            </Select>
          </FormControl>
          <Stack sx={{ flexDirection: 'row', marginTop: '10px' }}>
            <Button
              variant="outlined"
              size="small"
              onClick={handlePopClose}
              sx={{
                height: '35px',
                border: '1px solid #E3E4E4',
                textWrap: 'noWrap',
                background: '#F8F9FC',
                marginRight: '10px',
              }}
            >
              Cancel
            </Button>
            <Button
              variant="outlined"
              size="small"
              onClick={handleSubmit}
              sx={{
                height: '35px',
                border: '1px solid #E3E4E4',
                textWrap: 'noWrap',
                background: '#F8F9FC',
              }}
              disabled={activeSubmit()}
            >
              Add Subscription
            </Button>
          </Stack>
        </Stack>
      </Popover>
    </>
  )
}
