import {
  Paper,
  Stack,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableRow,
} from '@mui/material'
import React, { useState } from 'react'
import { IHeadCell } from '@dtbx/store/interfaces'
import { RootState, useAppDispatch, useAppSelector } from '@/store'
import { setOpenDevice } from '@/store/reducers'
import { getCustomerDeviceDetail, getCustomerDevices } from '@/store/actions'
import { CustomSkeleton } from '@dtbx/ui/components/Loading'
import {
  CustomPagination,
  CustomTableHeader,
  PaginationOptions,
} from '@dtbx/ui/components/Table'
import { CustomerStatusChip } from '@dtbx/ui/components/Chip'

import { DeviceMoreMenu } from '@/app/customers/customer/Details/Devices/MoreMenu'

import PageHeader from './pageHeader'
import EmptySearchAndFilter from './EmptySearchAndFilter'
import { formatTimestamp } from '@dtbx/store/utils'

const headers: IHeadCell[] = [
  {
    id: 'deviceId',
    label: 'Device ID',
    alignCenter: false,
    alignRight: false,
  },
  {
    id: 'deviceType',
    label: 'Device Type',
    alignCenter: false,
    alignRight: false,
  },
  {
    id: 'deviceName',
    label: 'Device Name',
    alignCenter: false,
    alignRight: false,
  },
  {
    id: 'deviceModel',
    label: 'Device Model',
    alignCenter: false,
    alignRight: false,
  },
  {
    id: 'deviceStatus',
    label: 'Device Status',
    alignCenter: false,
    alignRight: false,
  },
  {
    id: 'uuid',
    label: 'IMSI',
    alignCenter: false,
    alignRight: false,
  },
  {
    id: 'dateCreated',
    label: 'Date Created',
    alignCenter: false,
    alignRight: false,
  },
  {
    id: 'actions',
    label: 'Action',
    alignCenter: false,
    alignRight: false,
  },
]

export const ListView = () => {
  const dispatch = useAppDispatch()

  const { devicesResponse, isSuccessDevices, isLoadingDevices, customer } =
    useAppSelector((state) => state.customers)
  const data = devicesResponse.data
  const [paginationOptions, setPaginationOptions] = useState({
    page: devicesResponse.pageNumber,
    size: 10,
    totalPages: devicesResponse.totalNumberOfPages,
  })
  /*************************start pagination handlers***************************/
  const handlePagination = async (newOptions: PaginationOptions) => {
    setPaginationOptions(newOptions) // Update parent state
    await getCustomerDevices({
      params: {
        profileID: customer.id ? customer.id : '',
        ...newOptions,
      },
      dispatch,
    })
  }
  /*************************end pagination handlers**************************/
  return (
    <Stack
      sx={{
        height: '100%',
        px: '3%',
        gap: '2vh',
      }}
    >
      <PageHeader />
      {isLoadingDevices ? (
        <CustomSkeleton variant="rectangular" width={'100%'} height={'60vh'} />
      ) : (
        <>
          {data && data.length === 0 ? (
            <EmptySearchAndFilter />
          ) : (
            <Paper
              elevation={0}
              sx={{
                boxShadow:
                  '0px 1px 3px 0px rgba(16, 24, 40, 0.10), 0px 1px 2px 0px rgba(16, 24, 40, 0.06)',
                borderRadius: '4px',
                border: '1px solid #EAECF0',
                background: '#FFFFFF',
              }}
            >
              <TableContainer>
                <Table>
                  <CustomTableHeader
                    order={'desc'}
                    orderBy={''}
                    rowCount={0}
                    headLabel={[...headers]}
                    numSelected={0}
                  />
                  <TableBody>
                    {isSuccessDevices &&
                      [...data]
                        .sort(
                          (a, b) =>
                            new Date(b.dateCreated).getTime() -
                            new Date(a.dateCreated).getTime()
                        )
                        .map((row) => {
                          const {
                            deviceName,
                            deviceStatus,
                            deviceModel,
                            deviceType,
                            deviceId,
                            dateCreated,
                            uuid,
                          } = row
                          return (
                            <TableRow
                              key={uuid}
                              sx={{ cursor: 'pointer' }}
                              onClick={async () => {
                                if (!customer.isBlocked) {
                                  dispatch(setOpenDevice(true))
                                  await getCustomerDeviceDetail({
                                    deviceID: uuid,
                                    profileID: customer.id ? customer.id : '',
                                    dispatch: dispatch,
                                  })
                                }
                              }}
                            >
                              {' '}
                              <TableCell>{deviceId}</TableCell>
                              <TableCell>{deviceType}</TableCell>
                              <TableCell>{deviceName}</TableCell>
                              <TableCell>{deviceModel}</TableCell>
                              <TableCell>
                                <CustomerStatusChip label={deviceStatus} />
                              </TableCell>
                              <TableCell>{uuid}</TableCell>
                              <TableCell>
                                {formatTimestamp(dateCreated)}
                              </TableCell>
                              <TableCell sx={{ padding: '8px' }}>
                                <DeviceMoreMenu device={row} />
                              </TableCell>
                            </TableRow>
                          )
                        })}
                  </TableBody>
                </Table>
              </TableContainer>
              {devicesResponse.totalNumberOfPages > 0 && (
                <CustomPagination
                  options={{
                    ...paginationOptions,
                    totalPages: devicesResponse.totalNumberOfPages,
                  }}
                  handlePagination={handlePagination}
                />
              )}
            </Paper>
          )}
        </>
      )}
    </Stack>
  )
}
