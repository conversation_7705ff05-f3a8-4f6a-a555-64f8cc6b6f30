import React, { useCallback, useState } from 'react'
import { Stack } from '@mui/material'
import _ from 'lodash'
import { ICustomer } from '@/store/interfaces'
import { IFilter } from '@dtbx/store/interfaces'
import { useAppDispatch } from '@/store'
import { filterLinkedCustomerAccounts } from '@/store/actions'
import { CustomFilterBox } from '@/app/approval-requests/CustomFilterBox'

import { LinkAccountDialog } from '@/app/customers/customer/Details/Accounts/Create'

export const AccountsHeader = ({ customer }: { customer: ICustomer }) => {
  const [openFilter, setOpenFilter] = useState<boolean>(false)
  const [selectedFilterItems, setSelectedFilterItems] = useState<
    Record<string, string | string[]>
  >({})
  const [search, setSearchValue] = useState<string>('')
  const dispatch = useAppDispatch()
  const debouncedSearch = useCallback(
    _.debounce(
      (value: Record<string, string | string[]>) =>
        fetchFilteredAccounts(value),
      1000
    ),
    []
  )
  const handleSearch = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSearchValue(event.target.value)
    const concatFilter: Record<string, string | string[]> = {
      ...selectedFilterItems,
      ...(searchByValue === 'Number'
        ? { accountNo: event.target.value }
        : { accountName: event.target.value }),
    }
    debouncedSearch(concatFilter)
  }
  const handleFilterChange = (
    selectedFilters: Record<string, string | string[]>
  ) => {
    const concatFilter: Record<string, string | string[]> = {
      ...selectedFilters,
      ...(searchByValue === 'Number'
        ? { accountNo: search }
        : { accountName: search }),
    }
    setSelectedFilterItems(selectedFilters)
    fetchFilteredAccounts(concatFilter)
  }
  const fetchFilteredAccounts = async (
    concatFilter: Record<string, string | string[]>
  ) => {
    let params = ''
    Object.keys(concatFilter).map((item, x) => {
      return (params += `${x > 0 ? '&' : ''}${item}=${concatFilter[item]}`)
    })
    await filterLinkedCustomerAccounts(
      dispatch,
      customer.id ? customer.id : '',
      params
    )
  }
  const filters: IFilter[] = [
    {
      filterName: 'status',
      options: [
        { key: 'inactive', value: 'INACTIVE', label: 'Inactive' },
        { key: 'active', value: 'ACTIVE', label: 'Active' },
      ],
      type: 'dropdown/single',
    },
    {
      filterName: 'accountType',
      options: [
        { key: 'Individual', value: 'Individual', label: 'Individual' },
        { key: 'Joint', value: 'Joint', label: 'Joint' },
      ],
      type: 'dropdown/single',
    },
  ]
  const [searchByValue, setSearchByValue] = useState<string>('')
  return (
    <Stack direction="row" justifyContent="space-between">
      <CustomFilterBox
        openFilter={openFilter}
        setOpenFilter={setOpenFilter}
        searchValue={search}
        searchByValues={['Name', 'Number']}
        handleSearch={handleSearch}
        filters={filters}
        onFilterChange={handleFilterChange}
        setSearchByValue={setSearchByValue}
      />
      <LinkAccountDialog />
    </Stack>
  )
}
