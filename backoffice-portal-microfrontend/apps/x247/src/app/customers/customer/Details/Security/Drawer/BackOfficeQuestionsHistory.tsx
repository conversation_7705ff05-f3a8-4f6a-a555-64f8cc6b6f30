import {
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableRow,
} from '@mui/material'
import { IHeadCell } from '@dtbx/store/interfaces'
import { useAppDispatch, useAppSelector } from '@/store'
import {
  CustomPagination,
  CustomTableHeader,
  PaginationOptions,
} from '@dtbx/ui/components/Table'
import { CustomSkeleton, LoadingListsSkeleton } from '@dtbx/ui/components'

import { PinHistoryEmptyState } from './EmptyState'
import { useEffect, useState } from 'react'
import {
  getCustomerPinHistory,
  getCustomerPinLogsBackoffice,
} from '@/store/actions'
import { size } from 'lodash'
import { formatTimestamp } from '@dtbx/store/utils'

const header: IHeadCell[] = [
  {
    id: 'event',
    label: 'Event Type',
    alignCenter: false,
    alignRight: false,
  },
  {
    id: 'maker',
    label: 'Maker',
    alignCenter: false,
    alignRight: false,
  },
  {
    id: 'makerTimestamp',
    label: 'Maker Timestamp',
    alignCenter: false,
    alignRight: false,
  },
  {
    id: 'makerComments',
    label: 'Maker Comments',
    alignCenter: false,
    alignRight: false,
  },
  {
    id: 'checker',
    label: 'Checker',
    alignCenter: false,
    alignRight: false,
  },
  {
    id: 'checkerTimestamp',
    label: 'Checker Timestamp',
    alignCenter: false,
    alignRight: false,
  },
  {
    id: 'checkerComments',
    label: 'Checker Comments',
    alignCenter: false,
    alignRight: false,
  },
]

const BackOfficeQuestionsHistory = () => {
  const dispatch = useAppDispatch()
  const { customer, customerPinLogsBackOffice, isLoadingCustomerPinHistory } =
    useAppSelector((state) => state.customers)

  /*************************start pagination handlers***************************/
  const handlePagination = async (newOptions: PaginationOptions) => {
    await getCustomerPinLogsBackoffice(
      customer?.id || '',
      dispatch,
      newOptions.page,
      10
    )
  }
  /*************************end pagination handlers**************************/
  useEffect(() => {
    getCustomerPinLogsBackoffice(customer?.id || '', dispatch, 1, 10)
  }, [])
  return (
    <Paper
      elevation={0}
      sx={{
        border: '1px solid #EAECF0',
        boxShadow:
          '0px 1px 3px 0px rgba(16, 24, 40, 0.10), 0px 1px 2px 0px rgba(16, 24, 40, 0.06)',
      }}
    >
      <TableContainer>
        {isLoadingCustomerPinHistory ? (
          <LoadingListsSkeleton />
        ) : customerPinLogsBackOffice?.data?.length > 0 ? (
          <Table stickyHeader>
            <CustomTableHeader
              order={'desc'}
              orderBy={''}
              rowCount={0}
              headLabel={[...header]}
              numSelected={0}
            />
            <TableBody>
              {customerPinLogsBackOffice &&
                customerPinLogsBackOffice.data?.map((row) => {
                  return (
                    <TableRow key={row.id}>
                      <TableCell sx={{ textWrap: 'nowrap' }}>
                        {row.makerCheckerType.name}
                      </TableCell>
                      <TableCell sx={{ textWrap: 'nowrap' }}>
                        {row.maker}
                      </TableCell>
                      <TableCell sx={{ textWrap: 'nowrap' }}>
                        {formatTimestamp(row.dateCreated)}
                      </TableCell>
                      <TableCell>{row.makerComments}</TableCell>
                      <TableCell>{row.checker || 'N/A'}</TableCell>
                      <TableCell>
                        {row.checker ? formatTimestamp(row.dateModified) : ''}
                      </TableCell>

                      <TableCell>{row.checkerComments}</TableCell>
                    </TableRow>
                  )
                })}
            </TableBody>
          </Table>
        ) : (
          <PinHistoryEmptyState />
        )}
      </TableContainer>
      {customerPinLogsBackOffice.totalNumberOfPages > 0 && (
        <CustomPagination
          options={{
            page: customerPinLogsBackOffice.pageNumber || 1,
            size: customerPinLogsBackOffice.pageSize || 10,
            totalPages: customerPinLogsBackOffice.totalNumberOfPages,
          }}
          handlePagination={handlePagination}
          key={'pagination_pending_requests'}
        />
      )}
    </Paper>
  )
}

export default BackOfficeQuestionsHistory
