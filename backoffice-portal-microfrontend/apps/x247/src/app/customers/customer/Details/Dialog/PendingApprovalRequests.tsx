import {
  Box,
  Button,
  Drawer,
  IconButton,
  List,
  ListItem,
  Paper,
  Stack,
  Typography,
} from '@mui/material'
import CallMadeIcon from '@mui/icons-material/CallMade'
import React, { useEffect, useState } from 'react'
import { useAppDispatch, useAppSelector } from '@/store'
import {
  getCustomerAccountByAccountNo,
  getCustomerDeviceDetail,
} from '@/store/actions'
import {
  setApprovalDrawerOpen,
  setCustomerLinkedAccountsList,
  setDevices,
  setSelectedApprovalRequest,
} from '@/store/reducers'
import { setDrawer } from '@dtbx/store/reducers'
import {
  IApprovalRequest,
  ICustomerAccount,
  ICustomerProfileAccount,
  IDevice,
} from '@/store/interfaces'
import {
  AccountIcon,
  DeviceIcons,
  ProfileIcon,
} from '@dtbx/ui/components/SvgIcons'
import { CloseRounded } from '@mui/icons-material'

import ViewRequestDetails from './ViewRequestDetails'

const PendingApprovalRequests = () => {
  const dispatch = useAppDispatch()
  const [hideButton] = useState<boolean>(true)
  const { pendingSingleCustomerApprovalRequests } = useAppSelector(
    (state) => state.approvalRequests
  )
  const { open, header } = useAppSelector((state) => state.overlay.drawer)

  const { customer, devicesResponse } = useAppSelector(
    (state) => state.customers
  )

  const fetchApprovalRequestDetails = () => {
    let devices: IDevice[] = []
    let accounts: ICustomerProfileAccount[] = []
    pendingSingleCustomerApprovalRequests.forEach(async (request) => {
      switch (request.makerCheckerType.module) {
        case 'Customers':
          break
        case 'ProfileDevices':
          const ProfileDevices = await getCustomerDeviceDetail({
            profileID: customer.id || '',
            deviceID: request.entityId || '',
            dispatch,
          })
          devices = [...devices, ProfileDevices]
          dispatch(
            setDevices({
              ...devicesResponse,
              data: devices,
            })
          )
          break
        case 'accounts':
          const entity = JSON.parse(request?.entity || '')
          const accountNo = entity?.accountNo
            ? entity?.accountNo
            : entity?.accounts && entity?.accounts[0]?.accNumber
          const accountDetails = await getCustomerAccountByAccountNo(
            customer.id || '',
            accountNo || '',
            dispatch
          )
          accounts = [...accounts, accountDetails]
          dispatch(setCustomerLinkedAccountsList(accounts))
          break
      }
    })
  }

  const getRequestData = (request: IApprovalRequest) => {
    switch (request.makerCheckerType.module) {
      case 'Customers':
        return {
          title:
            `${customer?.firstName} ${customer?.lastName}` ||
            'Customer not found',
          icon: <ProfileIcon />,
        }
      case 'ProfileDevices':
        return {
          title:
            devicesResponse?.data?.find(
              (device) => device?.deviceId === request?.entityId
            )?.deviceName || 'Device not found',
          icon: <DeviceIcons />,
        }
      case 'accounts':
        const entity = JSON.parse(request?.entity || '')
        const accountNo: string = entity?.accountNo
          ? entity?.accountNo
          : entity?.accounts && entity?.accounts.length > 1
            ? entity.accounts
                .map((acc: ICustomerAccount) => acc.accNumber)
                .join(',')
            : ''
        return {
          title: `Account ${accountNo}`,
          icon: <AccountIcon />,
        }
    }
  }

  useEffect(() => {
    fetchApprovalRequestDetails()
  }, [])

  return (
    <Box>
      {/* drawer */}
      <Drawer
        open={open}
        anchor="right"
        onClose={() => {
          dispatch(
            setDrawer({
              open: false,
              drawerChildren: null,
              header: '',
            })
          )
        }}
        PaperProps={{
          sx: {
            maxWidth: '72%',
            minWidth: '30%',
          },
        }}
      >
        <Paper
          elevation={0}
          sx={{
            minWidth: '30vw',
            height: '100vh',
            display: 'flex',
            flexDirection: 'column',
            justifyContent: 'flex-start',
            gap: '10px',
            transition: 'width 3.3s ease-in-out',
          }}
        >
          <Box
            sx={{
              width: '100%',
              maxHeight: '60px',
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
              padding: '16px 20px 8px 24px',
              borderBottom: '1px solid lightgray',
              backgroundColor: '#F9FAFB',
            }}
          >
            {/* DrawerHeader */}
            <Box
              sx={{
                display: 'flex',
                gap: '10px',
                justifyContent: 'flex-start',
                alignItems: 'center',
              }}
            >
              <Typography variant="h6">{header}</Typography>
            </Box>
            <IconButton
              sx={{
                border: '1px solid #CBD5E1',
                backgroundColor: '#F1F5F9',
              }}
              onClick={() => {
                dispatch(
                  setDrawer({
                    open: false,
                    drawerChildren: null,
                    header: '',
                  })
                )
              }}
            >
              <CloseRounded
                sx={{
                  fontSize: '20px',
                }}
              />
            </IconButton>
          </Box>

          <Stack
            sx={{
              padding: '30px 35px',
              display: 'flex',
              gap: '22px',
            }}
          >
            {pendingSingleCustomerApprovalRequests.map((item, index) => (
              <Stack
                key={index}
                sx={{
                  width: '100%',
                  padding: '20px 25px',
                  display: 'flex',
                  flexDirection: 'row',
                  gap: '22px',
                  border: '1px solid #EAECF0',
                }}
              >
                <Stack
                  sx={{
                    background: '#E7E8E9',
                    borderRadius: '5px',
                    width: '50px',
                    height: '40px',
                    overflow: 'hidden',
                    alignItems: 'center',
                    justifyContent: 'center',
                  }}
                >
                  {getRequestData(item)?.icon}
                </Stack>
                <Stack
                  sx={{
                    flexDirection: 'row',
                    width: '100%',
                  }}
                >
                  <Stack>
                    <Typography
                      variant="label2"
                      sx={{
                        fontSize: '18px',
                        fontStyle: 'normal',
                        fontWeight: 600,
                        lineHeight: '16px',
                        color: '#555C61',
                      }}
                    >
                      {getRequestData(item)?.title}
                    </Typography>
                    <List sx={{ listStyleType: 'disc', marginLeft: '10px' }}>
                      <ListItem sx={{ display: 'list-item', padding: 0 }}>
                        <Typography
                          variant="label2"
                          sx={{
                            fontSize: '14px',
                            fontStyle: 'normal',
                            fontWeight: 500,
                            lineHeight: '16px',
                            color: '#555C61',
                          }}
                        >
                          {item.makerCheckerType.name}
                        </Typography>
                      </ListItem>
                    </List>
                  </Stack>
                  <Stack sx={{ marginLeft: 'auto' }}>
                    <Button
                      variant="outlined"
                      onClick={() => {
                        dispatch(setSelectedApprovalRequest(item))
                        dispatch(setApprovalDrawerOpen(true))
                        dispatch(
                          setDrawer({
                            drawerChildren: {
                              childType: 'pending_approval_requests',
                            },
                            header: 'Approval request details',
                            open: false,
                          })
                        )
                      }}
                      sx={{
                        border: '1px solid #E3E4E4',
                        fontSize: '15px',
                        fontWeight: '600',
                        padding: '0px 10px',
                      }}
                      endIcon={<CallMadeIcon style={{ fontSize: 15 }} />}
                    >
                      See details
                    </Button>
                  </Stack>
                </Stack>
              </Stack>
            ))}
            <ViewRequestDetails hideButton={hideButton} />
          </Stack>
        </Paper>
      </Drawer>
    </Box>
  )
}

export default PendingApprovalRequests
