import {
  Paper,
  Stack,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableRow,
} from '@mui/material'
import React from 'react'
import { ICustomerProfileAccount } from '@/store/interfaces'
import { IHeadCell } from '@dtbx/store/interfaces'
import { useAppDispatch, useAppSelector } from '@/store'
import { CustomerStatusChip } from '@dtbx/ui/components/Chip'
import { CustomTableHeader } from '@dtbx/ui/components/Table'

import { AccountsMoreMenu } from '@/app/customers/customer/Details/Accounts/AccountsMoreMenu'
import { getCustomerAccountByAccountNo } from '@/store/actions'
import { setIsViewAccountOpen } from '@/store/reducers'

const headerData: IHeadCell[] = [
  {
    id: 'name',
    label: 'Account Name',
    alignCenter: false,
    alignRight: false,
  },
  {
    id: 'accNumber',
    label: 'Account Number',
    alignCenter: false,
    alignRight: false,
  },

  {
    id: 'status',
    label: 'Status',
    alignCenter: false,
    alignRight: false,
  },

  {
    id: 'currency',
    label: 'Currency',
    alignCenter: false,
    alignRight: false,
  },

  {
    id: 'tariff',
    label: 'Tariff',
    alignCenter: false,
    alignRight: false,
  },

  {
    id: 'type',
    label: 'Type',
    alignCenter: false,
    alignRight: false,
  },

  {
    id: 'actions',
    label: 'Actions',
    alignCenter: false,
    alignRight: false,
  },
]

export const AccountsList = () => {
  const { customerLinkedAccountsList, customer } = useAppSelector(
    (state) => state.customers
  )
  const dispatch = useAppDispatch()
  const handleRowClick = async (account: ICustomerProfileAccount) => {
    const accountDetails = await getCustomerAccountByAccountNo(
      customer.id ? customer.id : '',
      account.accountNo,
      dispatch
    )
    dispatch(setIsViewAccountOpen(!!accountDetails?.profile?.phoneNumber))
  }
  return (
    <Stack direction="column">
      <TableContainer
        component={Paper}
        sx={{
          boxShadow:
            '0px 1px 3px 0px rgba(16, 24, 40, 0.10), 0px 1px 2px 0px rgba(16, 24, 40, 0.06)',
        }}
      >
        <Table>
          <CustomTableHeader
            order={'desc'}
            orderBy={'id'}
            headLabel={headerData}
            showCheckbox={false}
            rowCount={2}
            numSelected={0}
            onRequestSort={() => {}}
            onSelectAllClick={() => {}}
          />
          <TableBody>
            {customerLinkedAccountsList &&
              customerLinkedAccountsList.map((row) => (
                <TableRow key={row.accountNo}>
                  <TableCell
                    onClick={() => handleRowClick(row)}
                    sx={{
                      cursor: 'pointer',
                    }}
                  >
                    {row?.fullName || ''}
                  </TableCell>
                  <TableCell
                    onClick={() => handleRowClick(row)}
                    sx={{
                      cursor: 'pointer',
                    }}
                  >
                    {row.accountNo}
                  </TableCell>
                  <TableCell>
                    <CustomerStatusChip label={row.status} />
                  </TableCell>
                  <TableCell>{row.currency}</TableCell>
                  <TableCell>{row.tariffName}</TableCell>
                  <TableCell>{row.accountType}</TableCell>
                  <TableCell>
                    <AccountsMoreMenu account={row} />
                  </TableCell>
                </TableRow>
              ))}
          </TableBody>
        </Table>
      </TableContainer>
    </Stack>
  )
}
