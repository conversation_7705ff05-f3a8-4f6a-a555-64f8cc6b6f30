import { MoreHoriz } from '@mui/icons-material'
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>conButton,
  <PERSON>u,
  <PERSON>uI<PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>ield,
  Typography,
} from '@mui/material'
import CloseIcon from '@mui/icons-material/Close'
import EditIcon from '@mui/icons-material/Edit'
import { useEffect, useState } from 'react'
import { useAppDispatch, useAppSelector } from '@/store'
import {
  ACCESS_CONTROLS,
  AccessControlWrapper,
  formatTimestamp,
  reasonsForUnsubscribing,
} from '@dtbx/store/utils'
import { Dialog } from '@dtbx/ui/components/Overlay'
import {
  INotificationEventsPayload,
  INotificationEventsPerAccount,
  INotificationEventSubscriberPayload,
} from '@/store/interfaces'
import {
  editSubscriptionAlerts,
  fetchNotificationEventsPerAccount,
  unsubscribeToNotificationEvents,
} from '@/store/actions'

export const SubscriptionAlertsMoreMenu = ({
  notification,
}: {
  notification: INotificationEventsPerAccount
}) => {
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null)
  const open = Boolean(anchorEl)
  const handleClick = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget)
  }
  const handleClose = () => {
    setAnchorEl(null)
  }
  return (
    <>
      <IconButton
        sx={{
          width: '24px',
          height: '24px',
          borderRadius: '50%',
        }}
        onClick={handleClick}
      >
        <MoreHoriz />
      </IconButton>

      <Menu
        id="demo-customized-menu"
        MenuListProps={{
          'aria-labelledby': 'demo-customized-button',
        }}
        anchorEl={anchorEl}
        onClose={handleClose}
        open={open}
        sx={{
          borderRadius: '4px',
        }}
      >
        <EditSubscriptionsAlerts editMode={false} notification={notification} />
        <EditSubscriptionsAlerts editMode={true} notification={notification} />
        <RemoveSubscriptionAlerts notification={notification} />
      </Menu>
    </>
  )
}

const EditSubscriptionsAlerts = ({
  editMode,
  notification,
}: {
  editMode: boolean
  notification: INotificationEventsPerAccount
}) => {
  const dispatch = useAppDispatch()
  const handleClose = () => {
    setOpenDrawer(false)
  }
  const { customerProfileAccount } = useAppSelector((state) => state.customers)
  const [openDrawer, setOpenDrawer] = useState(false)
  const [edit, setEdit] = useState<boolean>(editMode)
  const [dialogSize] = useState<string>('25%')
  const [checkedAlerts, setCheckedAlerts] = useState<boolean[]>([])
  const [email, setEmail] = useState<string>(
    notification?.subscribers[0]?.recipient
  )

  const eventName = () => {
    return notification?.alertFrequency?.name?.replace(' Frequency', '')
  }
  const handleSubmit = async () => {
    setOpenDrawer(false)
    const subscribers: INotificationEventSubscriberPayload[] = [
      {
        recipients: [email],
        deliveryMode: notification?.subscribers?.[0]?.deliveryMode,
        name: `${customerProfileAccount?.profile?.firstName} ${customerProfileAccount?.profile?.lastName}`,
        recipientType: 'END_USER',
      },
    ]
    const payload: INotificationEventsPayload = {
      eventId: notification?.event?.id,
      profileId: customerProfileAccount?.profile?.id,
      branchCode: customerProfileAccount?.branchCode,
      accountSource: notification?.accountSource,
      accountId: notification?.accountId,
      thresholdAmount: '',
      subscribers,
      frequencyId: notification?.alertFrequency?.id,
      comments: 'Edit subscription alert',
    }
    await editSubscriptionAlerts({ events: payload, dispatch })
    fetchNotificationEventsPerAccount({
      accountId: notification?.accountId,
      dispatch,
    })
  }
  const handleChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    event.stopPropagation()
    setEmail(event.target.value)
  }
  useEffect(() => {
    setCheckedAlerts(
      notification?.subscribers?.map(
        (sub) => !!(sub?.deliveryMode && sub?.recipient)
      )
    )
  }, [])
  useEffect(() => {
    setEdit(editMode)
  }, [openDrawer])
  return (
    <>
      {editMode ? (
        <AccessControlWrapper
          rights={
            editMode ? [...ACCESS_CONTROLS.UPDATE_ACCOUNT_PREFERENCES] : []
          }
        >
          <MenuItem
            onClick={() => {
              setOpenDrawer(true)
            }}
          >
            <Typography variant="body1">{'Edit Details'}</Typography>
          </MenuItem>
        </AccessControlWrapper>
      ) : (
        <MenuItem
          onClick={() => {
            setOpenDrawer(true)
          }}
        >
          <Typography variant="body1">{'See more details'}</Typography>
        </MenuItem>
      )}
      <Drawer
        open={openDrawer}
        anchor="right"
        variant="persistent"
        sx={{
          '.MuiDrawer-paper': {
            width: dialogSize,
          },
        }}
        onClose={handleClose}
        PaperProps={{
          sx: {
            width: dialogSize,
          },
        }}
      >
        <Stack
          sx={{
            justifyContent: 'space-between',
            alignItems: 'center',
            flexDirection: 'row',
            padding: '10px 16px 10px 16px',
          }}
        >
          {/* tittle */}
          <Stack sx={{ flexDirection: 'row', alignItems: 'center' }}>
            <Typography
              sx={{
                fontSize: '16px',
                fontWeight: '700',
              }}
            >
              {`${eventName()} subscription`}
            </Typography>
            {!edit && (
              <IconButton
                onClick={() => {
                  setEdit(!edit)
                }}
              >
                <EditIcon sx={{ fontSize: '20px', cursor: 'pointer' }} />
              </IconButton>
            )}
          </Stack>
          {/* close button */}
          <IconButton
            onClick={() => {
              setOpenDrawer(false)
            }}
          >
            <CloseIcon />
          </IconButton>
        </Stack>
        <Stack
          direction="row"
          sx={{
            height: '100%',
          }}
        >
          <Stack
            sx={{
              padding: '0px 16px 0px 16px',
              height: '100%',
              width: '100%',
            }}
          >
            <TextField
              fullWidth
              label="Type"
              margin="normal"
              size="small"
              value={eventName()}
              inputProps={{ readOnly: !edit }}
              disabled={edit}
            />

            <TextField
              fullWidth
              label="Status"
              margin="normal"
              size="small"
              value={notification?.status}
              inputProps={{ readOnly: !edit }}
              disabled={edit}
            />
            <TextField
              fullWidth
              label="Date opted in"
              margin="normal"
              size="small"
              value={formatTimestamp(notification?.optedInDate)}
              inputProps={{ readOnly: !edit }}
              disabled={edit}
            />
            <TextField
              fullWidth
              label="Emails"
              margin="normal"
              size="small"
              value={email}
              onChange={handleChange}
              inputProps={{ readOnly: !edit }}
            />
            <TextField
              fullWidth
              label="Charge per email"
              margin="normal"
              size="small"
              value={'KES 10'}
              inputProps={{ readOnly: !edit }}
              disabled={edit}
            />
            <Stack></Stack>
            {edit && (
              <Stack direction="row" gap={'2%'} sx={{ marginTop: '30px' }}>
                <Button
                  variant="outlined"
                  fullWidth
                  sx={{
                    maxHeight: '34px',
                    background: '#ffffff',
                    border: '1px solid #AAADB0',
                    boxShadow: ' 0px 1px 2px 0px rgba(16, 24, 40, 0.05)',
                  }}
                  onClick={() => setOpenDrawer(false)}
                >
                  Cancel
                </Button>
                <Button
                  variant="contained"
                  fullWidth
                  sx={{
                    maxHeight: '34px',
                  }}
                  onClick={handleSubmit}
                >
                  Save changes
                </Button>
              </Stack>
            )}
          </Stack>
        </Stack>
      </Drawer>
    </>
  )
}

const RemoveSubscriptionAlerts = ({
  notification,
}: {
  notification: INotificationEventsPerAccount
}) => {
  const dispatch = useAppDispatch()
  const [open, setOpen] = useState(false)
  const { customerProfileAccount } = useAppSelector((state) => state.customers)
  const sumbit = async (reasons: string[]) => {
    await unsubscribeToNotificationEvents({
      accountId: notification?.accountId,
      eventId: notification?.event?.id,
      profileId: customerProfileAccount?.profile?.id,
      comments: reasons.join(', '),
      dispatch,
    })
    fetchNotificationEventsPerAccount({
      accountId: notification?.accountId,
      dispatch,
    })
  }
  return (
    <>
      <AccessControlWrapper
        rights={[...ACCESS_CONTROLS.UPDATE_ACCOUNT_PREFERENCES]}
      >
        <MenuItem
          onClick={() => {
            setOpen(true)
          }}
        >
          <Typography variant="body1">Remove subscription</Typography>
        </MenuItem>
      </AccessControlWrapper>
      <Dialog
        open={open}
        setOpen={setOpen}
        title={'Remove notification'}
        buttonText={'Remove'}
        descriptionText={'Please give a reason for removing notification.'}
        isLoading={false}
        reasons={reasonsForUnsubscribing}
        buttonProps={{
          color: '#EB0045',
        }}
        onClick={sumbit}
      />
    </>
  )
}
