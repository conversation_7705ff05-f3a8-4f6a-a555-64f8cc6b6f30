'use client'
import React, { useEffect } from 'react'

import PageHeader from './pageHeader'
import CustomerDetails from './Details'
import { useAppDispatch, useAppSelector } from '@/store'
import { getCustomerProfile } from '@/store/actions'

const CustomerPage = () => {
  const dispatch = useAppDispatch()
  const { customer } = useAppSelector((state) => state.customers)
  useEffect(() => {
    customer && customer.id && getCustomerProfile(customer.id, dispatch)
  }, [])
  return (
    <>
      <PageHeader />
      <CustomerDetails />
    </>
  )
}

export default CustomerPage
