'use client'
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, Typography } from '@mui/material'
import React, { useEffect } from 'react'
import { sentenceCase } from 'tiny-case'
import { CustomerInfoChip, CustomerStatusChip } from '@dtbx/ui/components/Chip'
import { useAppDispatch, useAppSelector } from '@/store'
import { setDrawer } from '@dtbx/store/reducers'
import {
  setApprovalDrawerOpen,
  setOpenChangeLogsDrawer,
  setSelectedApprovalRequest,
  setSelectedCustomer,
} from '@/store/reducers'
import {
  fetchPendingSingleCustomerApprovals,
  getLinkedCustomerAccountsByProfileId,
} from '@/store/actions'

import ViewRequestDetails from './Details/Dialog/ViewRequestDetails'
import PendingApprovalRequests from './Details/Dialog/PendingApprovalRequests'
import {
  ActivateCustomer,
  DeleteCustomer,
} from '@/app/customers/customers/CustomerLists/customerMoreMenu'

const PageHeader = () => {
  const dispatch = useAppDispatch()
  const { customer, isCustomerApprovalBarOpen } = useAppSelector(
    (state) => state.customers
  )
  const {
    selectedApprovalRequest,
    pendingSingleCustomerApprovalRequests,
    customersWithPendingApprovals,
  } = useAppSelector((state) => state.approvalRequests)

  const pendingRequestNames = () => {
    let result
    if (pendingSingleCustomerApprovalRequests.length > 3) {
      const names = pendingSingleCustomerApprovalRequests
        .slice(0, 3)
        .map((item) => item?.makerCheckerType?.name)
      const additionalCount = pendingSingleCustomerApprovalRequests.length - 3
      names.push(`+${additionalCount}`)
      result = names
    } else {
      result = pendingSingleCustomerApprovalRequests.map(
        (item) => item?.makerCheckerType?.name
      )
    }
    return result
  }
  useEffect(() => {
    if (
      selectedApprovalRequest &&
      selectedApprovalRequest.makerCheckerType?.type === 'CREATE_CUSTOMERS'
    ) {
    } else if (customer && customer.id) {
      fetchPendingSingleCustomerApprovals(
        dispatch,
        customer.id,
        {},
        customersWithPendingApprovals
      )
      getLinkedCustomerAccountsByProfileId(customer.id, dispatch)
    }
  }, [customer])

  useEffect(() => {
    if (
      pendingSingleCustomerApprovalRequests.length > 0 &&
      !isCustomerApprovalBarOpen
    ) {
      if (pendingSingleCustomerApprovalRequests.length === 1) {
        dispatch(
          setSelectedApprovalRequest(pendingSingleCustomerApprovalRequests[0])
        )
        dispatch(setApprovalDrawerOpen(true))
      } else {
        dispatch(
          setDrawer({
            drawerChildren: { childType: 'pending_approval_requests' },
            header: 'Approval request details',
            open: true,
          })
        )
      }
    }
  }, [
    pendingSingleCustomerApprovalRequests,
    isCustomerApprovalBarOpen,
    dispatch,
  ])
  return (
    <Stack
      sx={{
        padding: '1% 2%',
        flexDirection: 'row',
      }}
    >
      <Stack
        sx={{
          flexDirection: 'row',
          justifyContent: 'space-between',
          gap: '2vh',
          width: '100%',
        }}
      >
        <Stack
          sx={{
            gap: '16px',
            justifyContent: 'center',
            flexDirection: 'row',
            alignItems: 'center',
          }}
        >
          <Breadcrumbs>
            <Link
              sx={{
                cursor: 'pointer',
                color: '#555C61',
              }}
              onClick={() => {
                dispatch(
                  setSelectedCustomer({
                    customer: null,
                    isPendingAction: false,
                    openDetails: false,
                  })
                )
                window.history.back()
              }}
            >
              All Customers
            </Link>
            <Typography sx={{ color: '#000' }}>
              {customer && (
                <>
                  {customer.firstName ? sentenceCase(customer.firstName) : ''}{' '}
                  {customer.lastName ? sentenceCase(customer.lastName) : ''}
                </>
              )}
            </Typography>
          </Breadcrumbs>
          {customer.isBlocked ? (
            <>
              <CustomerStatusChip label="INACTIVE" /> -{' '}
              <Typography sx={{ color: '#F04438' }} variant="body2">
                {customer.blockReason}
              </Typography>
            </>
          ) : (
            <CustomerStatusChip label="ACTIVE" />
          )}
          {selectedApprovalRequest &&
            isCustomerApprovalBarOpen &&
            selectedApprovalRequest.status === 'PENDING' && (
              <CustomerStatusChip label="Pending approval details" />
            )}
          {pendingSingleCustomerApprovalRequests.length > 0 &&
            !isCustomerApprovalBarOpen && (
              <Stack
                sx={{
                  flexDirection: 'row',
                }}
              >
                <CustomerInfoChip
                  label={`Pending approval :`}
                  requests={pendingRequestNames()}
                />
              </Stack>
            )}
        </Stack>
        {pendingSingleCustomerApprovalRequests.length > 0 &&
          !isCustomerApprovalBarOpen && (
            <Stack>
              <Button
                onClick={() =>
                  dispatch(
                    setDrawer({
                      drawerChildren: {
                        childType: 'pending_approval_requests',
                        data: [],
                      },
                      header: 'Approval request details',
                      open: true,
                    })
                  )
                }
                variant="outlined"
                sx={{
                  border: '1px solid #D0D5DD',
                  borderRadius: '4px',
                  boxShadow: '0px 1px 2px 0px rgba(16, 24, 40, 0.05)',
                  height: '40px',
                  color: '#555C61',
                  fontSize: '15px',
                  fontWeight: '500',
                }}
              >
                View approval request details
                <Typography
                  sx={{
                    border: '1px solid #EAECF0',
                    background: '#F9FAFB',
                    width: '25px',
                    height: '25px',
                    borderRadius: '20px',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                  }}
                >
                  {pendingSingleCustomerApprovalRequests.length}
                </Typography>
              </Button>
              <ViewRequestDetails hideButton />
              <PendingApprovalRequests />
            </Stack>
          )}
        {isCustomerApprovalBarOpen &&
          (selectedApprovalRequest.status === 'REJECTED' ||
            selectedApprovalRequest.status === 'APPROVED') && (
            <Stack
              sx={{
                flexDirection: 'row',
                gap: '1.5vh',
                justifyContent: 'flex-end',
              }}
            >
              <Button
                onClick={() => dispatch(setOpenChangeLogsDrawer(true))}
                variant="outlined"
                sx={{
                  backgroundColor: '#E3E4E4',
                  border: '1px solid  #AAADB0',
                  borderRadius: '4px',
                  boxShadow: '0px 1px 2px 0px rgba(16, 24, 40, 0.05)',
                  height: '34px',
                  color: '#555C61',
                  fontSize: '13px',
                  fontStyle: 'normal',
                  fontWeight: '500',
                  lineHeight: '16px',
                  textAlign: 'center',
                  display: 'none',
                }}
              >
                Changes Log
              </Button>
            </Stack>
          )}

        {/* on select customer without approval pending | when click view customer details*/}
        {!isCustomerApprovalBarOpen && (
          <Stack
            sx={{
              flexDirection: 'row',
              gap: '1vh',
            }}
          >
            <ActivateCustomer customer={customer} origin={'view'} />
            <DeleteCustomer customer={customer} origin={'view'} />
            <Button
              onClick={() => dispatch(setOpenChangeLogsDrawer(true))}
              variant="outlined"
              sx={{
                backgroundColor: '#E3E4E4',
                border: '1px solid  #AAADB0',
                borderRadius: '4px',
                boxShadow: '0px 1px 2px 0px rgba(16, 24, 40, 0.05)',
                height: '34px',
                color: '#555C61',
                fontSize: '13px',
                fontStyle: 'normal',
                fontWeight: '500',
                lineHeight: '16px',
                textAlign: 'center',
                display: 'none',
              }}
            >
              Changes Log
            </Button>
          </Stack>
        )}

        {isCustomerApprovalBarOpen &&
          selectedApprovalRequest.status !== 'REJECTED' &&
          selectedApprovalRequest.status !== 'APPROVED' && (
            <Stack
              sx={{
                flexDirection: 'row',
                justifyContent: 'flex-end',
                gap: '1vh',
              }}
            >
              <ViewRequestDetails />
            </Stack>
          )}
      </Stack>
    </Stack>
  )
}

export default PageHeader
