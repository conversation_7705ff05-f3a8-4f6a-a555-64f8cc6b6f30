'use client'
import {
  <PERSON>ton,
  Chip,
  FormControl,
  InputAdornment,
  InputLabel,
  MenuItem,
  Select,
  SelectChangeEvent,
  Stack,
  Typography,
} from '@mui/material'
import React, { CSSProperties, FC, useState } from 'react'
import SearchOutlinedIcon from '@mui/icons-material/SearchOutlined'
import CloseIcon from '@mui/icons-material/Close'
import {
  FilterListOffRounded,
  FilterListRounded,
  KeyboardArrowDownRounded,
} from '@mui/icons-material'
import dayjs from 'dayjs'
import { IFilterOption } from '@dtbx/store/interfaces'

import { CustomSearchInput } from '@dtbx/ui/components/Input'
import RequestSearch from './RequestSearch'
import {
  DateRangePicker,
  DropdownMenuCheckBoxWithSearch,
} from '@dtbx/ui/components/DropDownMenus'
import { sentenceCase } from 'tiny-case'

export type FilterType =
  | 'select'
  | 'dropdown/checkbox'
  | 'dropdown/single'
  | 'date'
  | 'datetime'
  | 'autocomplete'

interface IFilterOptions {
  key: string
  value: string
  label: string
}

interface IFilter {
  filterName: string
  options: IFilterOptions[]
  type: FilterType
}

interface CustomFilterBoxProps {
  openFilter: boolean
  setOpenFilter: (value: boolean) => void
  searchValue: string
  handleSearch: (e: React.ChangeEvent<HTMLInputElement>) => void
  filters: IFilter[]
  searchByValues?: string[]
  onFilterChange: (filters: Record<string, string | string[]>) => void
  setSearchByValue?: (value: string) => void
  setDate?: (value: { start: dayjs.Dayjs; end: dayjs.Dayjs } | null) => void
  setMakerName?: (value: string) => void
  searchPlaceHolder?: string
}

export const CustomFilterBox = (props: CustomFilterBoxProps) => {
  const {
    openFilter,
    setOpenFilter,
    searchValue,
    handleSearch,
    filters,
    searchByValues,
    onFilterChange,
    setSearchByValue,
    setDate,
    setMakerName,
    searchPlaceHolder,
  } = props
  const defaultModule = () => {
    let defaultModule = {}
    filters.forEach((filter) => {
      if (filter.filterName === 'Module') {
        defaultModule =
          filter.options.filter((option) => option.key === 'customers').length >
          0
            ? { Module: 'Customers' }
            : {}
      }
    })
    return defaultModule
  }
  const [selectedFilters, setSelectedFilters] = useState<
    Record<string, string | string[]>
  >(defaultModule() ? defaultModule() : {})
  const [checkBoxValue, setCheckBoxValue] = useState<IFilterOption[]>([])
  const [openDropDownCheckbox, setOpenDropDownCheckbox] = useState<
    string | null
  >(null)
  const handleFilterChange = (
    filterName: string,
    selectedOption: string | string[]
  ) => {
    const newFilters = {
      ...selectedFilters,
      [filterName]: selectedOption,
    }
    setSelectedFilters(newFilters)
    onFilterChange(newFilters)
  }
  const handleFilterOpen = () => {
    setOpenFilter(!openFilter)
    // setSelectedFilters({})
    // onFilterChange({})
  }
  const handleClearAll = () => {
    setOpenFilter(false)
    setSelectedFilters({})
    onFilterChange({})
    setCheckBoxValue([])
    setOpenDropDownCheckbox(null)
    setDate ? setDate(null) : null
  }
  const handleDropdownCheckboxOpen = (filterName: string) => {
    setOpenDropDownCheckbox((prev) => (prev === filterName ? null : filterName))
  }
  return (
    <Stack
      sx={{
        flexDirection: 'column',
      }}
    >
      <Stack
        sx={{
          flexDirection: 'row',
          justifyContent: 'flex-start',
          gap: '10px',
        }}
      >
        {setMakerName ? (
          <RequestSearch
            searchByItems={[
              {
                label: 'Maker First Name',
                value: 'firstName',
              },
              {
                label: 'Maker Last Name',
                value: 'lastName',
              },
            ]}
            onSetSearch={(makerName: string) => {
              setMakerName(makerName)
            }}
          />
        ) : searchByValues && setSearchByValue ? (
          <SearchByValuesBox
            searchValue={searchValue}
            handleSearch={handleSearch}
            searchByValues={searchByValues}
            setSearchByValue={setSearchByValue}
          />
        ) : (
          <CustomSearchInput
            value={searchValue}
            onChange={handleSearch}
            placeholder={searchPlaceHolder || 'Search'}
            startAdornment={
              <InputAdornment position="start">
                <SearchOutlinedIcon sx={{ color: 'text.disabled' }} />
              </InputAdornment>
            }
          />
        )}

        <Button
          variant="outlined"
          sx={{
            height: '40px',
            gap: 0,
            boxShadow: openFilter
              ? '0px 1px 2px 0px rgba(16, 24, 40, 0.05)'
              : '0px 1px 2px 0px #1018280D',
            border: openFilter ? '1.5px solid #555C61' : '1.5px solid #D0D5DD',
          }}
          startIcon={
            !openFilter ? <FilterListOffRounded /> : <FilterListRounded />
          }
          onClick={handleFilterOpen}
        >
          <Typography
            sx={{
              textWrap: 'nowrap',
            }}
          >
            {openFilter ? 'Hide Filters' : 'Show Filters'}
          </Typography>
        </Button>
      </Stack>
      <Stack
        sx={{
          display: openFilter ? 'flex' : 'none',
          flexDirection: 'row',
          gap: '10px',
          mt: '10px',
        }}
      >
        <Button
          variant="text"
          sx={{
            width: '6vw',
            color: '#555C61',
            textWrap: 'nowrap',
            height: '40px',
            boxShadow: '0px 1px 2px 0px rgba(16, 24, 40, 0.05)',
            borderRadius: '6px',
            gap: 0,
          }}
          onClick={handleClearAll}
          endIcon={<CloseIcon />}
        >
          <Typography variant="body2" color="text.primary">
            Clear All
          </Typography>
        </Button>
        {filters.map((filter) => {
          return filter.type === 'select' ? (
            <FormControl
              key={filter.filterName}
              sx={{
                width: '40%',
              }}
            >
              <InputLabel
                id="demo-simple-select-outlined-label"
                sx={{
                  background: '#fcfcfc',
                  padding: '0 4px',
                  marginLeft: '-4px',
                }}
              >
                {filter.filterName}
              </InputLabel>
              <Select
                labelId="demo-simple-select-outlined-label"
                id="demo-simple-select-outlined"
                key={filter.filterName}
                sx={{
                  border: '1px solid #AAADB0',
                  height: '40px',
                }}
                value={selectedFilters[filter.filterName] || ''}
                onChange={(e) =>
                  handleFilterChange(filter.filterName, e.target.value)
                }
              >
                {filter.options.map((option) => (
                  <MenuItem key={option.key} value={option.label}>
                    {option.label}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          ) : filter.type === 'dropdown/checkbox' ? (
            <DropdownMenuCheckBoxWithSearch
              label={filter.filterName}
              key={filter.filterName}
              onClick={(selectedFilters: IFilterOption[]) => {
                if (selectedFilters.length >= 0) {
                  handleFilterChange(
                    filter.filterName,
                    selectedFilters.map((filt) => filt.key)
                  )
                  setCheckBoxValue(selectedFilters)
                }
              }}
              filters={filter.options}
              selectedFilter={checkBoxValue}
              open={openDropDownCheckbox === filter.filterName}
              handleOpenMenu={() =>
                handleDropdownCheckboxOpen(filter.filterName)
              }
              setOpen={(open) => {
                if (!open) handleDropdownCheckboxOpen('')
              }}
            />
          ) : filter.type === 'dropdown/single' ? (
            <DropdownMenu
              filter={filter}
              onSelected={(str: string) => {
                handleFilterChange(filter.filterName, str)
              }}
              selectedFilterValue={selectedFilters[filter.filterName] as string}
            />
          ) : filter.type === 'date' ? (
            <DateRangePicker
              onApplyDateRange={(date: {
                start: dayjs.Dayjs
                end: dayjs.Dayjs
              }) => {
                if (setDate !== undefined) {
                  setDate(date)
                }
              }}
              buttonText="Date Created"
            />
          ) : (
            <></>
          )
        })}
      </Stack>
    </Stack>
  )
}

interface IDropdownMenu {
  filter: IFilter
  onSelected: (str: string) => void
  selectedFilterValue?: string
  size?: 'small' | 'medium'
  sx?: CSSProperties
}

export const DropdownMenu: FC<IDropdownMenu> = ({
  filter,
  onSelected,
  selectedFilterValue,
  size,
  sx,
}) => {
  const disabledValue = 'none'
  return (
    <FormControl
      sx={{
        minWidth: '20%',
        ...sx,
      }}
      size="small"
    >
      <InputLabel
        id={filter.filterName}
        sx={{
          background: '#FFFFFF',
          px: '5px',
        }}
      >
        {sentenceCase(filter.filterName)}
      </InputLabel>
      <Select
        sx={{
          px: '5%',
          justifyContent: 'center',
        }}
        labelId={filter.filterName}
        size={size || 'small'}
        MenuProps={{
          sx: {
            maxHeight: size ? 'auto' : '50vh',
            borderRadius: '6px',
            '.MuiMenu-paper': { borderRadius: '8px' },
          },
        }}
        value={selectedFilterValue ?? disabledValue}
        onChange={(e) => onSelected(e.target.value)}
        IconComponent={KeyboardArrowDownRounded}
      >
        <MenuItem disabled value={disabledValue}>
          All
        </MenuItem>
        {filter.options.map((opt, index) => (
          <MenuItem key={index} value={opt.value}>
            {opt.label}
          </MenuItem>
        ))}
      </Select>
    </FormControl>
  )
}

interface SearchByValuesBoxProps {
  searchValue: string
  handleSearch: (e: React.ChangeEvent<HTMLInputElement>) => void
  searchByValues: string[]
  setSearchByValue: (value: string) => void
}

const SearchByValuesBox = ({
  searchValue,
  handleSearch,
  searchByValues,
  setSearchByValue,
}: SearchByValuesBoxProps) => {
  const [selectedSearchBy, setSelectedSearchBy] = useState<string>(
    searchByValues[0] ?? ''
  )
  const handleSelect = (e: SelectChangeEvent) => {
    setSelectedSearchBy(e.target.value)
    setSearchByValue(e.target.value)
  }
  return (
    <Stack direction="row">
      <Select
        fullWidth
        size="small"
        onChange={handleSelect}
        IconComponent={() => <KeyboardArrowDownRounded />}
        value={selectedSearchBy}
        sx={{
          width: '15vw',
          '.MuiInputBase-input.MuiOutlinedInput-input ': {
            py: '2px !important',
          },
          borderTopRightRadius: 0,
          borderBottomRightRadius: 0,
          borderRight: 'none',
        }}
      >
        {searchByValues.map((value) => (
          <MenuItem key={value} value={value}>
            <Chip label={`Search By ${value}`} />
          </MenuItem>
        ))}
      </Select>
      <CustomSearchInput
        value={searchValue}
        onChange={handleSearch}
        placeholder="Search"
        sx={{
          '& fieldset': {
            borderTopLeftRadius: 0,
            borderBottomLeftRadius: 0,
            borderLeft: 'none !important',
          },
        }}
        endAdornment={
          <InputAdornment position="start">
            <SearchOutlinedIcon sx={{ color: 'text.disabled' }} />
          </InputAdornment>
        }
      />
    </Stack>
  )
}
