import {
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableRow,
} from '@mui/material'
import {
  ICustomer,
  ICustomerAccount,
  ICustomerProfileAccount,
  IDevice,
} from '@/store/interfaces'
interface IEntityPreview {
  entity: ICustomer | ICustomerAccount | IDevice | ICustomerProfileAccount
  keys: string[]
}
export const EntityPreview = ({ entity, keys }: IEntityPreview) => {
  const getEntityValue = (
    entity: ICustomer | ICustomerAccount | IDevice | ICustomerProfileAccount,
    key: string
  ): any => {
    return (entity as any)[key]
  }
  return (
    <TableContainer
      component={Paper}
      elevation={0}
      sx={{ border: '1px solid #D0D5DD' }}
    >
      <Table>
        <TableBody>
          {entity &&
            keys.map((key) => (
              <TableRow key={key}>
                <TableCell>{key}</TableCell>
                <TableCell>{getEntityValue(entity, key)}</TableCell>
              </TableRow>
            ))}
        </TableBody>
      </Table>
    </TableContainer>
  )
}
