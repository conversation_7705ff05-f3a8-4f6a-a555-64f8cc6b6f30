import { CallMadeRounded, CloseRounded } from '@mui/icons-material'
import {
  <PERSON>ton,
  Dialog,
  DialogContent,
  Drawer,
  IconButton,
  MenuItem,
  Stack,
  TableCell,
  TableRow,
  TextField,
  Typography,
} from '@mui/material'
import React, { useState } from 'react'
import { sentenceCase } from 'tiny-case'
import { useAppDispatch, useAppSelector } from '@/store'
import {
  IApprovalRequest,
  ICustomer,
  ICustomerAccount,
  ICustomerProfileAccount,
  IDevice,
} from '@/store/interfaces'
import { useCustomRouter } from '@dtbx/ui/hooks'
import { RequestsApprovalIcon } from '@dtbx/ui/icons'
import {
  ACCESS_CONTROLS,
  AccessControlWrapper,
  formatTimestamp,
  handleDiff,
} from '@dtbx/store/utils'

import { ApprovalRequestRouting } from '@/app/approval-requests/RequestRouting'
import {
  getCustomerAccountByAccountNo,
  getCustomerDeviceDetail,
  getCustomerProfile,
} from '@/store/actions'
import { EntityPreview } from '@/app/approval-requests/Pending/Dialog/EntityPreview'
import { AllEntityPreview } from '@/app/approval-requests/All/Dialog/EntityPreview'
import { CheckerRequestsApiHandler } from '../../CheckerRequestsApiHandler'
import { setDrawer } from '@dtbx/store/reducers'

const ReviewRequest = ({
  request,
  closeParent,
}: {
  request: IApprovalRequest
  closeParent: () => void
}) => {
  const dispatch = useAppDispatch()
  const router = useCustomRouter()
  const [open, setOpen] = useState<boolean>(false)
  const [customer, setCustomer] = useState<ICustomer | null>()
  const [account, setAccount] = useState<
    ICustomerAccount[] | ICustomerAccount | null
  >()
  const [createAccount, setCreateAccount] = useState<
    ICustomerProfileAccount[] | null
  >()
  const [device, setDevice] = useState<IDevice | null>()
  const [commentsError, setCommentsError] = useState<boolean>(false)
  const [checkerComments, setCheckerComments] = useState<string>('')

  const handleSeeRequest = async () => {
    setOpen(!open)
    if (request.makerCheckerType.module === 'Customers') {
      if (!request.makerCheckerType.type.includes('CREATE')) {
        const res = await getCustomerProfile(request.entityId || '', dispatch)
        setCustomer(res)
      } else {
        const customerData = request.entity ? JSON.parse(request.entity) : {}
        setCustomer(customerData)
      }
    }
    if (request.makerCheckerType.module === 'Profiles') {
      const res = await getCustomerProfile(request.entityId || '', dispatch)
      setCustomer(res)
    }
    if (request.makerCheckerType.module === 'accounts') {
      if (request.makerCheckerType.type === 'CREATE_ACCOUNTS') {
        const accounts = request.entity
          ? JSON.parse(request.entity)?.accounts
          : {}
        setCreateAccount(accounts)
      } else {
        const profileId = request.entity
          ? JSON.parse(request.entity)?.profileId
          : ''
        const account = await getCustomerAccountByAccountNo(
          profileId,
          request?.entityId || '',
          dispatch
        )
        setAccount(account)
      }
    }
    if (request.makerCheckerType.module === 'ProfileDevices') {
      if (request.makerCheckerType.type === 'CREATE_PROFILEDEVICES') {
        const device = request.entity ? JSON.parse(request.entity) : {}
        setDevice(device)
      } else {
        const profileId = request.entity
          ? JSON.parse(request.entity)?.profileId
          : ''
        const device = await getCustomerDeviceDetail({
          profileID: profileId,
          deviceID: request?.entityId || '',
          dispatch,
        })
        setDevice(device)
      }
    }
  }

  const handleApprove = async () => {
    if (!checkerComments) {
      setCommentsError(true)
      return
    }
    await CheckerRequestsApiHandler(
      request,
      dispatch,
      router,
      `ACCEPT_${request.makerCheckerType.type}`,
      checkerComments
    )
    setOpen(false)
    setCheckerComments('')
    dispatch(
      setDrawer({
        open: false,
        drawerChildren: null,
        header: '',
      })
    )
  }

  const handleReject = async () => {
    if (!checkerComments) {
      setCommentsError(true)
      return
    }
    await CheckerRequestsApiHandler(
      request,
      dispatch,
      router,
      `REJECT_${request.makerCheckerType.type}`,
      checkerComments
    )
    setCheckerComments('')
    setOpen(false)
  }
  const handleClose = (e: object | {}, reason: string) => {
    if (reason && reason === 'backdropClick') return
    setOpen(false)
  }
  return (
    <>
      <MenuItem onClick={handleSeeRequest}>
        <Typography>See request summary</Typography>
      </MenuItem>
      <Drawer
        open={open}
        variant="persistent"
        hideBackdrop={false}
        sx={{
          '.MuiDrawer-paper': {
            width: '30%',
          },
        }}
        anchor={'right'}
        onClose={handleClose}
        PaperProps={{
          sx: {
            width: '30%',
          },
        }}
      >
        <Stack
          sx={{
            flexDirection: 'column',
            gap: '9px',
          }}
        >
          <Stack
            sx={{
              justifyContent: 'space-between',
              alignItems: 'center',
              flexDirection: 'row',
              background: '#F9FAFB',
              borderBottom: '2px solid  #F2F4F7',
            }}
          >
            <Typography
              variant="subtitle1"
              sx={{
                fontSize: '18px',
                px: '1vw',
              }}
            >
              Approval request details
            </Typography>
            <IconButton onClick={(e) => handleClose(e, 'close')}>
              <CloseRounded />
            </IconButton>
          </Stack>
          <Stack sx={{ px: '1vw', py: '1vh' }}>
            <Stack
              sx={{
                gap: '20px',
              }}
            >
              <TextField
                fullWidth
                label="Approval request type"
                sx={{}}
                value={
                  request?.makerCheckerType.type
                    ? sentenceCase(request?.makerCheckerType.type)
                    : ''
                }
              />
              {(() => {
                switch (request.makerCheckerType.module) {
                  case 'Customers':
                    return (
                      <Stack>
                        <Typography variant="body2">
                          Customer Details
                        </Typography>
                        <EntityPreview
                          entity={customer as ICustomer}
                          keys={[
                            'firstName',
                            'lastName',
                            'phoneNumber',
                            'email',
                          ]}
                        />
                      </Stack>
                    )

                  case 'Profiles':
                    return (
                      <Stack>
                        <Typography variant="body2">
                          Customer Details
                        </Typography>
                        <AllEntityPreview
                          entity={customer as ICustomer}
                          keys={[
                            'firstName',
                            'lastName',
                            'phoneNumber',
                            'email',
                          ]}
                        />
                      </Stack>
                    )
                  case 'accounts':
                    if (request.makerCheckerType.type === 'CREATE_ACCOUNTS') {
                      return (
                        <Stack>
                          {createAccount?.map(
                            (acc: ICustomerProfileAccount) => (
                              <Stack>
                                <Typography variant="body2">
                                  Account Details
                                </Typography>
                                <EntityPreview
                                  entity={acc}
                                  keys={[
                                    'accNumber',
                                    'tariffName',
                                    'accClassDesc',
                                  ]}
                                />
                              </Stack>
                            )
                          )}
                        </Stack>
                      )
                    } else {
                      return (
                        <Stack>
                          <Typography variant="body2">
                            Account Details
                          </Typography>
                          <EntityPreview
                            entity={account as ICustomerAccount}
                            keys={['fullName', 'accountNo', 'accountType']}
                          />
                        </Stack>
                      )
                    }
                  case 'ProfileDevices':
                    return (
                      <Stack>
                        <Typography variant="body2">Device Details</Typography>
                        <EntityPreview
                          entity={device as IDevice}
                          keys={[
                            'deviceId',
                            'phoneNumber',
                            'deviceModel',
                            'deviceName',
                            'deviceType',
                          ]}
                        />
                      </Stack>
                    )
                  default:
                    return ''
                }
              })()}
              <TextField
                fullWidth
                label="Changes made"
                sx={{
                  width: 'auto',
                }}
                multiline
                value={handleDiff(request.diff)}
              />
              <TextField
                fullWidth
                label="Maker"
                sx={{}}
                value={request && request.maker}
              />
              <TextField
                fullWidth
                label="Maker timestamp"
                sx={{}}
                value={request && formatTimestamp(request.dateCreated)}
              />
              <TextField
                fullWidth
                label="Maker comment"
                sx={{}}
                value={
                  request && request.makerComments
                    ? request.makerComments
                    : 'No comment'
                }
              />
              <TextField
                fullWidth
                label="Checker Comments"
                onChange={(e) => {
                  setCheckerComments(e.target.value)
                  e.target.value.length > 0
                    ? setCommentsError(false)
                    : setCommentsError(true)
                }}
                helperText={commentsError ? 'Please enter comments' : ''}
                error={commentsError}
                value={checkerComments}
                multiline
                rows={3}
              />
            </Stack>
            <Stack
              sx={{
                flexDirection: 'row',
                justifyContent: 'space-between',
                py: '2vh',
                gap: '10px',
              }}
            >
              <AccessControlWrapper
                rights={request?.makerCheckerType?.checkerPermissions?.filter(
                  (perm) => perm.includes('REJECT')
                )}
                makerId={request?.maker}
              >
                <Button
                  variant="outlined"
                  fullWidth
                  // disabled={checkerComments.length === 0}
                  sx={{
                    maxHeight: '34px',
                    background: '#E3E4E4',
                    border: '1px solid #AAADB0',
                    boxShadow: ' 0px 1px 2px 0px rgba(16, 24, 40, 0.05)',
                  }}
                  onClick={handleReject}
                >
                  Reject
                </Button>
              </AccessControlWrapper>

              <AccessControlWrapper
                rights={[
                  ...ACCESS_CONTROLS.ACCEPT_APPROVALREQUEST_CUSTOMERS,
                  ...ACCESS_CONTROLS.ACCEPT_APPROVALREQUEST_ROLES,
                  ...ACCESS_CONTROLS.ACCEPT_APPROVALREQUEST_ROLES,
                  ...ACCESS_CONTROLS.REJECT_APPROVALREQUEST_CUSTOMERS,
                  ...ACCESS_CONTROLS.REJECT_APPROVALREQUEST_ROLES,
                  ...ACCESS_CONTROLS.ACCEPT_APPROVALREQUEST_USERS,
                  ...ACCESS_CONTROLS.ACCEPT_APPROVALREQUEST_PROFILES,
                ]}
              >
                <Button
                  variant="contained"
                  sx={{
                    height: '34px',
                    textWrap: 'nowrap',
                  }}
                  fullWidth
                  onClick={handleApprove}
                  disabled={checkerComments.length === 0}
                >
                  Approve
                </Button>
              </AccessControlWrapper>
            </Stack>
          </Stack>
        </Stack>
      </Drawer>
    </>
  )
}

export default ReviewRequest
