import { CloseRounded } from '@mui/icons-material'
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  IconButton,
  MenuItem,
  Stack,
  TextField,
  Typography,
} from '@mui/material'
import React, { useEffect, useState } from 'react'
import { sentenceCase } from 'tiny-case'
import {
  IApprovalRequest,
  ICustomer,
  ICustomerAccount,
  ICustomerProfileAccount,
  IDevice,
} from '@/store/interfaces'
import { useAppDispatch } from '@/store'
import { useCustomRouter } from '@dtbx/ui/hooks'

import { ApprovalRequestRouting } from '@/app/approval-requests/RequestRouting'
import { formatTimestamp } from '@dtbx/store/utils'
import {
  getCustomerAccountByAccountNo,
  getCustomerDeviceDetail,
  getCustomerProfile,
} from '@/store/actions'
import { AllEntityPreview } from '@/app/approval-requests/All/Dialog/EntityPreview'
import { CheckerRequestsApiHandler } from '../../CheckerRequestsApiHandler'

const ResolvedRequestDetails = ({ request }: { request: IApprovalRequest }) => {
  const dispatch = useAppDispatch()
  const [open, setOpen] = useState<boolean>(false)
  const [customer, setCustomer] = useState<ICustomer | null>()
  const [account, setAccount] = useState<
    ICustomerAccount[] | ICustomerAccount | null
  >()
  const [createAccount, setCreateAccount] = useState<
    ICustomerProfileAccount[] | null
  >()
  const [device, setDevice] = useState<IDevice | null>()
  const [checkerComments, setCheckerComments] = useState<string>('')
  const [commentsError, setCommentsError] = useState<boolean>(false)

  const handleReject = async () => {
    if (!checkerComments) {
      setCommentsError(true)
      return
    }
    await CheckerRequestsApiHandler(
      request,
      dispatch,
      router,
      `REJECT_${request.makerCheckerType.type}`,
      checkerComments
    )
    setCheckerComments('')
  }
  const handleApprove = async () => {
    if (!checkerComments) {
      setCommentsError(true)
      return
    }
    await CheckerRequestsApiHandler(
      request,
      dispatch,
      router,
      `ACCEPT_${request.makerCheckerType.type}`,
      checkerComments
    )
    setOpen(false)
    setCheckerComments('')
    handleClose(null, 'close')
  }

  const handleClose = (
    e: React.MouseEvent<HTMLButtonElement> | null,
    action: string
  ) => {
    if (action !== 'backdropClick') {
      setOpen(false)
    }
  }
  const handleSeeRequest = async () => {
    setOpen(!open)
    if (request.makerCheckerType.module === 'Customers') {
      if (!request.makerCheckerType.type.includes('CREATE')) {
        const res = await getCustomerProfile(request.entityId || '', dispatch)
        setCustomer(res)
      } else {
        const customerData = request.entity ? JSON.parse(request.entity) : {}
        setCustomer(customerData)
      }
    }
    if (request.makerCheckerType.module === 'Profiles') {
      const res = await getCustomerProfile(request.entityId || '', dispatch)
      setCustomer(res)
    }
    if (request.makerCheckerType.module === 'accounts') {
      if (request.makerCheckerType.type === 'CREATE_ACCOUNTS') {
        const accounts = request.entity
          ? JSON.parse(request.entity)?.accounts
          : {}
        setCreateAccount(accounts)
      } else {
        const profileId = request.entity
          ? JSON.parse(request.entity)?.profileId
          : ''
        const account = await getCustomerAccountByAccountNo(
          profileId,
          request?.entityId || '',
          dispatch
        )
        setAccount(account)
      }
    }
    if (request.makerCheckerType.module === 'ProfileDevices') {
      if (request.makerCheckerType.type === 'CREATE_PROFILEDEVICES') {
        const device = request.entity ? JSON.parse(request.entity) : {}
        setDevice(device)
      } else {
        const profileId = request.entity
          ? JSON.parse(request.entity)?.profileId
          : ''
        const device = await getCustomerDeviceDetail({
          profileID: profileId,
          deviceID: request?.entityId || '',
          dispatch,
        })
        setDevice(device)
      }
    }
  }

  const router = useCustomRouter()

  const handleGoToModule = async () => {
    await ApprovalRequestRouting(request, dispatch, router)
    handleClose(null, 'close')
  }

  return (
    <>
      <MenuItem onClick={handleSeeRequest}>
        <Typography>See request summary</Typography>
      </MenuItem>
      <Drawer
        open={open}
        variant="persistent"
        sx={{
          '.MuiDrawer-paper': {
            width: '30%',
          },
        }}
        anchor={'right'}
        onClose={() => handleClose(null, 'close')}
        PaperProps={{
          sx: {
            width: '30%',
          },
        }}
      >
        <Stack
          sx={{
            justifyContent: 'space-between',
            alignItems: 'center',
            flexDirection: 'row',
            background: '#F9FAFB',
            borderBottom: '2px solid  #F2F4F7',
          }}
        >
          <Typography
            variant="subtitle1"
            sx={{
              fontSize: '18px',
              px: '1vw',
            }}
          >
            Approval request details
          </Typography>
          <IconButton onClick={() => handleClose(null, 'close')}>
            <CloseRounded />
          </IconButton>
        </Stack>
        <Stack sx={{ px: '1vw', py: '2vh' }}>
          <Stack
            sx={{
              flexDirection: 'column',
              gap: '20px',
            }}
          >
            <TextField
              fullWidth
              label="Approval request type"
              sx={{}}
              value={
                request?.makerCheckerType.type
                  ? sentenceCase(request?.makerCheckerType.type)
                  : 'No type'
              }
            />
            <TextField
              fullWidth
              label="Module"
              sx={{
                width: 'auto',
              }}
              value={request?.makerCheckerType.module}
            />
            {(() => {
              switch (request.makerCheckerType.module) {
                case 'Customers':
                  return (
                    <Stack>
                      <Typography variant="body2">Customer Details</Typography>
                      <AllEntityPreview
                        entity={customer as ICustomer}
                        keys={['firstName', 'lastName', 'phoneNumber', 'email']}
                      />
                    </Stack>
                  )
                case 'Profiles':
                  return (
                    <Stack>
                      <Typography variant="body2">Customer Details</Typography>
                      <AllEntityPreview
                        entity={customer as ICustomer}
                        keys={['firstName', 'lastName', 'phoneNumber', 'email']}
                      />
                    </Stack>
                  )
                case 'accounts':
                  if (request.makerCheckerType.type === 'CREATE_ACCOUNTS') {
                    return (
                      <Stack>
                        {createAccount?.map((acc: ICustomerProfileAccount) => (
                          <Stack>
                            <Typography variant="body2">
                              Account Details
                            </Typography>
                            <AllEntityPreview
                              entity={acc}
                              keys={['accNumber', 'tariffName', 'accClassDesc']}
                            />
                          </Stack>
                        ))}
                      </Stack>
                    )
                  } else {
                    return (
                      <Stack>
                        <Typography variant="body2">Account Details</Typography>
                        <AllEntityPreview
                          entity={account as ICustomerAccount}
                          keys={['fullName', 'accountNo', 'accountType']}
                        />
                      </Stack>
                    )
                  }
                case 'ProfileDevices':
                  return (
                    <Stack>
                      <Typography variant="body2">Device Details</Typography>
                      <AllEntityPreview
                        entity={device as IDevice}
                        keys={[
                          'deviceId',
                          'phoneNumber',
                          'deviceModel',
                          'deviceName',
                          'deviceType',
                        ]}
                      />
                    </Stack>
                  )
                default:
                  return ''
              }
            })()}

            <TextField fullWidth label="Maker" sx={{}} value={request?.maker} />
            <TextField
              fullWidth
              label="Maker timestamp"
              sx={{}}
              value={formatTimestamp(request?.dateCreated)}
            />
            <TextField
              fullWidth
              label="Maker comment"
              sx={{}}
              value={request?.makerComments || 'No comment'}
            />
            {request.status !== 'PENDING' && (
              <TextField
                fullWidth
                label="Checker"
                sx={{}}
                value={request?.checker || 'No checker'}
              />
            )}
            <TextField
              fullWidth
              label="Checker timestamp"
              sx={{}}
              value={formatTimestamp(request?.dateModified)}
            />
            <TextField
              fullWidth
              label="Checker comment"
              onChange={
                request.status === 'PENDING'
                  ? (e) => {
                      setCheckerComments(e.target.value)
                      e.target.value.length > 0
                        ? setCommentsError(false)
                        : setCommentsError(true)
                    }
                  : undefined
              }
              sx={{}}
              value={
                request.status === 'PENDING'
                  ? checkerComments
                  : request?.checkerComments || 'No comment'
              }
            />
          </Stack>
          <Stack
            sx={{
              flexDirection: 'row',
              justifyContent: 'space-between',
              py: '2vh',
            }}
          >
            {request.status === 'PENDING' ? (
              <>
                <Button
                  variant="outlined"
                  sx={{
                    height: '34px',
                    flex: 1,
                  }}
                  onClick={handleReject}
                  disabled={!checkerComments}
                >
                  Reject
                </Button>
                <Button
                  variant="contained"
                  sx={{
                    height: '34px',
                    flex: 1,
                    textWrap: 'nowrap',
                  }}
                  onClick={handleApprove}
                  // disabled={!checkerComments}
                >
                  Approve
                </Button>
              </>
            ) : (
              <>
                <Button
                  variant="outlined"
                  sx={{
                    height: '34px',
                    flex: 1,
                  }}
                  onClick={() => handleClose(null, 'close')}
                >
                  Back
                </Button>
                <Button
                  variant="contained"
                  sx={{
                    height: '34px',
                    flex: 1,
                    textWrap: 'nowrap',
                  }}
                  onClick={handleGoToModule}
                >
                  Go to module
                </Button>
              </>
            )}
          </Stack>
        </Stack>
      </Drawer>
    </>
  )
}

export default ResolvedRequestDetails
