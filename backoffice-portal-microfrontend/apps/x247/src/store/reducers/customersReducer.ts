import { createSlice, PayloadAction } from '@reduxjs/toolkit'

import {
  ICustomer,
  ICustomerAccount,
  ICustomerAccountDetails,
  ICustomerAccountHistoryLogs,
  ICustomerPinDetails,
  ICustomerPinLogResponse,
  ICustomerProfileAccount,
  ICustomersDataResponse,
  IDevice,
  IDevicesResponse,
  ILogs,
  INotificationEvents,
  INotificationFrequencies,
  INotificationEventsPerAccount,
  IApprovalRequest,
  IApprovalRequestsResponse,
} from '../interfaces'

import { IFilter } from '@dtbx/store/interfaces'

interface ISetCustomerSearchPayload {
  searchBy: string[]
  searchValue: string
}

interface ITariff {
  name: string
}

type SetCustomerSearchAction = PayloadAction<ISetCustomerSearchPayload>

interface initialStateProps {
  customersResponse: ICustomersDataResponse
  customer: ICustomer
  customerIdToView: string
  tariffs: ITariff[]
  tariffsLoading: boolean
  devicesResponse: IDevicesResponse
  isLoadingDevices: boolean
  isSuccessDevices: boolean
  isFailureDevices: boolean
  device: IDevice
  isLoadingDevice: boolean
  isSuccessDevice: boolean
  isFailureDevice: boolean

  // device logs
  deviceLogs: ILogs[]
  IsLoadingDeviceLogs: boolean
  IsSuccessfulDeviceLogs: boolean

  isLoadingSecurity: boolean
  isSuccessSecurity: boolean
  isFailureSecurity: boolean
  tabStateValue: number
  openDeactivateDevice: boolean
  openDevice: boolean
  deviceSearchParams: {
    searchBy: Array<keyof IDevice>
    searchValue: string
  }
  deviceFilters: {
    filterBy: Array<keyof IDevice>
    filterValue: string
  }
  isEditCustomerSuccess: boolean
  isEditCustomerLoading: boolean
  isCustomerSearchSuccess: boolean
  isCustomerSearchLoading: boolean
  isCustomersLoading: boolean
  isCustomersSuccess: boolean
  isCustomerLoading: boolean
  isCustomerSuccess: boolean
  isCustomerFailure: boolean
  isCustomersFailure: boolean
  openRejectCustomerModal: boolean
  openCancelSaveCustomerModal: boolean
  openViewRequestsModal: boolean
  selectedCustomer: {
    openDetails: boolean
    isPendingAction: boolean
    customer: ICustomer | null
  }
  documentViewer: {
    open: boolean
    imageUrl: string
  }
  changeLogs: object[]
  openChangeLogsDrawer: boolean
  selectedFilters: IFilter[]
  approvalRequestFilters: IFilter[]
  search: {
    searchBy: string[]
    searchValue: string
  }
  approvalRequestSearch: {
    searchBy: string[]
    searchValue: string
  }
  customerAccountSearch: {
    searchValue: string
  }

  //accounts
  isLoadingAccounts: boolean
  customerAccounts: ICustomerAccount[]
  isViewAccountOpen: boolean
  selectedAccount: ICustomerAccount
  customerLinkedAccountsList: ICustomerProfileAccount[]
  customerUnlinkedAccountsList: ICustomerAccount[]
  customerAccountsList: ICustomerAccount[]
  isLoadingUnlinkedAccounts: boolean
  isLoadingLinkAccounts: boolean
  customerAccountDetails: ICustomerAccountDetails
  customerProfileExists: boolean
  selectedCustomerSummaryAccount: ICustomerAccount
  openCustomerTab: number
  customerProfileAccount: ICustomerProfileAccount
  customerAccountExists: boolean
  accountsFilters: []
  selectedAccountsToLink: ICustomerAccount[]
  //Restriction of accounts
  restrictAccountLoading: boolean
  restrictAccountSuccess: boolean
  restrictAccountFailure: boolean
  accountLogs: ICustomerAccountHistoryLogs[]
  isLoadingAccountsLogs: boolean
  isSuccessfulAccountsLogs: boolean

  // pin
  customerPinDetails: ICustomerPinDetails[]
  customerPinLogResponse: ICustomerPinLogResponse
  customerPinLogsBackOffice: IApprovalRequestsResponse
  isLoadingCustomerPinHistory: boolean
  isCustomerApprovalBarOpen: boolean
  accountLogsBackOffice: IApprovalRequest[]
  isLoadingAccountsLogsBackOffice: boolean
  isSuccessfulAccountsLogsBackOffice: boolean

  //account relinking
  isRelinkingSuccessfull: boolean
  isRellinkingLoading: boolean
  isRelinkingApprovalSuccessfull: boolean
  isRelinkingApprovalLoading: boolean
  isRelinkingRejectionSuccessfull: boolean
  isRelinkingRejectionLoading: boolean
  relinkedAccount: ICustomerAccount

  //Activate customer profile
  isActivationLoading: boolean
  isActivationSuccess: boolean
  isActivationFailure: boolean

  //account unlinking
  isUnlinkingSuccessfull: boolean
  isUnlinkingLoading: boolean
  isUnlinkingSuccessfullSuper: boolean
  isUnlinkingLoadingSuper: boolean
  isUnlinkingApprovalSuccessfull: boolean
  isUnlinkingApprovalLoading: boolean
  isUnlinkingRejectionSuccessfull: boolean
  isUnlinkingRejectionLoading: boolean

  //account preferences
  notificationEvents: INotificationEvents[]
  notificationFrequencies: INotificationFrequencies[]
  accountNotificationPreferences: INotificationEventsPerAccount[]
  accountNotificationLogs: IApprovalRequest[]
  accountSubscriptionLogs: IApprovalRequest[]
}

const initialState: initialStateProps = {
  customersResponse: {} as ICustomersDataResponse,
  customerIdToView: '',
  devicesResponse: {} as IDevicesResponse,
  isLoadingDevices: false,
  isFailureDevices: false,
  isSuccessDevices: false,
  isLoadingSecurity: false,
  isSuccessSecurity: false,
  isFailureSecurity: false,
  tariffs: [] as ITariff[],
  tariffsLoading: false,

  // device logs
  deviceLogs: [],
  IsLoadingDeviceLogs: false,
  IsSuccessfulDeviceLogs: false,

  deviceSearchParams: {
    searchBy: ['deviceType'],
    searchValue: '',
  },
  deviceFilters: {
    filterBy: ['deviceStatus'],
    filterValue: '',
  },
  isEditCustomerSuccess: false,
  isEditCustomerLoading: false,
  isCustomerSearchSuccess: false,
  isCustomerSearchLoading: false,
  isCustomersLoading: false,
  isCustomersSuccess: false,
  isCustomerLoading: false,
  isCustomerSuccess: false,
  isCustomerFailure: false,
  isCustomersFailure: false,
  tabStateValue: 0,
  openDeactivateDevice: false,
  openDevice: false,
  customer: {} as ICustomer,
  selectedCustomer: {
    openDetails: false,
    isPendingAction: false,
    customer: null,
  },
  documentViewer: {
    open: false,
    imageUrl: '',
  },
  changeLogs: [],
  openChangeLogsDrawer: false,
  openRejectCustomerModal: false,
  openCancelSaveCustomerModal: false,
  openViewRequestsModal: false,
  selectedFilters: [],
  approvalRequestFilters: [],
  search: {
    searchBy: ['first_name'],
    searchValue: '',
  },
  approvalRequestSearch: {
    searchBy: ['status'],
    searchValue: '',
  },
  customerAccountSearch: {
    searchValue: '',
  },
  device: {} as IDevice,
  isLoadingDevice: false,
  isSuccessDevice: false,
  isFailureDevice: false,

  isLoadingAccounts: false,
  customerAccounts: [],
  isViewAccountOpen: false,
  selectedAccount: {} as ICustomerAccount,
  customerLinkedAccountsList: [],
  customerUnlinkedAccountsList: [] as ICustomerAccount[],
  customerAccountsList: [] as ICustomerAccount[],
  isLoadingUnlinkedAccounts: false,
  isLoadingLinkAccounts: false,
  customerAccountDetails: {} as ICustomerAccountDetails,
  customerProfileExists: false,
  selectedCustomerSummaryAccount: {} as ICustomerAccount,
  openCustomerTab: 0,
  customerProfileAccount: {} as ICustomerProfileAccount,
  customerAccountExists: false,

  restrictAccountLoading: false,
  restrictAccountSuccess: false,
  restrictAccountFailure: false,
  accountLogs: [] as ICustomerAccountHistoryLogs[],
  accountLogsBackOffice: [] as IApprovalRequest[],
  isLoadingAccountsLogs: false,
  isSuccessfulAccountsLogs: false,
  isCustomerApprovalBarOpen: false,
  accountsFilters: [],
  selectedAccountsToLink: [],

  // Pin
  customerPinDetails: {} as ICustomerPinDetails[],
  customerPinLogResponse: {} as ICustomerPinLogResponse,
  customerPinLogsBackOffice: {} as IApprovalRequestsResponse,
  isLoadingCustomerPinHistory: false,

  isLoadingAccountsLogsBackOffice: false,
  isSuccessfulAccountsLogsBackOffice: false,

  //account relinking
  isRelinkingSuccessfull: false,
  isRellinkingLoading: false,
  isRelinkingApprovalSuccessfull: false,
  isRelinkingApprovalLoading: false,
  isRelinkingRejectionSuccessfull: false,
  isRelinkingRejectionLoading: false,
  relinkedAccount: {} as ICustomerAccount,

  //account unlinking
  isUnlinkingSuccessfull: false,
  isUnlinkingLoading: false,
  isUnlinkingSuccessfullSuper: false,
  isUnlinkingLoadingSuper: false,
  isUnlinkingApprovalSuccessfull: false,
  isUnlinkingApprovalLoading: false,
  isUnlinkingRejectionSuccessfull: false,
  isUnlinkingRejectionLoading: false,
  isActivationLoading: false,
  isActivationSuccess: false,
  isActivationFailure: false,

  // account preferences
  notificationEvents: [] as INotificationEvents[],
  notificationFrequencies: [] as INotificationFrequencies[],
  accountNotificationPreferences: [] as INotificationEventsPerAccount[],
  accountNotificationLogs: [] as IApprovalRequest[],
  accountSubscriptionLogs: [] as IApprovalRequest[],
}

const customersSlice = createSlice({
  name: 'customers',
  initialState,
  reducers: {
    setCustomers: (state, action: PayloadAction<ICustomersDataResponse>) => {
      state.customersResponse = action.payload
    },
    setCustomer: (state, action: PayloadAction<ICustomer>) => {
      state.customer = action.payload
    },
    setCustomerIdToView: (state, action: PayloadAction<string>) => {
      state.customerIdToView = action.payload
    },
    setDevices: (state, action: PayloadAction<IDevicesResponse>) => {
      state.devicesResponse = action.payload
    },
    setDevicesLoading: (state, action: PayloadAction<boolean>) => {
      state.isLoadingDevices = action.payload
    },

    setDevicesSuccess: (state, action: PayloadAction<boolean>) => {
      state.isSuccessDevices = action.payload
    },

    setDevicesFailure: (state, action: PayloadAction<boolean>) => {
      state.isFailureDevices = action.payload
    },
    setDevice: (state, action: PayloadAction<IDevice>) => {
      state.device = action.payload
    },
    setDeviceLoading: (state, action: PayloadAction<boolean>) => {
      state.isLoadingDevice = action.payload
    },
    setDeviceSuccess: (state, action: PayloadAction<boolean>) => {
      state.isSuccessDevice = action.payload
    },
    setDeviceFailure: (state, action: PayloadAction<boolean>) => {
      state.isFailureDevice = action.payload
    },
    setSearchDeviceParams: (
      state,
      action: PayloadAction<{
        searchBy: Array<keyof IDevice>
        searchValue: string
      }>
    ) => {
      state.deviceSearchParams = action.payload
    },
    setDeviceFilters: (
      state,
      action: PayloadAction<{
        filterBy: Array<keyof IDevice>
        filterValue: string
      }>
    ) => {
      state.deviceFilters = action.payload
    },
    setAccountsFilters: (state, action: PayloadAction<[]>) => {
      state.accountsFilters = action.payload
    },
    setSecurityLoading: (state, action: PayloadAction<boolean>) => {
      state.isLoadingSecurity = action.payload
    },
    setSecuritySuccess: (state, action: PayloadAction<boolean>) => {
      state.isSuccessSecurity = action.payload
    },
    setSecurityFailure: (state, action: PayloadAction<boolean>) => {
      state.isFailureSecurity = action.payload
    },
    setCustomerLoading: (state, action: PayloadAction<boolean>) => {
      state.isCustomerLoading = action.payload
    },
    setCustomerSuccess: (state, action: PayloadAction<boolean>) => {
      state.isCustomerSuccess = action.payload
    },
    setCustomerFailure: (state, action: PayloadAction<boolean>) => {
      state.isCustomerFailure = action.payload
    },
    setCustomersLoading: (state, action: PayloadAction<boolean>) => {
      state.isCustomersLoading = action.payload
    },
    setCustomersSuccess: (state, action: PayloadAction<boolean>) => {
      state.isCustomersSuccess = action.payload
    },
    setCustomersFailure: (state, action: PayloadAction<boolean>) => {
      state.isCustomersFailure = action.payload
    },
    setSelectedCustomer: (
      state,
      action: PayloadAction<{
        openDetails: boolean
        isPendingAction: boolean
        customer: ICustomer | null
      }>
    ) => {
      state.selectedCustomer = action.payload
    },
    setOpenChangeLogsDrawer: (state, action) => {
      state.openChangeLogsDrawer = action.payload
    },
    setChangesLogs: (state, action) => {
      state.changeLogs = action.payload
    },
    clearCustomerState: () => initialState,
    setDocumentViewer: (state, action) => {
      state.documentViewer = action.payload
    },
    setOpenViewRequestsModal: (state, action) => {
      state.openViewRequestsModal = action.payload
    },
    setOpenCancelSaveCustomerModal: (state, action) => {
      state.openCancelSaveCustomerModal = action.payload
    },
    setOpenRejectCustomerModal: (state, action) => {
      state.openRejectCustomerModal = action.payload
    },
    setSelectedFilters: (state, action: PayloadAction<IFilter[]>) => {
      state.selectedFilters = action.payload
    },
    setCustomerSearch: (state, action: SetCustomerSearchAction) => {
      state.search = action.payload
    },
    setApprovalRequestFilter: (state, action) => {
      state.approvalRequestFilters = action.payload
    },
    setCustomerApprovalRequestSearch: (
      state,
      action: SetCustomerSearchAction
    ) => {
      state.approvalRequestSearch = action.payload
    },
    setIsLoadingAccounts: (state, action: PayloadAction<boolean>) => {
      state.isLoadingAccounts = action.payload
    },
    setCustomerAccounts: (state, action: PayloadAction<ICustomerAccount[]>) => {
      state.customerAccounts = action.payload
    },
    setIsViewAccountOpen: (state, action: PayloadAction<boolean>) => {
      state.isViewAccountOpen = action.payload
    },
    setSelectedAccount: (state, action: PayloadAction<ICustomerAccount>) => {
      state.selectedAccount = action.payload
    },
    setCustomerAccountDetails: (
      state,
      action: PayloadAction<ICustomerAccountDetails>
    ) => {
      state.customerAccountDetails = action.payload
    },
    setCustomerAccountSearch: (state, action) => {
      state.customerAccountSearch = action.payload
    },
    setCustomerProfileExists: (state, action) => {
      state.customerProfileExists = action.payload
    },
    setSelectedCustomerSummaryAccount: (state, action) => {
      state.selectedCustomerSummaryAccount = action.payload
    },
    setCustomerLinkedAccountsList: (
      state,
      action: PayloadAction<ICustomerProfileAccount[]>
    ) => {
      state.customerLinkedAccountsList = action.payload
    },
    setCustomerUnlinkedAccountsList: (
      state,
      action: PayloadAction<ICustomerAccount[]>
    ) => {
      state.customerUnlinkedAccountsList = action.payload
    },
    setCustomerAccountsList: (
      state,
      action: PayloadAction<ICustomerAccount[]>
    ) => {
      state.customerAccountsList = action.payload
    },
    setIsLoadingUnlinkedAccounts: (state, action: PayloadAction<boolean>) => {
      state.isLoadingUnlinkedAccounts = action.payload
    },
    setIsLoadingLinkAccounts: (state, action: PayloadAction<boolean>) => {
      state.isLoadingLinkAccounts = action.payload
    },
    setChangeTab: (state, action: PayloadAction<number>) => {
      state.tabStateValue = action.payload
    },
    setOpenDevice: (state, action: PayloadAction<boolean>) => {
      state.openDevice = action.payload
    },
    setOpenDeactivateDevice: (state, action: PayloadAction<boolean>) => {
      state.openDeactivateDevice = action.payload
    },
    setCustomerProfileAccount: (
      state,
      action: PayloadAction<ICustomerProfileAccount>
    ) => {
      state.customerProfileAccount = action.payload
    },
    setCustomerAccountExists: (state, action: PayloadAction<boolean>) => {
      state.customerAccountExists = action.payload
    },
    setAccountLogs: (
      state,
      action: PayloadAction<Array<ICustomerAccountHistoryLogs>>
    ) => {
      state.accountLogs = action.payload
    },
    setIsLoadingAccountsLogs: (state, action: PayloadAction<boolean>) => {
      state.isLoadingAccountsLogs = action.payload
    },
    setIsSuccessfulAccountsLogs: (state, action: PayloadAction<boolean>) => {
      state.isSuccessfulAccountsLogs = action.payload
    },
    setCustomerApprovalBarOpen: (state, action: PayloadAction<boolean>) => {
      state.isCustomerApprovalBarOpen = action.payload
    },

    // Customer Pin
    setCustomerPinDetails: (
      state,
      action: PayloadAction<ICustomerPinDetails[]>
    ) => {
      state.customerPinDetails = action.payload
    },

    setCustomerPinLogs: (
      state,
      action: PayloadAction<ICustomerPinLogResponse>
    ) => {
      state.customerPinLogResponse = action.payload
    },

    setCustomerPinLogsBackoffice: (
      state,
      action: PayloadAction<IApprovalRequestsResponse>
    ) => {
      state.customerPinLogsBackOffice = action.payload
    },

    setIsLoadingAccountsLogsBackOffice: (
      state,
      action: PayloadAction<boolean>
    ) => {
      state.isLoadingAccountsLogsBackOffice = action.payload
    },
    setIsSuccessfulAccountsLogsBackOffice: (
      state,
      action: PayloadAction<boolean>
    ) => {
      state.isSuccessfulAccountsLogsBackOffice = action.payload
    },
    setAccountLogsBackOffice: (
      state,
      action: PayloadAction<IApprovalRequest[]>
    ) => {
      state.accountLogsBackOffice = action.payload
    },

    setIsLoadingCustomerPinHistory: (state, action: PayloadAction<boolean>) => {
      state.isLoadingCustomerPinHistory = action.payload
    },

    //relinking

    setIsRelinkingSuccessfull: (state, action: PayloadAction<boolean>) => {
      state.isRelinkingSuccessfull = action.payload
    },
    setIsRelinkingLoading: (state, action: PayloadAction<boolean>) => {
      state.isRellinkingLoading = action.payload
    },
    setIsRelinkingApprovalSuccessfull: (
      state,
      action: PayloadAction<boolean>
    ) => {
      state.isRelinkingApprovalSuccessfull = action.payload
    },
    setIsRelinkingApprovalLoading: (state, action: PayloadAction<boolean>) => {
      state.isRelinkingApprovalLoading = action.payload
    },
    setIsRelinkingRejectionSuccessfull: (
      state,
      action: PayloadAction<boolean>
    ) => {
      state.isRelinkingRejectionSuccessfull = action.payload
    },
    setIsRelinkingRejectionLoading: (state, action: PayloadAction<boolean>) => {
      state.isRelinkingRejectionLoading = action.payload
    },
    setRelinkedAccount: (state, action: PayloadAction<ICustomerAccount>) => {
      state.relinkedAccount = action.payload
    },
    setRestrictAccountLoading: (state, action: PayloadAction<boolean>) => {
      state.restrictAccountLoading = action.payload
    },
    setRestrictAccountSuccess: (state, action: PayloadAction<boolean>) => {
      state.restrictAccountSuccess = action.payload
    },
    setRestrictAccountFailure: (state, action: PayloadAction<boolean>) => {
      state.restrictAccountFailure = action.payload
    },
    setSelectedAccountsToLink: (
      state,
      action: PayloadAction<ICustomerAccount[]>
    ) => {
      state.selectedAccountsToLink = action.payload
    },
    //Activate customer profile
    setActivationLoading: (state, action: PayloadAction<boolean>) => {
      state.isActivationLoading = action.payload
    },
    setActivationSuccess: (state, action: PayloadAction<boolean>) => {
      state.isActivationSuccess = action.payload
    },
    setActivationFailure: (state, action: PayloadAction<boolean>) => {
      state.isActivationFailure = action.payload
    },
    // device logs
    setDeviceLogs: (state, action: PayloadAction<ILogs[]>) => {
      state.deviceLogs = action.payload
    },
    setIsLoadingDeviceLogs: (state, action: PayloadAction<boolean>) => {
      state.IsLoadingDeviceLogs = action.payload
    },
    setIsSuccessfulDeviceLogs: (state, action: PayloadAction<boolean>) => {
      state.IsSuccessfulDeviceLogs = action.payload
    },

    // account unlinking

    setIsUnlinkingSuccessfull: (state, action: PayloadAction<boolean>) => {
      state.isUnlinkingSuccessfull = action.payload
    },
    setIsUnlinkingLoading: (state, action: PayloadAction<boolean>) => {
      state.isUnlinkingLoading = action.payload
    },
    setIsUnlinkingSuccessfullSuper: (state, action: PayloadAction<boolean>) => {
      state.isUnlinkingSuccessfullSuper = action.payload
    },
    setIsUnlinkingLoadingSuper: (state, action: PayloadAction<boolean>) => {
      state.isUnlinkingLoadingSuper = action.payload
    },
    setIsUnlinkingApprovalSuccessfull: (
      state,
      action: PayloadAction<boolean>
    ) => {
      state.isUnlinkingApprovalSuccessfull = action.payload
    },
    setIsUnlinkingApprovalLoading: (state, action: PayloadAction<boolean>) => {
      state.isUnlinkingApprovalLoading = action.payload
    },
    setIsUnlinkingRejectionSuccessfull: (
      state,
      action: PayloadAction<boolean>
    ) => {
      state.isUnlinkingRejectionSuccessfull = action.payload
    },
    setIsUnlinkingRejectionLoading: (state, action: PayloadAction<boolean>) => {
      state.isUnlinkingRejectionLoading = action.payload
    },
    setAccountTariffs: (state, action: PayloadAction<ITariff[]>) => {
      state.tariffs = action.payload
    },
    setAccountTariffsLoading: (state, action: PayloadAction<boolean>) => {
      state.tariffsLoading = action.payload
    },
    setNotificationEvents: (
      state,
      action: PayloadAction<INotificationEvents[]>
    ) => {
      state.notificationEvents = action.payload
    },
    setNotificationFrequencies: (
      state,
      action: PayloadAction<INotificationFrequencies[]>
    ) => {
      state.notificationFrequencies = action.payload
    },
    setAccountNotificationPreferences: (
      state,
      action: PayloadAction<INotificationEventsPerAccount[]>
    ) => {
      state.accountNotificationPreferences = action.payload
    },
    setAccountNotificationLogs: (
      state,
      action: PayloadAction<IApprovalRequest[]>
    ) => {
      state.accountNotificationLogs = action.payload
    },
    setAccountSubscriptionLogs: (
      state,
      action: PayloadAction<IApprovalRequest[]>
    ) => {
      state.accountSubscriptionLogs = action.payload
    },
  },
})
export const {
  setCustomer,
  setCustomerIdToView,
  setCustomers,
  setSelectedCustomer,
  clearCustomerState,
  setOpenChangeLogsDrawer,
  setChangesLogs,
  setDocumentViewer,
  setOpenViewRequestsModal,
  setOpenCancelSaveCustomerModal,
  setOpenRejectCustomerModal,
  setSelectedFilters,
  setCustomerSearch,
  setApprovalRequestFilter,
  setCustomerApprovalRequestSearch,
  setCustomerLoading,
  setCustomerSuccess,
  setCustomerFailure,
  setCustomersLoading,
  setCustomersSuccess,
  setCustomersFailure,
  setDevices,
  setDevicesFailure,
  setDevicesLoading,
  setDevicesSuccess,
  setSearchDeviceParams,
  setDevice,
  setDeviceFailure,
  setDeviceLoading,
  setDeviceSuccess,
  setDeviceFilters,

  setIsLoadingAccounts,
  setIsViewAccountOpen,
  setCustomerAccounts,
  setSelectedAccount,
  setSelectedAccountsToLink,
  setCustomerAccountDetails,
  setCustomerAccountSearch,
  setCustomerProfileExists,
  setSelectedCustomerSummaryAccount,
  setCustomerLinkedAccountsList,
  setCustomerUnlinkedAccountsList,
  setCustomerAccountsList,
  setChangeTab,
  setOpenDevice,
  setOpenDeactivateDevice,
  setIsLoadingUnlinkedAccounts,
  setCustomerProfileAccount,
  setCustomerAccountExists,
  setIsLoadingLinkAccounts,
  setRestrictAccountLoading,
  setRestrictAccountSuccess,
  setRestrictAccountFailure,
  setAccountLogs,
  setIsLoadingAccountsLogs,
  setIsSuccessfulAccountsLogs,
  setSecurityLoading,
  setSecuritySuccess,
  setSecurityFailure,
  setCustomerApprovalBarOpen,

  //relinking
  setIsRelinkingSuccessfull,
  setIsRelinkingLoading,
  setIsRelinkingApprovalSuccessfull,
  setIsRelinkingApprovalLoading,
  setIsRelinkingRejectionSuccessfull,
  setIsRelinkingRejectionLoading,
  setRelinkedAccount,
  //logs
  setAccountLogsBackOffice,
  setIsLoadingAccountsLogsBackOffice,
  setIsSuccessfulAccountsLogsBackOffice,
  //Activate customer profile
  setActivationLoading,
  setActivationSuccess,
  setActivationFailure,

  // pin logs
  setCustomerPinDetails,
  setCustomerPinLogs,
  setCustomerPinLogsBackoffice,
  setIsLoadingCustomerPinHistory,

  // device logs
  setDeviceLogs,
  setIsLoadingDeviceLogs,
  setIsSuccessfulDeviceLogs,

  // account unlinking
  setIsUnlinkingSuccessfull,
  setIsUnlinkingLoading,
  setIsUnlinkingSuccessfullSuper,
  setIsUnlinkingLoadingSuper,
  setIsUnlinkingApprovalSuccessfull,
  setIsUnlinkingApprovalLoading,
  setIsUnlinkingRejectionSuccessfull,
  setIsUnlinkingRejectionLoading,

  //Account Tariffs
  setAccountTariffs,
  setAccountTariffsLoading,

  //Notification preferences
  setNotificationEvents,
  setNotificationFrequencies,
  setAccountNotificationPreferences,
  setAccountNotificationLogs,
  setAccountSubscriptionLogs,
} = customersSlice.actions
export default customersSlice.reducer
