import { createSlice, PayloadAction } from '@reduxjs/toolkit'
import {
  AccordionItem,
  AccordionState,
  // ConfigurationResponse,
  IConfigurationLogs,
  IConfiguredService,
  ITariffCreate,
  ITariffData,
  TariffDataResponse,
  IConfigurationRow,
  IConfigurationItem,
  Service,
} from '../interfaces/chargeConfiguration'

export type Status = 'Active' | 'Inactive' | 'Pending Approval'

export interface ISelectedConfigurableService {
  tariffName: string
  currency: string
}

export interface ICharges {
  charge: number
  exciseDuty: number
}
export interface ILimit {
  type: string
  value: number
}
export interface ITierCharge {
  minimum: number
  maximum: number
  feeAmount: number
}

export interface ServiceConfig {
  charges: ICharges[]
  limits: ILimit[]
  tiers?: Array<{
    minimum: number
    maximum: number
    feeAmount: number
  }>
}

// export interface ChargeConfigurationState {
//   editingConfigs: Record<string, { charges: ICharges[]; limits: ILimit[] }>
// }

export interface chargeConfigurationProps {
  //Tariffs
  tariffs: ITariffData[]
  selectedTariff: ITariffCreate
  tariffsSummary: TariffDataResponse
  isLoading: boolean
  isTariffError: boolean

  //Configurations
  selectedConfiguration: IConfigurationItem | null
  configuredServices: IConfiguredService[]
  isEditable: boolean
  selectedConfigurationLog: IConfigurationLogs
  openChangeLogsDrawer: boolean
  tariffConfig: IConfigurationRow
  selectedConfigurable: ISelectedConfigurableService
  configurableServices: Service[]
  selectedTariffState: ISelectedTariffState
  editingConfigs: Record<
    string,
    {
      charges: ICharges[]
      limits: ILimit[]
      item: AccordionItem
      tiers?: ITierCharge[]
    }
  >

  //Accordion State
  accordionItems: AccordionState
  notificationStep: string
}

export interface ISelectedTariffState {
  name: string
  currency: string
}

const initialState: chargeConfigurationProps = {
  //Tariffs
  tariffs: [],
  selectedTariff: {} as ITariffCreate,
  tariffsSummary: {} as TariffDataResponse,
  isLoading: false,
  isTariffError: false,

  //Configurations
  selectedConfiguration: {} as IConfigurationItem,
  configuredServices: [] as IConfiguredService[],
  selectedConfigurationLog: {} as IConfigurationLogs,
  isEditable: false,
  openChangeLogsDrawer: false,
  tariffConfig: {} as IConfigurationRow,
  selectedConfigurable: {} as ISelectedConfigurableService,
  configurableServices: [] as Service[],
  selectedTariffState: {} as ISelectedTariffState,
  editingConfigs: {},

  //Accordion State
  accordionItems: {},
  notificationStep: '',
}

const chargeConfigurationSlice = createSlice({
  name: 'chargeConfiguration',
  initialState,
  reducers: {
    //Tariffs
    setTariffs: (state, action: PayloadAction<ITariffData[]>) => {
      state.tariffs = action.payload ?? []
    },
    setTariffsSummary: (state, action: PayloadAction<TariffDataResponse>) => {
      state.tariffsSummary = action.payload
    },
    setSelectedTariff: (state, action: PayloadAction<ITariffCreate>) => {
      state.selectedTariff = action.payload
    },
    setLoadingState: (state, action: PayloadAction<boolean>) => {
      state.isLoading = action.payload
    },
    setTariffIsError: (state, action: PayloadAction<boolean>) => {
      state.isTariffError = action.payload
    },
    setSelectedTariffState: (
      state,
      action: PayloadAction<ISelectedTariffState>
    ) => {
      state.selectedTariffState = action.payload
    },

    //Configurations
    setSelectedConfiguration: (
      state,
      action: PayloadAction<IConfigurationItem>
    ) => {
      state.selectedConfiguration = action.payload
    },
    setTariffConfig: (state, action: PayloadAction<IConfigurationRow>) => {
      state.tariffConfig = action.payload
    },
    setSelectedConfigurationLog: (
      state,
      action: PayloadAction<IConfigurationLogs>
    ) => {
      state.selectedConfigurationLog = action.payload
    },
    setEditableState: (state, action: PayloadAction<boolean>) => {
      state.isEditable = action.payload
    },
    setConfiguredServices: (
      state,
      action: PayloadAction<IConfiguredService>
    ) => {
      const isAlreadyConfigured = state.configuredServices.some(
        (service) => service.serviceId === action.payload.serviceId
      )

      if (!isAlreadyConfigured) {
        state.configuredServices = [...state.configuredServices, action.payload]
      }
    },
    clearConfiguredServices: (state) => {
      state.configuredServices = []
    },

    setConfigurableServices: (state, action: PayloadAction<Service[]>) => {
      state.configurableServices = action.payload
    },

    setSelectedConfigurable: (
      state,
      action: PayloadAction<ISelectedConfigurableService>
    ) => {
      state.selectedConfigurable = action.payload
    },
    removeConfiguredService: (state, action: PayloadAction<string>) => {
      state.configuredServices = state.configuredServices.filter(
        (service) => service.serviceId !== action.payload
      )
    },
    //Drawer Actions
    setOpenDrawerAction: (state, action: PayloadAction<boolean>) => {
      state.openChangeLogsDrawer = action.payload
    },
    //Accordion Actions
    addAccordionItem: (state, action: PayloadAction<AccordionItem>) => {
      state.accordionItems[action.payload.id] = action.payload
    },
    removeAccordionItem: (state, action: PayloadAction<string>) => {
      delete state.accordionItems[action.payload]
    },
    resetChargeConfigurationStore: () => initialState,

    updateEditingConfig: (
      state,
      action: PayloadAction<{
        id: string
        config: ServiceConfig
        item: AccordionItem
      }>
    ) => {
      const { id, config, item } = action.payload
      state.editingConfigs[id] = { ...config, item }
    },
    clearEditingConfigs: (state) => {
      state.editingConfigs = {}
    },
    setNotificationStep: (state, action: PayloadAction<string>) => {
      state.notificationStep = action.payload
    },
    clearNotificationStep: (state) => {
      state.notificationStep = ''
    },
  },
})

export const {
  //Tariffs
  setTariffs,
  setTariffsSummary,
  setSelectedTariff,
  setLoadingState,
  setSelectedTariffState,
  setTariffIsError,

  //Configurations
  setSelectedConfiguration,
  setTariffConfig,
  resetChargeConfigurationStore,
  setConfiguredServices,
  removeConfiguredService,
  setEditableState,
  setOpenDrawerAction,
  setSelectedConfigurationLog,
  setSelectedConfigurable,
  setConfigurableServices,
  updateEditingConfig,
  clearEditingConfigs,
  clearConfiguredServices,

  //Accordion Actions
  addAccordionItem,
  removeAccordionItem,
  setNotificationStep,
  clearNotificationStep,
} = chargeConfigurationSlice.actions

export default chargeConfigurationSlice.reducer
