import { createSlice, PayloadAction } from '@reduxjs/toolkit'
import {
  IIFTReportRecord,
  IRTGSReportRecord,
  IPagination,
  IHistoryRecord,
  TransactionReportTypes,
  CustomerReportTypes,
  IPesalinkReportRecord,
  IC2BReportRecord,
  IMpesaAccountTopUpReportRecord,
  IPayBillReportRecord,
  IBuyAirtimeReportRecord,
  ICustomerReportRecord,
  ICustomerAccountsRecord,
  ICustomerDevicesRecord,
  ICustomerSecurityStatusRecord,
  IClientChannel,
} from '@/store/interfaces'

interface IReportsState {
  isLoadingReports: boolean
  isLoadingExportReport: boolean
  iftReportList: IIFTReportRecord[]
  rtgsReportList: IRTGSReportRecord[]
  pesalinkReportList: IPesalinkReportRecord[]
  c2bReportList: IC2BReportRecord[]
  mpesaAccountTopUpReportList: IMpesaAccountTopUpReportRecord[]
  payBillReportList: IPayBillReportRecord[]
  buyAirtimeReportList: IBuyAirtimeReportRecord[]
  //events-reports
  customerReportList: ICustomerReportRecord[]
  //customers-module
  customerAccountsReportList: ICustomerAccountsRecord[]
  customerDevicesReportList: ICustomerDevicesRecord[]
  customerSecurityStatusReportList: ICustomerSecurityStatusRecord[]
  reportHistoryList: IHistoryRecord[]
  reportPaginationData: IPagination
  reportHistoryPaginationData: IPagination
  isLoadingReportsHistoryList: boolean
  isSearchingReportsHistory: boolean
  isLoadingSaveReport: boolean
  selectedReportType: TransactionReportTypes | CustomerReportTypes
  clientReportChannels: IClientChannel[]
  selectedReportFilters: Record<string, string | string[]>
}

const initialState: IReportsState = {
  isLoadingReports: false,
  isLoadingExportReport: false,
  reportPaginationData: {} as IPagination,
  reportHistoryPaginationData: {} as IPagination,
  iftReportList: [],
  reportHistoryList: [],
  rtgsReportList: [],
  isLoadingReportsHistoryList: false,
  isSearchingReportsHistory: false,
  isLoadingSaveReport: false,
  pesalinkReportList: [],
  c2bReportList: [],
  mpesaAccountTopUpReportList: [],
  payBillReportList: [],
  buyAirtimeReportList: [],
  customerReportList: [],
  customerAccountsReportList: [],
  customerDevicesReportList: [],
  customerSecurityStatusReportList: [],
  selectedReportType: '' as TransactionReportTypes | CustomerReportTypes,
  clientReportChannels: [],
  selectedReportFilters: {},
}
const reportsSlice = createSlice({
  name: 'reports',
  initialState,
  reducers: {
    setLoadingReports: (state, action: PayloadAction<boolean>) => {
      state.isLoadingReports = action.payload
    },
    setIFTReportList: (state, action: PayloadAction<IIFTReportRecord[]>) => {
      state.iftReportList = action.payload
    },
    setRTGSReportList: (state, action: PayloadAction<IRTGSReportRecord[]>) => {
      state.rtgsReportList = action.payload
    },
    setPesalinkReportList: (
      state,
      action: PayloadAction<IPesalinkReportRecord[]>
    ) => {
      state.pesalinkReportList = action.payload
    },
    setC2BReportList: (state, action: PayloadAction<IC2BReportRecord[]>) => {
      state.c2bReportList = action.payload
    },
    setMpesaAccountTopUpReportList: (
      state,
      action: PayloadAction<IMpesaAccountTopUpReportRecord[]>
    ) => {
      state.mpesaAccountTopUpReportList = action.payload
    },
    setPayBillReportList: (
      state,
      action: PayloadAction<IPayBillReportRecord[]>
    ) => {
      state.payBillReportList = action.payload
    },
    setBuyAirtimeReportList: (
      state,
      action: PayloadAction<IBuyAirtimeReportRecord[]>
    ) => {
      state.buyAirtimeReportList = action.payload
    },
    setCustomerReportList: (
      state,
      action: PayloadAction<ICustomerReportRecord[]>
    ) => {
      state.customerReportList = action.payload
    },
    setCustomerAccountsReportList: (
      state,
      action: PayloadAction<ICustomerAccountsRecord[]>
    ) => {
      state.customerAccountsReportList = action.payload
    },
    setCustomerDevicesReportList: (
      state,
      action: PayloadAction<ICustomerDevicesRecord[]>
    ) => {
      state.customerDevicesReportList = action.payload
    },
    setCustomerSecurityStatusReportList: (
      state,
      action: PayloadAction<ICustomerSecurityStatusRecord[]>
    ) => {
      state.customerSecurityStatusReportList = action.payload
    },
    setReportPaginationData: (state, action: PayloadAction<IPagination>) => {
      state.reportPaginationData = action.payload
    },
    setReportHistoryPaginationData: (
      state,
      action: PayloadAction<IPagination>
    ) => {
      state.reportHistoryPaginationData = action.payload
    },
    setReportHistoryList: (state, action: PayloadAction<IHistoryRecord[]>) => {
      state.reportHistoryList = action.payload
    },
    setLoadingReportsHistory: (state, action: PayloadAction<boolean>) => {
      state.isLoadingReportsHistoryList = action.payload
    },
    setSearchingReportsHistory: (state, action: PayloadAction<boolean>) => {
      state.isSearchingReportsHistory = action.payload
    },
    setLoadingSaveReport: (state, action: PayloadAction<boolean>) => {
      state.isLoadingSaveReport = action.payload
    },
    setLoadingExportReport: (state, action: PayloadAction<boolean>) => {
      state.isLoadingExportReport = action.payload
    },
    setSelectedReportType: (
      state,
      action: PayloadAction<TransactionReportTypes | CustomerReportTypes>
    ) => {
      state.selectedReportType = action.payload
    },
    setClientReportChannels: (
      state,
      action: PayloadAction<IClientChannel[]>
    ) => {
      state.clientReportChannels = action.payload
    },
    setSelectedReportFilters: (
      state,
      action: PayloadAction<Record<string, string | string[]>>
    ) => {
      state.selectedReportFilters = action.payload
    },
    clearReports: () => initialState,
  },
})
export const {
  setLoadingReports,
  setLoadingExportReport,
  setIFTReportList,
  setRTGSReportList,
  setPesalinkReportList,
  setC2BReportList,
  setMpesaAccountTopUpReportList,
  setPayBillReportList,
  setBuyAirtimeReportList,
  setCustomerReportList,
  setCustomerAccountsReportList,
  setCustomerDevicesReportList,
  setCustomerSecurityStatusReportList,
  setReportPaginationData,
  setReportHistoryPaginationData,
  setReportHistoryList,
  setLoadingReportsHistory,
  setSearchingReportsHistory,
  setLoadingSaveReport,
  setSelectedReportType,
  setClientReportChannels,
  setSelectedReportFilters,
  clearReports,
} = reportsSlice.actions
export default reportsSlice.reducer
