import { createSlice, PayloadAction } from '@reduxjs/toolkit'

import { IADUserProfile, IUser, IUsersResponse } from '../interfaces'

export interface UsersReducersProps {
  usersResponse: IUsersResponse
  openCreateUSer: boolean
  isLoadingUsers: boolean
  isLoadingCreateUser: boolean
  isLoadingEditUser: boolean
  isLoadingRoleUsers: boolean
  isSuccessRolelUsers: boolean
  isErrorRoleUsers: boolean
  selectedUser: IUser
  openEditUser: boolean
  searchUserValue: string
  singleUserData: IUser
  userFilterValue: Record<string, string | string[]>
  roleUsers: IUsersResponse
  isLoadingADUserDetails: boolean
  isLoadedADUserDetailsSuccess: boolean
  isLoadedADUserDetailsFailure: boolean
  loadedADUserDetails: IADUserProfile[]
  //user report
  isGeneratedUserReportLoading: boolean
  isGeneratedUserReportSuccess: boolean
  isGeneratedUserReportFailure: boolean
  isGeneratedUserReport: boolean
}
const initialState: UsersReducersProps = {
  usersResponse: {
    pageNumber: 0,
    pageSize: 0,
    totalNumberOfPages: 0,
    totalElements: 0,
    data: [],
  },
  isLoadingUsers: false,
  isLoadingCreateUser: false,
  isLoadingEditUser: false,
  openCreateUSer: false,
  openEditUser: false,
  selectedUser: {} as IUser,
  searchUserValue: '',
  userFilterValue: {},
  singleUserData: {} as IUser,
  roleUsers: {} as IUsersResponse,
  isLoadingADUserDetails: false,
  isLoadedADUserDetailsSuccess: false,
  isLoadedADUserDetailsFailure: false,
  loadedADUserDetails: [],
  isLoadingRoleUsers: false,
  isSuccessRolelUsers: false,
  isErrorRoleUsers: false,

  //user report
  isGeneratedUserReportLoading: false,
  isGeneratedUserReportSuccess: false,
  isGeneratedUserReportFailure: false,
  isGeneratedUserReport: false,
}
const usersSlice = createSlice({
  name: 'users',
  initialState,
  reducers: {
    setUsersResponse: (state, action: PayloadAction<IUsersResponse>) => {
      state.usersResponse = action.payload
    },
    setIsLoadingUsers: (state, action: PayloadAction<boolean>) => {
      state.isLoadingUsers = action.payload
    },
    setIsLoadingCreateUser: (state, action: PayloadAction<boolean>) => {
      state.isLoadingCreateUser = action.payload
    },
    setIsLoadingEditUser: (state, action: PayloadAction<boolean>) => {
      state.isLoadingEditUser = action.payload
    },
    setOpenEditUser: (state, action: PayloadAction<boolean>) => {
      state.openEditUser = action.payload
      state.openCreateUSer = false
    },
    setOpenCreateUser: (state, action: PayloadAction<boolean>) => {
      state.openCreateUSer = action.payload
      state.openEditUser = false
    },
    setUserSearchValue: (state, action: PayloadAction<string>) => {
      state.searchUserValue = action.payload
    },
    setUserFilterValue: (
      state,
      action: PayloadAction<Record<string, string | string[]>>
    ) => {
      state.userFilterValue = action.payload
    },
    setSingleUserData: (state, action: PayloadAction<IUser>) => {
      state.singleUserData = action.payload
    },
    setSelectedUser: (state, action: PayloadAction<IUser>) => {
      state.selectedUser = action.payload
    },
    setRoleUsers: (state, action: PayloadAction<IUsersResponse>) => {
      state.roleUsers = action.payload
    },
    setLoadedADUserDetails: (
      state,
      action: PayloadAction<IADUserProfile[]>
    ) => {
      state.loadedADUserDetails = action.payload
    },
    setLoadingADSuccess: (state, action: PayloadAction<boolean>) => {
      state.isLoadedADUserDetailsSuccess = action.payload
    },
    setLoadingADFailure: (state, action: PayloadAction<boolean>) => {
      state.isLoadedADUserDetailsFailure = action.payload
    },
    setLoadingADUserDetails: (state, action: PayloadAction<boolean>) => {
      state.isLoadingADUserDetails = action.payload
    },
    setLoadingRoleUsers: (state, action: PayloadAction<boolean>) => {
      state.isLoadingRoleUsers = action.payload
    },
    setSuccessRoleUsers: (state, action: PayloadAction<boolean>) => {
      state.isSuccessRolelUsers = action.payload
    },
    setErrorRoleUsers: (state, action: PayloadAction<boolean>) => {
      state.isErrorRoleUsers = action.payload
    },
    setGeneratedUserReportLoading: (state, action: PayloadAction<boolean>) => {
      state.isGeneratedUserReportLoading = action.payload
    },
    setGeneratedUserReportSuccess: (state, action: PayloadAction<boolean>) => {
      state.isGeneratedUserReportSuccess = action.payload
    },
    setGeneratedUserReportFailure: (state, action: PayloadAction<boolean>) => {
      state.isGeneratedUserReportFailure = action.payload
    },
    resetUsersStore: () => initialState,
  },
})
export const {
  setUsersResponse,
  setOpenCreateUser,
  setOpenEditUser,
  setIsLoadingCreateUser,
  setIsLoadingEditUser,
  setIsLoadingUsers,
  resetUsersStore,
  setUserSearchValue,
  setUserFilterValue,
  setSingleUserData,
  setRoleUsers,
  setSelectedUser,
  setLoadedADUserDetails,
  setLoadingADSuccess,
  setLoadingADFailure,
  setLoadingADUserDetails,
  //user report
  setGeneratedUserReportLoading,
  setGeneratedUserReportSuccess,
  setGeneratedUserReportFailure,
} = usersSlice.actions
export default usersSlice.reducer
