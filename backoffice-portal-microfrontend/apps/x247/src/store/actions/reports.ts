import { Dispatch } from '@reduxjs/toolkit'
import {
  setBuyAirtimeReportList,
  setC2BReportList,
  setClientReportChannels,
  setCustomerAccountsReportList,
  setCustomerDevicesReportList,
  setCustomerReportList,
  setCustomerSecurityStatusReportList,
  setIFTReportList,
  setLoadingExportReport,
  setLoadingReports,
  setLoadingReportsHistory,
  setLoadingSaveReport,
  setMpesaAccountTopUpReportList,
  setPayBillReportList,
  setPesalinkReportList,
  setReportHistoryList,
  setReportHistoryPaginationData,
  setReportPaginationData,
  setRTGSReportList,
  setSearchingReportsHistory,
} from '@/store/reducers'
import { downloadBlob, secureapi2 } from '@dtbx/store/utils'
import { setNotification } from '@dtbx/store/reducers'
import {
  CUSTOMER_REPORT_TYPES,
  CustomerReportTypes,
  TRANSACTION_REPORT_TYPES,
  TransactionReportTypes,
} from '@/store/interfaces'

export const getReports = async (
  dispatch: Dispatch,
  reportType: TransactionReportTypes,
  params?: string
) => {
  dispatch(setLoadingReports(true))
  const ENDPOINT = 'payment/transactions'

  try {
    const response = await secureapi2.get(
      `${ENDPOINT}/${reportType}${params ?? ''}`
    )
    const { pageNumber, pageSize, totalElements, totalNumberOfPages } =
      response.data
    dispatch(
      setReportPaginationData({
        pageNumber,
        pageSize,
        totalElements,
        totalNumberOfPages,
      })
    )
    dispatch(setLoadingReports(false))
    switch (reportType) {
      case 'ift':
        return dispatch(setIFTReportList(response.data.data))
      case 'rtgs':
        return dispatch(setRTGSReportList(response.data.data))
      case 'pesalink':
        return dispatch(setPesalinkReportList(response.data.data))
      case 'mpesa/c2b':
        return dispatch(setC2BReportList(response.data.data))
      case 'mpesa/account-top-up':
        return dispatch(setMpesaAccountTopUpReportList(response.data.data))
      case 'paybill':
        return dispatch(setPayBillReportList(response.data.data))
      case 'buy-airtime':
        return dispatch(setBuyAirtimeReportList(response.data.data))
      default:
        return
    }
  } catch (e) {
    const message = (e as Error).message
    dispatch(
      setNotification({
        message,
        type: 'error',
      })
    )
    dispatch(setLoadingReports(false))
  }
}

export const getCustomerReports = async (
  dispatch: Dispatch,
  eventType: CustomerReportTypes,
  params: string
) => {
  try {
    dispatch(setLoadingReports(true))
    const isTypeCustomerModule: boolean =
      eventType === 'CUSTOMER_ACCOUNTS' ||
      eventType === 'CUSTOMERS' ||
      eventType === 'CUSTOMER_DEVICES' ||
      eventType === 'CUSTOMER_SECURITY_STATUS'
    const queryParams = `${params}${
      isTypeCustomerModule
        ? `&reportType=${eventType}`
        : `&eventType=${eventType}`
    }`

    const response = await secureapi2.get(
      `/dbp/customers/${isTypeCustomerModule ? 'report-details' : 'event-details'}${queryParams}`
    )

    const { pageNumber, pageSize, totalElements, totalNumberOfPages } =
      response.data
    dispatch(
      setReportPaginationData({
        pageNumber,
        pageSize,
        totalElements,
        totalNumberOfPages,
      })
    )
    switch (eventType) {
      case 'CUSTOMER_ACCOUNTS':
        return dispatch(setCustomerAccountsReportList(response.data.data))
      case 'CUSTOMER_DEVICES':
        return dispatch(setCustomerDevicesReportList(response.data.data))
      case 'CUSTOMER_SECURITY_STATUS':
        return dispatch(setCustomerSecurityStatusReportList(response.data.data))
      default:
        return dispatch(setCustomerReportList(response.data.data))
    }
  } catch (e) {
    const message = (e as Error).message
    dispatch(
      setNotification({
        message,
        type: 'error',
      })
    )
  } finally {
    dispatch(setLoadingReports(false))
  }
}

export const exportReportByType = async (
  dispatch: Dispatch,
  reportType: TransactionReportTypes | CustomerReportTypes,
  params?: string
) => {
  try {
    dispatch(setLoadingExportReport(true))
    let url: string
    if (isTransactionReportType(reportType)) {
      url = `/reports/${reportType}/export-to-excel${params ?? ''}`
    } else {
      switch (reportType) {
        case 'CUSTOMER_ACCOUNTS':
        case 'CUSTOMERS':
        case 'CUSTOMER_DEVICES':
        case 'CUSTOMER_SECURITY_STATUS':
          url = `/reports/customers-details/export-to-excel?reportType=${reportType}`
          break
        default:
          url = `/reports/customers-events/export-to-excel?eventType=${reportType}`
      }
    }
    const res = await secureapi2.get(url, {
      responseType: 'blob',
    })
    dispatch(
      setNotification({
        message: ` ${reportType} report generated successfully`,
        type: 'success',
      })
    )
    const blob = new Blob([res.data], {
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    })
    downloadBlob(blob, `${reportType} report-${new Date().toDateString()}.xlsx`)
    dispatch(setLoadingExportReport(false))
  } catch (e) {
    const message = (e as Error).message
    dispatch(
      setNotification({
        message,
        type: 'error',
      })
    )
    dispatch(setLoadingExportReport(false))
  }
}
export const getTransactionChannels = async (
  dispatch: Dispatch,
  query: string
) => {
  try {
    const resp = await secureapi2.get(
      `payment/transactions/channels?searchQuery=${query}`
    )
    dispatch(setClientReportChannels(resp.data))
    return resp.data
  } catch (e) {
    const message = (e as Error).message
    dispatch(
      setNotification({
        message,
        type: 'error',
      })
    )
  }
}
// Reports History apis from here
export const getReportsHistory = async (
  dispatch: Dispatch,
  reportName?: string,
  page: number = 1,
  size: number = 10
) => {
  const isSearching = !!reportName
  try {
    if (isSearching) {
      dispatch(setSearchingReportsHistory(true)) // Only set this if searching
    } else {
      dispatch(setLoadingReportsHistory(true)) // Otherwise, set the main loader
    }

    let nameQuery = ''
    if (reportName) {
      nameQuery = `&reportName=${reportName}`
    }

    const response = await secureapi2.get(
      `/reports/report-filters?page=${page}&pageSize=${size}${nameQuery}`
    )
    const { pageNumber, pageSize, totalElements, totalNumberOfPages } =
      response.data
    dispatch(
      setReportHistoryPaginationData({
        pageNumber,
        pageSize,
        totalElements,
        totalNumberOfPages,
      })
    )
    dispatch(setReportHistoryList(response.data.data))
  } catch (e) {
    const message = (e as Error).message
    dispatch(
      setNotification({
        message,
        type: 'error',
      })
    )
  } finally {
    if (isSearching) {
      dispatch(setSearchingReportsHistory(false)) // Clear search state only if searching
    } else {
      dispatch(setLoadingReportsHistory(false)) // Otherwise, clear full loading state
    }
  }
}

export const postReportFilters = async (
  dispatch: Dispatch,
  payload: {
    reportName: string
    reportFilters: Record<string, string | string[]>[]
  }
) => {
  try {
    dispatch(setLoadingSaveReport(true))

    const response = await secureapi2.post('/reports/report-filters', payload)
    dispatch(setReportHistoryList(response.data.data))
    dispatch(
      setNotification({
        message: 'Report filters saved successfully',
        type: 'success',
      })
    )
  } catch (e) {
    const message = (e as Error).message
    dispatch(
      setNotification({
        message,
        type: 'error',
      })
    )
  } finally {
    dispatch(setLoadingSaveReport(false))
  }
}

//Type derivatives
export function isTransactionReportType(
  reportType: string
): reportType is TransactionReportTypes {
  return TRANSACTION_REPORT_TYPES.includes(reportType as TransactionReportTypes)
}

export function isCustomerReportType(
  reportType: string
): reportType is CustomerReportTypes {
  return CUSTOMER_REPORT_TYPES.includes(reportType as CustomerReportTypes)
}
