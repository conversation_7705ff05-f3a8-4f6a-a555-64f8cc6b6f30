import { Dispatch } from 'redux'
import dayjs from 'dayjs'

import {
  setCreateRoleFailed,
  setCreateRoleSuccess,
  setDeleteRoleSuccess,
  setGeneratedRoleReportFailure,
  setGeneratedRoleReportLoading,
  setGeneratedRoleReportSuccess,
  setIsGetRoleError,
  setIsGetRoleSuccess,
  setIsLoadingPermissions,
  setIsLoadingRole,
  setIsPermissionGroupLoading,
  setIsPermissionGroupSuccess,
  setLoadingCreateRole,
  setLoadingDeleteRole,
  setLoadingRoles,
  setLoadingUpdateRole,
  setPermissionGroup,
  setPermissions,
  setPermissionsWithFilters,
  setRole,
  setRoles,
  setRolesWithPermissions,
  setUpdateRoleFailed,
  setUpdateRoleSuccess,
} from '../reducers'
import { setNotification } from '@dtbx/store/reducers'

import { secureapi, secureapi2, downloadBlob } from '@dtbx/store/utils'
import {
  FileFormat,
  ICreateRole,
  IPermissionGroupResponse,
  IPermissionResponse,
  IRoleResponse,
} from '../interfaces'

//get apis
export const getRoles = async (dispatch: Dispatch) => {
  dispatch(setLoadingRoles(true))
  await secureapi
    .get(`/backoffice-auth/roles`)
    .then((res) => {
      if (res.status === 200) {
        dispatch(setRoles(res.data))
        dispatch(setLoadingRoles(false))
      }
    })
    .catch(() => {
      dispatch(setRoles([]))
      dispatch(setLoadingRoles(false))
    })
}

export const getRolesFilter = async (
  dispatch: Dispatch,
  filter?: {
    roleName?: string
    rights?: string | string[]
    isVisible?: string | string[]
    moduleName?: string | string[]
    date?: {
      start: dayjs.Dayjs
      end: dayjs.Dayjs
    }
    size?: number
    page?: number
  }
) => {
  const paramString = filter
    ? [
        `page=${filter.page ?? 1}`,
        `size=${filter.size ?? 10}`,
        filter.isVisible && `isVisible=${filter.isVisible === 'yes'}`,
        filter.roleName && `roleName=${filter.roleName}`,
        filter.rights && `rights=${filter.rights}`,
        filter.moduleName && `moduleName=${filter.moduleName}`,
        filter.date &&
          `&createDateFrom=${filter.date.start.format('YYYY-MM-DD')}&createDateTo=${filter.date.end.format('YYYY-MM-DD')}`,
      ].filter(Boolean)
    : []
  const url = '?' + paramString.join('&')
  dispatch(setLoadingRoles(true))
  await secureapi
    .get(`/backoffice-auth/roles/filter${url}`)
    .then((res) => {
      if (res.status === 200) {
        dispatch(setRolesWithPermissions(res?.data))
        dispatch(setLoadingRoles(false))
      }
    })
    .catch(() => {
      dispatch(setRolesWithPermissions({} as IRoleResponse))
      dispatch(setLoadingRoles(false))
    })
}

export const getRoleById = async (dispatch: Dispatch, roleId: string) => {
  dispatch(setIsLoadingRole(true))
  try {
    const response = await secureapi.get(`/backoffice-auth/roles/${roleId}`)
    dispatch(setIsLoadingRole(false))
    dispatch(setIsGetRoleSuccess(true))
    dispatch(setRole(response.data))
  } catch (err) {
    console.error(err)
    dispatch(setIsGetRoleError(true))
    dispatch(setIsLoadingRole(false))
  }
}

// creation apis
export const createRole = async (data: ICreateRole, dispatch: Dispatch) => {
  dispatch(setLoadingCreateRole(true))
  try {
    await secureapi.post('/backoffice-auth/roles', data)
    dispatch(setLoadingCreateRole(false))
    dispatch(setCreateRoleSuccess(true))
    dispatch(
      setNotification({
        message: 'Role was Successfully Created',
        type: 'success',
      })
    )
    await getRolesFilter(dispatch, { page: 1, size: 10 })
  } catch (e) {
    dispatch(setCreateRoleFailed(true))
    console.error(e)
    const message = (e as Error).message
    dispatch(
      setNotification({
        message,
        type: 'error',
      })
    )
    dispatch(setLoadingCreateRole(false))
  }
}

export const makeCreateRole = async (data: ICreateRole, dispatch: Dispatch) => {
  dispatch(setLoadingCreateRole(true))
  try {
    await secureapi.post('/backoffice-auth/roles/make', data)
    dispatch(setLoadingCreateRole(false))
    dispatch(setCreateRoleSuccess(true))
    dispatch(
      setNotification({
        message: 'Create role request is pending approval.',
        type: 'success',
      })
    )
    await getRolesFilter(dispatch, { page: 1, size: 10 })
  } catch (e) {
    dispatch(setCreateRoleFailed(true))
    console.error(e)
    const message = (e as Error).message
    dispatch(
      setNotification({
        message,
        type: 'error',
      })
    )
    dispatch(setLoadingCreateRole(false))
  }
}
export const approveCreateRole = async (
  approvalId: string,
  comments: string,
  dispatch: Dispatch
) => {
  dispatch(setLoadingUpdateRole(true))
  try {
    await secureapi.put(`/backoffice-auth/roles/approve/${approvalId}`, {
      comments,
    })
    dispatch(setLoadingUpdateRole(false))
    dispatch(setUpdateRoleSuccess(true))
    dispatch(
      setNotification({
        message: 'Create role request has been approved.',
        type: 'success',
      })
    )
    await getRolesFilter(dispatch, { page: 1, size: 10 })
  } catch (e) {
    dispatch(setUpdateRoleFailed(true))
    const message = (e as Error).message
    dispatch(
      setNotification({
        message,
        type: 'error',
      })
    )
    console.error(e)
    dispatch(setLoadingUpdateRole(false))
  }
}
export const rejectCreateRole = async (
  approvalId: string,
  comments: string,
  dispatch: Dispatch
) => {
  dispatch(setLoadingUpdateRole(true))
  try {
    await secureapi.put(`/backoffice-auth/roles/reject/${approvalId}`, {
      comments,
    })
    dispatch(setLoadingUpdateRole(false))
    dispatch(setUpdateRoleSuccess(true))
    dispatch(
      setNotification({
        message: 'Create role request has been rejected.',
        type: 'success',
      })
    )
    await getRolesFilter(dispatch, { page: 1, size: 10 })
  } catch (e) {
    const message = (e as Error).message
    dispatch(
      setNotification({
        message,
        type: 'error',
      })
    )
    dispatch(setUpdateRoleFailed(true))
    console.error(e)
    dispatch(setLoadingUpdateRole(false))
  }
}

//update apis
export const updateRole = async (
  roleId: string,
  data: ICreateRole,
  dispatch: Dispatch
) => {
  dispatch(setLoadingUpdateRole(true))
  try {
    await secureapi.put(`/backoffice-auth/roles/${roleId}`, data)
    dispatch(setLoadingUpdateRole(false))
    dispatch(setUpdateRoleSuccess(true))
    dispatch(
      setNotification({
        message: 'Role was Successfully Updated',
        type: 'success',
      })
    )
    await getRolesFilter(dispatch, { page: 1, size: 10 })
  } catch (e) {
    const message = (e as Error).message
    dispatch(
      setNotification({
        message,
        type: 'error',
      })
    )
    dispatch(setLoadingUpdateRole(false))
    console.error(e)
    dispatch(setUpdateRoleSuccess(false))
  }
}
export const makeUpdateRole = async (
  roleId: string,
  data: ICreateRole,
  dispatch: Dispatch
) => {
  dispatch(setLoadingUpdateRole(true))
  try {
    await secureapi.put(`/backoffice-auth/roles/${roleId}/make`, data)
    dispatch(setLoadingUpdateRole(false))
    dispatch(setUpdateRoleSuccess(true))
    dispatch(
      setNotification({
        message: 'Update role was successfully initiated',
        type: 'success',
      })
    )
    await getRolesFilter(dispatch, { page: 1, size: 10 })
  } catch (e) {
    const message = (e as Error).message
    dispatch(
      setNotification({
        message,
        type: 'error',
      })
    )
    dispatch(setLoadingUpdateRole(false))
    console.error(e)
    dispatch(setUpdateRoleSuccess(false))
  }
}
export const checkUpdateRole = async (
  roleId: string,
  action: string,
  dispatch: Dispatch,
  comments: string
) => {
  dispatch(setLoadingUpdateRole(true))
  try {
    await secureapi.put(`/backoffice-auth/roles/${roleId}/${action}`, {
      comments,
    })
    dispatch(setLoadingUpdateRole(false))
    dispatch(setUpdateRoleSuccess(true))
    const message =
      action === 'approve'
        ? 'Edit role request has been approved.'
        : ' Edit role request has been rejected.'
    dispatch(
      setNotification({
        message,
        type: 'success',
      })
    )
    await getRolesFilter(dispatch, { page: 1, size: 10 })
  } catch (e) {
    const message = (e as Error).message
    dispatch(
      setNotification({
        message,
        type: 'error',
      })
    )
    dispatch(setLoadingUpdateRole(false))
    console.error(e)
    dispatch(setUpdateRoleSuccess(false))
  }
}

//delete apis
export const deleteRole = async (
  roleId: string,
  dispatch: Dispatch,
  comments: string
) => {
  dispatch(setLoadingDeleteRole(true))
  try {
    await secureapi.delete(`/backoffice-auth/roles/${roleId}`, {
      data: { comments },
    })
    dispatch(setDeleteRoleSuccess(true))
    dispatch(
      setNotification({
        message: 'Role was successfully deleted',
        type: 'success',
      })
    )
    dispatch(setLoadingDeleteRole(false))
    await getRolesFilter(dispatch, { page: 1, size: 10 })
  } catch (e) {
    const message = (e as Error).message
    dispatch(
      setNotification({
        message,
        type: 'error',
      })
    )
    dispatch(setDeleteRoleSuccess(false))
    dispatch(setLoadingDeleteRole(false))
    console.error(e)
  }
}
export const makeDeleteRole = async (
  roleId: string,
  dispatch: Dispatch,
  comments: string
) => {
  dispatch(setLoadingDeleteRole(true))
  try {
    await secureapi.delete(`/backoffice-auth/roles/${roleId}/make`, {
      data: { comments },
    })
    dispatch(setDeleteRoleSuccess(true))
    dispatch(
      setNotification({
        message: 'Delete role was successfully initiated',
        type: 'success',
      })
    )
    dispatch(setLoadingDeleteRole(false))
    await getRolesFilter(dispatch, { page: 1, size: 10 })
  } catch (e) {
    const message = (e as Error).message
    dispatch(
      setNotification({
        message,
        type: 'error',
      })
    )
    dispatch(setDeleteRoleSuccess(false))
    dispatch(setLoadingDeleteRole(false))
    console.error(e)
  }
}
export const checkDeleteRole = async (
  roleId: string,
  action: string,
  dispatch: Dispatch,
  comments: string
) => {
  dispatch(setLoadingDeleteRole(true))
  try {
    await secureapi.delete(`/backoffice-auth/roles/${roleId}/${action}`, {
      data: { comments },
    })
    dispatch(setDeleteRoleSuccess(true))
    const message =
      action === 'approve'
        ? 'Delete role request has been approved.'
        : ' Delete role request has been rejected'
    dispatch(
      setNotification({
        message,
        type: 'success',
      })
    )
    dispatch(setLoadingDeleteRole(false))
    await getRolesFilter(dispatch, { page: 1, size: 10 })
  } catch (e) {
    dispatch(setDeleteRoleSuccess(false))
    const message = (e as Error).message
    dispatch(
      setNotification({
        message,
        type: 'error',
      })
    )
    dispatch(setLoadingDeleteRole(false))
    console.error(e)
  }
}

export const getPermissions = async (dispatch: Dispatch) => {
  dispatch(setIsLoadingPermissions(true))
  secureapi
    .get('/backoffice-auth/permissions')
    .then((res) => {
      if (res.status === 200) {
        dispatch(setIsLoadingPermissions(false))
        dispatch(setPermissions(res.data))
      }
    })
    .catch((err) => {
      console.error(err)
      dispatch(setIsLoadingPermissions(false))
      dispatch(setPermissions([]))
    })
}

export const getPermissionsFilter = async (
  dispatch: Dispatch,
  filter?: {
    permissionName?: string
    isVisible?: string | string[]
    groupName?: string | string[]
    date?: {
      start: dayjs.Dayjs
      end: dayjs.Dayjs
    }
    size?: number
    page?: number
  }
) => {
  const paramString = filter
    ? [
        `page=${filter.page ?? 1}`,
        `size=${filter.size ?? 10}`,
        filter.isVisible && `isVisible=${filter.isVisible === 'yes'}`,
        filter.permissionName && `permissionName=${filter.permissionName}`,
        filter.groupName && `groupName=${filter.groupName}`,
        filter.date &&
          `&createDateFrom=${filter.date.start.format('YYYY-MM-DD')}&createDateTo=${filter.date.end.format('YYYY-MM-DD')}`,
      ].filter(Boolean)
    : []
  const url = '?' + paramString.join('&')
  dispatch(setIsLoadingPermissions(true))
  secureapi
    .get(`/backoffice-auth/permissions/filter${url}`)
    .then((res) => {
      if (res.status === 200) {
        dispatch(setIsLoadingPermissions(false))
        dispatch(setPermissionsWithFilters(res?.data))
      }
    })
    .catch((err) => {
      console.error(err)
      dispatch(setIsLoadingPermissions(false))
      dispatch(setPermissionsWithFilters({} as IPermissionResponse))
    })
}

export const getPermissionsGroup = async (
  dispatch: Dispatch,
  pagination?: { page: number; size: number }
) => {
  dispatch(setIsPermissionGroupLoading(true))
  try {
    const response = await secureapi.get('/backoffice-auth/modules', {
      params: {
        page: pagination?.page,
        size: pagination?.size,
      },
    })

    if (response.status === 200) {
      dispatch(setIsPermissionGroupLoading(false))
      dispatch(setPermissionGroup(response.data))
      dispatch(setIsPermissionGroupSuccess(true))
    }
  } catch (err) {
    console.error(err)
    dispatch(setIsPermissionGroupLoading(false))
    dispatch(setPermissionGroup({} as IPermissionGroupResponse))
    dispatch(setIsPermissionGroupSuccess(false))
  }
}

//Role reports
interface DataItem {
  [key: string]: unknown
}

export const generateRoleReports = async ({
  dispatch,
  params,
  format,
}: {
  dispatch: Dispatch
  params: {
    firstName?: string
    lastName?: string
    size?: number
    page?: number
    roleIds?: string[]
    Rights: string
    Modules: string
    UsersAssigned: string
  }
  format: FileFormat
  filteredData: DataItem[]
}) => {
  const {
    firstName,
    lastName,
    size,
    page,
    roleIds,
    Rights,
    Modules,
    UsersAssigned,
  } = params
  let url = `/reports/roles/export-to-${format}?page=${page ?? 0}&size=${size ?? 7}`
  if (firstName) url += `&firstName=${firstName}`
  if (lastName) url += `&lastName=${lastName}`
  if (Rights) url += `&permissions=${Rights}`
  if (Modules) url += `&custom=${Modules}`
  if (UsersAssigned) url += `&description=${UsersAssigned}`

  if (roleIds && roleIds.length > 0)
    url += `&roleIds=${roleIds.join('&roleIds=')}`
  dispatch(setGeneratedRoleReportLoading(true))

  try {
    const res = await secureapi2.get(url, { responseType: 'arraybuffer' })
    const dataToExport = res.data
    dispatch(setGeneratedRoleReportSuccess(true))

    let mimeType: string
    let extension: string
    switch (format) {
      case 'excel':
        mimeType =
          'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        extension = 'xlsx'
        break
      case 'csv':
        mimeType = 'text/csv'
        extension = 'csv'
        break
      case 'json':
        mimeType = 'application/json'
        extension = 'json'
        break
      case 'pdf':
        mimeType = 'application/pdf'
        extension = 'pdf'
        break
      default:
        throw new Error('Unsupported format')
    }

    const blob = new Blob([dataToExport], { type: mimeType })
    downloadBlob(blob, `Role_Report.${extension}`)
  } catch (e) {
    const message = (e as Error).message
    dispatch(setGeneratedRoleReportFailure(true))
    dispatch(
      setNotification({
        message,
        type: 'error',
      })
    )
    dispatch(setGeneratedRoleReportLoading(false))
  }
}
