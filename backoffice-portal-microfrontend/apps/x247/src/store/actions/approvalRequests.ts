import { Dispatch } from '@reduxjs/toolkit'

import {
  setApprovalRequestResponse,
  setApprovals,
  setCustomersWithPendingApprovals,
  setLoadingApprovals,
  setLoadingRequestTypes,
  setMakerCheckerTypes,
  setPendingCustomerApprovals,
  setPendingCustomerApprovalsResponse,
  setPendingSingleCustomerApprovals,
  setPendingSingleCustomerApprovalsResponse,
  setRequestTypes,
  setRequestTypesSuccess,
} from '../reducers'

import { setNotification } from '@dtbx/store/reducers'

import { secureapi } from '@dtbx/store/utils'
import {
  IPendingCustomersFilter,
  IProfileApprovalRequests,
} from '../interfaces'

export const getApprovals = async (dispatch: Dispatch, params?: string) => {
  dispatch(setLoadingApprovals(true))
  try {
    const resp = await secureapi.get(
      `/backoffice-auth/maker-checker/approvals${params || ''}`
    )
    dispatch(setApprovals(resp.data.data))
    dispatch(setApprovalRequestResponse(resp.data))
    dispatch(setLoadingApprovals(false))
  } catch (e) {
    const message = (e as Error).message
    dispatch(
      setNotification({
        message: message,
        type: 'error',
      })
    )
    dispatch(setLoadingApprovals(false))
  }
}

export const getApprovalRequestTypes = async (
  dispatch: Dispatch,
  channel?: 'DBP' | 'LMS'
) => {
  let url = `/backoffice-auth/maker-checker/types`
  url += channel ? `?channel=${channel}` : ''
  dispatch(setLoadingRequestTypes(true))
  dispatch(setRequestTypesSuccess(false))
  try {
    const response = await secureapi.get(url)
    dispatch(setRequestTypes(response.data))
    dispatch(setRequestTypesSuccess(true))
    dispatch(setLoadingRequestTypes(false))
  } catch (e) {
    const message = (e as Error).message
    dispatch(
      setNotification({
        message: message,
        type: 'error',
      })
    )
    dispatch(setLoadingRequestTypes(false))
    dispatch(setRequestTypesSuccess(false))
  }
}

export const getMakerCheckerTypes = async (
  dispatch: Dispatch,
  params: string
) => {
  dispatch(setMakerCheckerTypes([]))
  try {
    const response = await secureapi.get(
      `/backoffice-auth/maker-checker/types${params || ''}`
    )
    dispatch(setMakerCheckerTypes(response.data))
  } catch (e) {
    const message = (e as Error).message
    dispatch(
      setNotification({
        message: message,
        type: 'error',
      })
    )
  }
}

export const fetchPendingCustomerApprovals = async (
  dispatch: Dispatch,
  params?: IPendingCustomersFilter
) => {
  dispatch(setLoadingApprovals(true))
  try {
    const paramString = [
      `page=${params?.page || 1}`,
      `size=${params?.size || 10}`,
      `requestType=${params?.requestType || '4cc7defa-9382-4f89-986c-aa910daa9ed1'}`,
      `module=${params?.module || 'Customers'}`,
      `status=${params?.status || 'PENDING'}`,
      params?.maker &&
        params?.makerSearchType &&
        `${params?.makerSearchType}=${params?.maker}`,
      params?.startDate && `createDateFrom=${params?.startDate}`,
      params?.endDate && `createDateFrom=${params?.endDate}`,
    ].filter(Boolean)
    const url = '?' + paramString.join('&')
    const resp = await secureapi.get(
      `/backoffice-auth/maker-checker/approvals${url}`
    )
    dispatch(setPendingCustomerApprovals(resp.data.data))
    dispatch(setPendingCustomerApprovalsResponse(resp.data))
    dispatch(setLoadingApprovals(false))
  } catch (e) {
    const message = (e as Error).message
    dispatch(
      setNotification({
        message: message,
        type: 'error',
      })
    )
    dispatch(setLoadingApprovals(false))
  }
}

export const fetchPendingSingleCustomerApprovals = async (
  dispatch: Dispatch,
  entityId: string,
  params?: IPendingCustomersFilter,
  customerList?: IProfileApprovalRequests[]
) => {
  dispatch(setLoadingApprovals(true))
  try {
    const paramString = [
      `page=${params?.page || 1}`,
      `size=${params?.size || 10}`,
      params?.startDate && `createDateFrom=${params?.startDate}`,
      params?.endDate && `createDateFrom=${params?.endDate}`,
    ].filter(Boolean)
    const url = '?' + paramString.join('&')
    const resp = await secureapi.get(
      `/backoffice-bff/dbp/customers/${entityId}/approvals${url}`
    )
    if (customerList && customerList?.length > 0) {
      const list = [...customerList]
      const updatedList = list.map((customer) =>
        customer.profileId === entityId
          ? { ...customer, requests: resp.data.data }
          : customer
      )
      dispatch(setCustomersWithPendingApprovals(updatedList))
    }
    dispatch(setPendingSingleCustomerApprovals(resp.data.data))
    dispatch(setPendingSingleCustomerApprovalsResponse(resp.data))
    dispatch(setLoadingApprovals(false))
    return resp.data.data
  } catch (e) {
    const message = (e as Error).message
    dispatch(
      setNotification({
        message: message,
        type: 'error',
      })
    )
    dispatch(setLoadingApprovals(false))
  }
}
