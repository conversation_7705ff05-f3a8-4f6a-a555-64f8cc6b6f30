import { secureapi } from '@dtbx/store/utils'
import { Dispatch } from 'redux'
import {
  setLoadingState,
  setTariffConfig,
  setTariffs,
  setTariffsSummary,
  setConfigurableServices,
  setSelectedTariffState,
  setTariffIsError,
  setLoadingApprovals,
} from '../reducers'
import { setNotification } from '@dtbx/store/reducers'
import {
  IConfigParams,
  TariffDataResponse,
  IConfiguredService,
} from '../interfaces'
import { PaginationOptions } from '@dtbx/ui/components/Table'
import { getApprovals } from './approvalRequests'

const ENDPOINT = '/backoffice-bff/fee/tariffs'

export const getAllTariffs = async (
  dispatch: Dispatch,
  params: {
    page: number
    size: number
    searchQuery?: string
    status?: string
  }
) => {
  const queryParams = new URLSearchParams()
  queryParams.append('page', params.page.toString())
  queryParams.append('size', params.size.toString())
  if (params.searchQuery) {
    queryParams.append('searchQuery', params.searchQuery)
  }
  if (params.status) {
    queryParams.append('status', params.status)
  }

  const url = `${ENDPOINT}?${queryParams.toString()}`

  dispatch(setLoadingState(true))

  try {
    const response = await secureapi.get(url)
    const { data, ...rest } = response.data
    dispatch(setTariffs(data))
    dispatch(setTariffsSummary(rest as TariffDataResponse))
  } catch (error) {
    const message = (error as Error).message
    dispatch(setNotification({ message, type: 'error' }))
  } finally {
    dispatch(setLoadingState(false))
  }
}

export const getTariffConfigs = async (
  { serviceCode, currency, countryCode, tariff }: IConfigParams,
  dispatch: Dispatch
) => {
  dispatch(setLoadingState(true))
  try {
    const response = await secureapi.get(
      '/backoffice-bff/fee/service-configuration',
      {
        headers: {
          'Content-Type': 'application/json',
        },
        data: {
          serviceCode: serviceCode,
          currency: currency,
          countryCode: countryCode,
          tariff: tariff,
        },
      }
    )

    const { data, totalElements, ...rest } = response.data
    dispatch(setTariffs(data))
  } catch (error) {
    const message = (error as Error).message
    dispatch(setNotification({ message, type: 'error' }))
  } finally {
    dispatch(setLoadingState(false))
  }
}

export const getTariffConfigByTariffCurrency = async (
  {
    tariff,
    currency,
    page = 0,
    size = 10,
    chargeType = '',
    serviceType = '',
  }: {
    tariff: string
    currency: string
    page?: number
    size?: number
    chargeType?: string
    serviceType?: string
  },
  dispatch: Dispatch
) => {
  dispatch(setLoadingState(true))
  try {
    const response = await secureapi.get(
      `/backoffice-bff/fee/service-configuration/tariff/${tariff}/currency/${currency}?chargeType=${chargeType}&serviceType=${serviceType}&page=${page}&size=${size}`
    )
    dispatch(setTariffConfig(response.data))
  } catch (error) {
    const message = (error as Error).message
    dispatch(setNotification({ message, type: 'error' }))
  } finally {
    dispatch(setLoadingState(false))
  }
}

export const getConfigurableServices = async (
  tariff: string,
  currency: string,
  dispatch: Dispatch,
  serviceType?: 'PAYMENT' | 'NOTIFICATION'
) => {
  dispatch(setLoadingState(true))
  try {
    let url = `/backoffice-bff/fee/payment-service/configurable/tariff/${tariff}/currency/${currency}`

    if (serviceType) {
      url += `?serviceType=${serviceType}`
    }
    const res = await secureapi.get(url)
    dispatch(setConfigurableServices(res.data))
  } catch (err) {
    const message = (err as Error).message
    dispatch(setNotification({ message, type: 'error' }))
    dispatch(setLoadingState(false))
  }
}

export const createServiceConfig = async (
  data: IConfiguredService[],
  type: string,
  dispatch: Dispatch
) => {
  dispatch(setLoadingState(true))
  try {
    await secureapi.post(
      `/backoffice-bff/fee/service-configuration/${type === 'make' ? 'make' : ''}`,
      {
        serviceConfigurations: data,
        comments: 'creating a service configuration',
      }
    )
    dispatch(setLoadingState(false))
    dispatch(
      setNotification({
        message: `Service configuration successfully created ${type === 'make' ? ', awaiting approval' : ''}`,
        type: 'success',
      })
    )
  } catch (err) {
    const message = (err as Error).message
    dispatch(setNotification({ message, type: 'error' }))
    dispatch(setLoadingState(false))
  }
}

export const acceptRejectConfigCreate = async (
  type: string,
  id: string,
  dispatch: Dispatch,
  comments: string
) => {
  dispatch(setLoadingState(true))
  try {
    await secureapi.put(
      `/backoffice-bff/fee/service-configuration/${type === 'reject' ? 'reject' : 'approve'}/${id}`,
      { comments }
    )

    dispatch(setLoadingState(false))

    dispatch(setLoadingApprovals(true))
    await getApprovals(dispatch, `?channel=DBP&module=Tariffs&page=1&size=10`)
    dispatch(setLoadingApprovals(false))

    dispatch(
      setNotification({
        message: `Request successfully ${type === 'reject' ? 'rejected' : 'approved'}`,
        type: 'success',
      })
    )
  } catch (error) {
    dispatch(setLoadingState(false))
    dispatch(setLoadingApprovals(false))
    dispatch(
      setNotification({
        message: (error as Error).message,
        type: 'error',
      })
    )
  } finally {
    dispatch(setLoadingState(false))
  }
}

export const deactivateServiceConfiguration = async (
  serviceConfigId: string,
  dispatch: Dispatch,
  comments: string,
  type: string
) => {
  try {
    dispatch(setLoadingState(true))
    const httpMethod = type === 'make' ? secureapi.post : secureapi.patch
    const url = `/backoffice-bff/fee/service-configuration/deactivate/${serviceConfigId}/${type === 'make' ? 'make' : ''}`

    await httpMethod(url, {
      comments: comments,
      serviceConfigurationId: serviceConfigId,
    })

    dispatch(setLoadingState(false))
    dispatch(
      setNotification({
        message: `Request  ${type === '' ? ', deactivated' : 'activated'}`,
        type: 'success',
      })
    )
  } catch (error) {
    dispatch(setLoadingState(false))
    const message = (error as Error).message
    dispatch(setNotification({ message, type: 'error' }))
  }
}

export const approveRejectServiceConfig = async (
  serviceConfigId: string,
  comments: string,
  dispatch: Dispatch,
  type: string
) => {
  dispatch(setLoadingState(true))
  try {
    await secureapi.post(
      `/backoffice-bff/fee/service-configuration/deactivate/${type === 'approve' ? 'approve' : 'reject'}/${serviceConfigId}`,
      { comments: comments }
    )
    dispatch(setLoadingState(false))
    dispatch(
      setNotification({
        message: `Service configuration successfully ${type === 'approve' ? 'approved' : 'rejected'}`,
        type: 'success',
      })
    )
  } catch (error) {
    dispatch(setLoadingState(false))
    const message = (error as Error).message
    dispatch(setNotification({ message, type: 'error' }))
  }
}

export const createTariffByName = async (
  dispatch: Dispatch,
  comments: string,
  name: string,
  currency: string
) => {
  try {
    dispatch(setLoadingState(true))
    await secureapi.post('/backoffice-bff/fee/tariffs', { name, comments })
    dispatch(setLoadingState(false))
    dispatch(setTariffIsError(false))
    dispatch(setSelectedTariffState({ name, currency }))
  } catch (error) {
    dispatch(setLoadingState(false))
    dispatch(setTariffIsError(true))
    const message = (error as Error).message
    dispatch(
      setNotification({
        message,
        type: 'error',
      })
    )
    throw error
  }
}

export const updateServiceConfig = async (
  type: string,
  serviceConfigurationId: string,
  data: IConfiguredService,
  dispatch: Dispatch
) => {
  dispatch(setLoadingState(true))
  try {
    await secureapi.post(
      `/backoffice-bff/fee/service-configuration/${serviceConfigurationId}/update/${type === 'make' ? 'make' : ''}`,
      data
    )

    dispatch(setLoadingState(false))
    dispatch(
      setNotification({
        message: `Service configuration updated successfully${type === 'make' ? ', pending approval' : ''} `,
        type: 'success',
      })
    )
  } catch (error) {
    dispatch(setLoadingState(false))
    const message = (error as Error).message
    dispatch(setNotification({ message, type: 'error' }))
  }
}

export const approveUpdateServiceConfiguration = async (
  approvalId: string,
  dispatch: Dispatch,
  comments: string,
  type: string
) => {
  dispatch(setLoadingState(true))
  try {
    await secureapi.put(
      `/backoffice-bff/fee/service-configuration/update/${type === 'approve' ? 'approve' : 'reject'}/${approvalId}`,
      { comments: comments }
    )

    dispatch(setLoadingState(false))
    dispatch(
      setNotification({
        message: `Service configuration successfully ${type === 'approve' ? 'approved' : 'rejected'}`,
        type: 'success',
      })
    )
  } catch (error) {
    dispatch(setLoadingState(false))
    dispatch(
      setNotification({ message: (error as Error).message, type: 'error' })
    )
  }
}
