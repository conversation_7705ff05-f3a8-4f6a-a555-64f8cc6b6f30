export type Status = 'ACTIVE' | 'INACTIVE' | 'PENDING' | 'REJECTED' | 'APPROVED'

export interface ITariffData {
  id: string
  name?: string
  status?: Status
  createdOn?: string
  dateCreated?: string
  actions?: string
  // Add other relevant fields for tariff configuration
}

export interface ITariffCreate {
  id: string
  name?: string
  status?: Status
  createdOn?: string
  dateCreated?: string
  actions?: string
}

export interface TariffDataResponse {
  pageNumber: number
  pageSize: number
  totalNumberOfPages: number
  totalElements: number
  data: ITariffData[]
}

export interface ErrorResponse {
  status: number
  message: string
  error: string
}

export interface IConfigParams {
  serviceCode: string
  currency: string
  countryCode: string
  tariff: string
}

// Configuration item interface
export interface FeeTier {
  id?: string
  createdOn?: string
  modifiedOn?: string
  minimum: number
  maximum: number
  feeAmount: number
}
export interface LimitConfiguration {
  createdOn?: string
  modifiedOn?: string
  dailyLimit?: number
  perTransactionLowerLimit?: number
  perTransactionUpperLimit?: number
  frequencyLimit?: number
}
export interface Service {
  createdOn: string
  modifiedOn: string
  id?: string
  serviceId: string
  serviceCode: string
  serviceName: string
  country: string
  serviceType: 'PAYMENT' | 'NOTIFICATION'
}

export interface FeeConfiguration {
  createdOn: string
  modifiedOn: string
  chargeType: 'Fixed' | 'Tiered'
  value: number
  exciseDuty: number
  feeTiers?: FeeTier[]
  fixedChargeType?: 'Value'
}

export interface IConfigurationItem {
  id?: string
  createdOn: string
  modifiedOn: string
  service: Service
  tariff: string
  currency: string
  feeConfiguration: FeeConfiguration
  limitConfiguration?: LimitConfiguration
  status: Status
}

export interface IConfigurationResponse {
  pageNumber: number
  pageSize: number
  totalNumberOfPages: number
  totalElements: number
  data: IConfigurationItem[]
}

// Single configuration row
export interface IConfigurationRow {
  data: IConfigurationItem[]
  pageNumber: number
  pageSize: number
  totalNumberOfPages: number
  totalElements: number
}

export interface IConfiguredService {
  comments?: string
  serviceId?: string
  tariff?: string
  currency?: string
  name?: string
  feeTiers?: FeeTier[]
  feeConfiguration: IFeeConfig
  limitConfiguration?: LimitConfiguration
}

export interface IConfigurationLogs {
  id: string
  name: string
  maker: string
  checker: string
  actions?: string
  status?: string
}

export interface IConfigurationComparisonData {
  id: string
  field: string
  oldValue: number
  newValue: number
}

export interface AccordionItem {
  title: string
  id: string
}

export interface AccordionState {
  [key: string]: AccordionItem
}

export interface IFeeConfig {
  chargeType?: string
  createdOn?: string
  exciseDuty: number
  fixedChargeType?: string
  feeTiers?: FeeTier[]
  modifiedOn?: string
  value: number
}

export interface IConfigurableService {
  createdOn: string
  currency: string
  modifiedOn: string
  service: Service
  tariff: string
  feeConfiguration: IFeeConfig
}

export interface ICreateServiceConfiguration {
  comments: string
  serviceId: string
  tariff: string
  currency: string
  feeConfiguration: IFeeConfig
  limitConfiguration: {
    dailyLimit: number
    perTransactionLowerLimit: number
    perTransactionUpperLimit: number
    frequencyLimit: number
  }
}
