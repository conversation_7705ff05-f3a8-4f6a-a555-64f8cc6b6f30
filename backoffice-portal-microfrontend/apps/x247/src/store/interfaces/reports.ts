export interface IIFTReportRecord {
  createdOn: string
  currency: string
  destinationAccountNo: string
  email: string
  firstName: string
  lastName: string
  narration: string
  paymentService: string
  phoneNumber: string
  sourceAccountNo: string
  status: string
  transactionRef: string
  transferAmount: number
  eventDate: string
  isBlocked: boolean
}
export interface IRTGSReportRecord {
  cbsReference: string
  createdOn: string
  destinationBankCode: string
  paymentService: string
  status: string
  transactionRef: string
  eventDate: string
  isBlocked: boolean
}
export interface IPesalinkReportRecord {
  createdOn: string
  firstName: string
  lastName: string
  email: string
  phoneNumber: string
  transactionRef: string
  narration: string
  recipientAccountNo: string
  transferAmount: string
  paymentService: string
  status: string
  eventDate: string
  isBlocked: boolean
}
export interface IC2BReportRecord {
  createdOn: string
  transactionRef: string
  narration: string
  paymentService: string
  status: string
  eventDate: string
  isBlocked: boolean
}
export interface IMpesaAccountTopUpReportRecord {
  recipientPhoneNumber: string
  createdOn: string
  transactionRef: string
  narration: string
  paymentService: string
  status: string
  eventDate: string
  isBlocked: boolean
}
export interface IPayBillReportRecord {
  createdOn: string
  firstName: string
  lastName: string
  email: string
  phoneNumber: string
  transactionRef: string
  narration: string
  cbsReference: string
  currency: string
  billerName: string
  amount: string
  status: string
  eventDate: string
  isBlocked: boolean
}
export interface IBuyAirtimeReportRecord {
  createdOn: string
  firstName: string
  lastName: string
  email: string
  phoneNumber: string
  transactionRef: string
  narration: string
  cbsReference: string
  currency: string
  provider: string
  amount: string
  status: string
  eventDate: string
  isBlocked: boolean
}

export interface IProfilePinRecord {
  type: string
  status: string
  attempts: number
}

export interface ICustomerReportRecord {
  initiatorPhoneNumber: string
  initiatorDeviceId: string
  eventType: string
  eventDate: string
  firstName: string
  lastName: string
  email: string
  idNumber: string
  isBlocked: boolean
  status: string
  createdOn: string
  profilePins: IProfilePinRecord[]
  security_questions_status: string
  security_questions_attempts: string
  pin_status: string
  pin_attempts: string
  customerIds?: string[]
}
export interface IAccountReportRecord {
  accountNo: string
  branchCode: string
  currency: string
  status: string
  tariffName: string
  pinSuppress: string
  dateActivated: string
  createdBy: string
}
export interface ICustomerAccountsRecord {
  customerName: string
  mobileNumber: string
  profilePins: IProfilePinRecord[]
  accounts: IAccountReportRecord[]
  dateLastLogin: string
  createdOn: string
  isBlocked: boolean
  eventDate: string
  status: string
}
export interface IDeviceRecordReport {
  deviceOs: string
  deviceId: string
  deviceName: string
  deviceModel: string
  status: string
  channelType: string
  dateCreated: string
  dateModified: string
}
export interface ICustomerDevicesRecord {
  customerName: string
  mobileNumber: string
  devices: IDeviceRecordReport[]
  dateLastLogin: string
  createdOn: string
  isBlocked: boolean
  eventDate: string
}
export interface IProfileAccountStoreIdReport {
  customerId: string
}
export interface ICustomerSecurityStatusRecord {
  customerName: string
  mobileNumber: string
  cardIds?: string
  email: string
  profileAccountStoreIds: IProfileAccountStoreIdReport[]
  idNumber: string
  lastDeviceTypeAccessed: string
  profilePins: IProfilePinRecord[]
  imsis: Imsi[]
  deviceType: string
  createdBy: string
  dateCreated: string
  dateActivated: string
  isBlocked: boolean
}
export interface IPagination {
  pageNumber: number
  pageSize: number
  totalElements: number
  totalNumberOfPages: number
}
export interface IReportFilters {
  [key: string]: string | number | boolean | object
}
export interface IHistoryRecord {
  id: string
  userId: string
  reportName: string
  reportFilters: IReportFilters
  dateCreated: string
  dateModified: string
  createdBy: string
  updatedBy: string
  approvalRequest: string
  userName: string
}
export interface Imsi {
  isActive: boolean
  imsi: string
  lastSwapDate: string
  lastUpdateDate: string
}

export interface IClientChannel {
  clientId: string
  clientName: string
}
export const TRANSACTION_REPORT_TYPES = [
  'ift',
  'rtgs',
  'mpesa/c2b',
  'mpesa/account-top-up',
  'pesalink',
  'buy-airtime',
  'paybill',
] as const

export const CUSTOMER_REPORT_TYPES = [
  'Change_Pin',
  'Reset_Pin',
  'Change_Security_Questions',
  'CUSTOMER_ACCOUNTS',
  'CUSTOMERS',
  'CUSTOMER_DEVICES',
  'CUSTOMER_SECURITY_STATUS',
] as const

export type TransactionReportTypes = (typeof TRANSACTION_REPORT_TYPES)[number]
export type CustomerReportTypes = (typeof CUSTOMER_REPORT_TYPES)[number]
