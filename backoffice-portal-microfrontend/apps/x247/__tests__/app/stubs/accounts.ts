import {
  type ICustomerAccount,
  ICustomerProfileAccount,
  INotificationEventsPerAccount,
} from '@/store/interfaces'

/**
 * Creates a stub customer profile account with default values
 * @param overrides Optional partial object to override default values
 * @returns A complete ICustomerProfileAccount object
 */
export function mockCustomerProfileAccount(
  overrides?: Partial<ICustomerProfileAccount>
): ICustomerProfileAccount {
  const defaultStub: ICustomerProfileAccount = {
    profileId: 'profile-123',
    id: {
      storeCode: 'STORE001',
      description: 'Main Store Account',
      profileId: 'profile-123',
      accountType: 'SAVINGS',
      accountNo: '**********',
    },
    profile: {
      id: 'profile-123',
      firstName: 'John',
      otherNames: '',
      lastName: 'Doe',
      email: '<EMAIL>',
      phoneNumber: '+**********',
      dateCreated: new Date().toISOString(),
      dateModified: new Date().toISOString(),
    },
    storeCode: 'STORE001',
    description: 'Main Store Account',
    accountNo: '**********',
    accountType: 'SAVINGS',
    mandate: 'SINGLE',
    isMobileLinked: true,
    fullName: 'John Doe',
    shortName: 'J. Doe',
    branchCode: 'BR001',
    currency: 'USD',
    isDormant: false,
    isBlocked: false,
    isFrozen: false,
    isNoDebit: false,
    isNoCredit: false,
    isStopPay: false,
    status: 'ACTIVE',
    tariffName: 'STANDARD',
    dateCreated: new Date().toISOString(),
    dateModified: new Date().toISOString(),
  }

  return { ...defaultStub, ...overrides }
}

export const mockTransactionAccount: ICustomerAccount = {
  accNumber: '**********',
  accOpenDate: '2023-01-01T12:00:00Z',
  customerType: 'Individual',
  customerCategory: 'Regular',
  accBranchCode: 'BR001',
  accClass: 'SAV',
  accClassDesc: 'Savings Account',
  accCurrency: 'USD',
  accDormant: 'N',
  accStatus: 'ACTIVE',
  accRecordStatus: 'A',
  accStatBlock: 'N',
  accFrozen: 'N',
  accNoDebit: 'N',
  accNoCredit: 'N',
  accStopPay: 'N',
  jointAccIndicator: 'N',
  customerRecordStatus: 'A',
  accountClass: 'Savings',
  isMobileLinkable: true,
  tariffName: 'Standard',
}

// Create mock notification data
export const mockNotifications: INotificationEventsPerAccount[] = [
  {
    accountSource: 'Core Banking',
    accountId: '**********',
    alertFrequency: {
      id: 'freq1',
      name: 'Daily Frequency',
      interval: 1,
      frequencyType: 'DAILY',
      description: 'Sends alerts daily',
    },
    thresholdAmount: 1000,
    optedInDate: '2023-01-01T12:00:00Z',
    status: 'active',
    numberOfSends: 0,
    event: {
      id: 'event1',
      eventType: 'BALANCE_ALERT',
      eventName: 'Account Balance Alert event',
      platform: null,
    },
    subscribers: [
      {
        id: 'sub1',
        recipient: '+**********',
        deliveryMode: 'SMS',
      },
      {
        id: 'sub2',
        recipient: '<EMAIL>',
        deliveryMode: 'EMAIL',
      },
    ],
  },
]

export const mockSubscriptions: INotificationEventsPerAccount[] = [
  {
    accountSource: 'Core Banking',
    accountId: '**********',
    alertFrequency: {
      id: 'freq2',
      name: 'Monthly Frequency',
      interval: 30,
      frequencyType: 'MONTHLY',
      description: 'Sends statements monthly',
    },
    thresholdAmount: 0,
    optedInDate: '2023-01-01T12:00:00Z',
    status: 'active',
    numberOfSends: 0,
    event: {
      id: 'event2',
      eventType: 'ACCOUNT_STATEMENT',
      eventName: 'Account Statement event',
      platform: null,
    },
    subscribers: [
      {
        id: 'sub3',
        recipient: '<EMAIL>',
        deliveryMode: 'EMAIL',
      },
    ],
  },
]
