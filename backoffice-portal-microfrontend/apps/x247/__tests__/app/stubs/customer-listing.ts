import {
  ICustomer,
  StoreOfValue,
  IprofileAccountStoreIds,
  IProfileApprovalRequests,
} from '@/store/interfaces'
import { IApprovalRequest, IDiffValues } from '@dtbx/store/utils'

/**
 * Generate a mock store of value
 * @param customerId - The customer ID
 * @returns A mock StoreOfValue object
 */
export const generateMockStoreOfValue = (customerId: string): StoreOfValue => ({
  storeCode: `STORE-${Math.floor(Math.random() * 1000)}`,
  customerId,
})

/**
 * Generate a mock profile account store ID
 * @param profileId - The profile ID
 * @param customerId - The customer ID
 * @returns A mock IprofileAccountStoreIds object
 */
export const generateMockProfileAccountStoreId = (
  profileId: string,
  customerId: string
): IprofileAccountStoreIds => ({
  profileId,
  storeCode: `STORE-${Math.floor(Math.random() * 1000)}`,
  description: `Store description ${Math.floor(Math.random() * 100)}`,
  customerId,
})

/**
 * Generate a random date string within the last year
 * @returns A date string in ISO format
 */
export const generateRandomDate = (): string => {
  const now = new Date()
  const pastDate = new Date(
    now.getFullYear() - Math.floor(Math.random() * 2),
    Math.floor(Math.random() * 12),
    Math.floor(Math.random() * 28) + 1
  )
  return pastDate.toISOString()
}

/**
 * Generate a mock customer
 * @param index - The index of the customer (used to generate unique IDs)
 * @param overrides - Optional properties to override in the generated customer
 * @returns A mock ICustomer object
 */
export const generateMockCustomer = (
  index: number,
  overrides: Partial<ICustomer> = {}
): ICustomer => {
  const id = `customer-${index}`
  const firstName = [
    'John',
    'Jane',
    'Bob',
    'Alice',
    'Michael',
    'Sarah',
    'David',
    'Emma',
  ][index % 8]
  const lastName = [
    'Smith',
    'Johnson',
    'Williams',
    'Jones',
    'Brown',
    'Davis',
    'Miller',
    'Wilson',
  ][index % 8]
  const dateCreated = generateRandomDate()
  // Updated to use 'Not_Set' instead of null
  const pinStatusOptions = [
    'Active_PIN',
    'One_Time_PIN',
    'Locked_PIN',
    'Not_Set',
  ]
  const customer: ICustomer = {
    id,
    blockReason: '',
    country: [
      'USA',
      'UK',
      'Canada',
      'Australia',
      'Germany',
      'France',
      'Spain',
      'Italy',
    ][index % 8],
    firstName,
    otherNames: index % 3 === 0 ? 'Middle' : '',
    lastName,
    phoneNumber: `+${Math.floor(Math.random() * 9) + 1}${Math.floor(
      Math.random() * ***********
    )}`,
    accountNumber: `ACC${Math.floor(Math.random() * 1000000)}`,
    email:
      index % 5 === 0
        ? ''
        : `${firstName.toLowerCase()}.${lastName.toLowerCase()}@example.com`,
    nationality: [
      'American',
      'British',
      'Canadian',
      'Australian',
      'German',
      'French',
      'Spanish',
      'Italian',
    ][index % 8],
    idNumber:
      index % 4 === 0 ? 'null' : `ID${Math.floor(Math.random() * 1000000)}`,
    idType: ['PASSPORT', 'NATIONAL_ID', 'DRIVERS_LICENSE', 'SOCIAL_SECURITY'][
      index % 4
    ],
    onboardingType: ['ONLINE', 'BRANCH', 'AGENT', 'PARTNER'][index % 4],
    pinStatus: pinStatusOptions[index % 4],
    isBlocked: index % 5 === 0,
    dateCreated,
    dateModified: dateCreated,
    sex: ['M', 'F'][index % 2],
    postalAddress:
      index % 3 === 0 ? '' : `P.O. Box ${Math.floor(Math.random() * 10000)}`,
    storeOfValues: Array(Math.floor(Math.random() * 3) + 1)
      .fill(null)
      .map(() => generateMockStoreOfValue(id)),
    profileAccountStoreIds: Array(Math.floor(Math.random() * 3) + 1)
      .fill(null)
      .map(() => generateMockProfileAccountStoreId(id, id)),
  }

  return {
    ...customer,
    ...overrides,
  }
}

/**
 * Generate a list of mock customers
 * @param count - The number of customers to generate
 * @param overrides - Optional array of property overrides for each customer
 * @returns An array of mock ICustomer objects
 */
export const generateMockCustomers = (
  count: number = 10,
  overrides: Partial<ICustomer>[] = []
): ICustomer[] => {
  return Array(count)
    .fill(null)
    .map((_, index) => generateMockCustomer(index, overrides[index] || {}))
}

/**
 * Generate a mock diff value
 * @param field - The field name
 * @param oldValue - The old value
 * @param newValue - The new value
 * @returns A mock IDiffValues object
 */
export const generateMockDiffValue = (
  field: string,
  oldValue: string | IDiffValues[],
  newValue: string | IDiffValues[]
): IDiffValues => ({
  field,
  name:
    field.charAt(0).toUpperCase() + field.slice(1).replace(/([A-Z])/g, ' $1'),
  oldValue,
  newValue,
})

/**
 * Generate a mock approval request
 * @param index - The index of the request (used to generate unique IDs)
 * @param profileId - The profile ID associated with the request
 * @param overrides - Optional properties to override in the generated request
 * @returns A mock IApprovalRequest object
 */
export const generateMockApprovalRequest = (
  index: number,
  profileId: string,
  overrides: Partial<IApprovalRequest> = {}
): IApprovalRequest => {
  const id = `request-${index}`
  const dateCreated = generateRandomDate()
  const requestTypes = [
    'PIN_RESET',
    'ACCOUNT_ACTIVATION',
    'PROFILE_UPDATE',
    'LIMIT_CHANGE',
    'BLOCK_CUSTOMER',
    'UNBLOCK_CUSTOMER',
  ]
  const requestType = requestTypes[index % requestTypes.length]

  // Generate different diff values based on request type
  let diffValues: IDiffValues[] = []

  switch (requestType) {
    case 'PIN_RESET':
      diffValues = [
        generateMockDiffValue('pinStatus', 'Locked_PIN', 'One_Time_PIN'),
      ]
      break
    case 'ACCOUNT_ACTIVATION':
      diffValues = [
        generateMockDiffValue('isBlocked', 'true', 'false'),
        generateMockDiffValue('blockReason', 'Inactive account', ''),
      ]
      break
    case 'PROFILE_UPDATE':
      diffValues = [
        generateMockDiffValue('phoneNumber', '+**********', '+**********'),
        generateMockDiffValue(
          'email',
          '<EMAIL>',
          '<EMAIL>'
        ),
        generateMockDiffValue('postalAddress', 'Old Address', 'New Address'),
      ]
      break
    case 'LIMIT_CHANGE':
      diffValues = [
        generateMockDiffValue('transactionLimit', '1000', '5000'),
        generateMockDiffValue('dailyLimit', '5000', '10000'),
      ]
      break
    case 'BLOCK_CUSTOMER':
      diffValues = [
        generateMockDiffValue('isBlocked', 'false', 'true'),
        generateMockDiffValue('blockReason', '', 'Suspicious activity'),
      ]
      break
    case 'UNBLOCK_CUSTOMER':
      diffValues = [
        generateMockDiffValue('isBlocked', 'true', 'false'),
        generateMockDiffValue('blockReason', 'Suspicious activity', ''),
      ]
      break
    default:
      diffValues = [generateMockDiffValue('someField', 'oldValue', 'newValue')]
  }

  const request: IApprovalRequest = {
    id,
    maker: `maker-${Math.floor(Math.random() * 1000)}`,
    dateCreated,
    dateModified: dateCreated,
    makerCheckerType: {
      channel: 'WEB',
      checkerPermissions: ['APPROVE_CUSTOMERS', 'REJECT_CUSTOMERS'],
      description: `${requestType} approval request`,
      makerPermissions: ['CREATE_CUSTOMERS', 'EDIT_CUSTOMERS'],
      module: 'CUSTOMERS',
      name: requestType,
      overridePermissions: ['SUPER_APPROVE_CUSTOMERS'],
      type: requestType,
    },
    entityId: profileId,
    entity: 'CUSTOMER',
    diff: diffValues,
    makerComments: `Request to ${requestType.toLowerCase().replace(/_/g, ' ')}`,
    status: ['PENDING', 'APPROVED', 'REJECTED'][index % 3],
  }

  // Add checker details if the request is not pending
  if (request.status !== 'PENDING') {
    request.checker = `checker-${Math.floor(Math.random() * 1000)}`
    request.checkerComments =
      request.status === 'APPROVED'
        ? 'Approved after verification'
        : 'Rejected due to incomplete information'
  }

  return {
    ...request,
    ...overrides,
  }
}

/**
 * Generate a list of mock approval requests for a profile
 * @param profileId - The profile ID
 * @param count - The number of requests to generate
 * @param overrides - Optional array of property overrides for each request
 * @returns A mock IProfileApprovalRequests object
 */
export const generateMockProfileApprovalRequests = (
  profileId: string,
  count: number = 2,
  overrides: Partial<IApprovalRequest>[] = []
): IProfileApprovalRequests => {
  return {
    profileId,
    requests: Array(count)
      .fill(null)
      .map((_, index) =>
        generateMockApprovalRequest(index, profileId, overrides[index] || {})
      ),
  }
}

/**
 * Generate a list of mock profile approval requests for multiple profiles
 * @param profileIds - Array of profile IDs
 * @param requestsPerProfile - Number of requests to generate per profile
 * @returns An array of mock IProfileApprovalRequests objects
 */
export const generateMockCustomerApprovals = (
  profileIds: string[],
  requestsPerProfile: number = 1
): IProfileApprovalRequests[] => {
  return profileIds.map((profileId) =>
    generateMockProfileApprovalRequests(profileId, requestsPerProfile)
  )
}

/**
 * Predefined mock customers for common test scenarios
 */
export const mockCustomers = {
  // Standard set of customers with various states
  standard: generateMockCustomers(5),

  // Customer with pending approval
  withPendingApproval: generateMockCustomer(100, {
    id: 'customer-with-pending-approval',
    firstName: 'Pending',
    lastName: 'Approval',
  }),

  // Blocked customer
  blocked: generateMockCustomer(101, {
    id: 'blocked-customer',
    firstName: 'Blocked',
    lastName: 'User',
    isBlocked: true,
    blockReason: 'Suspicious activity',
  }),

  // Customer with one-time PIN
  oneTimePin: generateMockCustomer(102, {
    id: 'one-time-pin-customer',
    firstName: 'Onetime',
    lastName: 'Pin',
    pinStatus: 'One_Time_PIN',
  }),
  // Customer with Not_Set pin status
  notSetPin: generateMockCustomer(103, {
    id: 'not-set-pin-customer',
    firstName: 'Notset',
    lastName: 'Pin',
    pinStatus: 'Not_Set',
  }),
  // Customer with missing data
  incomplete: generateMockCustomer(104, {
    id: 'incomplete-customer',
    firstName: 'Incomplete',
    lastName: 'Data',
    email: '',
    idNumber: '',
    postalAddress: '',
    pinStatus: 'Not_Set',
  }),

  // Customer with many store values
  manyStores: generateMockCustomer(105, {
    id: 'many-stores-customer',
    firstName: 'Many',
    lastName: 'Stores',
    storeOfValues: Array(5)
      .fill(null)
      .map((_, i) => ({
        storeCode: `STORE-${i}`,
        customerId: 'many-stores-customer',
      })),
  }),
  //active customer
  active: generateMockCustomer(106, {
    id: 'active-customer',
    firstName: 'Active',
    lastName: 'User',
    isBlocked: false,
  }),
}

/**
 * Predefined mock approval requests for common test scenarios
 */
export const mockApprovalRequests = {
  // Standard pending approval request
  pendingPinReset: generateMockApprovalRequest(0, 'customer-100', {
    status: 'PENDING',
    makerCheckerType: {
      channel: 'WEB',
      checkerPermissions: ['APPROVE_CUSTOMERS', 'REJECT_CUSTOMERS'],
      description: 'PIN reset approval request',
      makerPermissions: ['CREATE_CUSTOMERS', 'EDIT_CUSTOMERS'],
      module: 'CUSTOMERS',
      name: 'PIN_RESET',
      overridePermissions: ['SUPER_APPROVE_CUSTOMERS'],
      type: 'PIN_RESET',
    },
    diff: [generateMockDiffValue('pinStatus', 'Locked_PIN', 'One_Time_PIN')],
  }),

  // Approved profile update request
  approvedProfileUpdate: generateMockApprovalRequest(1, 'customer-101', {
    status: 'APPROVED',
    checker: 'checker-123',
    checkerComments: 'Approved after verification',
    makerCheckerType: {
      channel: 'WEB',
      checkerPermissions: ['APPROVE_CUSTOMERS', 'REJECT_CUSTOMERS'],
      description: 'Profile update approval request',
      makerPermissions: ['CREATE_CUSTOMERS', 'EDIT_CUSTOMERS'],
      module: 'CUSTOMERS',
      name: 'PROFILE_UPDATE',
      overridePermissions: ['SUPER_APPROVE_CUSTOMERS'],
      type: 'PROFILE_UPDATE',
    },
    diff: [
      generateMockDiffValue('phoneNumber', '+**********', '+**********'),
      generateMockDiffValue(
        'email',
        '<EMAIL>',
        '<EMAIL>'
      ),
    ],
  }),

  // Rejected block customer request
  rejectedBlockCustomer: generateMockApprovalRequest(2, 'customer-102', {
    status: 'REJECTED',
    checker: 'checker-456',
    checkerComments: 'Rejected due to insufficient evidence',
    makerCheckerType: {
      channel: 'WEB',
      checkerPermissions: ['APPROVE_CUSTOMERS', 'REJECT_CUSTOMERS'],
      description: 'Block customer approval request',
      makerPermissions: ['CREATE_CUSTOMERS', 'EDIT_CUSTOMERS'],
      module: 'CUSTOMERS',
      name: 'BLOCK_CUSTOMER',
      overridePermissions: ['SUPER_APPROVE_CUSTOMERS'],
      type: 'BLOCK_CUSTOMER',
    },
    diff: [
      generateMockDiffValue('isBlocked', 'false', 'true'),
      generateMockDiffValue('blockReason', '', 'Suspicious activity'),
    ],
  }),

  // Multiple approval requests for a single customer
  multipleRequests: {
    profileId: 'customer-103',
    requests: [
      generateMockApprovalRequest(3, 'customer-103', {
        status: 'PENDING',
        makerCheckerType: {
          type: 'PIN_RESET',
          name: 'PIN_RESET',
          module: 'CUSTOMERS',
          channel: 'WEB',
          checkerPermissions: ['APPROVE_CUSTOMERS'],
          makerPermissions: ['EDIT_CUSTOMERS'],
          overridePermissions: [],
        },
        diff: [
          generateMockDiffValue('pinStatus', 'Locked_PIN', 'One_Time_PIN'),
        ],
      }),
      generateMockApprovalRequest(4, 'customer-103', {
        status: 'PENDING',
        makerCheckerType: {
          type: 'PROFILE_UPDATE',
          name: 'PROFILE_UPDATE',
          module: 'CUSTOMERS',
          channel: 'WEB',
          checkerPermissions: ['APPROVE_CUSTOMERS'],
          makerPermissions: ['EDIT_CUSTOMERS'],
          overridePermissions: [],
        },
        diff: [
          generateMockDiffValue('phoneNumber', '+**********', '+**********'),
        ],
      }),
    ],
  },
}

export default generateMockCustomers
