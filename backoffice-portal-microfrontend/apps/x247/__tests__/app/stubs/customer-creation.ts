import type {
  ICustomerAccountDetails,
  ICustomerAccount,
} from '@/store/interfaces'

export const mockCustomerCreation = (
  overrides = {}
): ICustomerAccountDetails => {
  const defaultCustomerAccount: ICustomerAccount = {
    accNumber: 'ACC123456789',
    accOpenDate: '2023-01-01',
    customerType: 'Individual',
    customerCategory: 'Standard',
    accBranchCode: 'BR001',
    accClass: 'Savings',
    accClassDesc: 'Standard Savings Account',
    accCurrency: 'USD',
    accDormant: 'N',
    accStatus: 'Active',
    accRecordStatus: 'A',
    accStatBlock: 'N',
    accFrozen: 'N',
    accNoDebit: 'N',
    accNoCredit: 'N',
    accStopPay: 'N',
    jointAccIndicator: 'N',
    customerRecordStatus: 'A',
    accountClass: 'Savings',
    isMobileLinkable: true,
    tariffName: 'Standard',
  }

  const defaultCustomer: ICustomerAccountDetails = {
    firstName: 'John',
    lastName: 'Doe',
    email: '<EMAIL>',
    phoneNumber: '+**********',
    postalAddress: '123 Postal St, City, Country',
    gender: 'Male',
    idType: 'Passport',
    idValue: 'AB123456',
    cif: 'CIF123456',
    physicalAddress: '123 Main St, City, Country',
    country: 'United States',
    customerPrefix: 'Mr',
    dateOfBirth: '1990-01-01',
    nationality: 'American',
    customerCategory: 'Standard',
    customerType: 'Individual',
    isM247Customer: false,
    customerAccounts: [defaultCustomerAccount],
    comments: 'Test comments',
  }

  return {
    ...defaultCustomer,
    ...overrides,
  }
}
export const mockCustomerSearchByIdData = () => ({
  firstName: 'John',
  lastName: 'Doe',
  email: '<EMAIL>',
  phoneNumber: '+**********',
  gender: 'M',
  idType: 'ID_NO',
  idValue: 'AB123456',
  cif: 'CIF123456',
  postalAddress: '123 Postal St',
  physicalAddress: '123 Main St',
  isM247Customer: false,
})
