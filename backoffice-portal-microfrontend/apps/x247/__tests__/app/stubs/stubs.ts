import {
  ICustomer,
  ICustomerAccount,
  ICustomerProfileAccount,
  IDevice,
} from '@dtbx/store/interfaces'
import { IDiffValues } from '@dtbx/store/utils'
import { IApprovalRequest } from '@dtbx/store/utils'

export const mockCustomerStub: ICustomer = {
  id: '123',
  blockReason: 'test reason',
  country: 'test country',
  firstName: 'John',
  otherNames: 'Dee',
  lastName: 'Doe',
  phoneNumber: '**********',
  accountNumber: '***********',
  email: '<EMAIL>',
  nationality: 'Kenya',
  idNumber: '*********',
  idType: 'Passport',
  onboardingType: 'test type',
  isBlocked: false,
  dateCreated: '2023-01-01',
  dateModified: '2023-02-01',
  sex: 'Male',
  storeOfValues: [],
  profileAccountStoreIds: [],
}
export const mockCustomerAccount: ICustomerAccount = {
  accNumber: '************',
  accOpenDate: '2023-01-01',
  customerType: 'test type',
  customerCategory: 'string',
  accBranchCode: 'string',
  accClass: 'string',
  accClassDesc: 'string',
  accCurrency: 'string',
  accDormant: 'string',
  accStatus: 'string',
  accRecordStatus: 'string',
  accStatBlock: 'string',
  accFrozen: 'string',
  accNoDebit: 'string,',
  accNoCredit: 'string',
  accStopPay: 'string',
  jointAccIndicator: 'string',
  customerRecordStatus: 'string',
  accountClass: 'string',
  isMobileLinkable: false,
  tariffName: 'staff',
}

export const mockCustomerProfileAccount: ICustomerProfileAccount = {
  profileId: 'string',
  id: {
    storeCode: 'string',
    description: 'string',
    profileId: 'string',
    accountType: 'string',
    accountNo: 'string',
  },
  profile: {
    id: 'string',
    firstName: 'string',
    otherNames: 'string',
    lastName: 'string',
    email: 'string',
    phoneNumber: 'string',
    dateCreated: 'string',
    dateModified: 'string',
  },
  storeCode: 'string',
  description: 'string',
  accountNo: 'string',
  accountType: 'string',
  mandate: 'string',
  isMobileLinked: false,
  fullName: 'string',
  shortName: 'string',
  branchCode: 'string',
  currency: 'string',
  isDormant: true,
  isBlocked: true,
  isFrozen: true,
  isNoDebit: true,
  isNoCredit: true,
  isStopPay: true,
  status: 'string',
  tariffName: 'string',
  dateCreated: 'string',
  dateModified: 'string',
}
export const mockDevice: IDevice = {
  deviceId: 'device-123',
  deviceType: 'Mobile',
  deviceName: 'Primary',
  deviceStatus: 'blocked',
  deviceModel: 'Samsung',
  uuid: 'a695fe20-fc53-4042-a57f-e68be4f597e3',
  dateCreated: new Date('2024-07-20'),
  devicePlatform: 'Android',
  phoneNumber: '**********',
}
export const mockActiveDevice: IDevice = {
  deviceId: 'device-123',
  deviceType: 'Mobile',
  deviceName: 'iPhone 12',
  deviceStatus: 'ACTIVE',
  deviceModel: 'Samsung',
  uuid: 'a695fe20-fc53-4042-a57f-e68be4f597e3',
  dateCreated: new Date('2024-07-20'),
  devicePlatform: 'Android',
  phoneNumber: '**********',
}
export const mockApprovalRequest: IApprovalRequest = {
  checker: 'Leroy',
  checkerComments: 'test approval checker comment',
  id: 'a695fe20-fc53-4042-a57f-e68be4f597e3',
  maker: 'Leroy',
  dateCreated: '2024-07-20',
  dateModified: '2024-07-20',
  makerCheckerType: {
    channel: 'USSD',
    checkerPermissions: ['ACCEPT_CREATE_USER'],
    description: 'test description',
    makerPermissions: ['MAKE_CREATE_USER'],
    module: 'Users',
    name: 'Create Users',
    overridePermissions: ['SUPER_CREATE_USER'],
    type: 'CREATE_USER',
  },
  entityId: 'a695fe20-fc53-4042-a57f-e68be4f597e3',
  entity: 'users',
  diff: [
    {
      field: 'pin',
      oldValue: '',
      newValue: '',
    },
  ],
  makerComments: 'test maker comments',
  status: 'PENDING',
}
