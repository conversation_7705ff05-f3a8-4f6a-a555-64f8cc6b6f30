import { render, screen, within } from '@testing-library/react'
import { expect, describe, it } from 'vitest'

import { AllEntityPreview } from '@/app/approval-requests/All/Dialog/EntityPreview'
import { mockCustomerStub } from '__tests__/app/stubs/stubs'

const mockCustomer = mockCustomerStub

const mockKeys = Object.keys(mockCustomer)

describe('AllEntityPreview Component', () => {
  it('renders the component without errors', () => {
    //@ts-ignore
    render(<AllEntityPreview entity={mockCustomer} keys={mockKeys} />)
    expect(screen.getByRole('table')).toBeInTheDocument()
  })

  it('renders table rows dynamically based on keys', () => {
    //@ts-ignore
    render(<AllEntityPreview entity={mockCustomer} keys={mockKeys} />)

    const rows = screen.getAllByRole('row')

    mockKeys.forEach((key, index) => {
      const cells = within(rows[index]).getAllByRole('cell')
      const keyCell = cells[0]
      const valueCell = cells[1]

      const rawValue = mockCustomer[key as keyof typeof mockCustomer]

      let expectedText: string
      if (rawValue === false || rawValue === null || rawValue === undefined) {
        expectedText = ''
      } else if (Array.isArray(rawValue)) {
        expectedText = rawValue.join(', ')
      } else {
        expectedText = String(rawValue)
      }

      expect(keyCell).toHaveTextContent(key)
      expect(valueCell.textContent).toBe(expectedText)
    })
  })

  it('handles missing or undefined values gracefully', () => {
    const entityWithMissingValues = {
      id: '123',
      name: undefined,
      email: null,
    }
    const keys = ['id', 'name', 'email']

    //@ts-ignore
    render(<AllEntityPreview entity={entityWithMissingValues} keys={keys} />)

    const rows = screen.getAllByRole('row')

    keys.forEach((key, index) => {
      const cells = within(rows[index]).getAllByRole('cell')
      const keyCell = cells[0]
      const valueCell = cells[1]

      const rawValue =
        entityWithMissingValues[key as keyof typeof entityWithMissingValues]
      const expectedText =
        rawValue === null || rawValue === undefined
          ? ''
          : Array.isArray(rawValue)
            ? rawValue.join(', ')
            : String(rawValue)

      expect(keyCell).toHaveTextContent(key)
      expect(valueCell.textContent).toBe(expectedText)
    })
  })

  it('renders nothing when keys array is empty', () => {
    //@ts-ignore
    render(<AllEntityPreview entity={mockCustomer} keys={[]} />)

    const table = screen.getByRole('table')
    expect(table).toBeInTheDocument()
    const rows = screen.queryAllByRole('row')
    expect(rows.length).toBe(0)
  })

  it('applies the correct styling to the table container', () => {
    //@ts-ignore
    render(<AllEntityPreview entity={mockCustomer} keys={mockKeys} />)
    const tableContainer = screen.getByRole('table').parentElement

    expect(tableContainer).toHaveStyle('border: 1px solid #D0D5DD')
  })

  it('supports different entity types', () => {
    const mockDevice = {
      deviceId: 'device-456',
      model: 'iPhone 14',
      status: 'active',
    }

    const deviceKeys = ['deviceId', 'model', 'status']
    //@ts-ignore
    render(<AllEntityPreview entity={mockDevice} keys={deviceKeys} />)

    deviceKeys.forEach((key) => {
      expect(screen.getByText(key)).toBeInTheDocument()
    })

    deviceKeys.forEach((key) => {
      //@ts-ignore
      expect(screen.getByText(mockDevice[key])).toBeInTheDocument()
    })
  })
})
