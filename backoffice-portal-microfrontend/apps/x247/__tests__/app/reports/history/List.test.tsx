import { describe, it, expect, vi, beforeEach } from 'vitest'
import { screen } from '@testing-library/react'
import { render } from '__tests__/test-utils'
import { ReportsHistoryList } from '@/app/reports/history/List'

describe('ReportsHistoryList component', () => {
  const mockInitialState = {
    reports: {
      reportHistoryList: [],
      reportHistoryPaginationData: {
        pageNumber: 1,
        pageSize: 10,
        totalNumberOfPages: 1,
        totalElements: 0,
      },
    },
  }
  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('renders the component without errors', async () => {
    render(<ReportsHistoryList searchValue={'mockSearchValue'} />)
    const bodyText = await screen.findByText(/Selected Saved Reports/i)
    expect(bodyText).toBeInTheDocument()
  })
  it('renders correctly with empty report list', () => {
    // render(
    //   <ReportsHistoryList searchValue={''} />,
    //   {initialState =  mockInitialState })
    // )
    render(<ReportsHistoryList searchValue={''} />, {
      initialState: mockInitialState,
    })
  })
  it.skip('renders report rows when data is provided', async () => {
    const mockState = {
      reports: {
        ...mockInitialState.reports,
        reportHistoryList: [
          {
            id: '1',
            userName: 'Leroy',
            reportName: 'Transaction Reports',
            dateCreated: '2023-01-01T00:00:00Z',
            reportFilters: { region: 'North' },
          },
        ],
        reportHistoryPaginationData: {
          ...mockInitialState.reports.reportHistoryPaginationData,
          totalElements: 1,
        },
      },
    }

    render(<ReportsHistoryList searchValue={''} />, { initialState: mockState })

    expect(await screen.findByText('Transaction Reports')).toBeInTheDocument()
    expect(screen.getByText('Test User')).toBeInTheDocument()
    expect(
      screen.getByText('formatted:2023-01-01T00:00:00Z')
    ).toBeInTheDocument()
    expect(screen.getByText('region')).toBeInTheDocument()
    expect(screen.getByText('North')).toBeInTheDocument()
  })
  it('calls handleExport on Export button click', () => {})
  it('calls getReportsHistory on page change', () => {})
  it('displays object-type filter values as JSON string', () => {})
  it('selects a row on click', () => {})
  it("renders FiltersCell with no filters (doesn't break when filters is empty)", () => {})
})
