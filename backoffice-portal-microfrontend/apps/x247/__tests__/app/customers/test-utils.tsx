import { NextAppDirEmotionCacheProvider, ThemeConfig } from '@dtbx/ui/theme'
import React, { ReactNode } from 'react'
import { render, RenderOptions } from '@testing-library/react'
import { AppStore, RootState, setupStore } from '@/store'
import { Provider } from 'react-redux'
import { vi } from 'vitest'

// Create router mocks
const baseRouterMock = {
  push: vi.fn(),
  replace: vi.fn(),
  prefetch: vi.fn(),
  back: vi.fn(),
  forward: vi.fn(),
  refresh: vi.fn(),
  pathname: '/customers',
}

const customRouterMock = {
  ...baseRouterMock,
  pushWithTrailingSlash: vi.fn((url) => {
    const urlWithTrailingSlash = url.endsWith('/') ? url : `${url}/`
    baseRouterMock.push(urlWithTrailingSlash)
  }),
}

// Mock Next.js navigation
vi.mock('next/navigation', () => ({
  useRouter: () => baseRouterMock,
  usePathname: () => '/customers',
  useSearchParams: () => new URLSearchParams(),
  useParams: () => ({}),
  // Add the missing function
  useServerInsertedHTML: vi.fn((callback) => callback()),
}))

// Mock the CustomRouter hook with the CORRECT PATH
vi.mock('@dtbx/ui/hooks', () => ({
  useCustomRouter: () => customRouterMock,
}))
//TODO: Add the missing store items
const mockStore = {
  getState: vi.fn(() => ({
    customers: {
      customersResponse: {
        data: [],
        pageNumber: 1,
        totalNumberOfPages: 0,
      },
      isCustomersLoading: false,
      isCustomersSuccess: true,
    },
    approvalRequests: {
      customersWithPendingApprovals: [],
    },
  })),
  dispatch: vi.fn(),
  subscribe: vi.fn(),
}
vi.mock('@/store', () => ({
  useAppDispatch: vi.fn(() => vi.fn()),
  useAppSelector: vi.fn((selector) => selector(mockStore.getState())),
  setupStore: vi.fn(() => mockStore),
}))

interface ExtendedRenderOptions
  extends Omit<RenderOptions, 'queries' | 'wrapper'> {
  preloadedState?: Partial<RootState>
  store?: AppStore
}

const renderWithProviders = (
  ui: ReactNode,
  {
    preloadedState = {},
    store = setupStore(mockStore),
    ...renderOptions
  }: ExtendedRenderOptions = {}
) => {
  const Wrapper = ({ children }: { children: ReactNode }) => {
    return (
      <Provider store={store}>
        <NextAppDirEmotionCacheProvider options={{ key: 'mui' }}>
          <ThemeConfig themeType={'main'}>{children}</ThemeConfig>
        </NextAppDirEmotionCacheProvider>
      </Provider>
    )
  }
  return render(ui, { wrapper: Wrapper, ...renderOptions })
}

export * from '@testing-library/react'
export { renderWithProviders as render, customRouterMock, baseRouterMock }
