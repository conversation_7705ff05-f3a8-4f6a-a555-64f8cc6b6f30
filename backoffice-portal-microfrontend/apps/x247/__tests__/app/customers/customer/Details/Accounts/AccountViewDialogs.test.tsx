import { describe, it, expect, beforeEach, vi } from 'vitest'
import { screen, waitFor, fireEvent, render } from '../../../test-utils'
import type React from 'react'
import {
  AddNotificationDialog,
  AddSubscriptionsDialog,
} from '@/app/customers/customer/Details/Accounts/AccountViewDialogs'
import { useAppDispatch, useAppSelector } from '@/store'
import { mockCustomers } from '../../../../stubs/customer-listing'
vi.mock('@/store', () => ({
  useAppSelector: vi.fn(),
  useAppDispatch: vi.fn(),
}))
const accountsState = {
  customers: {
    customer: mockCustomers.active,
  },
}
const mockDispatch = vi.fn()
describe('AddNotificationDialog', () => {
  beforeEach(() => {
    // Reset mocks before each test
    vi.clearAllMocks()

    // Set up default mock for useAppDispatch
    vi.mocked(useAppDispatch).mockReturnValue(mockDispatch)
    // Set up default mock for useAppSelector
    vi.mocked(useAppSelector).mockImplementation((selector) =>
      selector(accountsState as any)
    )
  })
  it.skip('Should render addNotificationDialog', () => {
    render(<AddNotificationDialog />)

    expect(screen.getByTestId('add-notification-button')).toBeInTheDocument()
  })
})
describe('AddSubscriptionsDialog', () => {
  beforeEach(() => {
    // Reset mocks before each test
    vi.clearAllMocks()

    // Set up default mock for useAppDispatch
    vi.mocked(useAppDispatch).mockReturnValue(mockDispatch)
    // Set up default mock for useAppSelector
    vi.mocked(useAppSelector).mockImplementation((selector) =>
      selector(accountsState as any)
    )
  })
  it.skip('Should render addSubscriptionsDialog', () => {
    render(<AddSubscriptionsDialog />)

    expect(screen.getByTestId('add-subscription-button')).toBeInTheDocument()
  })
})
