import { beforeEach, describe, it, vi } from 'vitest'
import { render } from '../../../test-utils'
import { useAppDispatch, useAppSelector } from '@/store'
import { generateMockApprovalRequest } from '../../../../stubs/customer-listing'
import { DeleteCustomerPreview } from '@/app/customers/customer/Details/Dialog/DeleteCustomerPreview'
vi.mock('@/store', () => ({
  useAppSelector: vi.fn(),
  useAppDispatch: vi.fn(),
}))
const approvalRequest = generateMockApprovalRequest(100, 'profile-101')
const mockDispatch = vi.fn()
const defaultState = {
  customers: {
    openRejectCustomerModal: true,
  },
  approvalRequests: {
    selectedApprovalRequest: approvalRequest,
  },
}
describe('DeleteCustomerPreview Component', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    // Set up default mock for useAppDispatch
    vi.mocked(useAppDispatch).mockReturnValue(mockDispatch)

    // Set up default mock for useAppSelector
    vi.mocked(useAppSelector).mockImplementation((selector) =>
      selector(defaultState as any)
    )
  })
  it('Should render delete customer dialog component', () => {
    render(<DeleteCustomerPreview selectedApprovalRequest={approvalRequest} />)
  })
})
