import { describe, it } from 'vitest'
import { render } from '../../../../test-utils'
import { mockSubscriptions } from '../../../../../stubs/accounts'
import { SubscriptionAlertsMoreMenu } from '@/app/customers/customer/Details/Accounts/Alerts/SubscriptionsMoreMenu'

describe('SubscriptionsMoreMenu Component', () => {
  it('Should render subscriptions more menu component', () => {
    render(<SubscriptionAlertsMoreMenu notification={mockSubscriptions[0]} />)
  })
})
