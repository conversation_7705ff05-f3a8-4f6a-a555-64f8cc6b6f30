import { beforeEach, describe, it, vi } from 'vitest'
import { render } from '../../../test-utils'
import { useAppDispatch, useAppSelector } from '@/store'
import {
  generateMockApprovalRequest,
  mockCustomers,
} from '../../../../stubs/customer-listing'
import { mockCustomerAccount } from '../../../../stubs/stubs'
import AccountDetailsPreview from '@/app/customers/customer/Details/Dialog/accountDetailsPreview'
import { mockNotifications } from '../../../../stubs/accounts'
vi.mock('@/store', () => ({
  useAppSelector: vi.fn(),
  useAppDispatch: vi.fn(),
}))
const mockDispatch = vi.fn()
const mockApprovalRequest = generateMockApprovalRequest(100, 'profile-104')
const defaultState = {
  approvalRequests: {
    selectedApprovalRequest: mockApprovalRequest,
  },
  loans: {
    bankBranches: [],
  },
  customers: {
    account: mockCustomerAccount,
    selectedCustomerSummaryAccount: mockCustomers.active,
    customerProfileAccount: mockCustomerAccount,
    accountNotificationPreferences: mockNotifications,
  },
}
const mockOnClose = vi.fn()
describe('AccountDetailsPreview Component', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    vi.mocked(useAppDispatch).mockReturnValue(mockDispatch)

    // Set up default mock for useAppSelector
    vi.mocked(useAppSelector).mockImplementation((selector) =>
      selector(defaultState as any)
    )
  })
  it('Should render account details preview component', () => {
    render(<AccountDetailsPreview onClose={mockOnClose} />)
  })
})
