'use client'

import type React from 'react'
import type { IHeadCell } from '@dtbx/store/interfaces'

import { describe, it, expect, vi, beforeEach } from 'vitest'
import { render, fireEvent, waitFor, screen } from '../../../test-utils'
import { ListView } from '@/app/customers/customer/Details/Devices/ListDevices'
import * as storeActions from '@/store/actions'
import * as storeReducers from '@/store/reducers'
import { useAppDispatch, useAppSelector } from '@/store'
import * as utils from '@dtbx/store/utils'
import { mockCustomers } from '../../../../stubs/customer-listing'

// Mock the store actions
vi.mock('@/store/actions', () => ({
  getCustomerDevices: vi.fn().mockResolvedValue(undefined),
  getCustomerDeviceDetail: vi.fn().mockResolvedValue(undefined),
}))

// Mock the store reducers
vi.mock('@/store/reducers', () => ({
  setOpenDevice: vi.fn().mockReturnValue({ type: 'customers/setOpenDevice' }),
}))

// Mock the store hooks
vi.mock('@/store', () => ({
  useAppDispatch: vi.fn(),
  useAppSelector: vi.fn(),
}))

// Mock the utils
vi.mock('@dtbx/store/utils', () => ({
  formatTimestamp: vi.fn((timestamp) => `Formatted: ${timestamp}`),
}))

// Mock the PageHeader component
vi.mock('@/app/customers/customer/Details/Devices/pageHeader', () => ({
  default: () => <div data-testid="page-header">Page Header</div>,
}))

// Mock the EmptySearchAndFilter component
vi.mock(
  '@/app/customers/customer/Details/Devices/EmptySearchAndFilter',
  () => ({
    default: () => (
      <div data-testid="empty-search-and-filter">No devices found</div>
    ),
  })
)

// Mock the DeviceMoreMenu component
vi.mock('@/app/customers/customer/Details/Devices/MoreMenu', () => ({
  DeviceMoreMenu: ({ device }: { device: any }) => (
    <div data-testid="device-more-menu" data-device-id={device.uuid}>
      More Menu
    </div>
  ),
}))

// Mock the custom components from @dtbx/ui/components/Table
vi.mock('@dtbx/ui/components/Table', () => ({
  CustomPagination: ({
    options,
    handlePagination,
  }: {
    options: { page: number; size: number; totalPages: number }
    handlePagination: (newOptions: any) => void
  }) => (
    <div
      data-testid="custom-pagination"
      data-page={options.page}
      data-size={options.size}
      data-total-pages={options.totalPages}
    >
      <button
        data-testid="next-page-button"
        onClick={() =>
          handlePagination({ page: options.page + 1, size: options.size })
        }
      >
        Next Page
      </button>
    </div>
  ),
  CustomTableHeader: ({
    order,
    orderBy,
    rowCount,
    headLabel,
    numSelected,
  }: {
    order: string
    orderBy: string
    rowCount: number
    headLabel: IHeadCell[]
    numSelected: number
  }) => (
    <thead data-testid="custom-table-header">
      <tr>
        {headLabel.map((cell) => (
          <th key={cell.id} data-id={cell.id}>
            {cell.label}
          </th>
        ))}
      </tr>
    </thead>
  ),
}))

// Mock the custom components from @dtbx/ui/components/Loading
vi.mock('@dtbx/ui/components/Loading', () => ({
  CustomSkeleton: ({
    variant,
    width,
    height,
  }: {
    variant: string
    width: string | number
    height: string | number
  }) => (
    <div
      data-testid="custom-skeleton"
      data-variant={variant}
      style={{ width, height }}
    >
      Loading...
    </div>
  ),
}))

// Mock the custom components from @dtbx/ui/components/Chip
vi.mock('@dtbx/ui/components/Chip', () => ({
  CustomerStatusChip: ({ label }: { label: string }) => (
    <div data-testid="customer-status-chip" data-label={label}>
      {label}
    </div>
  ),
}))

// Mock MUI components
vi.mock('@mui/material', async () => {
  const actual = await vi.importActual('@mui/material')
  return {
    ...actual,
    Paper: ({
      children,
      elevation,
      sx,
      ...props
    }: {
      children: React.ReactNode
      elevation?: number
      sx?: any
      [key: string]: any
    }) => (
      <div data-testid="paper" data-elevation={elevation} {...props}>
        {children}
      </div>
    ),
    Stack: ({
      children,
      sx,
      ...props
    }: {
      children: React.ReactNode
      sx?: any
      [key: string]: any
    }) => (
      <div data-testid="stack" {...props}>
        {children}
      </div>
    ),
    TableContainer: ({
      children,
      ...props
    }: {
      children: React.ReactNode
      [key: string]: any
    }) => (
      <div data-testid="table-container" {...props}>
        {children}
      </div>
    ),
    Table: ({
      children,
      ...props
    }: {
      children: React.ReactNode
      [key: string]: any
    }) => (
      <table data-testid="table" {...props}>
        {children}
      </table>
    ),
    TableBody: ({
      children,
      ...props
    }: {
      children: React.ReactNode
      [key: string]: any
    }) => (
      <tbody data-testid="table-body" {...props}>
        {children}
      </tbody>
    ),
    TableRow: ({
      children,
      onClick,
      sx,
      ...props
    }: {
      children: React.ReactNode
      onClick?: () => void
      sx?: any
      [key: string]: any
    }) => (
      <tr data-testid="table-row" onClick={onClick} {...props}>
        {children}
      </tr>
    ),
    TableCell: ({
      children,
      sx,
      ...props
    }: {
      children: React.ReactNode
      sx?: any
      [key: string]: any
    }) => (
      <td data-testid="table-cell" {...props}>
        {children}
      </td>
    ),
  }
})

// Mock dispatch function
const mockDispatch = vi.fn()

// Create mock devices data
const mockDevices = [
  {
    deviceId: 'DEV-001',
    deviceType: 'Mobile',
    deviceName: 'iPhone 12',
    deviceModel: 'A2403',
    deviceStatus: 'Active',
    uuid: '12345',
    dateCreated: '2023-01-01T12:00:00Z',
  },
  {
    deviceId: 'DEV-002',
    deviceType: 'Tablet',
    deviceName: 'iPad Pro',
    deviceModel: 'A2301',
    deviceStatus: 'Inactive',
    uuid: '67890',
    dateCreated: '2023-02-15T14:30:00Z',
  },
]

// Create mock states for different scenarios
const loadingState = {
  customers: {
    devicesResponse: {
      data: [],
      pageNumber: 1,
      pageSize: 10,
      totalNumberOfPages: 0,
    },
    isSuccessDevices: false,
    isLoadingDevices: true,
    customer: mockCustomers.active,
  },
}

const emptyState = {
  customers: {
    devicesResponse: {
      data: [],
      pageNumber: 1,
      pageSize: 10,
      totalNumberOfPages: 0,
    },
    isSuccessDevices: true,
    isLoadingDevices: false,
    customer: mockCustomers.active,
  },
}

const withDataState = {
  customers: {
    devicesResponse: {
      data: mockDevices,
      pageNumber: 1,
      pageSize: 10,
      totalNumberOfPages: 2,
    },
    isSuccessDevices: true,
    isLoadingDevices: false,
    customer: mockCustomers.active,
  },
}

const blockedCustomerState = {
  customers: {
    devicesResponse: {
      data: mockDevices,
      pageNumber: 1,
      pageSize: 10,
      totalNumberOfPages: 2,
    },
    isSuccessDevices: true,
    isLoadingDevices: false,
    customer: { ...mockCustomers.active, isBlocked: true },
  },
}

describe('ListView Component', () => {
  beforeEach(() => {
    // Reset mocks before each test
    vi.clearAllMocks()

    // Set up default mock for useAppDispatch
    vi.mocked(useAppDispatch).mockReturnValue(mockDispatch)

    // Set up default mock for useAppSelector
    vi.mocked(useAppSelector).mockImplementation((selector) =>
      selector(withDataState as any)
    )
  })

  it('renders the page header', () => {
    render(<ListView />)

    // Check that the page header is rendered
    expect(screen.getByTestId('page-header')).toBeInTheDocument()
  })

  it('displays loading skeleton when data is loading', () => {
    // Mock useAppSelector to return loading state
    vi.mocked(useAppSelector).mockImplementation((selector) =>
      selector(loadingState as any)
    )

    render(<ListView />)

    // Check that the loading skeleton is displayed
    expect(screen.getByTestId('custom-skeleton')).toBeInTheDocument()
    expect(screen.getByTestId('custom-skeleton')).toHaveAttribute(
      'data-variant',
      'rectangular'
    )

    // The table should not be displayed
    expect(screen.queryByTestId('table')).not.toBeInTheDocument()
  })

  it('displays empty state when there is no data', () => {
    // Mock useAppSelector to return empty state
    vi.mocked(useAppSelector).mockImplementation((selector) =>
      selector(emptyState as any)
    )

    render(<ListView />)

    // Check that the empty state is displayed
    expect(screen.getByTestId('empty-search-and-filter')).toBeInTheDocument()

    // The table should not be displayed
    expect(screen.queryByTestId('table')).not.toBeInTheDocument()
  })

  it('displays table with data when data is available', () => {
    render(<ListView />)

    // Check that the table is displayed
    expect(screen.getByTestId('table')).toBeInTheDocument()
    expect(screen.getByTestId('table-body')).toBeInTheDocument()

    // Check that the table header is displayed with correct columns
    expect(screen.getByTestId('custom-table-header')).toBeInTheDocument()
    expect(screen.getByText('Device ID')).toBeInTheDocument()
    expect(screen.getByText('Device Type')).toBeInTheDocument()
    expect(screen.getByText('Device Name')).toBeInTheDocument()
    expect(screen.getByText('Device Model')).toBeInTheDocument()
    expect(screen.getByText('Device Status')).toBeInTheDocument()
    expect(screen.getByText('IMSI')).toBeInTheDocument()
    expect(screen.getByText('Date Created')).toBeInTheDocument()
    expect(screen.getByText('Action')).toBeInTheDocument()

    // Check that the table rows are displayed with correct data
    const tableRows = screen.getAllByTestId('table-row')
    expect(tableRows).toHaveLength(2)

    // Check the content of the first row (should be the second device due to sorting by date)
    const firstRowCells = tableRows[0].querySelectorAll(
      '[data-testid="table-cell"]'
    )
    expect(firstRowCells[0]).toHaveTextContent('DEV-002')
    expect(firstRowCells[1]).toHaveTextContent('Tablet')
    expect(firstRowCells[2]).toHaveTextContent('iPad Pro')
    expect(firstRowCells[3]).toHaveTextContent('A2301')
    expect(firstRowCells[5]).toHaveTextContent('67890')
    expect(firstRowCells[6]).toHaveTextContent(
      'Formatted: 2023-02-15T14:30:00Z'
    )

    // Check the status chip
    const statusChips = screen.getAllByTestId('customer-status-chip')
    expect(statusChips[0]).toHaveAttribute('data-label', 'Inactive')
    expect(statusChips[1]).toHaveAttribute('data-label', 'Active')

    // Check the more menu
    const moreMenus = screen.getAllByTestId('device-more-menu')
    expect(moreMenus[0]).toHaveAttribute('data-device-id', '67890')
    expect(moreMenus[1]).toHaveAttribute('data-device-id', '12345')
  })

  it('displays pagination when there are multiple pages', () => {
    render(<ListView />)

    // Check that the pagination is displayed
    expect(screen.getByTestId('custom-pagination')).toBeInTheDocument()
    expect(screen.getByTestId('custom-pagination')).toHaveAttribute(
      'data-page',
      '1'
    )
    expect(screen.getByTestId('custom-pagination')).toHaveAttribute(
      'data-total-pages',
      '2'
    )
  })

  it('does not display pagination when there is only one page', () => {
    // Mock useAppSelector to return state with data but only one page
    vi.mocked(useAppSelector).mockImplementation((selector) =>
      selector({
        ...withDataState,
        customers: {
          ...withDataState.customers,
          devicesResponse: {
            ...withDataState.customers.devicesResponse,
            totalNumberOfPages: 0,
          },
        },
      } as any)
    )

    render(<ListView />)

    // Check that the pagination is not displayed
    expect(screen.queryByTestId('custom-pagination')).not.toBeInTheDocument()
  })

  it('handles pagination correctly', async () => {
    render(<ListView />)

    // Check that the pagination is displayed
    expect(screen.getByTestId('custom-pagination')).toBeInTheDocument()

    // Click the next page button
    const nextPageButton = screen.getByTestId('next-page-button')
    fireEvent.click(nextPageButton)

    // Check that getCustomerDevices was called with the correct parameters
    await waitFor(() => {
      expect(storeActions.getCustomerDevices).toHaveBeenCalledWith({
        params: {
          profileID: mockCustomers.active.id,
          page: 2,
          size: 10,
        },
        dispatch: mockDispatch,
      })
    })
  })

  it('handles row click to view device details', async () => {
    render(<ListView />)

    // Get the first table row
    const tableRows = screen.getAllByTestId('table-row')
    fireEvent.click(tableRows[0])

    // Check that setOpenDevice was called
    expect(storeReducers.setOpenDevice).toHaveBeenCalledWith(true)

    // Check that getCustomerDeviceDetail was called with the correct parameters
    await waitFor(() => {
      expect(storeActions.getCustomerDeviceDetail).toHaveBeenCalledWith({
        deviceID: '67890', // The UUID of the first device (after sorting)
        profileID: mockCustomers.active.id,
        dispatch: mockDispatch,
      })
    })
  })

  it('does not open device details for blocked customers', async () => {
    // Mock useAppSelector to return blocked customer state
    vi.mocked(useAppSelector).mockImplementation((selector) =>
      selector(blockedCustomerState as any)
    )

    render(<ListView />)

    // Get the first table row
    const tableRows = screen.getAllByTestId('table-row')
    fireEvent.click(tableRows[0])

    // Check that setOpenDevice was not called
    expect(storeReducers.setOpenDevice).not.toHaveBeenCalled()

    // Check that getCustomerDeviceDetail was not called
    expect(storeActions.getCustomerDeviceDetail).not.toHaveBeenCalled()
  })

  it('formats timestamps correctly', () => {
    render(<ListView />)

    // Check that formatTimestamp was called with the correct parameters
    expect(utils.formatTimestamp).toHaveBeenCalledWith('2023-02-15T14:30:00Z')
    expect(utils.formatTimestamp).toHaveBeenCalledWith('2023-01-01T12:00:00Z')

    // Check that the formatted timestamps are displayed
    expect(
      screen.getByText('Formatted: 2023-02-15T14:30:00Z')
    ).toBeInTheDocument()
    expect(
      screen.getByText('Formatted: 2023-01-01T12:00:00Z')
    ).toBeInTheDocument()
  })

  it('handles missing customer ID gracefully', () => {
    // Mock useAppSelector to return state with customer having no ID
    vi.mocked(useAppSelector).mockImplementation((selector) =>
      selector({
        ...withDataState,
        customers: {
          ...withDataState.customers,
          customer: { ...mockCustomers.active, id: '' },
        },
      } as any)
    )

    // Render the component and click the pagination button
    render(<ListView />)
    const nextPageButton = screen.getByTestId('next-page-button')
    fireEvent.click(nextPageButton)

    // Check that getCustomerDevices was called with an empty string
    expect(storeActions.getCustomerDevices).toHaveBeenCalledWith({
      params: {
        profileID: '',
        page: 2,
        size: 10,
      },
      dispatch: mockDispatch,
    })
  })

  it('sorts devices by date created in descending order', () => {
    render(<ListView />)

    // Get all table rows
    const tableRows = screen.getAllByTestId('table-row')

    // The first row should be the device with the more recent date
    const firstRowCells = tableRows[0].querySelectorAll(
      '[data-testid="table-cell"]'
    )
    expect(firstRowCells[0]).toHaveTextContent('DEV-002') // This is the device with the more recent date

    // The second row should be the device with the older date
    const secondRowCells = tableRows[1].querySelectorAll(
      '[data-testid="table-cell"]'
    )
    expect(secondRowCells[0]).toHaveTextContent('DEV-001') // This is the device with the older date
  })

  it('initializes pagination options from store state', () => {
    // Mock useAppSelector to return state with custom pagination
    vi.mocked(useAppSelector).mockImplementation((selector) =>
      selector({
        ...withDataState,
        customers: {
          ...withDataState.customers,
          devicesResponse: {
            ...withDataState.customers.devicesResponse,
            pageNumber: 3,
          },
        },
      } as any)
    )

    render(<ListView />)

    // Check that the pagination options are initialized from the store state
    expect(screen.getByTestId('custom-pagination')).toHaveAttribute(
      'data-page',
      '3'
    )
  })
})
