import { beforeEach, describe, it, vi } from 'vitest'
import { render } from '../../../test-utils'
import { ProfileUpdatePreview } from '@/app/customers/customer/Details/Dialog/ProfileUpdatePreview'
import { generateMockApprovalRequest } from '../../../../stubs/customer-listing'
vi.mock('@/store', () => ({
  useAppSelector: vi.fn(),
  useAppDispatch: vi.fn(),
}))
const mockApprovalRequest = generateMockApprovalRequest(100, 'profile-104')

describe('ProfileUpdatePreview Component', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })
  it('Should render profile update preview component', () => {
    render(
      <ProfileUpdatePreview selectedApprovalRequest={mockApprovalRequest} />
    )
  })
})
