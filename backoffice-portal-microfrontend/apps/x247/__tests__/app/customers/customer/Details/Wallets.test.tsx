import { describe, it, expect, vi } from 'vitest'
import { render } from '../../test-utils'
import Wallets from '@/app/customers/customer/Details/Wallets'

// Mock MUI components
vi.mock('@mui/material', async () => {
  const actual = await vi.importActual('@mui/material')
  return {
    ...actual,
    Stack: ({ children }: any) => <div data-testid="mui-stack">{children}</div>,
  }
})

describe('Wallets Component', () => {
  it('renders without errors', () => {
    const { container } = render(<Wallets />)
    expect(container).toBeInTheDocument()
  })

  it('renders a Stack component', () => {
    const { getByTestId } = render(<Wallets />)
    expect(getByTestId('mui-stack')).toBeInTheDocument()
  })

  it("displays the text 'Wallets'", () => {
    const { getByText } = render(<Wallets />)
    expect(getByText('Wallets')).toBeInTheDocument()
  })
})
