import { beforeEach, describe, it, vi } from 'vitest'
import { render } from '../../../test-utils'
import { useAppDispatch, useAppSelector } from '@/store'
import {
  generateMockApprovalRequest,
  mockCustomers,
} from '../../../../stubs/customer-listing'
import { AccountsEditPreview } from '@/app/customers/customer/Details/Dialog/accountsEditPreview'
vi.mock('@/store', () => ({
  useAppSelector: vi.fn(),
  useAppDispatch: vi.fn(),
}))
const approvalRequest = generateMockApprovalRequest(100, 'profile-101')
const mockDispatch = vi.fn()
const defaultState = {
  customers: {
    customer: mockCustomers.active,
  },
  approvalRequests: {
    selectedApprovalRequest: approvalRequest,
  },
}
const mockSetOpenAccountDetails = vi.fn()
describe('AccountsEditPreview Component', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    // Set up default mock for useAppDispatch
    vi.mocked(useAppDispatch).mockReturnValue(mockDispatch)

    // Set up default mock for useAppSelector
    vi.mocked(useAppSelector).mockImplementation((selector) =>
      selector(defaultState as any)
    )
  })
  it.skip('Should render accounts edit preview component', () => {
    render(
      <AccountsEditPreview
        selectedApprovalRequest={approvalRequest}
        setOpenAccountDetails={mockSetOpenAccountDetails}
      />
    )
  })
})
