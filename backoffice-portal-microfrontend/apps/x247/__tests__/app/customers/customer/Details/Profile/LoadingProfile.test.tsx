import { describe, it, expect, vi } from 'vitest'
import { render } from '../../../test-utils'
import LoadingProfile from '@/app/customers/customer/Details/Profile/LoadingProfile'

// Mock the dependencies
vi.mock('@dtbx/ui/components/Loading', () => ({
  CustomSkeleton: ({ sx, variant, key }: any) => (
    <div
      data-testid="custom-skeleton"
      data-variant={variant || 'text'}
      data-width={sx?.width}
      data-height={sx?.height}
      data-key={key}
    />
  ),
}))

// Mock MUI components
vi.mock('@mui/material', async () => {
  const actual = await vi.importActual('@mui/material')
  return {
    ...actual,
    Stack: ({ children, sx, direction, spacing }: any) => {
      // Convert sx prop to data attributes for easier testing
      const dataAttributes: Record<string, string> = {}

      if (sx) {
        Object.entries(sx).forEach(([key, value]) => {
          dataAttributes[`data-sx-${key}`] = String(value)
        })
      }

      return (
        <div
          data-testid="stack"
          data-direction={direction}
          data-spacing={spacing}
          {...dataAttributes}
        >
          {children}
        </div>
      )
    },
  }
})

describe('LoadingProfile Component', () => {
  it('renders without errors', () => {
    const { getAllByTestId } = render(<LoadingProfile />)

    // Check that at least one Stack component is rendered
    const stacks = getAllByTestId('stack')
    expect(stacks.length).toBeGreaterThan(0)
  })

  it('renders the correct number of skeletons', () => {
    const { getAllByTestId } = render(<LoadingProfile />)

    // Check that all skeletons are rendered
    // 1 header + 1 avatar + 2 text lines + 2 buttons + 9 form fields = 15 skeletons
    const skeletons = getAllByTestId('custom-skeleton')
    expect(skeletons).toHaveLength(15)
  })

  it('renders the header skeleton with correct properties', () => {
    const { getAllByTestId } = render(<LoadingProfile />)

    // Find the header skeleton (first rectangular skeleton)
    const headerSkeleton = getAllByTestId('custom-skeleton').find(
      (skeleton) => skeleton.getAttribute('data-variant') === 'rectangular'
    )

    expect(headerSkeleton).toBeInTheDocument()
    expect(headerSkeleton).toHaveAttribute('data-width', '150px')
    expect(headerSkeleton).toHaveAttribute('data-height', '40px')
  })

  it('renders the avatar skeleton with correct properties', () => {
    const { getAllByTestId } = render(<LoadingProfile />)

    // Find the avatar skeleton (rounded skeleton)
    const avatarSkeleton = getAllByTestId('custom-skeleton').find(
      (skeleton) => skeleton.getAttribute('data-variant') === 'rounded'
    )

    expect(avatarSkeleton).toBeInTheDocument()
    expect(avatarSkeleton).toHaveAttribute('data-width', '50px')
    expect(avatarSkeleton).toHaveAttribute('data-height', '50px')
  })

  it('renders the form field skeletons', () => {
    const { getAllByTestId } = render(<LoadingProfile />)

    // Check that we have 9 form field skeletons
    // These are the ones created with Array.from({ length: 9 })
    const formFieldSkeletons = getAllByTestId('custom-skeleton').filter(
      (skeleton) =>
        !skeleton.hasAttribute('data-width') &&
        !skeleton.hasAttribute('data-height')
    )

    expect(formFieldSkeletons).toHaveLength(9)
  })

  it('renders the button skeletons with correct properties', () => {
    const { getAllByTestId } = render(<LoadingProfile />)

    // Find the button skeletons (100px width, 46px height)
    const buttonSkeletons = getAllByTestId('custom-skeleton').filter(
      (skeleton) =>
        skeleton.getAttribute('data-width') === '100px' &&
        skeleton.getAttribute('data-height') === '46px'
    )

    expect(buttonSkeletons).toHaveLength(2)
  })

  it('renders the profile text skeletons with correct properties', () => {
    const { getAllByTestId } = render(<LoadingProfile />)

    // Find the profile text skeletons (150px width, 20px height)
    const profileTextSkeletons = getAllByTestId('custom-skeleton').filter(
      (skeleton) =>
        skeleton.getAttribute('data-width') === '150px' &&
        skeleton.getAttribute('data-height') === '20px'
    )

    expect(profileTextSkeletons).toHaveLength(2)
  })

  it('renders the correct layout structure', () => {
    const { getAllByTestId } = render(<LoadingProfile />)

    // Check that we have the correct number of Stack components
    const stacks = getAllByTestId('stack')

    // Let's count the Stack components in the LoadingProfile component:
    // 1. Main outer Stack
    // 2. Content Stack
    // 3. Profile info row Stack
    // 4. Avatar and text column Stack
    // 5. Text Stack (inside the avatar and text column)
    // 6. Buttons row Stack
    // 7. Form fields grid Stack
    // Total: 7 Stack components
    expect(stacks).toHaveLength(7)

    // Check that the main container has the correct styles
    // The main container is the outermost Stack
    const mainContainer = stacks[0]
    expect(mainContainer).toHaveAttribute('data-sx-py', '1%')
    expect(mainContainer).toHaveAttribute('data-sx-px', '2%')

    // Check that the content container has the correct styles
    const contentContainer = stacks[1]
    expect(contentContainer).toHaveAttribute('data-sx-backgroundColor', '#FFF')
    expect(contentContainer).toHaveAttribute('data-sx-padding', '10px')
    expect(contentContainer).toHaveAttribute(
      'data-sx-border',
      '1px solid #eff0f0'
    )
    expect(contentContainer).toHaveAttribute('data-direction', 'column')
    expect(contentContainer).toHaveAttribute('data-spacing', '2')

    // Check that the form fields grid has the correct styles
    // The form fields grid is the last Stack
    const formFieldsGrid = stacks[6]
    expect(formFieldsGrid).toHaveAttribute('data-sx-height', '250px')
    expect(formFieldsGrid).toHaveAttribute('data-sx-display', 'grid')
    expect(formFieldsGrid).toHaveAttribute(
      'data-sx-gridTemplateColumns',
      'repeat(3, 1fr)'
    )
    expect(formFieldsGrid).toHaveAttribute('data-sx-gap', '5px')
  })
})
