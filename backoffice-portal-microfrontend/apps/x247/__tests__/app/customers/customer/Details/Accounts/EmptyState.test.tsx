import { render, screen } from '@testing-library/react'
import { AccountsEmptyState } from '@/app/customers/customer/Details/Accounts/EmptyState'
import { describe, it, expect, beforeEach, vi } from 'vitest'

// Mock the LinkAccountDialog component
vi.mock('@/app/customers/customer/Details/Accounts/Create', () => ({
  LinkAccountDialog: () => (
    <button data-testid="link-account-button">Link Account</button>
  ),
}))

describe('AccountsEmptyState', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('renders the component correctly', () => {
    render(<AccountsEmptyState />)

    // Component should be in the document
    expect(screen.getByTestId('link-account-button')).toBeInTheDocument()
  })

  it('displays the correct empty state message', () => {
    render(<AccountsEmptyState />)

    // Check for the main heading text
    expect(
      screen.getByText("Seems like you haven't linked any accounts yet")
    ).toBeInTheDocument()

    // Check for the description text
    expect(
      screen.getByText(
        'You can do this by clicking on the button below to link an account.'
      )
    ).toBeInTheDocument()
  })

  it('includes the LinkAccountDialog component', () => {
    render(<AccountsEmptyState />)

    // Verify the mocked LinkAccountDialog is rendered
    const linkButton = screen.getByTestId('link-account-button')
    expect(linkButton).toBeInTheDocument()
    expect(linkButton).toHaveTextContent('Link Account')
  })

  it('has the correct styling structure', () => {
    const { container } = render(<AccountsEmptyState />)

    // Get all Stack components (there should be 2)
    const stackElements = container.querySelectorAll('.MuiStack-root')
    expect(stackElements.length).toBe(2)

    // Check that the outer Stack has the background image set
    const outerStack = stackElements[0]
    expect(outerStack).toHaveStyle({
      backgroundImage: "url('/background-empty.svg')",
      backgroundSize: 'center',
      backgroundRepeat: 'no-repeat',
      backgroundPosition: 'top',
    })

    // Check that Typography elements are present
    const typographyElements = container.querySelectorAll('.MuiTypography-root')
    expect(typographyElements.length).toBe(2)

    // First Typography should be h6
    expect(typographyElements[0]).toHaveClass('MuiTypography-h6')

    // Second Typography should be body1
    expect(typographyElements[1]).toHaveClass('MuiTypography-body1')
  })

  it('applies correct text alignment to description', () => {
    const { container } = render(<AccountsEmptyState />)

    // Get the body1 Typography element (description)
    const descriptionElement = container.querySelector('.MuiTypography-body1')
    expect(descriptionElement).toHaveStyle({
      textAlign: 'center',
    })
  })
})
