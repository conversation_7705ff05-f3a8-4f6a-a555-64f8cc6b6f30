import { beforeEach, describe, it, vi } from 'vitest'
import { render } from '../../../../test-utils'
import { AccountInfoTable } from '@/app/customers/customer/Details/Accounts/Create/AccountTooltip'
import { mockCustomers } from '../../../../../stubs/customer-listing'
import { mockCustomerProfileAccount } from '../../../../../stubs/accounts'
import { useAppDispatch, useAppSelector } from '@/store'
vi.mock('@/store', () => ({
  useAppSelector: vi.fn(),
  useAppDispatch: vi.fn(),
}))
const mockDispatch = vi.fn()
const loadedState = {
  customers: {
    customer: mockCustomers.active,
    account: mockCustomerProfileAccount(),
  },
}
describe('AccountTooltip Component', () => {
  beforeEach(() => {
    vi.clearAllMocks()

    // Set up default mock for useAppDispatch
    vi.mocked(useAppDispatch).mockReturnValue(mockDispatch)
    vi.mocked(useAppSelector).mockImplementation((selector) => {
      return selector(loadedState as any)
    })
  })
  it('Should render account information tooltip', () => {
    render(<AccountInfoTable selectedAccount={mockCustomerProfileAccount()} />)
  })
})
