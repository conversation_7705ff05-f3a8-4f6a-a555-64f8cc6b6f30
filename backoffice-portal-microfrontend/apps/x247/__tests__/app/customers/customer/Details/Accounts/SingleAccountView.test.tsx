'use client'
import type {
  ICustomerAccount,
  INotificationEventsPerAccount,
} from '@/store/interfaces'

import { describe, it, expect, vi, beforeEach } from 'vitest'
import { render, fireEvent, waitFor, screen } from '../../../test-utils'
import { SingleAccountView } from '@/app/customers/customer/Details/Accounts/SingleAccountView'
import * as storeActions from '@/store/actions'
import * as storeReducers from '@/store/reducers'
import { useAppDispatch, useAppSelector } from '@/store'
import * as utils from '@dtbx/store/utils'
import { mockCustomers } from '../../../../stubs/customer-listing'
import {
  mockNotifications,
  mockSubscriptions,
} from '../../../../stubs/accounts'

// Mock the store actions
vi.mock('@/store/actions', () => ({
  getCustomerAccountsLogsBackoffice: vi.fn().mockResolvedValue(undefined),
  getCustomersAccountHistory: vi.fn().mockResolvedValue(undefined),
  fetchNotificationEvents: vi.fn().mockResolvedValue(undefined),
  fetchNotificationEventFrequencies: vi.fn().mockResolvedValue(undefined),
  fetchNotificationEventsPerAccount: vi.fn().mockResolvedValue(undefined),
  fetchNotificationAlertsHistory: vi.fn().mockResolvedValue(undefined),
}))

// Mock the store reducers
vi.mock('@/store/reducers', () => ({
  setIsViewAccountOpen: vi
    .fn()
    .mockReturnValue({ type: 'customers/setIsViewAccountOpen' }),
  setSelectedAccount: vi
    .fn()
    .mockReturnValue({ type: 'customers/setSelectedAccount' }),
}))

// Mock the store hooks
vi.mock('@/store', () => ({
  useAppDispatch: vi.fn(),
  useAppSelector: vi.fn(),
}))

// Mock the utils
vi.mock('@dtbx/store/utils', () => ({
  formatTimestamp: vi.fn((timestamp) => `Formatted: ${timestamp}`),
}))

// Mock the child components
vi.mock('@/app/customers/customer/Details/Accounts/AccountViewDialogs', () => ({
  AddNotificationDialog: () => (
    <button data-testid="add-notification-dialog">Add Notification</button>
  ),
  AddSubscriptionsDialog: () => (
    <button data-testid="add-subscriptions-dialog">Add Subscription</button>
  ),
}))

vi.mock('@/app/customers/customer/Details/Accounts/AccountsMoreMenu', () => ({
  ActivateAccount: ({
    isMainMenu,
    account,
    customer,
  }: {
    isMainMenu: boolean
    account: any
    customer: any
  }) => (
    <button
      data-testid="activate-account"
      data-account-id={account?.accountNo}
      data-is-main-menu={isMainMenu}
    >
      Activate Account
    </button>
  ),
  DeactivateAccount: ({
    isMainMenu,
    account,
    customer,
  }: {
    isMainMenu: boolean
    account: any
    customer: any
  }) => (
    <button
      data-testid="deactivate-account"
      data-account-id={account?.accountNo}
      data-is-main-menu={isMainMenu}
    >
      Deactivate Account
    </button>
  ),
  RestrictAccount: () => (
    <button data-testid="restrict-account">Restrict Account</button>
  ),
  UnlinkAccount: ({ account, customer }: { account: any; customer: any }) => (
    <button data-testid="unlink-account" data-account-id={account?.accountNo}>
      Unlink Account
    </button>
  ),
  ViewAccountHistory: () => (
    <button data-testid="view-account-history">View Account History</button>
  ),
}))

vi.mock('@/app/customers/customer/Details/Accounts/History', () => ({
  default: ({ onHistoryButton }: { onHistoryButton: () => void }) => (
    <button data-testid="account-history" onClick={onHistoryButton}>
      Account History
    </button>
  ),
}))

vi.mock(
  '@/app/customers/customer/Details/Accounts/Alerts/AlertsMoreMenu',
  () => ({
    NotificationsAlertsMoreMenu: ({ notification }: { notification: any }) => (
      <button
        data-testid="notifications-alerts-more-menu"
        data-notification-id={notification?.id}
      >
        <span data-testid="more-icon">...</span>
      </button>
    ),
    SubscriptionAlertsMoreMenu: ({ notification }: { notification: any }) => (
      <button
        data-testid="subscription-alerts-more-menu"
        data-notification-id={notification?.id}
      >
        <span data-testid="more-icon">...</span>
      </button>
    ),
  })
)

vi.mock(
  '@/app/customers/customer/Details/Accounts/Alerts/NotificationHistory',
  () => ({
    NotificationHistory: ({ type }: { type: string }) => (
      <button data-testid={`notification-history-${type}`}>
        Notification History
      </button>
    ),
  })
)

// Mock the custom components
vi.mock('@dtbx/ui/components/Chip', () => ({
  CustomerStatusChip: ({ label, sx }: { label: string; sx?: any }) => (
    <div data-testid="customer-status-chip" data-label={label} style={sx}>
      {label}
    </div>
  ),
}))

// Mock the icons
vi.mock('@dtbx/ui/icons', () => ({
  AccountDetailsIcon: () => (
    <span data-testid="account-details-icon">Account Details Icon</span>
  ),
  AccountIcon: () => <span data-testid="account-icon">Account Icon</span>,
  NotificationDetailsIcon: () => (
    <span data-testid="notification-details-icon">
      Notification Details Icon
    </span>
  ),
  StatementDetailsIcon: () => (
    <span data-testid="statement-details-icon">Statement Details Icon</span>
  ),
}))

// Mock MUI components and icons
vi.mock('@mui/material', async () => {
  const actual = await vi.importActual('@mui/material')
  return {
    ...actual,
    Stack: ({
      children,
      sx,
      direction,
      justifyContent,
      gap,
      ...props
    }: any) => (
      <div
        data-testid="stack"
        data-direction={direction}
        data-justify-content={justifyContent}
        data-gap={gap}
        data-width={sx?.width}
        data-padding={sx?.padding}
        data-border={sx?.border}
        data-border-radius={sx?.borderRadius}
        data-text-align={sx?.textAlign}
        {...props}
      >
        {children}
      </div>
    ),
    Typography: ({ children, variant, sx, ...props }: any) => (
      <div
        data-testid={`typography-${variant || 'default'}`}
        data-fontSize={sx?.fontSize}
        data-fontWeight={sx?.fontWeight}
        data-margin-bottom={sx?.marginBottom}
        data-text-transform={sx?.textTransform}
        data-width={sx?.width}
        data-margin={sx?.margin}
        data-text-wrap={sx?.textWrap}
        {...props}
      >
        {children}
      </div>
    ),
    IconButton: ({ children, sx, onClick, ...props }: any) => (
      <button
        data-testid="icon-button"
        data-border={sx?.border}
        data-border-radius={sx?.borderRadius}
        data-fontWeight={sx?.fontWeight}
        data-width={sx?.width}
        onClick={onClick}
        {...props}
      >
        {children}
      </button>
    ),
    Button: ({
      children,
      variant,
      startIcon,
      size,
      sx,
      disabled,
      ...props
    }: any) => (
      <button
        data-testid="button"
        data-variant={variant}
        data-size={size}
        data-height={sx?.height}
        data-border={sx?.border}
        data-disabled={disabled}
        {...props}
      >
        {startIcon}
        {children}
      </button>
    ),
    TextField: ({ fullWidth, label, margin, value, size, ...props }: any) => (
      <div
        data-testid={`text-field-${label?.toLowerCase().replace(/\s+/g, '-')}`}
        data-full-width={fullWidth}
        data-margin={margin}
        data-size={size}
        {...props}
      >
        <label>{label}</label>
        <input value={value || ''} readOnly />
      </div>
    ),
    Divider: () => <hr data-testid="divider" />,
    Paper: ({ children, elevation, sx, ...props }: any) => (
      <div
        data-testid="paper"
        data-elevation={elevation}
        data-border={sx?.border}
        data-box-shadow={sx?.boxShadow}
        {...props}
      >
        {children}
      </div>
    ),
    TableContainer: ({ children, component, elevation, sx, ...props }: any) => (
      <div
        data-testid="table-container"
        data-component={component?.name}
        data-elevation={elevation}
        data-border={sx?.border}
        data-box-shadow={sx?.boxShadow}
        {...props}
      >
        {children}
      </div>
    ),
    Table: ({ children, sx, size, ...props }: any) => (
      <table data-testid="table" data-size={size} {...props}>
        {children}
      </table>
    ),
    TableHead: ({ children, sx, ...props }: any) => (
      <thead
        data-testid="table-head"
        data-background={sx?.background}
        {...props}
      >
        {children}
      </thead>
    ),
    TableBody: ({ children, ...props }: any) => (
      <tbody data-testid="table-body" {...props}>
        {children}
      </tbody>
    ),
    TableRow: ({ children, ...props }: any) => (
      <tr data-testid="table-row" {...props}>
        {children}
      </tr>
    ),
    TableCell: ({ children, width, ...props }: any) => (
      <td data-testid="table-cell" data-width={width} {...props}>
        {children}
      </td>
    ),
    Chip: ({ label, sx, ...props }: any) => (
      <span
        data-testid="chip"
        data-label={label}
        data-margin-right={sx?.marginRight}
        {...props}
      >
        {label}
      </span>
    ),
  }
})

vi.mock('@mui/icons-material', () => ({
  EditOutlined: () => <span data-testid="edit-outlined-icon">Edit Icon</span>,
  History: () => <span data-testid="history-icon">History Icon</span>,
  MoreHoriz: () => <span data-testid="more-horiz-icon">More Icon</span>,
  AddOutlined: () => <span data-testid="add-outlined-icon">Add Icon</span>,
}))

vi.mock('@mui/icons-material/ArrowBack', () => ({
  default: () => <span data-testid="arrow-back-icon">Back Icon</span>,
}))

vi.mock('@mui/icons-material/AddOutlined', () => ({
  default: () => <span data-testid="add-outlined-icon">Add Icon</span>,
}))

// Mock dispatch function
const mockDispatch = vi.fn()

// Create mock account data
const mockAccount: ICustomerAccount = {
  accNumber: '**********',
  accOpenDate: '2023-01-01T12:00:00Z',
  customerType: 'Individual',
  customerCategory: 'Regular',
  accBranchCode: 'BR001',
  accClass: 'SAV',
  accClassDesc: 'Savings Account',
  accCurrency: 'USD',
  accDormant: 'N',
  accStatus: 'ACTIVE',
  accRecordStatus: 'A',
  accStatBlock: 'N',
  accFrozen: 'N',
  accNoDebit: 'N',
  accNoCredit: 'N',
  accStopPay: 'N',
  jointAccIndicator: 'N',
  customerRecordStatus: 'A',
  accountClass: 'Savings',
  isMobileLinkable: true,
  tariffName: 'Standard',
}

const mockInactiveAccount: ICustomerAccount = {
  ...mockAccount,
  accStatus: 'INACTIVE',
  accStatBlock: 'Y',
}

// Create mock states for different scenarios
const defaultState = {
  customers: {
    customerProfileAccount: {
      accountNo: mockAccount.accNumber,
      fullName: 'John Doe',
      shortName: 'JD',
      branchCode: mockAccount.accBranchCode,
      status: mockAccount.accStatus,
      currency: mockAccount.accCurrency,
      tariffName: mockAccount.tariffName,
      accountType: mockAccount.accountClass || 'Savings',
      isBlocked: mockAccount.accStatBlock === 'Y',
      profile: {
        id: 'prof123',
        firstName: 'John',
        lastName: 'Doe',
        phoneNumber: '+**********',
      },
    },
    customer: mockCustomers.active,
    accountLogsBackOffice: [],
    accountNotificationLogs: [],
    accountSubscriptionLogs: [],
    accountNotificationPreferences: [
      ...mockNotifications,
      ...mockSubscriptions,
    ],
  },
}

// Update the inactive account state
const inactiveAccountState = {
  customers: {
    customerProfileAccount: {
      accountNo: mockInactiveAccount.accNumber,
      fullName: 'John Doe',
      shortName: 'JD',
      branchCode: mockInactiveAccount.accBranchCode,
      status: mockInactiveAccount.accStatus,
      currency: mockInactiveAccount.accCurrency,
      tariffName: mockInactiveAccount.tariffName,
      accountType: mockInactiveAccount.accountClass || 'Savings',
      isBlocked: mockInactiveAccount.accStatBlock === 'Y',
      profile: {
        id: 'prof123',
        firstName: 'John',
        lastName: 'Doe',
        phoneNumber: '+**********',
      },
    },
    customer: mockCustomers.active,
    accountLogsBackOffice: [],
    accountNotificationLogs: [],
    accountSubscriptionLogs: [],
    accountNotificationPreferences: [
      ...mockNotifications,
      ...mockSubscriptions,
    ],
  },
}

const noNotificationsState = {
  customers: {
    customerProfileAccount: {
      accountNo: mockAccount.accNumber,
      fullName: 'John Doe',
      shortName: 'JD',
      branchCode: mockAccount.accBranchCode,
      status: mockAccount.accStatus,
      currency: mockAccount.accCurrency,
      tariffName: mockAccount.tariffName,
      accountType: mockAccount.accountClass || 'Savings',
      isBlocked: mockAccount.accStatBlock === 'Y',
      profile: {
        id: 'prof123',
        firstName: 'John',
        lastName: 'Doe',
        phoneNumber: '+**********',
      },
    },
    customer: mockCustomers.active,
    accountLogsBackOffice: [],
    accountNotificationLogs: [],
    accountSubscriptionLogs: [],
    accountNotificationPreferences: [],
  },
}

const withNotificationLogsState = {
  customers: {
    customerProfileAccount: {
      accountNo: mockAccount.accNumber,
      fullName: 'John Doe',
      shortName: 'JD',
      branchCode: mockAccount.accBranchCode,
      status: mockAccount.accStatus,
      currency: mockAccount.accCurrency,
      tariffName: mockAccount.tariffName,
      accountType: mockAccount.accountClass || 'Savings',
      isBlocked: mockAccount.accStatBlock === 'Y',
      profile: {
        id: 'prof123',
        firstName: 'John',
        lastName: 'Doe',
        phoneNumber: '+**********',
      },
    },
    customer: mockCustomers.active,
    accountLogsBackOffice: [],
    accountNotificationLogs: [
      { id: 'log1', timestamp: '2023-01-01T12:00:00Z' },
    ],
    accountSubscriptionLogs: [
      { id: 'log2', timestamp: '2023-01-01T12:00:00Z' },
    ],
    accountNotificationPreferences: [
      ...mockNotifications,
      ...mockSubscriptions,
    ],
  },
}

const blockedCustomerState = {
  customers: {
    customerProfileAccount: {
      accountNo: mockAccount.accNumber,
      fullName: 'John Doe',
      shortName: 'JD',
      branchCode: mockAccount.accBranchCode,
      status: mockAccount.accStatus,
      currency: mockAccount.accCurrency,
      tariffName: mockAccount.tariffName,
      accountType: mockAccount.accountClass || 'Savings',
      isBlocked: mockAccount.accStatBlock === 'Y',
      profile: {
        id: 'prof123',
        firstName: 'John',
        lastName: 'Doe',
        phoneNumber: '+**********',
      },
    },
    customer: { ...mockCustomers.active, isBlocked: true },
    accountLogsBackOffice: [],
    accountNotificationLogs: [],
    accountSubscriptionLogs: [],
    accountNotificationPreferences: [
      ...mockNotifications,
      ...mockSubscriptions,
    ],
  },
}

describe('SingleAccountView Component', () => {
  beforeEach(() => {
    // Reset mocks before each test
    vi.clearAllMocks()

    // Set up default mock for useAppDispatch
    vi.mocked(useAppDispatch).mockReturnValue(mockDispatch)

    // Set up default mock for useAppSelector
    vi.mocked(useAppSelector).mockImplementation((selector) =>
      selector(defaultState as any)
    )
  })

  it.skip('renders the component with correct layout', () => {
    render(<SingleAccountView />)

    // Check that the main stack is rendered with correct props
    const mainStack = screen.getAllByTestId('stack')[0]
    expect(mainStack).toBeInTheDocument()
    expect(mainStack).toHaveAttribute('data-direction', 'row')
    expect(mainStack).toHaveAttribute('data-gap', '2vw')

    // Check that the sidebar stack is rendered
    const sidebarStack = screen.getAllByTestId('stack')[1]
    expect(sidebarStack).toBeInTheDocument()
    expect(sidebarStack).toHaveAttribute('data-width', '22%')
    expect(sidebarStack).toHaveAttribute('data-gap', '2vh')
    expect(sidebarStack).toHaveAttribute('data-direction', 'column')

    // Check that the content stack is rendered
    const contentStack = screen.getAllByTestId('stack')[3]
    expect(contentStack).toBeInTheDocument()
    expect(contentStack).toHaveAttribute('data-width', '78%')
    expect(contentStack).toHaveAttribute('data-gap', '3vh')
    expect(contentStack).toHaveAttribute('data-direction', 'column')
  })

  it('fetches required data on component mount', () => {
    render(<SingleAccountView />)

    // Check that all required data fetching functions were called with the correct account number
    expect(storeActions.getCustomerAccountsLogsBackoffice).toHaveBeenCalledWith(
      mockDispatch,
      defaultState.customers.customerProfileAccount.accountNo
    )
    expect(storeActions.getCustomersAccountHistory).toHaveBeenCalledWith({
      dispatch: mockDispatch,
      profileID: defaultState.customers.customerProfileAccount.profile.id,
      accountNo: defaultState.customers.customerProfileAccount.accountNo,
    })
    expect(storeActions.fetchNotificationEvents).toHaveBeenCalledWith({
      dispatch: mockDispatch,
    })
    expect(storeActions.fetchNotificationEventFrequencies).toHaveBeenCalledWith(
      { dispatch: mockDispatch }
    )
    expect(storeActions.fetchNotificationEventsPerAccount).toHaveBeenCalledWith(
      {
        accountId: defaultState.customers.customerProfileAccount.accountNo,
        dispatch: mockDispatch,
      }
    )
    expect(storeActions.fetchNotificationAlertsHistory).toHaveBeenCalledWith({
      accountNo: defaultState.customers.customerProfileAccount.accountNo,
      dispatch: mockDispatch,
    })
  })

  it.skip('renders the back button and handles navigation', () => {
    render(<SingleAccountView />)

    // Check that the back button is rendered
    const backButton = screen.getByTestId('icon-button')
    expect(backButton).toBeInTheDocument()
    expect(backButton).toHaveAttribute('data-border', '1px solid #AAADB0')
    expect(backButton).toHaveAttribute('data-border-radius', '8px')
    expect(backButton).toHaveAttribute('data-font-weight', '500')
    expect(backButton).toHaveAttribute('data-width', '20%')

    // Check that the back arrow icon is rendered
    expect(screen.getByTestId('arrow-back-icon')).toBeInTheDocument()

    // Click the back button
    fireEvent.click(backButton)

    // Check that the correct actions were dispatched
    expect(storeReducers.setIsViewAccountOpen).toHaveBeenCalledWith(false)
    expect(storeReducers.setSelectedAccount).toHaveBeenCalledWith({})
  })

  it.skip('renders account information correctly', () => {
    render(<SingleAccountView />)

    // Check that the account icon is rendered
    expect(screen.getByTestId('account-icon')).toBeInTheDocument()

    // Check that the account number is displayed
    const accountNumber = screen.getByTestId('typography-subtitle1')
    expect(accountNumber).toBeInTheDocument()
    expect(accountNumber).toHaveTextContent(`Account ${mockAccount.accNumber}`)
    expect(accountNumber).toHaveAttribute('data-text-wrap', 'noWrap')

    // Check that the account status chip is rendered
    const statusChip = screen.getByTestId('customer-status-chip')
    expect(statusChip).toBeInTheDocument()
    expect(statusChip).toHaveAttribute('data-label', mockAccount.accStatus)
  })

  it('renders account action buttons', () => {
    render(<SingleAccountView />)

    // Check that all action buttons are rendered
    expect(screen.getByTestId('view-account-history')).toBeInTheDocument()
    expect(screen.getByTestId('restrict-account')).toBeInTheDocument()
    expect(screen.getByTestId('deactivate-account')).toBeInTheDocument()
    expect(screen.getByTestId('unlink-account')).toBeInTheDocument()

    // Check that dividers are rendered between buttons
    expect(screen.getAllByTestId('divider')).toHaveLength(4)
  })

  it('renders activate button for inactive accounts', () => {
    // Mock useAppSelector to return inactive account state
    vi.mocked(useAppSelector).mockImplementation((selector) =>
      selector(inactiveAccountState as any)
    )

    render(<SingleAccountView />)

    // Check that the activate button is rendered instead of deactivate
    expect(screen.getByTestId('activate-account')).toBeInTheDocument()
    expect(screen.queryByTestId('deactivate-account')).not.toBeInTheDocument()
  })

  it.skip('renders account details section correctly', () => {
    render(<SingleAccountView />)

    // Check that the account details section is rendered
    const accountDetailsSection = screen.getAllByTestId('stack')[4]
    expect(accountDetailsSection).toBeInTheDocument()
    expect(accountDetailsSection).toHaveAttribute(
      'data-border',
      '1px solid #E3E4E4'
    )
    expect(accountDetailsSection).toHaveAttribute('data-border-radius', '8px')
    expect(accountDetailsSection).toHaveAttribute('data-padding', '2%')

    // Check that the section title is rendered
    const sectionTitle = screen.getAllByTestId('typography-subtitle2')[0]
    expect(sectionTitle).toBeInTheDocument()
    expect(sectionTitle).toHaveTextContent('Account Details')

    // Check that the account details icon is rendered
    expect(screen.getByTestId('account-details-icon')).toBeInTheDocument()

    // Check that the edit button is rendered and not disabled
    const editButton = screen.getByTestId('button')
    expect(editButton).toBeInTheDocument()
    expect(editButton).toHaveAttribute('data-variant', 'outlined')
    expect(editButton).toHaveAttribute('data-size', 'small')
    expect(editButton).toHaveAttribute('data-height', '35px')
    expect(editButton).toHaveAttribute('data-border', '1px solid #E3E4E4')
    expect(editButton).toHaveAttribute('data-disabled', 'false')

    // Check that all text fields are rendered with correct values
    // Note: We're checking against the transformed properties in customerProfileAccount
    expect(screen.getByTestId('text-field-branch')).toHaveTextContent(
      defaultState.customers.customerProfileAccount.branchCode
    )
    expect(screen.getByTestId('text-field-account-name')).toHaveTextContent(
      defaultState.customers.customerProfileAccount.fullName
    )
    expect(screen.getByTestId('text-field-account-alias')).toHaveTextContent(
      defaultState.customers.customerProfileAccount.shortName
    )
    expect(screen.getByTestId('text-field-account-number')).toHaveTextContent(
      defaultState.customers.customerProfileAccount.accountNo
    )
    expect(screen.getByTestId('text-field-status')).toHaveTextContent(
      defaultState.customers.customerProfileAccount.status
    )
    expect(screen.getByTestId('text-field-currency')).toHaveTextContent(
      defaultState.customers.customerProfileAccount.currency
    )
    expect(screen.getByTestId('text-field-tariff')).toHaveTextContent(
      defaultState.customers.customerProfileAccount.tariffName || ''
    )
    expect(screen.getByTestId('text-field-type')).toHaveTextContent(
      defaultState.customers.customerProfileAccount.accountType
    )
    expect(screen.getByTestId('text-field-phone-number')).toHaveTextContent(
      defaultState.customers.customerProfileAccount.profile.phoneNumber
    )
  })

  it('disables edit button for blocked customers', () => {
    // Mock useAppSelector to return blocked customer state
    vi.mocked(useAppSelector).mockImplementation((selector) =>
      selector(blockedCustomerState as any)
    )

    render(<SingleAccountView />)

    // Check that the edit button is disabled
    const editButton = screen.getByTestId('button')
    expect(editButton).toHaveAttribute('data-disabled', 'true')
  })

  it.skip('renders notifications section correctly', () => {
    render(<SingleAccountView />)

    // Check that the notifications section is rendered
    const notificationsSection = screen.getAllByTestId('stack')[8]
    expect(notificationsSection).toBeInTheDocument()
    expect(notificationsSection).toHaveAttribute(
      'data-border',
      '1px solid #E3E4E4'
    )
    expect(notificationsSection).toHaveAttribute('data-border-radius', '8px')
    expect(notificationsSection).toHaveAttribute('data-padding', '2%')
    expect(notificationsSection).toHaveAttribute('data-gap', '2vh')

    // Check that the section title is rendered
    const sectionTitle = screen.getAllByTestId('typography-subtitle2')[1]
    expect(sectionTitle).toBeInTheDocument()
    expect(sectionTitle).toHaveTextContent('Notifications/Alerts')

    // Check that the notification details icon is rendered
    expect(screen.getByTestId('notification-details-icon')).toBeInTheDocument()

    // Check that the add notification button is rendered
    expect(screen.getByTestId('add-notification-dialog')).toBeInTheDocument()

    // Check that the notification history button is not rendered (no logs)
    expect(
      screen.queryByTestId('notification-history-notification')
    ).not.toBeInTheDocument()
  })

  it('renders notification history button when logs exist', () => {
    // Mock useAppSelector to return state with notification logs
    vi.mocked(useAppSelector).mockImplementation((selector) =>
      selector(withNotificationLogsState as any)
    )

    render(<SingleAccountView />)

    // Check that the notification history button is rendered
    expect(
      screen.getByTestId('notification-history-notification')
    ).toBeInTheDocument()
  })

  it.skip('renders subscriptions section correctly', () => {
    render(<SingleAccountView />)

    // Check that the subscriptions section is rendered
    const subscriptionsSection = screen.getAllByTestId('stack')[10]
    expect(subscriptionsSection).toBeInTheDocument()
    expect(subscriptionsSection).toHaveAttribute(
      'data-border',
      '1px solid #E3E4E4'
    )
    expect(subscriptionsSection).toHaveAttribute('data-border-radius', '8px')
    expect(subscriptionsSection).toHaveAttribute('data-padding', '2%')
    expect(subscriptionsSection).toHaveAttribute('data-gap', '2vh')

    // Check that the section title is rendered
    const sectionTitle = screen.getAllByTestId('typography-subtitle2')[2]
    expect(sectionTitle).toBeInTheDocument()
    expect(sectionTitle).toHaveTextContent('E-statement subscriptions')

    // Check that the statement details icon is rendered
    expect(screen.getByTestId('statement-details-icon')).toBeInTheDocument()

    // Check that the add subscription button is rendered
    expect(screen.getByTestId('add-subscriptions-dialog')).toBeInTheDocument()

    // Check that the subscription history button is not rendered (no logs)
    expect(
      screen.queryByTestId('notification-history-subscription')
    ).not.toBeInTheDocument()
  })

  it('renders subscription history button when logs exist', () => {
    // Mock useAppSelector to return state with subscription logs
    vi.mocked(useAppSelector).mockImplementation((selector) =>
      selector(withNotificationLogsState as any)
    )

    render(<SingleAccountView />)

    // Check that the subscription history button is rendered
    expect(
      screen.getByTestId('notification-history-subscription')
    ).toBeInTheDocument()
  })

  it.skip('renders notifications table with correct data', () => {
    render(<SingleAccountView />)

    // Check that the notifications table is rendered
    const notificationsTable = screen.getAllByTestId('table')[0]
    expect(notificationsTable).toBeInTheDocument()
    expect(notificationsTable).toHaveAttribute('data-size', 'small')

    // Check that the table header is rendered with correct columns
    const tableHead = screen.getAllByTestId('table-head')[0]
    expect(tableHead).toBeInTheDocument()
    expect(tableHead).toHaveAttribute('data-background', '#F9FAFB')

    // Check that the table body is rendered
    const tableBody = screen.getAllByTestId('table-body')[0]
    expect(tableBody).toBeInTheDocument()

    // Check that the notification row is rendered with correct data
    const tableRow = screen.getAllByTestId('table-row')[1] // First row after header
    expect(tableRow).toBeInTheDocument()

    // Check the notification type
    const typeCells = tableRow.querySelectorAll('[data-testid="table-cell"]')
    expect(typeCells[0]).toHaveTextContent('Daily Balance Alert')

    // Check the status chip
    const statusChip = screen.getAllByTestId('customer-status-chip')[1] // First one is account status
    expect(statusChip).toBeInTheDocument()
    expect(statusChip).toHaveAttribute('data-label', 'ACTIVE')

    // Check the date
    expect(typeCells[2]).toHaveTextContent('Formatted: 2023-01-01T12:00:00Z')

    // Check the channels
    const channelChips = screen.getAllByTestId('chip')
    expect(channelChips[0]).toHaveAttribute('data-label', 'SMS')
    expect(channelChips[1]).toHaveAttribute('data-label', 'EMAIL')

    // Check the phone numbers
    expect(channelChips[2]).toHaveAttribute('data-label', '+**********')

    // Check the more menu
    const moreMenu = screen.getByTestId('notifications-alerts-more-menu')
    expect(moreMenu).toBeInTheDocument()
    expect(moreMenu).toHaveAttribute('data-notification-id', 'notif1')
  })

  it.skip('renders subscriptions table with correct data', () => {
    render(<SingleAccountView />)

    // Check that the subscriptions table is rendered
    const subscriptionsTable = screen.getAllByTestId('table')[1]
    expect(subscriptionsTable).toBeInTheDocument()
    expect(subscriptionsTable).toHaveAttribute('data-size', 'small')

    // Check that the table header is rendered with correct columns
    const tableHead = screen.getAllByTestId('table-head')[1]
    expect(tableHead).toBeInTheDocument()
    expect(tableHead).toHaveAttribute('data-background', '#F9FAFB')

    // Check that the table body is rendered
    const tableBody = screen.getAllByTestId('table-body')[1]
    expect(tableBody).toBeInTheDocument()

    // Check that the subscription row is rendered with correct data
    const tableRow = screen.getAllByTestId('table-row')[3] // First row after header in second table
    expect(tableRow).toBeInTheDocument()

    // Check the subscription type
    const typeCells = tableRow.querySelectorAll('[data-testid="table-cell"]')
    expect(typeCells[0]).toHaveTextContent('Monthly Subscription')

    // Check the status chip
    const statusChip = screen.getAllByTestId('customer-status-chip')[2] // Third one (account + notification + subscription)
    expect(statusChip).toBeInTheDocument()
    expect(statusChip).toHaveAttribute('data-label', 'ACTIVE')

    // Check the date
    expect(typeCells[2]).toHaveTextContent('Formatted: 2023-01-01T12:00:00Z')

    // Check the email
    const emailChip = screen.getAllByTestId('chip')[3] // Fourth chip
    expect(emailChip).toHaveAttribute('data-label', 'EMAIL')

    // Check the more menu
    const moreMenu = screen.getByTestId('subscription-alerts-more-menu')
    expect(moreMenu).toBeInTheDocument()
    expect(moreMenu).toHaveAttribute('data-notification-id', 'sub1')
  })

  it('displays empty state when no notifications exist', () => {
    // Mock useAppSelector to return state with no notifications
    vi.mocked(useAppSelector).mockImplementation((selector) =>
      selector(noNotificationsState as any)
    )

    render(<SingleAccountView />)

    // Check that the empty state is displayed for notifications
    const emptyStateStacks = screen
      .getAllByTestId('stack')
      .filter((stack) => stack.getAttribute('data-text-align') === 'center')
    expect(emptyStateStacks.length).toBeGreaterThanOrEqual(2)

    // Check the empty state text
    const emptyStateTexts = screen
      .getAllByTestId('typography-default')
      .filter(
        (text) =>
          text.textContent?.includes('No notifications') ||
          text.textContent?.includes('No subscriptions')
      )
    expect(emptyStateTexts.length).toBeGreaterThanOrEqual(2)

    // Check that the tables are not rendered
    expect(screen.queryByTestId('table')).not.toBeInTheDocument()
  })

  it('refreshes account history when history button is clicked', async () => {
    render(<SingleAccountView />)

    // Find and click the account history button
    const historyButton = screen.getByTestId('account-history')
    fireEvent.click(historyButton)

    // Check that the history data was fetched
    await waitFor(() => {
      expect(
        storeActions.getCustomerAccountsLogsBackoffice
      ).toHaveBeenCalledTimes(2) // Once on mount, once on click
      expect(storeActions.getCustomersAccountHistory).toHaveBeenCalledTimes(2) // Once on mount, once on click
    })
  })

  it('formats timestamps correctly', () => {
    render(<SingleAccountView />)

    // Check that formatTimestamp was called with the correct parameters
    expect(utils.formatTimestamp).toHaveBeenCalledWith('2023-01-01T12:00:00Z')

    // Check that the formatted timestamps are displayed
    const formattedDates = screen.getAllByText(
      'Formatted: 2023-01-01T12:00:00Z'
    )
    expect(formattedDates.length).toBeGreaterThanOrEqual(2) // At least one for notifications and one for subscriptions
  })
})
