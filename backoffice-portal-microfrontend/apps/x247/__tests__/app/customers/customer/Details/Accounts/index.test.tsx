'use client'

import { describe, it, expect, vi, beforeEach } from 'vitest'
import { render, screen } from '../../../test-utils'
import CustomerAccounts from '@/app/customers/customer/Details/Accounts/index'
import * as storeActions from '@/store/actions'
import { useAppDispatch, useAppSelector } from '@/store'
import { mockCustomers } from '../../../../stubs/customer-listing'
import { ICustomer } from '@/store/interfaces'

// Mock the store actions
vi.mock('@/store/actions', () => ({
  getLinkedCustomerAccountsByProfileId: vi.fn(),
}))

// Mock the store hooks
vi.mock('@/store', () => ({
  useAppDispatch: vi.fn(),
  useAppSelector: vi.fn(),
}))

// Mock the child components
vi.mock('@/app/customers/customer/Details/Accounts/SingleAccountView', () => ({
  SingleAccountView: () => (
    <div data-testid="single-account-view">Single Account View</div>
  ),
}))

vi.mock('@/app/customers/customer/Details/Accounts/EmptyState', () => ({
  AccountsEmptyState: () => (
    <div data-testid="accounts-empty-state">No accounts found</div>
  ),
}))

vi.mock('@/app/customers/customer/Details/Accounts/AccountsList', () => ({
  AccountsList: () => <div data-testid="accounts-list">Accounts List</div>,
}))

vi.mock('@/app/customers/customer/Details/Accounts/Header', () => ({
  AccountsHeader: ({ customer }: { customer: ICustomer }) => (
    <div data-testid="accounts-header" data-customer-id={customer?.id}>
      Accounts Header
    </div>
  ),
}))

// Mock the loading skeleton
vi.mock('@dtbx/ui/components/Loading', () => ({
  LoadingListsSkeleton: () => (
    <div data-testid="loading-lists-skeleton">Loading accounts...</div>
  ),
}))

// Mock MUI components
vi.mock('@mui/material', async () => {
  const actual = await vi.importActual('@mui/material')
  return {
    ...actual,
    Stack: ({ children, direction, gap, sx, ...props }: any) => (
      <div
        data-testid="stack"
        data-direction={direction}
        data-gap={gap}
        data-px={sx?.px}
        {...props}
      >
        {children}
      </div>
    ),
    Typography: ({ children, variant, ...props }: any) => (
      <div data-testid={`typography-${variant}`} {...props}>
        {children}
      </div>
    ),
  }
})

// Mock dispatch function
const mockDispatch = vi.fn()

// Create mock states for different scenarios
const defaultState = {
  customers: {
    isViewAccountOpen: false,
    customer: mockCustomers.active,
    isLoadingAccounts: false,
    customerLinkedAccountsList: [
      { id: 'acc1', accountNumber: '*********', accountType: 'Savings' },
      { id: 'acc2', accountNumber: '*********', accountType: 'Checking' },
    ],
  },
}

const loadingState = {
  customers: {
    isViewAccountOpen: false,
    customer: mockCustomers.active,
    isLoadingAccounts: true,
    customerLinkedAccountsList: [],
  },
}

const singleAccountViewState = {
  customers: {
    isViewAccountOpen: true,
    customer: mockCustomers.active,
    isLoadingAccounts: false,
    customerLinkedAccountsList: [
      { id: 'acc1', accountNumber: '*********', accountType: 'Savings' },
    ],
  },
}

const emptyAccountsState = {
  customers: {
    isViewAccountOpen: false,
    customer: mockCustomers.active,
    isLoadingAccounts: false,
    customerLinkedAccountsList: [],
  },
}

const noCustomerIdState = {
  customers: {
    isViewAccountOpen: false,
    customer: { ...mockCustomers.active, id: '' },
    isLoadingAccounts: false,
    customerLinkedAccountsList: [],
  },
}

const nullCustomerState = {
  customers: {
    isViewAccountOpen: false,
    customer: null,
    isLoadingAccounts: false,
    customerLinkedAccountsList: [],
  },
}

describe('CustomerAccounts Component', () => {
  beforeEach(() => {
    // Reset mocks before each test
    vi.clearAllMocks()

    // Set up default mock for useAppDispatch
    vi.mocked(useAppDispatch).mockReturnValue(mockDispatch)

    // Set up default mock for useAppSelector
    vi.mocked(useAppSelector).mockImplementation((selector) =>
      selector(defaultState as any)
    )
  })

  it('renders the component with correct layout', () => {
    render(<CustomerAccounts />)

    // Check that the stack is rendered with correct props
    const stack = screen.getByTestId('stack')
    expect(stack).toBeInTheDocument()
    expect(stack).toHaveAttribute('data-direction', 'column')
    expect(stack).toHaveAttribute('data-gap', '1vh')
    expect(stack).toHaveAttribute('data-px', '1.5%')
  })

  it('fetches linked customer accounts on mount', () => {
    render(<CustomerAccounts />)

    // Check that getLinkedCustomerAccountsByProfileId was called with the correct parameters
    expect(
      storeActions.getLinkedCustomerAccountsByProfileId
    ).toHaveBeenCalledWith(mockCustomers.active.id, mockDispatch)
    expect(
      storeActions.getLinkedCustomerAccountsByProfileId
    ).toHaveBeenCalledTimes(1)
  })

  it('does not fetch accounts when customer ID is missing', () => {
    // Mock useAppSelector to return state with no customer ID
    vi.mocked(useAppSelector).mockImplementation((selector) =>
      selector(noCustomerIdState as any)
    )

    render(<CustomerAccounts />)

    // Check that getLinkedCustomerAccountsByProfileId was not called
    expect(
      storeActions.getLinkedCustomerAccountsByProfileId
    ).not.toHaveBeenCalled()
  })

  it('does not fetch accounts when customer is null', () => {
    // Mock useAppSelector to return state with null customer
    vi.mocked(useAppSelector).mockImplementation((selector) =>
      selector(nullCustomerState as any)
    )

    render(<CustomerAccounts />)

    // Check that getLinkedCustomerAccountsByProfileId was not called
    expect(
      storeActions.getLinkedCustomerAccountsByProfileId
    ).not.toHaveBeenCalled()
  })

  it('renders the accounts header when not in single account view', () => {
    render(<CustomerAccounts />)

    // Check that the accounts title is displayed
    const title = screen.getByTestId('typography-h6')
    expect(title).toBeInTheDocument()
    expect(title).toHaveTextContent('Accounts')

    // Check that the accounts header is displayed
    const header = screen.getByTestId('accounts-header')
    expect(header).toBeInTheDocument()
    expect(header).toHaveAttribute('data-customer-id', mockCustomers.active.id)
  })

  it('does not render the accounts header when in single account view', () => {
    // Mock useAppSelector to return single account view state
    vi.mocked(useAppSelector).mockImplementation((selector) =>
      selector(singleAccountViewState as any)
    )

    render(<CustomerAccounts />)

    // Check that the accounts title is not displayed
    expect(screen.queryByTestId('typography-h6')).not.toBeInTheDocument()

    // Check that the accounts header is not displayed
    expect(screen.queryByTestId('accounts-header')).not.toBeInTheDocument()
  })

  it('renders loading skeleton when accounts are loading', () => {
    // Mock useAppSelector to return loading state
    vi.mocked(useAppSelector).mockImplementation((selector) =>
      selector(loadingState as any)
    )

    render(<CustomerAccounts />)

    // Check that the loading skeleton is displayed
    expect(screen.getByTestId('loading-lists-skeleton')).toBeInTheDocument()

    // Check that other components are not displayed
    expect(screen.queryByTestId('single-account-view')).not.toBeInTheDocument()
    expect(screen.queryByTestId('accounts-empty-state')).not.toBeInTheDocument()
    expect(screen.queryByTestId('accounts-list')).not.toBeInTheDocument()
  })

  it('renders single account view when isViewAccountOpen is true', () => {
    // Mock useAppSelector to return single account view state
    vi.mocked(useAppSelector).mockImplementation((selector) =>
      selector(singleAccountViewState as any)
    )

    render(<CustomerAccounts />)

    // Check that the single account view is displayed
    expect(screen.getByTestId('single-account-view')).toBeInTheDocument()

    // Check that other components are not displayed
    expect(
      screen.queryByTestId('loading-lists-skeleton')
    ).not.toBeInTheDocument()
    expect(screen.queryByTestId('accounts-empty-state')).not.toBeInTheDocument()
    expect(screen.queryByTestId('accounts-list')).not.toBeInTheDocument()
  })

  it('renders empty state when no accounts are available', () => {
    // Mock useAppSelector to return empty accounts state
    vi.mocked(useAppSelector).mockImplementation((selector) =>
      selector(emptyAccountsState as any)
    )

    render(<CustomerAccounts />)

    // Check that the empty state is displayed
    expect(screen.getByTestId('accounts-empty-state')).toBeInTheDocument()

    // Check that other components are not displayed
    expect(
      screen.queryByTestId('loading-lists-skeleton')
    ).not.toBeInTheDocument()
    expect(screen.queryByTestId('single-account-view')).not.toBeInTheDocument()
    expect(screen.queryByTestId('accounts-list')).not.toBeInTheDocument()
  })

  it('renders accounts list when accounts are available', () => {
    render(<CustomerAccounts />)

    // Check that the accounts list is displayed
    expect(screen.getByTestId('accounts-list')).toBeInTheDocument()

    // Check that other components are not displayed
    expect(
      screen.queryByTestId('loading-lists-skeleton')
    ).not.toBeInTheDocument()
    expect(screen.queryByTestId('single-account-view')).not.toBeInTheDocument()
    expect(screen.queryByTestId('accounts-empty-state')).not.toBeInTheDocument()
  })

  it('handles re-rendering with different state', () => {
    // Start with default state
    const { rerender } = render(<CustomerAccounts />)

    // Check initial render
    expect(screen.getByTestId('accounts-list')).toBeInTheDocument()
    expect(screen.getByTestId('typography-h6')).toBeInTheDocument()

    // Change to loading state
    vi.mocked(useAppSelector).mockImplementation((selector) =>
      selector(loadingState as any)
    )
    rerender(<CustomerAccounts />)

    // Check that loading state is displayed
    expect(screen.getByTestId('loading-lists-skeleton')).toBeInTheDocument()
    expect(screen.queryByTestId('accounts-list')).not.toBeInTheDocument()

    // Change to single account view state
    vi.mocked(useAppSelector).mockImplementation((selector) =>
      selector(singleAccountViewState as any)
    )
    rerender(<CustomerAccounts />)

    // Check that single account view is displayed
    expect(screen.getByTestId('single-account-view')).toBeInTheDocument()
    expect(
      screen.queryByTestId('loading-lists-skeleton')
    ).not.toBeInTheDocument()
    expect(screen.queryByTestId('typography-h6')).not.toBeInTheDocument()
  })

  it('only fetches accounts once on initial render', () => {
    const { rerender } = render(<CustomerAccounts />)

    // Check that getLinkedCustomerAccountsByProfileId was called once
    expect(
      storeActions.getLinkedCustomerAccountsByProfileId
    ).toHaveBeenCalledTimes(1)

    // Re-render the component
    rerender(<CustomerAccounts />)

    // Check that getLinkedCustomerAccountsByProfileId was still only called once
    expect(
      storeActions.getLinkedCustomerAccountsByProfileId
    ).toHaveBeenCalledTimes(1)
  })
})
