import { beforeEach, describe, it, vi } from 'vitest'
import { render } from '../../../test-utils'
import { useAppDispatch, useAppSelector } from '@/store'
import { mockCustomers } from '../../../../stubs/customer-listing'
import { CustomerCreateChangesMadePreview } from '@/app/customers/customer/Details/Dialog/CustomerCreatePreview'

vi.mock('@/store', () => ({
  useAppSelector: vi.fn(),
  useAppDispatch: vi.fn(),
}))
const mockDispatch = vi.fn()
const defaultState = {
  customers: {
    customer: mockCustomers.active,
    customerAccountsList: [],
  },
}
const mockOpenProfileTab = vi.fn()
const mockOpenAccountTab = vi.fn()
describe('CustomerCreateChangesMadePreview Component', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    // Set up default mock for useAppDispatch
    vi.mocked(useAppDispatch).mockReturnValue(mockDispatch)

    // Set up default mock for useAppSelector
    vi.mocked(useAppSelector).mockImplementation((selector) =>
      selector(defaultState as any)
    )
  })
  it('Should render customer create changes component', () => {
    render(
      <CustomerCreateChangesMadePreview
        openProfileTab={mockOpenProfileTab}
        openAccountTab={mockOpenAccountTab}
      />
    )
  })
})
