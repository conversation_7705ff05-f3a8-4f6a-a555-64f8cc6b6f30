'use client'

import { describe, it, expect, vi, beforeEach } from 'vitest'
import { render } from '../../../test-utils'
import SecurityQuestions from '@/app/customers/customer/Details/Security/SecurityQuestions'
import { useAppDispatch, useAppSelector } from '@/store'
import { getCustomerPinDetails } from '@/store/actions'
import * as Utils from '@dtbx/store/utils'
import { mockCustomers } from '../../../../stubs/customer-listing'

// Mock the dependencies
vi.mock('@/store', () => ({
  useAppDispatch: vi.fn(),
  useAppSelector: vi.fn(),
}))

vi.mock('@/store/actions', () => ({
  getCustomerPinDetails: vi.fn(),
}))

// Mock the formatTimestamp utility
vi.mock('@dtbx/store/utils', () => ({
  formatTimestamp: vi.fn((timestamp) => (timestamp ? 'Formatted Date' : '')),
}))

// Mock the child components
vi.mock('@/app/customers/customer/Details/Security/ResetPin', () => ({
  ResetPin: () => <div data-testid="reset-pin-component">Reset PIN</div>,
}))

vi.mock('@/app/customers/customer/Details/Security/Drawer/PIN', () => ({
  PinDrawer: ({ customer }: any) => (
    <div data-testid="pin-drawer-component" data-customer-id={customer?.id}>
      PIN Drawer
    </div>
  ),
}))

vi.mock('@/app/customers/customer/Details/Security/Drawer/Security', () => ({
  default: ({ customer }: any) => (
    <div
      data-testid="security-drawer-component"
      data-customer-id={customer?.id}
    >
      Security Drawer
    </div>
  ),
}))

vi.mock('@/app/customers/customer/Details/Security/LoadingSecurity', () => ({
  LoadingSecurity: () => (
    <div data-testid="loading-security-component">Loading Security...</div>
  ),
}))

// Mock the UI components
vi.mock('@dtbx/ui/components', () => ({
  ReadOnlyTypography: ({ label, value }: any) => (
    <div
      data-testid={`readonly-typography-${label.toLowerCase().replace(/\s+/g, '-')}`}
      data-value={value}
    >
      {label}: {value}
    </div>
  ),
}))

vi.mock('@dtbx/ui/icons', () => ({
  CheckIcon: () => <div data-testid="check-icon">✓</div>,
  XIcon: () => <div data-testid="x-icon">✗</div>,
  KeyIcon: () => <div data-testid="key-icon">🔑</div>,
  LockIcon: () => <div data-testid="lock-icon">🔒</div>,
}))

// Mock MUI components
vi.mock('@mui/material', async () => {
  const actual = await vi.importActual('@mui/material')
  return {
    ...actual,
    Avatar: ({ children, sx }: any) => (
      <div data-testid="mui-avatar" style={sx}>
        {children}
      </div>
    ),
    Box: ({ children, sx }: any) => (
      <div data-testid="mui-box" style={sx}>
        {children}
      </div>
    ),
    Button: ({ children, variant, sx, disabled, onClick }: any) => (
      <button
        data-testid={`button-${variant}-${children?.toString().toLowerCase().replace(/\s+/g, '-')}`}
        disabled={disabled}
        onClick={onClick}
        style={sx}
      >
        {children}
      </button>
    ),
    Stack: ({ children, sx, direction, spacing }: any) => (
      <div
        data-testid="mui-stack"
        data-direction={direction}
        data-spacing={spacing}
        style={sx}
      >
        {children}
      </div>
    ),
    Typography: ({ children, sx }: any) => (
      <span data-testid="mui-typography" style={sx}>
        {children}
      </span>
    ),
  }
})

// Mock dispatch function
const mockDispatch = vi.fn()

// Create mock PIN details
const mockPinDetails = [
  {
    type: 'PIN',
    status: 'Active PIN',
    attempts: 3,
    dateFirstCreated: '2023-01-01T12:00:00Z',
    dateLastChanged: '2023-06-01T12:00:00Z',
  },
  {
    type: 'Security Questions',
    status: 'Active Set',
    attempts: 5,
    dateFirstCreated: '2023-01-02T12:00:00Z',
    dateLastChanged: '2023-06-02T12:00:00Z',
  },
]

const mockInactivePinDetails = [
  {
    type: 'PIN',
    status: 'Inactive PIN',
    attempts: 0,
    dateFirstCreated: null,
    dateLastChanged: null,
  },
  {
    type: 'Security Questions',
    status: 'Not Set',
    attempts: 0,
    dateFirstCreated: null,
    dateLastChanged: null,
  },
]

// Create mock states for different scenarios
const loadingState = {
  customers: {
    customer: mockCustomers.standard[0],
    customerPinDetails: [],
    isLoadingSecurity: true,
  },
}

const loadedState = {
  customers: {
    customer: mockCustomers.standard[0],
    customerPinDetails: mockPinDetails,
    isLoadingSecurity: false,
  },
}

const inactiveState = {
  customers: {
    customer: mockCustomers.standard[0],
    customerPinDetails: mockInactivePinDetails,
    isLoadingSecurity: false,
  },
}

const emptyState = {
  customers: {
    customer: mockCustomers.standard[0],
    customerPinDetails: [],
    isLoadingSecurity: false,
  },
}

describe('SecurityQuestions Component', () => {
  beforeEach(() => {
    vi.clearAllMocks()

    // Set up default mock for useAppDispatch
    vi.mocked(useAppDispatch).mockReturnValue(mockDispatch)
  })

  it('renders loading state when security data is loading', () => {
    // Mock useAppSelector to return loading state
    vi.mocked(useAppSelector).mockImplementation((selector) =>
      selector(loadingState as any)
    )

    const { getByTestId } = render(<SecurityQuestions />)

    // Check that loading component is rendered
    expect(getByTestId('loading-security-component')).toBeInTheDocument()

    // Check that getCustomerPinDetails was called with correct parameters
    expect(getCustomerPinDetails).toHaveBeenCalledWith({
      profileID: mockCustomers.standard[0].id,
      dispatch: mockDispatch,
    })
  })

  it.skip('renders security information when data is loaded', () => {
    // Mock useAppSelector to return loaded state
    vi.mocked(useAppSelector).mockImplementation((selector) =>
      selector(loadedState as any)
    )

    const { getByTestId, getAllByTestId } = render(<SecurityQuestions />)

    // Check that PIN section is rendered
    expect(getByTestId('key-icon')).toBeInTheDocument()
    expect(getByTestId('check-icon')).toBeInTheDocument()
    expect(getByTestId('readonly-typography-pin-status')).toBeInTheDocument()
    expect(getByTestId('readonly-typography-pin-status')).toHaveAttribute(
      'data-value',
      'Active PIN'
    )

    // Check that Security Questions section is rendered
    expect(getByTestId('lock-icon')).toBeInTheDocument()
    expect(
      getByTestId('readonly-typography-security-questions-status')
    ).toBeInTheDocument()
    expect(
      getByTestId('readonly-typography-security-questions-status')
    ).toHaveAttribute('data-value', 'Active Set')

    // Check that child components are rendered
    expect(getByTestId('reset-pin-component')).toBeInTheDocument()
    expect(getByTestId('pin-drawer-component')).toBeInTheDocument()
    expect(getByTestId('security-drawer-component')).toBeInTheDocument()

    // Check that formatTimestamp was called for dates
    expect(Utils.formatTimestamp).toHaveBeenCalledWith('2023-01-01T12:00:00Z')
    expect(Utils.formatTimestamp).toHaveBeenCalledWith('2023-06-01T12:00:00Z')
    expect(Utils.formatTimestamp).toHaveBeenCalledWith('2023-01-02T12:00:00Z')
    expect(Utils.formatTimestamp).toHaveBeenCalledWith('2023-06-02T12:00:00Z')
  })

  it.skip('renders inactive PIN and security question status correctly', () => {
    // Mock useAppSelector to return inactive state
    vi.mocked(useAppSelector).mockImplementation((selector) =>
      selector(inactiveState as any)
    )

    const { getByTestId, getAllByTestId } = render(<SecurityQuestions />)

    // Check that PIN status shows as inactive
    expect(getByTestId('x-icon')).toBeInTheDocument()
    expect(getByTestId('readonly-typography-pin-status')).toHaveAttribute(
      'data-value',
      'Inactive PIN'
    )

    // Check that Security Questions status shows as not set
    expect(
      getByTestId('readonly-typography-security-questions-status')
    ).toHaveAttribute('data-value', 'Not Set')

    // Check that N/A is displayed for dates when they are null
    expect(
      getByTestId('readonly-typography-date-first-created')
    ).toHaveAttribute('data-value', 'N/A')
    expect(
      getByTestId('readonly-typography-date-last-changed')
    ).toHaveAttribute('data-value', 'N/A')
  })

  it('handles empty customer PIN details gracefully', () => {
    // Mock useAppSelector to return empty state
    vi.mocked(useAppSelector).mockImplementation((selector) =>
      selector(emptyState as any)
    )

    const { getByTestId } = render(<SecurityQuestions />)

    // Check that PIN status shows as Not Set
    expect(getByTestId('readonly-typography-pin-status')).toHaveAttribute(
      'data-value',
      'Not Set'
    )

    // Check that Security Questions status shows as Not Set
    expect(
      getByTestId('readonly-typography-security-questions-status')
    ).toHaveAttribute('data-value', 'Not Set')
  })

  it('passes customer data to child components', () => {
    // Mock useAppSelector to return loaded state
    vi.mocked(useAppSelector).mockImplementation((selector) =>
      selector(loadedState as any)
    )

    const { getByTestId } = render(<SecurityQuestions />)

    // Check that customer data is passed to PIN drawer
    expect(getByTestId('pin-drawer-component')).toHaveAttribute(
      'data-customer-id',
      mockCustomers.standard[0].id
    )

    // Check that customer data is passed to Security drawer
    expect(getByTestId('security-drawer-component')).toHaveAttribute(
      'data-customer-id',
      mockCustomers.standard[0].id
    )
  })

  it('fetches customer PIN details on mount', () => {
    // Mock useAppSelector to return loaded state
    vi.mocked(useAppSelector).mockImplementation((selector) =>
      selector(loadedState as any)
    )

    render(<SecurityQuestions />)

    // Check that getCustomerPinDetails was called with correct parameters
    expect(getCustomerPinDetails).toHaveBeenCalledWith({
      profileID: mockCustomers.standard[0].id,
      dispatch: mockDispatch,
    })
  })

  it('handles different customer types correctly', () => {
    // Test with blocked customer
    vi.mocked(useAppSelector).mockImplementation((selector) =>
      selector({
        customers: {
          customer: {
            ...mockCustomers.blocked,
            isBlocked: true,
          },
          customerPinDetails: mockPinDetails,
          isLoadingSecurity: false,
        },
      } as any)
    )

    const { getByTestId, rerender } = render(<SecurityQuestions />)

    // Check that PIN status is still displayed correctly
    expect(getByTestId('readonly-typography-pin-status')).toHaveAttribute(
      'data-value',
      'Active PIN'
    )

    // Test with one-time PIN customer
    vi.mocked(useAppSelector).mockImplementation((selector) =>
      selector({
        customers: {
          customer: mockCustomers.oneTimePin,
          customerPinDetails: mockPinDetails,
          isLoadingSecurity: false,
        },
      } as any)
    )

    rerender(<SecurityQuestions />)

    // Check that PIN status is still displayed correctly
    expect(getByTestId('readonly-typography-pin-status')).toHaveAttribute(
      'data-value',
      'Active PIN'
    )
  })
})
