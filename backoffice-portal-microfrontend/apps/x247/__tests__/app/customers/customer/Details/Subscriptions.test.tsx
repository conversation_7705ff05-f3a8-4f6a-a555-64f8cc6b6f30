import { describe, it, expect, vi } from 'vitest'
import { render } from '../../test-utils'
import Subscriptions from '@/app/customers/customer/Details/Subscriptions'

// Mock MUI components
vi.mock('@mui/material', async () => {
  const actual = await vi.importActual('@mui/material')
  return {
    ...actual,
    Stack: ({ children }: any) => <div data-testid="mui-stack">{children}</div>,
  }
})

describe('Subscriptions Component', () => {
  it('renders without errors', () => {
    const { container } = render(<Subscriptions />)
    expect(container).toBeInTheDocument()
  })

  it('renders a Stack component', () => {
    const { getByTestId } = render(<Subscriptions />)
    expect(getByTestId('mui-stack')).toBeInTheDocument()
  })

  it('displays the text Subscriptions', () => {
    const { getByText } = render(<Subscriptions />)
    expect(getByText('Subscriptions')).toBeInTheDocument()
  })
})
