import { beforeEach, describe, it, vi } from 'vitest'
import { render } from '../../../test-utils'
import { useAppDispatch, useAppSelector } from '@/store'
import {
  generateMockApprovalRequest,
  mockCustomers,
} from '../../../../stubs/customer-listing'
import { AccountLinkingPreview } from '@/app/customers/customer/Details/Dialog/AccountLinkingPreview'

vi.mock('@/store', () => ({
  useAppSelector: vi.fn(),
  useAppDispatch: vi.fn(),
}))
const mockDispatch = vi.fn()
const approvalRequest = generateMockApprovalRequest(100, 'profile-101')
const defaultState = {
  approvalRequests: {
    selectedApprovalRequest: approvalRequest,
  },
}
describe('AccountLinkingPreview Component', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    // Set up default mock for useAppDispatch
    vi.mocked(useAppDispatch).mockReturnValue(mockDispatch)

    // Set up default mock for useAppSelector
    vi.mocked(useAppSelector).mockImplementation((selector) =>
      selector(defaultState as any)
    )
  })
  it.skip('Should render account linking preview component', () => {
    render(<AccountLinkingPreview selectedApprovalRequest={approvalRequest} />)
  })
})
