import { beforeEach, describe, it, vi } from 'vitest'
import { render } from '../../../test-utils'
import { useAppDispatch, useAppSelector } from '@/store'
import {
  generateMockApprovalRequest,
  mockCustomers,
} from '../../../../stubs/customer-listing'
import { CustomerUpdatePreview } from '@/app/customers/customer/Details/Dialog/CustomerUpdatePreview'
vi.mock('@/store', () => ({
  useAppSelector: vi.fn(),
  useAppDispatch: vi.fn(),
}))
const approvalRequest = generateMockApprovalRequest(100, 'profile-101')
const mockDispatch = vi.fn()
const defaultState = {
  customers: {
    customer: mockCustomers.active,
  },
  approvalRequests: {
    selectedApprovalRequest: approvalRequest,
  },
}
describe('CustomerUpdatePreview Component', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    // Set up default mock for useAppDispatch
    vi.mocked(useAppDispatch).mockReturnValue(mockDispatch)

    // Set up default mock for useAppSelector
    vi.mocked(useAppSelector).mockImplementation((selector) =>
      selector(defaultState as any)
    )
  })
  it('Should render customer update dialog component', () => {
    render(<CustomerUpdatePreview selectedApprovalRequest={approvalRequest} />)
  })
})
