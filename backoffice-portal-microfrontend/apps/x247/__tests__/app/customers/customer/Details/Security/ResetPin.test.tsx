'use client'

import { describe, it, expect, vi, beforeEach } from 'vitest'
import { render, fireEvent, waitFor, screen } from '../../../test-utils'
import * as storeActions from '@/store/actions'
import * as accessControls from '@dtbx/store/utils'
import { useAppDispatch, useAppSelector } from '@/store'
import { mockCustomers } from '../../../../stubs/customer-listing'
import { ResetPin } from '@/app/customers/customer/Details/Security/ResetPin'

// Mock the store actions
vi.mock('@/store/actions', () => ({
  getCustomerPinDetails: vi.fn().mockResolvedValue(undefined),
  getCustomerProfileById: vi.fn().mockResolvedValue(undefined),
  resetCustomerPin: vi.fn().mockResolvedValue(undefined),
}))

// Mock the access control functions
vi.mock('@dtbx/store/utils', async () => {
  const actual = await vi.importActual('@dtbx/store/utils')
  return {
    ...actual,
    HasAccessToRights: vi.fn(),
    ACCESS_CONTROLS: {
      RESET_SECURITY_QUESTIONS: [
        'SUPER_RESET_SECURITY_QUESTIONS',
        'MAKE_RESET_SECURITY_QUESTIONS',
      ],
    },
    AccessControlWrapper: ({ children }: any) => <>{children}</>,
  }
})

// Mock the store hooks
vi.mock('@/store', () => ({
  useAppDispatch: vi.fn(),
  useAppSelector: vi.fn(),
}))

// Mock the LoadingButton component
vi.mock('@dtbx/ui/components/Loading', () => ({
  LoadingButton: ({ width, height }: any) => (
    <div data-testid="loading-button" style={{ width, height }}>
      Loading...
    </div>
  ),
}))

// Mock the CustomCheckBox component
vi.mock('@dtbx/ui/components/CheckBox', () => ({
  CustomCheckBox: ({ checked, onChange }: any) => (
    <input
      type="checkbox"
      checked={checked}
      onChange={onChange}
      data-testid="custom-checkbox"
    />
  ),
}))

// Mock MUI components
vi.mock('@mui/material', async () => {
  const actual = await vi.importActual('@mui/material')
  return {
    ...actual,
    Button: ({ children, onClick, disabled, variant, sx, ...props }: any) => {
      // Determine which button this is based on context clues
      let testId = 'button'

      // The open dialog button is outside the dialog and has height: '35px'
      if (sx && sx.height === '35px') {
        testId = 'open-dialog-button'
      }
      // The submit button inside the dialog has a red background
      else if (sx && sx.background === '#EB0045') {
        testId = 'submit-reset-button'
      }
      // The cancel button has variant outlined
      else if (variant === 'outlined') {
        testId = 'cancel-button'
      }

      return (
        <button
          onClick={onClick}
          disabled={disabled}
          data-variant={variant}
          data-testid={testId}
          {...props}
        >
          {children}
        </button>
      )
    },
    Dialog: ({ open, children, ...props }: any) =>
      open ? (
        <div data-testid="dialog" {...props}>
          {children}
        </div>
      ) : null,
    DialogTitle: ({ children, ...props }: any) => (
      <div data-testid="dialog-title" {...props}>
        {children}
      </div>
    ),
    DialogContent: ({ children, ...props }: any) => (
      <div data-testid="dialog-content" {...props}>
        {children}
      </div>
    ),
    TextField: ({
      value,
      onChange,
      placeholder,
      error,
      helperText,
      multiline,
      rows,
      ...props
    }: any) => (
      <div>
        <input
          value={value}
          onChange={onChange}
          placeholder={placeholder}
          data-testid="text-field"
          data-multiline={multiline ? 'true' : 'false'}
          data-rows={rows}
          {...props}
        />
        {error && helperText && (
          <div data-testid="error-text">{helperText}</div>
        )}
      </div>
    ),
    IconButton: ({ onClick, children, ...props }: any) => (
      <button onClick={onClick} data-testid="icon-button" {...props}>
        {children}
      </button>
    ),
    Box: ({ children, ...props }: any) => <div {...props}>{children}</div>,
    Typography: ({ children, ...props }: any) => (
      <div {...props}>{children}</div>
    ),
  }
})

// Mock the CloseRounded icon
vi.mock('@mui/icons-material', () => ({
  CloseRounded: () => <span data-testid="close-icon">×</span>,
}))

// Mock dispatch function
const mockDispatch = vi.fn()

// Create mock states for different scenarios
const defaultState = {
  customers: {
    customer: mockCustomers.active,
    isLoadingSecurity: false,
  },
  approvalRequests: {
    customersWithPendingApprovals: [],
  },
}

const loadingState = {
  customers: {
    customer: mockCustomers.active,
    isLoadingSecurity: true,
  },
  approvalRequests: {
    customersWithPendingApprovals: [],
  },
}

describe('ResetPin Component', () => {
  beforeEach(() => {
    // Reset mocks before each test
    vi.clearAllMocks()

    // Set up default mock for useAppDispatch
    vi.mocked(useAppDispatch).mockReturnValue(mockDispatch)

    // Set up default mock for useAppSelector
    vi.mocked(useAppSelector).mockImplementation((selector) =>
      selector(defaultState as any)
    )
  })

  it('renders the open dialog button correctly', () => {
    render(<ResetPin />)

    const openDialogButton = screen.getByTestId('open-dialog-button')
    expect(openDialogButton).toBeInTheDocument()
    expect(openDialogButton.textContent).toContain('Reset')
  })

  it('opens the dialog when open dialog button is clicked', async () => {
    render(<ResetPin />)

    const openDialogButton = screen.getByTestId('open-dialog-button')
    fireEvent.click(openDialogButton)

    await waitFor(() => {
      expect(screen.getByTestId('dialog')).toBeInTheDocument()
    })
  })

  it('displays all reset reason options', async () => {
    render(<ResetPin />)

    const openDialogButton = screen.getByTestId('open-dialog-button')
    fireEvent.click(openDialogButton)

    await waitFor(() => {
      expect(screen.getByText('Compromised PIN')).toBeInTheDocument()
      expect(screen.getByText('Lost or Stolen Phone')).toBeInTheDocument()
      expect(screen.getByText('Forgotten PIN')).toBeInTheDocument()
      expect(screen.getByText('PIN Reset Policy')).toBeInTheDocument()
      expect(screen.getByText('Other')).toBeInTheDocument()
    })
  })

  it('shows text field when a reason is selected', async () => {
    render(<ResetPin />)

    const openDialogButton = screen.getByTestId('open-dialog-button')
    fireEvent.click(openDialogButton)

    // Wait for the dialog to be fully rendered
    await waitFor(() => {
      expect(screen.getByTestId('dialog')).toBeInTheDocument()
      expect(screen.getAllByTestId('custom-checkbox').length).toBeGreaterThan(0)
    })

    // Find and click the first checkbox
    const checkboxes = screen.getAllByTestId('custom-checkbox')
    fireEvent.click(checkboxes[0])

    // Wait for the text field to appear
    await waitFor(() => {
      const textField = screen.getByTestId('text-field')
      expect(textField).toBeInTheDocument()
    })
  })

  it('validates custom reason with error message for short input', async () => {
    render(<ResetPin />)

    const openDialogButton = screen.getByTestId('open-dialog-button')
    fireEvent.click(openDialogButton)

    // Wait for the dialog to be fully rendered
    await waitFor(() => {
      expect(screen.getByTestId('dialog')).toBeInTheDocument()
      expect(screen.getAllByTestId('custom-checkbox').length).toBeGreaterThan(0)
    })

    // Find and click the first checkbox
    const checkboxes = screen.getAllByTestId('custom-checkbox')
    fireEvent.click(checkboxes[0])

    // Wait for the text field to appear
    await waitFor(() => {
      const textField = screen.getByTestId('text-field')
      expect(textField).toBeInTheDocument()

      // Change the text field value
      fireEvent.change(textField, { target: { value: 'Short' } })
    })

    // Wait for the error message to appear
    await waitFor(() => {
      expect(screen.getByTestId('error-text')).toBeInTheDocument()
      expect(
        screen.getByText(/reason must be at least 10 characters/i)
      ).toBeInTheDocument()
    })
  })

  it('enables submit button when valid reason is provided', async () => {
    render(<ResetPin />)

    const openDialogButton = screen.getByTestId('open-dialog-button')
    fireEvent.click(openDialogButton)

    // Wait for the dialog to be fully rendered
    await waitFor(() => {
      expect(screen.getByTestId('dialog')).toBeInTheDocument()
      expect(screen.getAllByTestId('custom-checkbox').length).toBeGreaterThan(0)
    })

    // Find and click the first checkbox
    const checkboxes = screen.getAllByTestId('custom-checkbox')
    fireEvent.click(checkboxes[0])

    // Wait for the text field to appear
    await waitFor(() => {
      const textField = screen.getByTestId('text-field')
      expect(textField).toBeInTheDocument()

      // Change the text field value
      fireEvent.change(textField, {
        target: {
          value: 'This is a valid reason with more than 10 characters',
        },
      })
    })

    // Wait for the submit button to be enabled
    await waitFor(() => {
      const submitButton = screen.getByTestId('submit-reset-button')
      expect(submitButton).not.toBeDisabled()
    })
  })

  it('closes the dialog when cancel button is clicked', async () => {
    render(<ResetPin />)

    // Open the dialog
    const openDialogButton = screen.getByTestId('open-dialog-button')
    fireEvent.click(openDialogButton)

    // Wait for the dialog to be visible
    await waitFor(() => {
      expect(screen.getByTestId('dialog')).toBeInTheDocument()
    })

    // Find the cancel button by test ID
    const cancelButton = screen.getByTestId('cancel-button')
    expect(cancelButton).toBeInTheDocument()

    // Click the cancel button
    fireEvent.click(cancelButton)

    // Verify the dialog is closed
    await waitFor(() => {
      expect(screen.queryByTestId('dialog')).not.toBeInTheDocument()
    })
  })

  it('calls resetCustomerPin with super role when user has super rights', async () => {
    // Mock the HasAccessToRights function to return true for super rights
    vi.mocked(accessControls.HasAccessToRights).mockImplementation((rights) => {
      return rights.includes('SUPER_RESET_SECURITY_QUESTIONS')
    })

    render(<ResetPin />)

    const openDialogButton = screen.getByTestId('open-dialog-button')
    fireEvent.click(openDialogButton)

    // Wait for the dialog to be fully rendered
    await waitFor(() => {
      expect(screen.getByTestId('dialog')).toBeInTheDocument()
      expect(screen.getAllByTestId('custom-checkbox').length).toBeGreaterThan(0)
    })

    // Find and click the first checkbox
    const checkboxes = screen.getAllByTestId('custom-checkbox')
    fireEvent.click(checkboxes[0])

    // Wait for the text field to appear and enter a valid reason
    await waitFor(() => {
      const textField = screen.getByTestId('text-field')
      expect(textField).toBeInTheDocument()

      // Change the text field value
      fireEvent.change(textField, {
        target: {
          value: 'This is a valid reason with more than 10 characters',
        },
      })
    })

    // Wait for the submit button to be enabled and click it
    await waitFor(() => {
      const submitButton = screen.getByTestId('submit-reset-button')
      expect(submitButton).not.toBeDisabled()
      fireEvent.click(submitButton)
    })

    // Verify that resetCustomerPin was called with the correct parameters
    await waitFor(() => {
      expect(storeActions.resetCustomerPin).toHaveBeenCalledWith({
        profileID: mockCustomers.active.id,
        dispatch: mockDispatch,
        comments:
          'Compromised PIN: This is a valid reason with more than 10 characters',
        role: 'super',
      })
    })
  })

  it('calls resetCustomerPin with maker role when user has maker rights', async () => {
    // Mock the HasAccessToRights function to return true for maker rights only
    vi.mocked(accessControls.HasAccessToRights).mockImplementation((rights) => {
      return (
        rights.includes('MAKE_RESET_SECURITY_QUESTIONS') &&
        !rights.includes('SUPER_RESET_SECURITY_QUESTIONS')
      )
    })

    render(<ResetPin />)

    const openDialogButton = screen.getByTestId('open-dialog-button')
    fireEvent.click(openDialogButton)

    // Wait for the dialog to be fully rendered
    await waitFor(() => {
      expect(screen.getByTestId('dialog')).toBeInTheDocument()
      expect(screen.getAllByTestId('custom-checkbox').length).toBeGreaterThan(0)
    })

    // Find and click the first checkbox
    const checkboxes = screen.getAllByTestId('custom-checkbox')
    fireEvent.click(checkboxes[0])

    // Wait for the text field to appear and enter a valid reason
    await waitFor(() => {
      const textField = screen.getByTestId('text-field')
      expect(textField).toBeInTheDocument()

      // Change the text field value
      fireEvent.change(textField, {
        target: {
          value: 'This is a valid reason with more than 10 characters',
        },
      })
    })

    // Wait for the submit button to be enabled and click it
    await waitFor(() => {
      const submitButton = screen.getByTestId('submit-reset-button')
      expect(submitButton).not.toBeDisabled()
      fireEvent.click(submitButton)
    })

    // Verify that resetCustomerPin was called with the correct parameters
    await waitFor(() => {
      expect(storeActions.resetCustomerPin).toHaveBeenCalledWith({
        profileID: mockCustomers.active.id,
        dispatch: mockDispatch,
        comments:
          'Compromised PIN: This is a valid reason with more than 10 characters',
        role: 'maker',
      })
    })
  })

  it('refreshes customer data after successful PIN reset', async () => {
    vi.mocked(accessControls.HasAccessToRights).mockReturnValue(true)

    render(<ResetPin />)

    const openDialogButton = screen.getByTestId('open-dialog-button')
    fireEvent.click(openDialogButton)

    // Wait for the dialog to be fully rendered
    await waitFor(() => {
      expect(screen.getByTestId('dialog')).toBeInTheDocument()
      expect(screen.getAllByTestId('custom-checkbox').length).toBeGreaterThan(0)
    })

    // Find and click the first checkbox
    const checkboxes = screen.getAllByTestId('custom-checkbox')
    fireEvent.click(checkboxes[0])

    // Wait for the text field to appear and enter a valid reason
    await waitFor(() => {
      const textField = screen.getByTestId('text-field')
      expect(textField).toBeInTheDocument()

      // Change the text field value
      fireEvent.change(textField, {
        target: {
          value: 'This is a valid reason with more than 10 characters',
        },
      })
    })

    // Wait for the submit button to be enabled and click it
    await waitFor(() => {
      const submitButton = screen.getByTestId('submit-reset-button')
      expect(submitButton).not.toBeDisabled()
      fireEvent.click(submitButton)
    })

    // Verify that the customer data was refreshed
    await waitFor(() => {
      expect(storeActions.getCustomerPinDetails).toHaveBeenCalledWith({
        profileID: mockCustomers.active.id,
        dispatch: mockDispatch,
      })
      expect(storeActions.getCustomerProfileById).toHaveBeenCalledWith(
        mockCustomers.active.id,
        mockDispatch
      )
    })
  })

  it('shows loading button during PIN reset process', async () => {
    // Mock useAppSelector to return loading state
    vi.mocked(useAppSelector).mockImplementation((selector) =>
      selector(loadingState as any)
    )

    vi.mocked(accessControls.HasAccessToRights).mockReturnValue(true)

    render(<ResetPin />)

    const openDialogButton = screen.getByTestId('open-dialog-button')
    fireEvent.click(openDialogButton)

    // Wait for the dialog to be fully rendered
    await waitFor(() => {
      expect(screen.getByTestId('dialog')).toBeInTheDocument()
      expect(screen.getAllByTestId('custom-checkbox').length).toBeGreaterThan(0)
    })

    // Find and click the first checkbox
    const checkboxes = screen.getAllByTestId('custom-checkbox')
    fireEvent.click(checkboxes[0])

    // Wait for the text field to appear and enter a valid reason
    await waitFor(() => {
      const textField = screen.getByTestId('text-field')
      expect(textField).toBeInTheDocument()

      // Change the text field value
      fireEvent.change(textField, {
        target: {
          value: 'This is a valid reason with more than 10 characters',
        },
      })
    })

    // Verify that the loading button is shown instead of the submit button
    await waitFor(() => {
      expect(
        screen.queryByTestId('submit-reset-button')
      ).not.toBeInTheDocument()
      expect(screen.getByTestId('loading-button')).toBeInTheDocument()
    })
  })

  it('disables dialog close during loading', async () => {
    // Mock useAppSelector to return loading state
    vi.mocked(useAppSelector).mockImplementation((selector) =>
      selector(loadingState as any)
    )

    render(<ResetPin />)

    const openDialogButton = screen.getByTestId('open-dialog-button')
    fireEvent.click(openDialogButton)

    // Wait for the dialog to be visible
    await waitFor(() => {
      expect(screen.getByTestId('dialog')).toBeInTheDocument()
    })

    // Find the cancel button and click it
    const cancelButton = screen.getByTestId('cancel-button')
    fireEvent.click(cancelButton)

    // Verify that the dialog is still open (because isLoadingSecurity is true)
    await waitFor(() => {
      expect(screen.getByTestId('dialog')).toBeInTheDocument()
    })
  })

  it('handles different customer states correctly', () => {
    // Test with one-time PIN customer
    vi.mocked(useAppSelector).mockImplementation((selector) =>
      selector({
        ...defaultState,
        customers: {
          ...defaultState.customers,
          customer: mockCustomers.oneTimePin,
        },
      } as any)
    )

    const { rerender } = render(<ResetPin />)

    // Check that the component renders without errors
    expect(screen.getByTestId('open-dialog-button')).toBeInTheDocument()

    // Test with not-set PIN customer
    vi.mocked(useAppSelector).mockImplementation((selector) =>
      selector({
        ...defaultState,
        customers: {
          ...defaultState.customers,
          customer: mockCustomers.notSetPin,
        },
      } as any)
    )

    rerender(<ResetPin />)

    // Check that the component renders without errors
    expect(screen.getByTestId('open-dialog-button')).toBeInTheDocument()
  })

  it('handles error during PIN reset', async () => {
    // Mock resetCustomerPin to reject with an error
    vi.mocked(storeActions.resetCustomerPin).mockRejectedValueOnce(
      new Error('Failed to reset PIN')
    )

    vi.mocked(accessControls.HasAccessToRights).mockReturnValue(true)

    render(<ResetPin />)

    const openDialogButton = screen.getByTestId('open-dialog-button')
    fireEvent.click(openDialogButton)

    // Wait for the dialog to be fully rendered
    await waitFor(() => {
      expect(screen.getByTestId('dialog')).toBeInTheDocument()
      expect(screen.getAllByTestId('custom-checkbox').length).toBeGreaterThan(0)
    })

    // Find and click the first checkbox
    const checkboxes = screen.getAllByTestId('custom-checkbox')
    fireEvent.click(checkboxes[0])

    // Wait for the text field to appear and enter a valid reason
    await waitFor(() => {
      const textField = screen.getByTestId('text-field')
      expect(textField).toBeInTheDocument()

      // Change the text field value
      fireEvent.change(textField, {
        target: {
          value: 'This is a valid reason with more than 10 characters',
        },
      })
    })

    // Wait for the submit button to be enabled and click it
    await waitFor(() => {
      const submitButton = screen.getByTestId('submit-reset-button')
      expect(submitButton).not.toBeDisabled()
      fireEvent.click(submitButton)
    })

    // Verify that the dialog is still open after error
    await waitFor(() => {
      expect(screen.getByTestId('dialog')).toBeInTheDocument()
    })
  })
})
