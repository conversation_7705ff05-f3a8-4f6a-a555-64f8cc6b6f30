'use client'

import type React from 'react'
import type { IHeadCell } from '@dtbx/store/interfaces'

import { describe, it, expect, vi, beforeEach } from 'vitest'
import { render, fireEvent, screen } from '../../../test-utils'
import DeviceEventHistory from '@/app/customers/customer/Details/Devices/DeviceEventHistory'
import * as storeActions from '@/store/actions'
import { useAppDispatch, useAppSelector } from '@/store'
import * as utils from '@dtbx/store/utils'
import { mockCustomers } from '../../../../stubs/customer-listing'

// Mock the store actions
vi.mock('@/store/actions', () => ({
  getCustomerDeviceHistory: vi.fn(),
}))

// Mock the store hooks
vi.mock('@/store', () => ({
  useAppDispatch: vi.fn(),
  useAppSelector: vi.fn(),
}))

// Mock the utils
vi.mock('@dtbx/store/utils', () => ({
  formatTimestamp: vi.fn((timestamp) => `Formatted: ${timestamp}`),
}))

// Mock the custom components
vi.mock('@dtbx/ui/components/Table', () => ({
  CustomTableHeader: ({
    headLabel,
    order,
    orderBy,
    rowCount,
    numSelected,
  }: {
    headLabel: IHeadCell[]
    order: string
    orderBy: string
    rowCount: number
    numSelected: number
  }) => (
    <thead data-testid="custom-table-header">
      <tr>
        {headLabel.map((cell) => (
          <th key={cell.id} data-id={cell.id}>
            {cell.label}
          </th>
        ))}
      </tr>
    </thead>
  ),
}))

vi.mock('@/app/approval-requests/CustomFilterBox', () => ({
  CustomFilterBox: ({
    openFilter,
    setOpenFilter,
    searchValue,
    handleSearch,
    filters,
    onFilterChange,
  }: {
    openFilter: boolean
    setOpenFilter: (open: boolean) => void
    searchValue: string
    handleSearch: () => void
    filters: any[]
    onFilterChange: () => void
  }) => (
    <div data-testid="custom-filter-box" data-open-filter={openFilter}>
      <button
        data-testid="toggle-filter-button"
        onClick={() => setOpenFilter(!openFilter)}
      >
        {openFilter ? 'Close Filter' : 'Open Filter'}
      </button>
    </div>
  ),
}))

// Mock MUI components
vi.mock('@mui/material', async () => {
  const actual = await vi.importActual('@mui/material')
  return {
    ...actual,
    Button: ({
      children,
      onClick,
      variant,
      sx,
      disabled,
      ...props
    }: {
      children: React.ReactNode
      onClick?: () => void
      variant?: string
      sx?: any
      disabled?: boolean
      [key: string]: any
    }) => (
      <button
        data-testid="device-history-button"
        onClick={onClick}
        data-variant={variant}
        data-disabled={disabled}
        {...props}
      >
        {children}
      </button>
    ),
    Drawer: ({
      open,
      children,
      anchor,
      onClose,
      ...props
    }: {
      open: boolean
      children: React.ReactNode
      anchor?: string
      onClose: (event: unknown, reason: string) => void
      [key: string]: any
    }) =>
      open ? (
        <div data-testid="drawer" data-anchor={anchor} {...props}>
          {children}
          <button
            data-testid="backdrop"
            onClick={(e) => onClose(e, 'backdropClick')}
          >
            Backdrop
          </button>
        </div>
      ) : null,
    IconButton: ({
      children,
      onClick,
      sx,
      ...props
    }: {
      children: React.ReactNode
      onClick?: (e: any) => void
      sx?: any
      [key: string]: any
    }) => (
      <button data-testid="icon-button" onClick={onClick} {...props}>
        {children}
      </button>
    ),
    Paper: ({
      children,
      elevation,
      sx,
      ...props
    }: {
      children: React.ReactNode
      elevation?: number
      sx?: any
      [key: string]: any
    }) => (
      <div data-testid="paper" data-elevation={elevation} {...props}>
        {children}
      </div>
    ),
    Stack: ({
      children,
      sx,
      ...props
    }: {
      children: React.ReactNode
      sx?: any
      [key: string]: any
    }) => (
      <div
        data-testid="stack"
        data-background={sx?.background}
        data-border-bottom={sx?.borderBottom}
        data-padding-left={sx?.paddingLeft}
        data-padding-top={sx?.paddingTop}
        data-px={sx?.px}
        data-py={sx?.py}
        data-gap={sx?.gap}
        data-width={sx?.width}
        {...props}
      >
        {children}
      </div>
    ),
    TableContainer: ({
      children,
      sx,
      ...props
    }: {
      children: React.ReactNode
      sx?: any
      [key: string]: any
    }) => (
      <div
        data-testid="table-container"
        data-overflow={sx?.overflow}
        data-maxHeight={sx?.maxHeight}
        {...props}
      >
        {children}
      </div>
    ),
    Table: ({
      children,
      stickyHeader,
      ...props
    }: {
      children: React.ReactNode
      stickyHeader?: boolean
      [key: string]: any
    }) => (
      <table data-testid="table" data-sticky-header={stickyHeader} {...props}>
        {children}
      </table>
    ),
    TableBody: ({
      children,
      ...props
    }: {
      children: React.ReactNode
      [key: string]: any
    }) => (
      <tbody data-testid="table-body" {...props}>
        {children}
      </tbody>
    ),
    TableRow: ({
      children,
      ...props
    }: {
      children: React.ReactNode
      [key: string]: any
    }) => (
      <tr data-testid="table-row" {...props}>
        {children}
      </tr>
    ),
    TableCell: ({
      children,
      ...props
    }: {
      children: React.ReactNode
      [key: string]: any
    }) => (
      <td data-testid="table-cell" {...props}>
        {children}
      </td>
    ),
    Typography: ({
      children,
      variant,
      sx,
      ...props
    }: {
      children: React.ReactNode
      variant?: string
      sx?: any
      [key: string]: any
    }) => (
      <div
        data-testid={`typography-${variant || 'default'}`}
        data-fontSize={sx?.fontSize}
        data-fontWeight={sx?.fontWeight}
        data-py={sx?.py}
        data-text-wrap={sx?.textWrap}
        data-text-align={sx?.textAlign}
        {...props}
      >
        {children}
      </div>
    ),
  }
})

// Mock MUI icons
vi.mock('@mui/icons-material/Close', () => ({
  default: () => <span data-testid="close-icon">×</span>,
}))

// Mock dispatch function
const mockDispatch = vi.fn()

// Create mock device data
const mockDevice = {
  deviceId: 'DEV-001',
  deviceType: 'Mobile',
  deviceName: 'iPhone 12',
  deviceModel: 'A2403',
  deviceStatus: 'ACTIVE',
  uuid: '12345',
  dateCreated: '2023-01-01T12:00:00Z',
  devicePlatform: 'iOS',
  phoneNumber: '+1234567890',
}

// Create mock event logs data
const mockEventLogs = [
  {
    id: '1',
    event: 'DEVICE_REGISTERED',
    eventSource: 'MOBILE_APP',
    eventDate: '2023-01-01T12:00:00Z',
  },
  {
    id: '2',
    event: 'DEVICE_ACTIVATED',
    eventSource: 'BACK_OFFICE',
    eventDate: '2023-01-02T14:30:00Z',
  },
  {
    id: '3',
    event: 'LOGIN_ATTEMPT',
    eventSource: 'MOBILE_APP',
    eventDate: '2023-01-03T09:15:00Z',
  },
]

// Create mock states for different scenarios
const defaultState = {
  customers: {
    deviceLogs: mockEventLogs,
    IsLoadingDeviceLogs: false,
    IsSuccessfulDeviceLogs: true,
    customer: mockCustomers.active,
    device: mockDevice,
  },
}

const loadingState = {
  customers: {
    deviceLogs: [],
    IsLoadingDeviceLogs: true,
    IsSuccessfulDeviceLogs: false,
    customer: mockCustomers.active,
    device: mockDevice,
  },
}

const emptyState = {
  customers: {
    deviceLogs: [],
    IsLoadingDeviceLogs: false,
    IsSuccessfulDeviceLogs: true,
    customer: mockCustomers.active,
    device: mockDevice,
  },
}

const blockedCustomerState = {
  customers: {
    deviceLogs: mockEventLogs,
    IsLoadingDeviceLogs: false,
    IsSuccessfulDeviceLogs: true,
    customer: { ...mockCustomers.active, isBlocked: true },
    device: mockDevice,
  },
}

const noDeviceState = {
  customers: {
    deviceLogs: mockEventLogs,
    IsLoadingDeviceLogs: false,
    IsSuccessfulDeviceLogs: true,
    customer: mockCustomers.active,
    device: null,
  },
}

const noCustomerIdState = {
  customers: {
    deviceLogs: mockEventLogs,
    IsLoadingDeviceLogs: false,
    IsSuccessfulDeviceLogs: true,
    customer: { ...mockCustomers.active, id: '' },
    device: mockDevice,
  },
}

describe('DeviceEventHistory Component', () => {
  beforeEach(() => {
    // Reset mocks before each test
    vi.clearAllMocks()

    // Set up default mock for useAppDispatch
    vi.mocked(useAppDispatch).mockReturnValue(mockDispatch)

    // Set up default mock for useAppSelector
    vi.mocked(useAppSelector).mockImplementation((selector) =>
      selector(defaultState as any)
    )
  })

  it('renders the device history button', () => {
    render(<DeviceEventHistory />)

    // Check that the button is rendered
    const button = screen.getByTestId('device-history-button')
    expect(button).toBeInTheDocument()
    expect(button).not.toHaveAttribute('data-disabled', 'true')

    // Check that the button text is correct
    const buttonText = screen.getByTestId('typography-label1')
    expect(buttonText).toBeInTheDocument()
    expect(buttonText).toHaveTextContent('Device History')
  })

  it('disables the button when customer is blocked', () => {
    // Mock useAppSelector to return blocked customer state
    vi.mocked(useAppSelector).mockImplementation((selector) =>
      selector(blockedCustomerState as any)
    )

    render(<DeviceEventHistory />)

    // Check that the button is disabled
    const button = screen.getByTestId('device-history-button')
    expect(button).toHaveAttribute('data-disabled', 'true')
  })

  it('opens the drawer when the button is clicked', () => {
    render(<DeviceEventHistory />)

    // Initially, the drawer should not be visible
    expect(screen.queryByTestId('drawer')).not.toBeInTheDocument()

    // Click the button to open the drawer
    const button = screen.getByTestId('device-history-button')
    fireEvent.click(button)

    // The drawer should now be visible
    expect(screen.getByTestId('drawer')).toBeInTheDocument()
    expect(screen.getByTestId('drawer')).toHaveAttribute('data-anchor', 'right')
  })

  it('fetches device history on initial render', () => {
    render(<DeviceEventHistory />)

    // Check that getCustomerDeviceHistory was called with the correct parameters
    expect(storeActions.getCustomerDeviceHistory).toHaveBeenCalledWith(
      mockDispatch,
      mockCustomers.active.id,
      mockDevice.uuid
    )
  })

  it('fetches device history when device or customer ID changes', () => {
    const { rerender } = render(<DeviceEventHistory />)

    // Check initial call
    expect(storeActions.getCustomerDeviceHistory).toHaveBeenCalledTimes(1)

    // Update the device
    const updatedDeviceState = {
      ...defaultState,
      customers: {
        ...defaultState.customers,
        device: { ...mockDevice, uuid: '67890' },
      },
    }
    vi.mocked(useAppSelector).mockImplementation((selector) =>
      selector(updatedDeviceState as any)
    )
    rerender(<DeviceEventHistory />)

    // Check that getCustomerDeviceHistory was called again with the new device ID
    expect(storeActions.getCustomerDeviceHistory).toHaveBeenCalledTimes(2)
    expect(storeActions.getCustomerDeviceHistory).toHaveBeenLastCalledWith(
      mockDispatch,
      mockCustomers.active.id,
      '67890'
    )

    // Update the customer ID
    const updatedCustomerState = {
      ...updatedDeviceState,
      customers: {
        ...updatedDeviceState.customers,
        customer: { ...mockCustomers.active, id: 'new-customer-id' },
      },
    }
    vi.mocked(useAppSelector).mockImplementation((selector) =>
      selector(updatedCustomerState as any)
    )
    rerender(<DeviceEventHistory />)

    // Check that getCustomerDeviceHistory was called again with the new customer ID
    expect(storeActions.getCustomerDeviceHistory).toHaveBeenCalledTimes(3)
    expect(storeActions.getCustomerDeviceHistory).toHaveBeenLastCalledWith(
      mockDispatch,
      'new-customer-id',
      '67890'
    )
  })

  it('handles missing device gracefully', () => {
    // Mock useAppSelector to return state with no device
    vi.mocked(useAppSelector).mockImplementation((selector) =>
      selector(noDeviceState as any)
    )

    render(<DeviceEventHistory />)

    // Check that getCustomerDeviceHistory was called with an empty string for device ID
    expect(storeActions.getCustomerDeviceHistory).toHaveBeenCalledWith(
      mockDispatch,
      mockCustomers.active.id,
      ''
    )
  })

  it('handles missing customer ID gracefully', () => {
    // Mock useAppSelector to return state with no customer ID
    vi.mocked(useAppSelector).mockImplementation((selector) =>
      selector(noCustomerIdState as any)
    )

    render(<DeviceEventHistory />)

    // Check that getCustomerDeviceHistory was called with an empty string for customer ID
    expect(storeActions.getCustomerDeviceHistory).toHaveBeenCalledWith(
      mockDispatch,
      '',
      mockDevice.uuid
    )
  })

  it.skip('displays the drawer header with the correct title', () => {
    render(<DeviceEventHistory />)

    // Click the button to open the drawer
    const button = screen.getByTestId('device-history-button')
    fireEvent.click(button)

    // Check that the drawer header is displayed with the correct title
    const title = screen.getByText('Device History')
    expect(title).toBeInTheDocument()
  })

  it('closes the drawer when the close button is clicked', () => {
    render(<DeviceEventHistory />)

    // Click the button to open the drawer
    const button = screen.getByTestId('device-history-button')
    fireEvent.click(button)

    // The drawer should be visible
    expect(screen.getByTestId('drawer')).toBeInTheDocument()

    // Click the close button
    const closeButton = screen.getByTestId('icon-button')
    fireEvent.click(closeButton)

    // The drawer should no longer be visible
    expect(screen.queryByTestId('drawer')).not.toBeInTheDocument()
  })

  it('prevents closing the drawer on backdrop click', () => {
    render(<DeviceEventHistory />)

    // Click the button to open the drawer
    const button = screen.getByTestId('device-history-button')
    fireEvent.click(button)

    // The drawer should be visible
    expect(screen.getByTestId('drawer')).toBeInTheDocument()

    // Click the backdrop
    const backdrop = screen.getByTestId('backdrop')
    fireEvent.click(backdrop)

    // The drawer should still be visible
    expect(screen.getByTestId('drawer')).toBeInTheDocument()
  })

  it('displays the filter box in the drawer', () => {
    render(<DeviceEventHistory />)

    // Click the button to open the drawer
    const button = screen.getByTestId('device-history-button')
    fireEvent.click(button)

    // Check that the filter box is displayed
    expect(screen.getByTestId('custom-filter-box')).toBeInTheDocument()
  })

  it('toggles the filter box', () => {
    render(<DeviceEventHistory />)

    // Click the button to open the drawer
    const button = screen.getByTestId('device-history-button')
    fireEvent.click(button)

    // Initially, the filter box should be closed
    expect(screen.getByTestId('custom-filter-box')).toHaveAttribute(
      'data-open-filter',
      'false'
    )

    // Click the toggle filter button
    const toggleFilterButton = screen.getByTestId('toggle-filter-button')
    fireEvent.click(toggleFilterButton)

    // The filter box should now be open
    expect(screen.getByTestId('custom-filter-box')).toHaveAttribute(
      'data-open-filter',
      'true'
    )

    // Click the toggle filter button again
    fireEvent.click(toggleFilterButton)

    // The filter box should now be closed again
    expect(screen.getByTestId('custom-filter-box')).toHaveAttribute(
      'data-open-filter',
      'false'
    )
  })

  it.skip('adjusts table container max height based on filter box state', () => {
    render(<DeviceEventHistory />)

    // Click the button to open the drawer
    const button = screen.getByTestId('device-history-button')
    fireEvent.click(button)

    // Initially, the filter box should be closed and table container should have max height of 85vh
    expect(screen.getByTestId('table-container')).toHaveAttribute(
      'data-max-height',
      '85vh'
    )

    // Click the toggle filter button to open the filter box
    const toggleFilterButton = screen.getByTestId('toggle-filter-button')
    fireEvent.click(toggleFilterButton)

    // The table container should now have max height of 80vh
    expect(screen.getByTestId('table-container')).toHaveAttribute(
      'data-max-height',
      '80vh'
    )
  })

  it('displays the table with correct headers', () => {
    render(<DeviceEventHistory />)

    // Click the button to open the drawer
    const button = screen.getByTestId('device-history-button')
    fireEvent.click(button)

    // Check that the table is displayed
    expect(screen.getByTestId('table')).toBeInTheDocument()
    expect(screen.getByTestId('table')).toHaveAttribute(
      'data-sticky-header',
      'true'
    )

    // Check that the table header is displayed with correct columns
    expect(screen.getByTestId('custom-table-header')).toBeInTheDocument()
    expect(screen.getByText('Event')).toBeInTheDocument()
    expect(screen.getByText('Event Source')).toBeInTheDocument()
    expect(screen.getByText('Event Date')).toBeInTheDocument()
  })

  it('displays the event logs in the table', () => {
    render(<DeviceEventHistory />)

    // Click the button to open the drawer
    const button = screen.getByTestId('device-history-button')
    fireEvent.click(button)

    // Check that the table body is displayed
    expect(screen.getByTestId('table-body')).toBeInTheDocument()

    // Check that the table rows are displayed with correct data
    const tableRows = screen.getAllByTestId('table-row')
    expect(tableRows).toHaveLength(3)

    // Check the content of the first row
    const firstRowCells = tableRows[0].querySelectorAll(
      '[data-testid="table-cell"]'
    )
    expect(firstRowCells[0]).toHaveTextContent('DEVICE_REGISTERED')
    expect(firstRowCells[1]).toHaveTextContent('MOBILE_APP')
    expect(firstRowCells[2]).toHaveTextContent(
      'Formatted: 2023-01-01T12:00:00Z'
    )

    // Check the content of the second row
    const secondRowCells = tableRows[1].querySelectorAll(
      '[data-testid="table-cell"]'
    )
    expect(secondRowCells[0]).toHaveTextContent('DEVICE_ACTIVATED')
    expect(secondRowCells[1]).toHaveTextContent('BACK_OFFICE')
    expect(secondRowCells[2]).toHaveTextContent(
      'Formatted: 2023-01-02T14:30:00Z'
    )

    // Check the content of the third row
    const thirdRowCells = tableRows[2].querySelectorAll(
      '[data-testid="table-cell"]'
    )
    expect(thirdRowCells[0]).toHaveTextContent('LOGIN_ATTEMPT')
    expect(thirdRowCells[1]).toHaveTextContent('MOBILE_APP')
    expect(thirdRowCells[2]).toHaveTextContent(
      'Formatted: 2023-01-03T09:15:00Z'
    )
  })

  it('formats timestamps correctly', () => {
    render(<DeviceEventHistory />)

    // Click the button to open the drawer
    const button = screen.getByTestId('device-history-button')
    fireEvent.click(button)

    // Check that formatTimestamp was called with the correct parameters
    expect(utils.formatTimestamp).toHaveBeenCalledWith('2023-01-01T12:00:00Z')
    expect(utils.formatTimestamp).toHaveBeenCalledWith('2023-01-02T14:30:00Z')
    expect(utils.formatTimestamp).toHaveBeenCalledWith('2023-01-03T09:15:00Z')

    // Check that the formatted timestamps are displayed
    expect(
      screen.getByText('Formatted: 2023-01-01T12:00:00Z')
    ).toBeInTheDocument()
    expect(
      screen.getByText('Formatted: 2023-01-02T14:30:00Z')
    ).toBeInTheDocument()
    expect(
      screen.getByText('Formatted: 2023-01-03T09:15:00Z')
    ).toBeInTheDocument()
  })

  it('does not display any rows when there are no events', () => {
    // Mock useAppSelector to return empty state
    vi.mocked(useAppSelector).mockImplementation((selector) =>
      selector(emptyState as any)
    )

    render(<DeviceEventHistory />)

    // Click the button to open the drawer
    const button = screen.getByTestId('device-history-button')
    fireEvent.click(button)

    // Check that the table body is displayed
    expect(screen.getByTestId('table-body')).toBeInTheDocument()

    // Check that no table rows are displayed
    expect(screen.queryByTestId('table-row')).not.toBeInTheDocument()
  })

  it('does not display any rows when loading', () => {
    // Mock useAppSelector to return loading state
    vi.mocked(useAppSelector).mockImplementation((selector) =>
      selector(loadingState as any)
    )

    render(<DeviceEventHistory />)

    // Click the button to open the drawer
    const button = screen.getByTestId('device-history-button')
    fireEvent.click(button)

    // Check that the table body is displayed
    expect(screen.getByTestId('table-body')).toBeInTheDocument()

    // Check that no table rows are displayed
    expect(screen.queryByTestId('table-row')).not.toBeInTheDocument()
  })

  it('renders the drawer with correct styling', () => {
    render(<DeviceEventHistory />)

    // Click the button to open the drawer
    const button = screen.getByTestId('device-history-button')
    fireEvent.click(button)

    // Check that the header stack has correct styling
    const headerStack = screen.getAllByTestId('stack')[0]
    expect(headerStack).toHaveAttribute('data-background', '#F9FAFB')
    expect(headerStack).toHaveAttribute(
      'data-border-bottom',
      '2px solid  #F2F4F7'
    )
    expect(headerStack).toHaveAttribute('data-padding-left', '5%')
    expect(headerStack).toHaveAttribute('data-padding-top', '2%')

    // Check that the content stack has correct styling
    const contentStack = screen.getAllByTestId('stack')[1]
    expect(contentStack).toHaveAttribute('data-px', '5%')
    expect(contentStack).toHaveAttribute('data-py', '3%')
    expect(contentStack).toHaveAttribute('data-gap', '2vh')
    expect(contentStack).toHaveAttribute('data-width', '100%')
  })

  it('renders the button with correct styling', () => {
    render(<DeviceEventHistory />)

    // Check that the button has the correct variant
    const button = screen.getByTestId('device-history-button')
    expect(button).toHaveAttribute('data-variant', 'outlined')

    // Check that the button text has correct styling
    const buttonText = screen.getByTestId('typography-label1')
    expect(buttonText).toHaveAttribute('data-text-wrap', 'nowrap')
    expect(buttonText).toHaveAttribute('data-text-align', 'center')
  })

  it.skip('renders the drawer title with correct styling', () => {
    render(<DeviceEventHistory />)

    // Click the button to open the drawer
    const button = screen.getByTestId('device-history-button')
    fireEvent.click(button)

    // Check that the title has correct styling
    const title = screen.getByText('Device History')
    expect(title).toHaveAttribute('data-font-size', '16px')
    expect(title).toHaveAttribute('data-font-weight', '600')
    expect(title).toHaveAttribute('data-py', '1%')
  })
})
