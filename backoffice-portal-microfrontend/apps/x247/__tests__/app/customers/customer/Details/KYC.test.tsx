'use client'

import { describe, it, expect, vi, beforeEach } from 'vitest'
import { render, fireEvent, waitFor } from '../../test-utils'
import KYC from '@/app/customers/customer/Details/KYC'
import { useAppDispatch, useAppSelector } from '@/store'
import { setDocumentToggle } from '@dtbx/store/reducers'
import { mockCustomers } from '../../../stubs/customer-listing'

// Mock the dependencies
vi.mock('@/store', () => ({
  useAppDispatch: vi.fn(),
  useAppSelector: vi.fn(),
}))

vi.mock('@dtbx/store/reducers', () => ({
  setDocumentToggle: vi.fn(),
}))

// Mock the dummy data
vi.mock('@/app/customers/customers/Details/dummy', () => ({
  dummyKyc: [
    {
      id: 1,
      parameter: 'ID Verification',
      checked: 'yes',
      status: 'passed',
      score: '95%',
      documents: 'id_verification.pdf',
    },
    {
      id: 2,
      parameter: 'Address Verification',
      checked: 'yes',
      status: 'failed',
      score: '45%',
      documents: 'address_verification.pdf',
    },
    {
      id: 3,
      parameter: 'Face Verification',
      checked: 'no',
      status: 'failed',
      score: '0%',
      documents: 'face_verification.pdf',
    },
  ],
}))

// Mock the CustomFilterBox component
vi.mock('@/app/approval-requests/CustomFilterBox', () => ({
  CustomFilterBox: ({
    openFilter,
    setOpenFilter,
    searchValue,
    handleSearch,
    filters,
    onFilterChange,
  }: any) => (
    <div data-testid="custom-filter-box">
      <input
        data-testid="search-input"
        value={searchValue}
        onChange={handleSearch}
      />
      <button
        data-testid="filter-toggle-button"
        onClick={() => setOpenFilter(!openFilter)}
      >
        Toggle Filter
      </button>
      <button
        data-testid="apply-filter-button"
        onClick={() => onFilterChange({ status: 'passed' })}
      >
        Apply Filter
      </button>
    </div>
  ),
}))

// Mock the TableDropDownMenu component
vi.mock('@dtbx/ui/components/DropDownMenus', () => ({
  TableDropDownMenu: ({ disabled, menuItems }: any) => (
    <div data-testid="table-dropdown-menu" data-disabled={disabled}>
      <button data-testid="dropdown-toggle">Actions</button>
      <div data-testid="dropdown-menu">
        {menuItems.map((item: any, index: number) => (
          <button
            key={index}
            data-testid={`dropdown-item-${item.label.toLowerCase()}`}
            onClick={item.onClick}
          >
            {item.label}
          </button>
        ))}
      </div>
    </div>
  ),
}))

// Mock MUI components
vi.mock('@mui/material', async () => {
  const actual = await vi.importActual('@mui/material')
  return {
    ...actual,
    Box: ({ children, sx }: any) => (
      <div data-testid="mui-box" style={sx}>
        {children}
      </div>
    ),
    Button: ({ children, variant, sx, disabled, onClick }: any) => (
      <button
        data-testid={`button-${variant}-${children?.toString().toLowerCase().replace(/\s+/g, '-')}`}
        disabled={disabled}
        onClick={onClick}
        style={sx}
      >
        {children}
      </button>
    ),
    Chip: ({ label, sx, ...props }: any) => (
      <div data-testid="mui-chip" style={sx} {...props}>
        {label}
      </div>
    ),
    Paper: ({ children, elevation, sx }: any) => (
      <div data-testid="mui-paper" data-elevation={elevation} style={sx}>
        {children}
      </div>
    ),
    Table: ({ children, stickyHeader }: any) => (
      <table data-testid="mui-table" data-sticky-header={stickyHeader}>
        {children}
      </table>
    ),
    TableContainer: ({ children, sx }: any) => (
      <div data-testid="mui-table-container" style={sx}>
        {children}
      </div>
    ),
    TableHead: ({ children }: any) => (
      <thead data-testid="mui-table-head">{children}</thead>
    ),
    TableBody: ({ children }: any) => (
      <tbody data-testid="mui-table-body">{children}</tbody>
    ),
    TableRow: ({ children, key }: any) => (
      <tr data-testid="mui-table-row" data-key={key}>
        {children}
      </tr>
    ),
    TableCell: ({ children, key, sx }: any) => (
      <td data-testid="mui-table-cell" data-key={key} style={sx}>
        {children}
      </td>
    ),
    Typography: ({ children, sx, onClick }: any) => (
      <span data-testid="mui-typography" style={sx} onClick={onClick}>
        {children}
      </span>
    ),
  }
})

// Mock MUI icons
vi.mock('@mui/icons-material', () => ({
  CheckRounded: () => <div data-testid="check-rounded-icon" />,
  CloseRounded: () => <div data-testid="close-rounded-icon" />,
}))

// Mock dispatch function
const mockDispatch = vi.fn()

// Create mock states for different scenarios
const standardCustomerState = {
  customers: {
    customer: mockCustomers.standard[0],
  },
}

const blockedCustomerState = {
  customers: {
    customer: mockCustomers.blocked,
  },
}

describe('KYC Component', () => {
  beforeEach(() => {
    vi.clearAllMocks()

    // Set up default mock for useAppDispatch
    vi.mocked(useAppDispatch).mockReturnValue(mockDispatch)
  })

  it.skip('renders the component with table and filters', () => {
    // Mock useAppSelector to return standard customer state
    vi.mocked(useAppSelector).mockImplementation((selector) =>
      selector(standardCustomerState as any)
    )

    const { getByTestId, getAllByTestId } = render(<KYC />)

    // Check that the filter box is rendered
    expect(getByTestId('custom-filter-box')).toBeInTheDocument()

    // Check that the table is rendered
    expect(getByTestId('mui-table')).toBeInTheDocument()
    expect(getByTestId('mui-table-head')).toBeInTheDocument()
    expect(getByTestId('mui-table-body')).toBeInTheDocument()

    // Check that the table rows are rendered (3 data rows)
    const tableRows = getAllByTestId('mui-table-row')
    expect(tableRows.length).toBeGreaterThan(0)

    // Check that the "Initiate KYC Check" button is rendered and enabled
    const initiateButton = getByTestId('button-outlined-initiate-kyc-check')
    expect(initiateButton).toBeInTheDocument()
    expect(initiateButton).not.toBeDisabled()
  })

  it('disables the Initiate KYC Check button for blocked customers', () => {
    // Mock useAppSelector to return blocked customer state
    vi.mocked(useAppSelector).mockImplementation((selector) =>
      selector(blockedCustomerState as any)
    )

    const { getByTestId } = render(<KYC />)

    // Check that the "Initiate KYC Check" button is disabled
    const initiateButton = getByTestId('button-outlined-initiate-kyc-check')
    expect(initiateButton).toBeDisabled()
  })

  it('handles search input correctly', () => {
    // Mock useAppSelector to return standard customer state
    vi.mocked(useAppSelector).mockImplementation((selector) =>
      selector(standardCustomerState as any)
    )

    const { getByTestId } = render(<KYC />)

    // Find the search input
    const searchInput = getByTestId('search-input')

    // Simulate typing in the search input
    fireEvent.change(searchInput, { target: { value: 'verification' } })

    // Check that the search value was updated
    expect(searchInput).toHaveValue('verification')
  })

  it('handles filter toggle correctly', () => {
    // Mock useAppSelector to return standard customer state
    vi.mocked(useAppSelector).mockImplementation((selector) =>
      selector(standardCustomerState as any)
    )

    const { getByTestId } = render(<KYC />)

    // Find the filter toggle button
    const filterToggleButton = getByTestId('filter-toggle-button')

    // Click the filter toggle button
    fireEvent.click(filterToggleButton)

    // Apply a filter
    const applyFilterButton = getByTestId('apply-filter-button')
    fireEvent.click(applyFilterButton)

    // Check that the filter was applied (this would normally update the table)
    // Since we're mocking the filter function, we can't directly test the result
    // But we can verify that the filter toggle and apply buttons work
    expect(filterToggleButton).toBeInTheDocument()
    expect(applyFilterButton).toBeInTheDocument()
  })

  it.skip('opens document viewer when clicking on a document', async () => {
    // Mock useAppSelector to return standard customer state
    vi.mocked(useAppSelector).mockImplementation((selector) =>
      selector(standardCustomerState as any)
    )

    const { getAllByTestId } = render(<KYC />)

    // Find all typography elements (document links)
    const documentLinks = getAllByTestId('mui-typography')

    // Click on the first document link
    fireEvent.click(documentLinks[0])

    // Check that setDocumentToggle was called with the correct parameters
    await waitFor(() => {
      expect(setDocumentToggle).toHaveBeenCalledWith({
        open: true,
        imageUrl: 'id_verification.pdf',
      })
    })
  })

  it.skip('renders dropdown menu for actions', () => {
    // Mock useAppSelector to return standard customer state
    vi.mocked(useAppSelector).mockImplementation((selector) =>
      selector(standardCustomerState as any)
    )

    const { getAllByTestId } = render(<KYC />)

    // Find all dropdown menus
    const dropdownMenus = getAllByTestId('table-dropdown-menu')

    // Check that there are dropdown menus for each row
    expect(dropdownMenus.length).toBeGreaterThan(0)

    // Check that the dropdown menus are not disabled
    expect(dropdownMenus[0]).toHaveAttribute('data-disabled', 'false')
  })

  it('disables dropdown menu for blocked customers', () => {
    // Mock useAppSelector to return blocked customer state
    vi.mocked(useAppSelector).mockImplementation((selector) =>
      selector(blockedCustomerState as any)
    )

    const { getAllByTestId } = render(<KYC />)

    // Find all dropdown menus
    const dropdownMenus = getAllByTestId('table-dropdown-menu')

    // Check that the dropdown menus are disabled
    expect(dropdownMenus[0]).toHaveAttribute('data-disabled', 'true')
  })

  it.skip('renders KycChip with correct styling based on status', () => {
    // Mock useAppSelector to return standard customer state
    vi.mocked(useAppSelector).mockImplementation((selector) =>
      selector(standardCustomerState as any)
    )

    const { getAllByTestId } = render(<KYC />)

    // Find all chips
    const chips = getAllByTestId('mui-chip')

    // We should have at least 6 chips (2 per row - checked and status)
    expect(chips.length).toBeGreaterThanOrEqual(6)

    // Check that the chips have the correct content
    // Note: In a real test, we would check the styling more thoroughly
    // but since we're mocking the Chip component, we're limited in what we can test
    expect(chips[0].textContent).toBe('Yes')
    expect(chips[1].textContent).toBe('Passed')
    expect(chips[2].textContent).toBe('Yes')
    expect(chips[3].textContent).toBe('Failed')
  })
})
