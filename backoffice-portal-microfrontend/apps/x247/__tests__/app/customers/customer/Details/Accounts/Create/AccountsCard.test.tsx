import { beforeEach, describe, it, vi } from 'vitest'
import { render } from '../../../../test-utils'
import { AccountsCard } from '@/app/customers/customer/Details/Accounts/Create/AccountsCard'
import { mockTransactionAccount } from '../../../../../stubs/accounts'
import { useAppSelector } from '@/store'
import { mockCustomers } from '../../../../../stubs/customer-listing'
vi.mock('@/store', () => ({
  useAppDispatch: vi.fn(),
  useAppSelector: vi.fn(),
}))
const mockHandleLinkedStatus = vi.fn()
const mockHandleExpand = vi.fn()
const mockHandleExpandCard = vi.fn()
const mockOnUpdateAccount = vi.fn()
const loadedState = {
  customers: {
    customer: mockCustomers.active,
  },
}
describe('AccountsCard Component', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    vi.mocked(useAppSelector).mockImplementation((selector) => {
      return selector(loadedState as any)
    })
  })
  it('Should render Accounts Card', () => {
    render(
      <AccountsCard
        key={''}
        account={mockTransactionAccount}
        handleLinkedStatus={mockHandleLinkedStatus}
        handleExpand={mockHandleExpand}
        handleExpandCard={mockHandleExpandCard}
        onUpdateAccount={mockOnUpdateAccount}
        expanded={[]}
        expandedCard={[]}
        selectedAccounts={[]}
        expandedTariffs={[]}
        expandedNotifications={[]}
        expandedStatements={[]}
        expandedBalanceAlerts={[]}
      />
    )
  })
})
