import { beforeEach, describe, it, vi } from 'vitest'
import { render } from '../../../test-utils'
import ConfirmCancelSave from '@/app/customers/customer/Details/Dialog/ConfirmCancelSave'
vi.mock('@/store', () => ({
  useAppSelector: vi.fn(),
  useAppDispatch: vi.fn(),
}))
const mockOnClose = vi.fn()
const mockOnConfirmCancel = vi.fn()
const mockOnConfirmSubmit = vi.fn()
describe('CustomerUpdatePreview Component', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })
  it('Should render customer update dialog component', () => {
    render(
      <ConfirmCancelSave
        open={true}
        onClose={mockOnClose}
        onConfirmCancel={mockOnConfirmCancel}
        onConfirmSubmit={mockOnConfirmSubmit}
      />
    )
  })
})
