import { beforeEach, describe, it, vi } from 'vitest'
import { render } from '../../../test-utils'
import { useAppDispatch, useAppSelector } from '@/store'
import { generateMockApprovalRequest } from '../../../../stubs/customer-listing'
import { DeviceChangesMadePreview } from '@/app/customers/customer/Details/Dialog/DevicesView'
import { mockDevice } from '../../../../stubs/stubs'
vi.mock('@/store', () => ({
  useAppSelector: vi.fn(),
  useAppDispatch: vi.fn(),
}))
const mockDispatch = vi.fn()
const mockApprovalRequest = generateMockApprovalRequest(100, 'profile-104')
const defaultState = {
  approvalRequests: {
    selectedApprovalRequest: mockApprovalRequest,
  },
  customers: {
    device: mockDevice,
  },
}
describe('DeviceChangesMadePreview Component', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    vi.mocked(useAppDispatch).mockReturnValue(mockDispatch)

    // Set up default mock for useAppSelector
    vi.mocked(useAppSelector).mockImplementation((selector) =>
      selector(defaultState as any)
    )
  })
  it('Should render Devices changes preview component', () => {
    render(
      <DeviceChangesMadePreview selectedApprovalRequest={mockApprovalRequest} />
    )
  })
})
