'use client'

import { describe, it, expect, vi, beforeEach } from 'vitest'
import { render, screen } from '../../../test-utils'
import * as storeActions from '@/store/actions'
import { useAppDispatch, useAppSelector } from '@/store'
import Devices from '@/app/customers/customer/Details/Devices'
import { mockCustomers } from '../../../../stubs/customer-listing'

// Mock the store actions
vi.mock('@/store/actions', () => ({
  getCustomerDevices: vi.fn().mockResolvedValue(undefined),
}))

// Mock the store hooks
vi.mock('@/store', () => ({
  useAppDispatch: vi.fn(),
  useAppSelector: vi.fn(),
}))

// Mock the child components
vi.mock('@/app/customers/customer/Details/Devices/ListDevices', () => ({
  ListView: () => <div data-testid="list-view">List View Component</div>,
}))

vi.mock('@/app/customers/customer/Details/Devices/DeviceDetails', () => ({
  DeviceDetail: () => (
    <div data-testid="device-detail">Device Detail Component</div>
  ),
}))

// Mock dispatch function
const mockDispatch = vi.fn()

// Create mock states for different scenarios
const listViewState = {
  customers: {
    openDevice: false,
    customer: mockCustomers.active,
  },
}

const deviceDetailState = {
  customers: {
    openDevice: true,
    customer: mockCustomers.active,
  },
}

const noCustomerIdState = {
  customers: {
    openDevice: false,
    customer: { ...mockCustomers.active, id: '' },
  },
}

describe('Devices Index Component', () => {
  beforeEach(() => {
    // Reset mocks before each test
    vi.clearAllMocks()

    // Set up default mock for useAppDispatch
    vi.mocked(useAppDispatch).mockReturnValue(mockDispatch)

    // Set up default mock for useAppSelector
    vi.mocked(useAppSelector).mockImplementation((selector) =>
      selector(listViewState as any)
    )
  })

  it('fetches customer devices on initial render', () => {
    render(<Devices />)

    // Check that getCustomerDevices was called with the correct parameters
    expect(storeActions.getCustomerDevices).toHaveBeenCalledWith({
      params: {
        profileID: mockCustomers.active.id,
        page: 1,
        size: 7,
      },
      dispatch: mockDispatch,
    })
  })

  it('renders ListView when openDevice is false', () => {
    // Mock useAppSelector to return listViewState
    vi.mocked(useAppSelector).mockImplementation((selector) =>
      selector(listViewState as any)
    )

    render(<Devices />)

    // Check that ListView is rendered
    expect(screen.getByTestId('list-view')).toBeInTheDocument()

    // Check that DeviceDetail is not rendered
    expect(screen.queryByTestId('device-detail')).not.toBeInTheDocument()
  })

  it('renders DeviceDetail when openDevice is true', () => {
    // Mock useAppSelector to return deviceDetailState
    vi.mocked(useAppSelector).mockImplementation((selector) =>
      selector(deviceDetailState as any)
    )

    render(<Devices />)

    // Check that DeviceDetail is rendered
    expect(screen.getByTestId('device-detail')).toBeInTheDocument()

    // Check that ListView is not rendered
    expect(screen.queryByTestId('list-view')).not.toBeInTheDocument()
  })

  it('handles missing customer ID gracefully', () => {
    // Mock useAppSelector to return noCustomerIdState
    vi.mocked(useAppSelector).mockImplementation((selector) =>
      selector(noCustomerIdState as any)
    )

    render(<Devices />)

    // Check that getCustomerDevices was called with an empty string
    expect(storeActions.getCustomerDevices).toHaveBeenCalledWith({
      params: {
        profileID: '',
        page: 1,
        size: 7,
      },
      dispatch: mockDispatch,
    })
  })

  it('renders correctly when switching between states', () => {
    // Initially render with listViewState
    vi.mocked(useAppSelector).mockImplementation((selector) =>
      selector(listViewState as any)
    )
    const { rerender } = render(<Devices />)

    // Check that ListView is rendered
    expect(screen.getByTestId('list-view')).toBeInTheDocument()

    // Switch to deviceDetailState
    vi.mocked(useAppSelector).mockImplementation((selector) =>
      selector(deviceDetailState as any)
    )
    rerender(<Devices />)

    // Check that DeviceDetail is now rendered
    expect(screen.getByTestId('device-detail')).toBeInTheDocument()

    // Switch back to listViewState
    vi.mocked(useAppSelector).mockImplementation((selector) =>
      selector(listViewState as any)
    )
    rerender(<Devices />)

    // Check that ListView is rendered again
    expect(screen.getByTestId('list-view')).toBeInTheDocument()
  })

  it('only fetches devices once on initial render', () => {
    // Render the component
    const { rerender } = render(<Devices />)

    // Check that getCustomerDevices was called once
    expect(storeActions.getCustomerDevices).toHaveBeenCalledTimes(1)

    // Re-render the component
    rerender(<Devices />)

    // Check that getCustomerDevices was still only called once
    expect(storeActions.getCustomerDevices).toHaveBeenCalledTimes(1)
  })

  it('renders nothing when both components are not available', () => {
    // Create a modified version of the component for testing
    const TestComponent = () => {
      const { openDevice } = useAppSelector((state) => state.customers)
      // Return null for both cases
      return <>{openDevice ? null : null}</>
    }

    // Mock useAppSelector to return listViewState
    vi.mocked(useAppSelector).mockImplementation((selector) =>
      selector(listViewState as any)
    )

    const { container } = render(<TestComponent />)
    // The component should render an empty fragment
    expect(container.firstChild).toBeNull()
  })
})
