'use client'

import { describe, it, expect, vi, beforeEach } from 'vitest'
import { fireEvent, waitFor } from '@testing-library/react'
import { render } from '../../../test-utils'
import Profile from '@/app/customers/customer/Details/Profile/Profile'
import {
  getCustomerProfileById,
  makerUpdateCustomer,
  updateCustomerDetails,
} from '@/store/actions'
import { setChangeTab } from '@/store/reducers'
import { useAppDispatch, useAppSelector } from '@/store'
import * as AccessControlUtils from '@dtbx/store/utils'
import { mockCustomers } from '../../../../stubs/customer-listing'

// Mock the dependencies
vi.mock('@/store/actions', () => ({
  getCustomerProfileById: vi.fn().mockResolvedValue({}),
  makerUpdateCustomer: vi.fn().mockResolvedValue({}),
  updateCustomerDetails: vi.fn().mockResolvedValue({}),
}))

vi.mock('@/store/reducers', () => ({
  setChangeTab: vi.fn(),
}))

vi.mock('@/store', () => ({
  useAppDispatch: vi.fn(),
  useAppSelector: vi.fn(),
}))

// Mock the utils
vi.mock('@dtbx/store/utils', () => ({
  ACCESS_CONTROLS: {
    UPDATE_CUSTOMERS: ['UPDATE_CUSTOMERS'],
  },
  AccessControlWrapper: ({ children, rights }: any) => (
    <div data-testid="access-control-wrapper" data-rights={rights.join(',')}>
      {children}
    </div>
  ),
  formatTimestamp: vi.fn((timestamp) => (timestamp ? 'Formatted Date' : '')),
  HasAccessToRights: vi.fn(),
}))

// Mock the LoadingProfile component
vi.mock('@/app/customers/customer/Details/Profile/LoadingProfile', () => ({
  default: () => <div data-testid="loading-profile">Loading Profile...</div>,
}))

// Mock the ConfirmCancelSave component
vi.mock('@dtbx/ui/components/Overlay', () => ({
  ConfirmCancelSave: ({
    open,
    onConfirmCancel,
    onClose,
    onConfirmSubmit,
  }: any) =>
    open ? (
      <div data-testid="confirm-cancel-save">
        <button data-testid="confirm-cancel-button" onClick={onConfirmCancel}>
          Confirm Cancel
        </button>
        <button data-testid="close-button" onClick={onClose}>
          Close
        </button>
        <button data-testid="confirm-submit-button" onClick={onConfirmSubmit}>
          Confirm Submit
        </button>
      </div>
    ) : null,
}))

// Mock the ProfileIcon component
vi.mock('@dtbx/ui/icons', () => ({
  ProfileIcon: () => <div data-testid="profile-icon">Profile Icon</div>,
}))

// Mock MUI components
vi.mock('@mui/material', async () => {
  const actual = await vi.importActual('@mui/material')
  return {
    ...actual,
    Avatar: ({ children, sx }: any) => (
      <div data-testid="avatar" style={sx}>
        {children}
      </div>
    ),
    Button: ({ children, onClick, variant, sx, disabled, type }: any) => {
      // Check if the button should be hidden
      const isHidden = sx && sx.display === 'none'

      if (isHidden) {
        return null
      }

      return (
        <button
          data-testid={`button-${variant}-${children?.toString().toLowerCase().replace(/\s+/g, '-')}`}
          onClick={onClick}
          disabled={disabled}
          type={type}
          style={sx}
        >
          {children}
        </button>
      )
    },
    Stack: ({ children, sx, direction, spacing }: any) => (
      <div
        data-testid="stack"
        data-direction={direction}
        data-spacing={spacing}
        style={sx}
      >
        {children}
      </div>
    ),
    TextField: ({
      value,
      label,
      fullWidth,
      name,
      inputProps,
      disabled,
      onChange,
    }: any) => (
      <div
        data-testid={`textfield-${label.toLowerCase().replace(/\s+/g, '-')}`}
        data-fullwidth={fullWidth}
        data-readonly={inputProps?.readOnly}
        data-disabled={disabled}
      >
        <label>{label}</label>
        <input
          value={value || ''}
          name={name}
          readOnly={inputProps?.readOnly}
          disabled={disabled}
          onChange={onChange}
          data-testid={`input-${label.toLowerCase().replace(/\s+/g, '-')}`}
        />
      </div>
    ),
    Typography: ({ children, sx, variant }: any) => (
      <span data-testid="typography" style={sx}>
        {children}
      </span>
    ),
  }
})

// Mock localStorage
const localStorageMock = (() => {
  let store: Record<string, string> = {
    tab: '0',
  }

  return {
    getItem: (key: string) => store[key] || null,
    setItem: (key: string, value: string) => {
      store[key] = value
    },
    removeItem: (key: string) => {
      delete store[key]
    },
    clear: () => {
      store = {}
    },
    // Add direct property access
    tab: '0',
  }
})()

Object.defineProperty(window, 'localStorage', { value: localStorageMock })

// Mock dispatch function
const mockDispatch = vi.fn()

// Create mock states for different scenarios
const loadingState = {
  customers: {
    customer: {},
    isCustomerLoading: true,
  },
}

const loadedState = {
  customers: {
    customer: mockCustomers.standard[0],
    isCustomerLoading: false,
  },
}

const blockedCustomerState = {
  customers: {
    customer: mockCustomers.blocked,
    isCustomerLoading: false,
  },
}

describe('Profile Component', () => {
  beforeEach(() => {
    vi.clearAllMocks()

    // Set up default mock for useAppDispatch
    vi.mocked(useAppDispatch).mockReturnValue(mockDispatch)
  })

  it('renders loading state when customer is loading', () => {
    // Mock useAppSelector to return loading state
    vi.mocked(useAppSelector).mockImplementation((selector) =>
      selector(loadingState as any)
    )

    const { getByTestId } = render(<Profile />)

    // Check that loading component is rendered
    expect(getByTestId('loading-profile')).toBeInTheDocument()
  })

  it('renders customer profile data correctly', () => {
    // Mock useAppSelector to return loaded state
    vi.mocked(useAppSelector).mockImplementation((selector) =>
      selector(loadedState as any)
    )

    const { getByTestId } = render(<Profile />)

    // Check that profile data is rendered
    expect(getByTestId('avatar')).toBeInTheDocument()
    expect(getByTestId('profile-icon')).toBeInTheDocument()

    // Check that text fields are rendered with correct values
    expect(getByTestId('textfield-first-name')).toBeInTheDocument()
    expect(getByTestId('input-first-name')).toHaveValue(
      mockCustomers.standard[0].firstName
    )

    expect(getByTestId('textfield-email')).toBeInTheDocument()
    expect(getByTestId('input-email')).toHaveValue(
      mockCustomers.standard[0].email
    )

    // Check that formatTimestamp was called
    expect(AccessControlUtils.formatTimestamp).toHaveBeenCalledWith(
      mockCustomers.standard[0].dateCreated
    )
  })

  it('verifies edit button is not visible due to display:none', () => {
    // Mock HasAccessToRights to return true
    vi.mocked(AccessControlUtils.HasAccessToRights).mockReturnValue(true)

    // Mock useAppSelector to return loaded state
    vi.mocked(useAppSelector).mockImplementation((selector) =>
      selector(loadedState as any)
    )

    const { queryByTestId } = render(<Profile />)

    // Check that the edit button is not in the DOM due to display:none
    // We use queryByTestId instead of getByTestId because we expect the element to not be found
    expect(queryByTestId('button-outlined-edit')).not.toBeInTheDocument()
    expect(queryByTestId('button-outlined-cancel')).not.toBeInTheDocument()
  })

  it('handles missing customer data gracefully', () => {
    // Create state with missing customer data
    const missingDataState = {
      customers: {
        customer: {
          ...mockCustomers.standard[0],
          firstName: null,
          lastName: null,
          otherNames: null,
          email: null,
          phoneNumber: null,
          postalAddress: null,
          dateCreated: null,
          profileAccountStoreIds: [],
        },
        isCustomerLoading: false,
      },
    }

    // Mock useAppSelector to return state with missing data
    vi.mocked(useAppSelector).mockImplementation((selector) =>
      selector(missingDataState as any)
    )

    // This should not throw an error
    const { getByTestId } = render(<Profile />)

    // Check that the component still renders
    expect(getByTestId('avatar')).toBeInTheDocument()

    // Check that text fields handle null values
    expect(getByTestId('input-first-name')).toHaveValue('')
    expect(getByTestId('input-middle-name')).toHaveValue('')
    expect(getByTestId('input-email')).toHaveValue('')
  })

  it('sets tab from localStorage on initial render', () => {
    // Set localStorage tab value
    localStorage.tab = '2'

    // Mock useAppSelector to return loaded state
    vi.mocked(useAppSelector).mockImplementation((selector) =>
      selector(loadedState as any)
    )

    render(<Profile />)

    // Check that setChangeTab was called with correct parameter
    expect(setChangeTab).toHaveBeenCalledWith(2)
  })

  it('handles gender display correctly', () => {
    // Test with male customer
    const maleCustomerState = {
      customers: {
        customer: {
          ...mockCustomers.standard[0],
          sex: 'M',
        },
        isCustomerLoading: false,
      },
    }

    // Mock useAppSelector to return male customer state
    vi.mocked(useAppSelector).mockImplementation((selector) =>
      selector(maleCustomerState as any)
    )

    const { getByTestId, rerender } = render(<Profile />)

    // Check that gender is displayed as Male
    expect(getByTestId('input-gender')).toHaveValue('Male')

    // Test with female customer
    const femaleCustomerState = {
      customers: {
        customer: {
          ...mockCustomers.standard[0],
          sex: 'F',
        },
        isCustomerLoading: false,
      },
    }

    // Mock useAppSelector to return female customer state
    vi.mocked(useAppSelector).mockImplementation((selector) =>
      selector(femaleCustomerState as any)
    )

    // Rerender the component
    rerender(<Profile />)

    // Check that gender is displayed as Female
    expect(getByTestId('input-gender')).toHaveValue('Female')
  })

  // Add more tests that don't rely on interacting with the hidden edit button
})
