import { beforeEach, describe, it, vi } from 'vitest'
import { render } from '../../../test-utils'
import ViewRequestDetails from '@/app/customers/customer/Details/Dialog/ViewRequestDetails'
import { useAppDispatch, useAppSelector } from '@/store'
vi.mock('@/store', () => ({
  useAppSelector: vi.fn(),
  useAppDispatch: vi.fn(),
}))
const mockDispatch = vi.fn()
const defaultState = {
  approvalRequests: {
    approvalDrawerOpen: true,
    selectedApprovalRequest: {},
  },
}
describe('ViewRequestDetails Component', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    // Set up default mock for useAppDispatch
    vi.mocked(useAppDispatch).mockReturnValue(mockDispatch)

    // Set up default mock for useAppSelector
    vi.mocked(useAppSelector).mockImplementation((selector) =>
      selector(defaultState as any)
    )
  })
  it('Should render view request details component', () => {
    render(<ViewRequestDetails />)
  })
})
