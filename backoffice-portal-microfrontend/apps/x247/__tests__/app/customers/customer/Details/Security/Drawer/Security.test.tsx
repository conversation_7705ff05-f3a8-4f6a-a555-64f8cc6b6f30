'use client'

import { describe, it, expect, vi, beforeEach } from 'vitest'
import { render, fireEvent, waitFor, screen } from '../../../../test-utils'
import * as storeActions from '@/store/actions'
import { useAppDispatch } from '@/store'
import SecurityDrawer from '@/app/customers/customer/Details/Security/Drawer/Security'
import { mockCustomers } from '../../../../../stubs/customer-listing'

// Mock the store actions
vi.mock('@/store/actions', () => ({
  getCustomerPinLogsBackoffice: vi.fn().mockResolvedValue(undefined),
}))

// Mock the store hooks
vi.mock('@/store', () => ({
  useAppDispatch: vi.fn(),
}))

// Mock the custom components from @dtbx/ui/components/Input
vi.mock('@dtbx/ui/components/Input', () => ({
  CustomSearchInput: ({ startAdornment, placeholder, sx, onChange }: any) => (
    <div data-testid="custom-search-input" data-placeholder={placeholder}>
      {startAdornment}
      <input onChange={onChange} data-testid="search-input" />
    </div>
  ),
}))

// Mock the custom components from @dtbx/ui/components
vi.mock('@dtbx/ui/components', () => ({
  CustomAntTab: ({ label, ...props }: any) => (
    <button
      data-testid={`tab-${label.toLowerCase().replace(/\s+/g, '-')}`}
      {...props}
    >
      {label}
    </button>
  ),
  CustomTabPanel: ({ children, value, index }: any) =>
    value === index ? (
      <div data-testid={`tab-panel-${index}`}>{children}</div>
    ) : null,
  CustomToggleTabs: ({ children, value, onChange, sx, ...props }: any) => (
    <div data-testid="custom-toggle-tabs" data-value={value} {...props}>
      {Array.isArray(children)
        ? children.map((child, index) => (
            <div key={index} onClick={(e) => onChange(e, index)}>
              {child}
            </div>
          ))
        : children}
    </div>
  ),
}))

// Mock the child components
vi.mock(
  '@/app/customers/customer/Details/Security/Drawer/BackOfficeQuestionsHistory',
  () => ({
    default: () => (
      <div data-testid="back-office-questions-history">
        Back Office Questions History
      </div>
    ),
  })
)

vi.mock(
  '@/app/customers/customer/Details/Security/Drawer/CustomerQuestionsHistory',
  () => ({
    default: () => (
      <div data-testid="customer-questions-history">
        Customer Questions History
      </div>
    ),
  })
)

// Mock MUI components
vi.mock('@mui/material', async () => {
  const actual = await vi.importActual('@mui/material')
  return {
    ...actual,
    Button: ({ children, onClick, variant, sx, ...props }: any) => (
      <button
        data-testid="security-history-button"
        onClick={onClick}
        data-variant={variant}
        {...props}
      >
        {children}
      </button>
    ),
    Drawer: ({ open, children, anchor, variant, slotProps, ...props }: any) =>
      open ? (
        <div
          data-testid="drawer"
          data-anchor={anchor}
          data-variant={variant}
          {...props}
        >
          {children}
        </div>
      ) : null,
    IconButton: ({ children, onClick, sx, ...props }: any) => (
      <button data-testid="icon-button" onClick={onClick} {...props}>
        {children}
      </button>
    ),
    Paper: ({ children, elevation, sx, ...props }: any) => (
      <div data-testid="paper" data-elevation={elevation} {...props}>
        {children}
      </div>
    ),
    Box: ({ children, sx, ...props }: any) => (
      <div data-testid="box" {...props}>
        {children}
      </div>
    ),
    Stack: ({ children, direction, sx, ...props }: any) => (
      <div data-testid="stack" data-direction={direction} {...props}>
        {children}
      </div>
    ),
    Typography: ({ children, variant, sx, ...props }: any) => (
      <span data-testid={`typography-${variant || 'default'}`} {...props}>
        {children}
      </span>
    ),
  }
})

// Mock MUI icons
vi.mock('@mui/icons-material', () => ({
  CloseRounded: () => <span data-testid="close-icon">×</span>,
  SearchRounded: () => <span data-testid="search-icon">🔍</span>,
}))

// Mock dispatch function
const mockDispatch = vi.fn()

describe('SecurityDrawer Component', () => {
  beforeEach(() => {
    // Reset mocks before each test
    vi.clearAllMocks()

    // Set up default mock for useAppDispatch
    vi.mocked(useAppDispatch).mockReturnValue(mockDispatch)
  })

  it('renders the security history button correctly', () => {
    render(<SecurityDrawer customer={mockCustomers.active} />)

    const button = screen.getByTestId('security-history-button')
    expect(button).toBeInTheDocument()
    expect(button).toHaveTextContent('Security History')
    expect(button).toHaveAttribute('data-variant', 'outlined')
  })

  it('opens the drawer when the button is clicked', async () => {
    render(<SecurityDrawer customer={mockCustomers.active} />)

    // Initially, the drawer should not be visible
    expect(screen.queryByTestId('drawer')).not.toBeInTheDocument()

    // Click the button to open the drawer
    const button = screen.getByTestId('security-history-button')
    fireEvent.click(button)

    // The drawer should now be visible
    await waitFor(() => {
      expect(screen.getByTestId('drawer')).toBeInTheDocument()
    })

    // Check drawer properties
    const drawer = screen.getByTestId('drawer')
    expect(drawer).toHaveAttribute('data-anchor', 'right')
    expect(drawer).toHaveAttribute('data-variant', 'persistent')
  })

  it('closes the drawer when the close button is clicked', async () => {
    render(<SecurityDrawer customer={mockCustomers.active} />)

    // Open the drawer
    const button = screen.getByTestId('security-history-button')
    fireEvent.click(button)

    // Wait for the drawer to be visible
    await waitFor(() => {
      expect(screen.getByTestId('drawer')).toBeInTheDocument()
    })

    // Click the close button
    const closeButton = screen.getByTestId('icon-button')
    fireEvent.click(closeButton)

    // The drawer should now be hidden
    await waitFor(() => {
      expect(screen.queryByTestId('drawer')).not.toBeInTheDocument()
    })
  })

  it('fetches customer pin logs when the drawer is opened', async () => {
    render(<SecurityDrawer customer={mockCustomers.active} />)

    // Open the drawer
    const button = screen.getByTestId('security-history-button')
    fireEvent.click(button)

    // Wait for the drawer to be visible
    await waitFor(() => {
      expect(screen.getByTestId('drawer')).toBeInTheDocument()
    })

    // Check that getCustomerPinLogsBackoffice was called with the correct parameters
    expect(storeActions.getCustomerPinLogsBackoffice).toHaveBeenCalledWith(
      mockCustomers.active.id,
      mockDispatch,
      1,
      10
    )
  })

  it('displays the back office tab panel by default', async () => {
    render(<SecurityDrawer customer={mockCustomers.active} />)

    // Open the drawer
    const button = screen.getByTestId('security-history-button')
    fireEvent.click(button)

    // Wait for the drawer to be visible
    await waitFor(() => {
      expect(screen.getByTestId('drawer')).toBeInTheDocument()
    })

    // Check that the back office tab panel is displayed
    expect(screen.getByTestId('tab-panel-0')).toBeInTheDocument()
    expect(
      screen.getByTestId('back-office-questions-history')
    ).toBeInTheDocument()

    // The customer tab panel should not be displayed
    expect(screen.queryByTestId('tab-panel-1')).not.toBeInTheDocument()
  })

  it('switches to the customer tab when clicked', async () => {
    render(<SecurityDrawer customer={mockCustomers.active} />)

    // Open the drawer
    const button = screen.getByTestId('security-history-button')
    fireEvent.click(button)

    // Wait for the drawer to be visible
    await waitFor(() => {
      expect(screen.getByTestId('drawer')).toBeInTheDocument()
    })

    // Click the customer tab
    const customerTab = screen.getByTestId('tab-customer')
    fireEvent.click(customerTab)

    // Check that the customer tab panel is displayed
    await waitFor(() => {
      expect(screen.getByTestId('tab-panel-1')).toBeInTheDocument()
      expect(
        screen.getByTestId('customer-questions-history')
      ).toBeInTheDocument()
    })

    // The back office tab panel should not be displayed
    expect(screen.queryByTestId('tab-panel-0')).not.toBeInTheDocument()
  })

  it('displays the search input in the drawer', async () => {
    render(<SecurityDrawer customer={mockCustomers.active} />)

    // Open the drawer
    const button = screen.getByTestId('security-history-button')
    fireEvent.click(button)

    // Wait for the drawer to be visible
    await waitFor(() => {
      expect(screen.getByTestId('drawer')).toBeInTheDocument()
    })

    // Check that the search input is displayed
    const searchInput = screen.getByTestId('custom-search-input')
    expect(searchInput).toBeInTheDocument()
    expect(searchInput).toHaveAttribute('data-placeholder', 'Search Event')

    // Check that the search icon is displayed
    expect(screen.getByTestId('search-icon')).toBeInTheDocument()
  })

  it('displays the drawer header with the correct title', async () => {
    render(<SecurityDrawer customer={mockCustomers.active} />)

    // Open the drawer
    const button = screen.getByTestId('security-history-button')
    fireEvent.click(button)

    // Wait for the drawer to be visible
    await waitFor(() => {
      expect(screen.getByTestId('drawer')).toBeInTheDocument()
    })

    // Check that the drawer header is displayed with the correct title
    const title = screen.getByTestId('typography-h6')
    expect(title).toBeInTheDocument()
    expect(title).toHaveTextContent('Security Questions History')
  })

  it('handles different customer states correctly', async () => {
    // Test with one-time PIN customer
    const { rerender } = render(
      <SecurityDrawer customer={mockCustomers.oneTimePin} />
    )

    // Open the drawer
    let button = screen.getByTestId('security-history-button')
    fireEvent.click(button)

    // Wait for the drawer to be visible
    await waitFor(() => {
      expect(screen.getByTestId('drawer')).toBeInTheDocument()
    })

    // Check that getCustomerPinLogsBackoffice was called with the correct ID
    expect(storeActions.getCustomerPinLogsBackoffice).toHaveBeenCalledWith(
      mockCustomers.oneTimePin.id,
      mockDispatch,
      1,
      10
    )

    // Close the drawer
    const closeButton = screen.getByTestId('icon-button')
    fireEvent.click(closeButton)

    // Clear mocks
    vi.clearAllMocks()

    // Test with not-set PIN customer
    rerender(<SecurityDrawer customer={mockCustomers.notSetPin} />)

    // Open the drawer again
    button = screen.getByTestId('security-history-button')
    fireEvent.click(button)

    // Wait for the drawer to be visible
    await waitFor(() => {
      expect(screen.getByTestId('drawer')).toBeInTheDocument()
    })

    // Check that getCustomerPinLogsBackoffice was called with the correct ID
    expect(storeActions.getCustomerPinLogsBackoffice).toHaveBeenCalledWith(
      mockCustomers.notSetPin.id,
      mockDispatch,
      1,
      10
    )
  })

  it('handles missing customer ID gracefully', async () => {
    // Create a customer with no ID
    const customerWithNoId = { ...mockCustomers.active, id: '' }

    render(<SecurityDrawer customer={customerWithNoId} />)

    // Open the drawer
    const button = screen.getByTestId('security-history-button')
    fireEvent.click(button)

    // Wait for the drawer to be visible
    await waitFor(() => {
      expect(screen.getByTestId('drawer')).toBeInTheDocument()
    })

    // Check that getCustomerPinLogsBackoffice was called with an empty string
    expect(storeActions.getCustomerPinLogsBackoffice).toHaveBeenCalledWith(
      '',
      mockDispatch,
      1,
      10
    )
  })

  it('does not fetch data if the drawer is not opened', () => {
    render(<SecurityDrawer customer={mockCustomers.active} />)

    // The drawer should not be visible
    expect(screen.queryByTestId('drawer')).not.toBeInTheDocument()

    // getCustomerPinLogsBackoffice should not have been called
    expect(storeActions.getCustomerPinLogsBackoffice).not.toHaveBeenCalled()
  })

  it('renders both tab buttons', async () => {
    render(<SecurityDrawer customer={mockCustomers.active} />)

    // Open the drawer
    const button = screen.getByTestId('security-history-button')
    fireEvent.click(button)

    // Wait for the drawer to be visible
    await waitFor(() => {
      expect(screen.getByTestId('drawer')).toBeInTheDocument()
    })

    // Check that both tab buttons are displayed
    expect(screen.getByTestId('tab-back-office')).toBeInTheDocument()
    expect(screen.getByTestId('tab-customer')).toBeInTheDocument()
  })

  it('maintains the selected tab when closing and reopening the drawer', async () => {
    render(<SecurityDrawer customer={mockCustomers.active} />)

    // Open the drawer
    const button = screen.getByTestId('security-history-button')
    fireEvent.click(button)

    // Wait for the drawer to be visible
    await waitFor(() => {
      expect(screen.getByTestId('drawer')).toBeInTheDocument()
    })

    // Switch to the customer tab
    const customerTab = screen.getByTestId('tab-customer')
    fireEvent.click(customerTab)

    // Check that the customer tab panel is displayed
    await waitFor(() => {
      expect(screen.getByTestId('tab-panel-1')).toBeInTheDocument()
    })

    // Close the drawer
    const closeButton = screen.getByTestId('icon-button')
    fireEvent.click(closeButton)

    // Wait for the drawer to be hidden
    await waitFor(() => {
      expect(screen.queryByTestId('drawer')).not.toBeInTheDocument()
    })

    // Reopen the drawer
    fireEvent.click(button)

    // Wait for the drawer to be visible again
    await waitFor(() => {
      expect(screen.getByTestId('drawer')).toBeInTheDocument()
    })

    // The customer tab should still be selected
    expect(screen.getByTestId('tab-panel-1')).toBeInTheDocument()
    expect(screen.queryByTestId('tab-panel-0')).not.toBeInTheDocument()
  })
})
