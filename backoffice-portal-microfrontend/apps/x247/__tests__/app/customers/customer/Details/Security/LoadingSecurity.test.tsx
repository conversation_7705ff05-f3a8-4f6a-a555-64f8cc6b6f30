'use client'

import { describe, it, expect, vi } from 'vitest'
import { render, screen } from '../../../test-utils'
import { LoadingSecurity } from '@/app/customers/customer/Details/Security/LoadingSecurity'

// Mock MUI components
vi.mock('@mui/material', async () => {
  const actual = await vi.importActual('@mui/material')
  return {
    ...actual,
    Skeleton: ({ variant, sx, ...props }: any) => (
      <div
        data-testid="skeleton"
        data-variant={variant}
        data-height={sx?.height}
        {...props}
      />
    ),
    Stack: ({ children, sx, ...props }: any) => (
      <div
        data-testid="stack"
        data-gap={sx?.gap}
        data-px={sx?.px}
        data-py={sx?.py}
        {...props}
      >
        {children}
      </div>
    ),
  }
})

describe('LoadingSecurity Component', () => {
  it('renders the component correctly', () => {
    render(<LoadingSecurity />)

    // Check that the Stack component is rendered with correct props
    const stack = screen.getByTestId('stack')
    expect(stack).toBeInTheDocument()
    expect(stack).toHaveAttribute('data-gap', '5vh')
    expect(stack).toHaveAttribute('data-px', '3%')
    expect(stack).toHaveAttribute('data-py', '1%')

    // Check that two Skeleton components are rendered
    const skeletons = screen.getAllByTestId('skeleton')
    expect(skeletons).toHaveLength(2)

    // Check that both Skeletons have the correct variant and height
    skeletons.forEach((skeleton) => {
      expect(skeleton).toHaveAttribute('data-variant', 'rectangular')
      expect(skeleton).toHaveAttribute('data-height', '20vh')
    })
  })

  it('renders within a container', () => {
    const { container } = render(
      <div data-testid="container">
        <LoadingSecurity />
      </div>
    )

    // Check that the component renders within a container
    const containerElement = screen.getByTestId('container')
    expect(containerElement).toContainElement(screen.getByTestId('stack'))
  })

  it('renders with correct accessibility attributes', () => {
    render(<LoadingSecurity />)

    // Check that the Stack has appropriate structure for a loading state
    const stack = screen.getByTestId('stack')
    expect(stack).toBeInTheDocument()

    // Verify the skeleton elements are present
    const skeletons = screen.getAllByTestId('skeleton')
    expect(skeletons).toHaveLength(2)
  })

  it('renders consistently across multiple renders', () => {
    const { rerender } = render(<LoadingSecurity />)

    // Get the initial counts and attributes
    const initialStack = screen.getByTestId('stack')
    const initialSkeletons = screen.getAllByTestId('skeleton')
    const initialGap = initialStack.getAttribute('data-gap')
    const initialPx = initialStack.getAttribute('data-px')
    const initialPy = initialStack.getAttribute('data-py')

    // Re-render the component
    rerender(<LoadingSecurity />)

    // Get the new elements
    const newStack = screen.getByTestId('stack')
    const newSkeletons = screen.getAllByTestId('skeleton')

    // Check that the structure and props remain the same
    expect(newSkeletons).toHaveLength(initialSkeletons.length)
    expect(newStack.getAttribute('data-gap')).toBe(initialGap)
    expect(newStack.getAttribute('data-px')).toBe(initialPx)
    expect(newStack.getAttribute('data-py')).toBe(initialPy)
  })

  it('renders skeleton elements with correct dimensions', () => {
    render(<LoadingSecurity />)

    // Get all skeleton elements
    const skeletons = screen.getAllByTestId('skeleton')

    // Check that each skeleton has the correct height
    skeletons.forEach((skeleton) => {
      expect(skeleton).toHaveAttribute('data-height', '20vh')
    })
  })

  it('renders the correct number of skeleton elements', () => {
    render(<LoadingSecurity />)

    // The component should render exactly 2 skeleton elements
    const skeletons = screen.getAllByTestId('skeleton')
    expect(skeletons).toHaveLength(2)
  })
})
