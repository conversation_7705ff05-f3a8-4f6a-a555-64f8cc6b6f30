import { describe, it, vi } from 'vitest'
import { AccountsSummary } from '@/app/customers/customer/Details/Accounts/Create/AccountsSummary'
import { render } from '../../../../test-utils'
import { FormikProps } from 'formik'
import { ICustomerAccountLink } from '@/store/interfaces'
vi.mock('@/store', () => ({
  useAppDispatch: vi.fn(),
  useAppSelector: vi.fn(),
}))
const mockSetStep = vi.fn()
const mockFormik: FormikProps<ICustomerAccountLink> = {
  values: {},
  initialValues: {},
  errors: {},
  touched: {},
  isSubmitting: false,
  isValidating: false,
  submitCount: 0,
  initialErrors: {},
  initialTouched: {},
  handleBlur: vi.fn(),
  handleChange: vi.fn(),
  handleReset: vi.fn(),
  handleSubmit: vi.fn(),
  resetForm: vi.fn(),
  setErrors: vi.fn(),
  setFieldError: vi.fn(),
  setFieldTouched: vi.fn(),
  setFieldValue: vi.fn(),
  setFormikState: vi.fn(),
  setStatus: vi.fn(),
  setSubmitting: vi.fn(),
  setTouched: vi.fn(),
  setValues: vi.fn(),
  submitForm: vi.fn(),
  validateForm: vi.fn(),
  validateField: vi.fn(),
  getFieldProps: vi.fn(),
  getFieldMeta: vi.fn(),
  getFieldHelpers: vi.fn(),
  dirty: false,
  isValid: true,
  status: undefined,
  registerField: vi.fn(),
  unregisterField: vi.fn(),
}

describe('AccountsSummary', () => {
  it.skip('should render accounts summary view', () => {
    render(<AccountsSummary setStep={mockSetStep} formik={mockFormik} />)
  })
})
