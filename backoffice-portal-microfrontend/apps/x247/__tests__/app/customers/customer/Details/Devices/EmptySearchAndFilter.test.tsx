'use client'

import type React from 'react'

import { describe, it, expect, vi, beforeEach } from 'vitest'
import { render, fireEvent, waitFor, screen } from '../../../test-utils'
import EmptySearchAndFilter from '@/app/customers/customer/Details/Devices/EmptySearchAndFilter'
import * as storeActions from '@/store/actions'
import { useAppDispatch, useAppSelector } from '@/store'
import { mockCustomers } from '../../../../stubs/customer-listing'

// Mock the store actions
vi.mock('@/store/actions', () => ({
  getCustomerDevices: vi.fn().mockResolvedValue(undefined),
}))

// Mock the store hooks
vi.mock('@/store', () => ({
  useAppDispatch: vi.fn(),
  useAppSelector: vi.fn(),
}))

// Mock tiny-case
vi.mock('tiny-case', () => ({
  sentenceCase: (str: string) =>
    str.charAt(0).toUpperCase() + str.slice(1).toLowerCase(),
}))

// Mock MUI components
vi.mock('@mui/material', async () => {
  const actual = await vi.importActual('@mui/material')
  return {
    ...actual,
    Stack: ({
      children,
      sx,
      ...props
    }: {
      children: React.ReactNode
      sx?: any
      [key: string]: any
    }) => (
      <div
        data-testid="stack"
        data-height={sx?.height}
        data-width={sx?.width}
        data-justify-content={sx?.justifyContent}
        data-align-items={sx?.alignItems}
        data-padding={sx?.padding}
        data-gap={sx?.gap}
        data-background-image={sx?.backgroundImage}
        data-flex-direction={sx?.flexDirection}
        data-margin-top={sx?.marginTop}
        {...props}
      >
        {children}
      </div>
    ),
    Typography: ({
      children,
      variant,
      sx,
      ...props
    }: {
      children: React.ReactNode
      variant?: string
      sx?: any
      [key: string]: any
    }) => (
      <div
        data-testid={`typography-${variant || 'default'}`}
        data-text-align={sx?.textAlign}
        {...props}
      >
        {children}
      </div>
    ),
    Button: ({
      children,
      variant,
      onClick,
      ...props
    }: {
      children: React.ReactNode
      variant?: string
      onClick?: () => void
      [key: string]: any
    }) => (
      <button
        data-testid={`button-${variant || 'default'}`}
        onClick={onClick}
        {...props}
      >
        {children}
      </button>
    ),
  }
})

// Mock dispatch function
const mockDispatch = vi.fn()

// Create mock states for different scenarios
const noFiltersState = {
  customers: {
    deviceFilters: {
      filterValue: [],
    },
    deviceSearchParams: {
      searchValue: '',
      searchBy: [''],
    },
    selectedCustomer: {
      customer: mockCustomers.active,
    },
  },
}

const withSearchState = {
  customers: {
    deviceFilters: {
      filterValue: [],
    },
    deviceSearchParams: {
      searchValue: 'iPhone',
      searchBy: ['deviceType'],
    },
    selectedCustomer: {
      customer: mockCustomers.active,
    },
  },
}

const withFilterState = {
  customers: {
    deviceFilters: {
      filterValue: ['ACTIVE'],
    },
    deviceSearchParams: {
      searchValue: '',
      searchBy: [''],
    },
    selectedCustomer: {
      customer: mockCustomers.active,
    },
  },
}

const withBothState = {
  customers: {
    deviceFilters: {
      filterValue: ['ACTIVE'],
    },
    deviceSearchParams: {
      searchValue: 'iPhone',
      searchBy: ['deviceType'],
    },
    selectedCustomer: {
      customer: mockCustomers.active,
    },
  },
}

const noCustomerIdState = {
  customers: {
    deviceFilters: {
      filterValue: ['ACTIVE'],
    },
    deviceSearchParams: {
      searchValue: 'iPhone',
      searchBy: ['deviceType'],
    },
    selectedCustomer: {
      customer: { ...mockCustomers.active, id: '' },
    },
  },
}

describe('EmptySearchAndFilter Component', () => {
  beforeEach(() => {
    // Reset mocks before each test
    vi.clearAllMocks()

    // Set up default mock for useAppDispatch
    vi.mocked(useAppDispatch).mockReturnValue(mockDispatch)

    // Set up default mock for useAppSelector
    vi.mocked(useAppSelector).mockImplementation((selector) =>
      selector(noFiltersState as any)
    )
  })

  it('renders the component with correct layout', () => {
    render(<EmptySearchAndFilter />)

    // Check that the main stack is rendered with correct styling
    const mainStack = screen.getAllByTestId('stack')[0]
    expect(mainStack).toBeInTheDocument()
    expect(mainStack).toHaveAttribute('data-height', '100%')
    expect(mainStack).toHaveAttribute('data-width', '100%')
    expect(mainStack).toHaveAttribute('data-justify-content', 'center')
    expect(mainStack).toHaveAttribute('data-align-items', 'center')
    expect(mainStack).toHaveAttribute('data-padding', '10px')

    // Check that the background image stack is rendered
    const backgroundStack = screen.getAllByTestId('stack')[1]
    expect(backgroundStack).toBeInTheDocument()
    expect(backgroundStack).toHaveAttribute('data-width', '480px')
    expect(backgroundStack).toHaveAttribute('data-height', '480px')
    expect(backgroundStack).toHaveAttribute(
      'data-background-image',
      'url(/dashboard/combo.svg)'
    )
    expect(backgroundStack).toHaveAttribute('data-justify-content', 'flex-end')
    expect(backgroundStack).toHaveAttribute('data-gap', '45px')
  })

  it('displays the correct header text', () => {
    render(<EmptySearchAndFilter />)

    // Check that the header text is displayed
    const headerText = screen.getByTestId('typography-subtitle1')
    expect(headerText).toBeInTheDocument()
    expect(headerText).toHaveTextContent('No device was found')
  })

  it('does not display search message when no search or filter is active', () => {
    render(<EmptySearchAndFilter />)

    // Check that the search message is not displayed
    expect(screen.queryByTestId('typography-subtitle3')).not.toBeInTheDocument()
  })

  it('does not display clear search button when no search or filter is active', () => {
    render(<EmptySearchAndFilter />)

    // Check that the clear search button is not displayed
    expect(screen.queryByTestId('button-outlined')).not.toBeInTheDocument()
  })

  it('displays search message when search is active', () => {
    // Mock useAppSelector to return state with search
    vi.mocked(useAppSelector).mockImplementation((selector) =>
      selector(withSearchState as any)
    )

    render(<EmptySearchAndFilter />)

    // Check that the search message is displayed
    const searchMessage = screen.getByTestId('typography-subtitle3')
    expect(searchMessage).toBeInTheDocument()
    expect(searchMessage).toHaveTextContent(
      'Your search "iPhone" by Devicetype did not match any devices. Please try again'
    )
  })

  it.skip('displays search message when filter is active', () => {
    // Mock useAppSelector to return state with filter
    vi.mocked(useAppSelector).mockImplementation((selector) =>
      selector(withFilterState as any)
    )

    render(<EmptySearchAndFilter />)

    // Check that the search message is displayed
    const searchMessage = screen.getByTestId('typography-subtitle3')
    expect(searchMessage).toBeInTheDocument()
    expect(searchMessage).toHaveTextContent(
      'Your filter criteria did not match any devices. Please try again'
    )
  })

  it('displays search message when both search and filter are active', () => {
    // Mock useAppSelector to return state with both search and filter
    vi.mocked(useAppSelector).mockImplementation((selector) =>
      selector(withBothState as any)
    )

    render(<EmptySearchAndFilter />)

    // Check that the search message is displayed
    const searchMessage = screen.getByTestId('typography-subtitle3')
    expect(searchMessage).toBeInTheDocument()
    expect(searchMessage).toHaveTextContent(
      'Your search "iPhone" by Devicetype did not match any devices. Please try again'
    )
  })

  it('displays clear search button when search is active', () => {
    // Mock useAppSelector to return state with search
    vi.mocked(useAppSelector).mockImplementation((selector) =>
      selector(withSearchState as any)
    )

    render(<EmptySearchAndFilter />)

    // Check that the clear search button is displayed
    const clearButton = screen.getByTestId('button-outlined')
    expect(clearButton).toBeInTheDocument()
    expect(clearButton).toHaveTextContent('Clear search')
  })

  it('displays clear search button when filter is active', () => {
    // Mock useAppSelector to return state with filter
    vi.mocked(useAppSelector).mockImplementation((selector) =>
      selector(withFilterState as any)
    )

    render(<EmptySearchAndFilter />)

    // Check that the clear search button is displayed
    const clearButton = screen.getByTestId('button-outlined')
    expect(clearButton).toBeInTheDocument()
    expect(clearButton).toHaveTextContent('Clear search')
  })

  it('calls getCustomerDevices when clear search button is clicked', async () => {
    // Mock useAppSelector to return state with search
    vi.mocked(useAppSelector).mockImplementation((selector) =>
      selector(withSearchState as any)
    )

    render(<EmptySearchAndFilter />)

    // Get the clear search button
    const clearButton = screen.getByTestId('button-outlined')

    // Click the clear search button
    fireEvent.click(clearButton)

    // Check that getCustomerDevices was called with the correct parameters
    await waitFor(() => {
      expect(storeActions.getCustomerDevices).toHaveBeenCalledWith({
        params: {
          profileID: mockCustomers.active.id,
          page: 0,
          size: 7,
          deviceType: '',
        },
        dispatch: mockDispatch,
      })
    })
  })

  it('handles missing customer ID gracefully', async () => {
    // Mock useAppSelector to return state with no customer ID
    vi.mocked(useAppSelector).mockImplementation((selector) =>
      selector(noCustomerIdState as any)
    )

    render(<EmptySearchAndFilter />)

    // Get the clear search button
    const clearButton = screen.getByTestId('button-outlined')

    // Click the clear search button
    fireEvent.click(clearButton)

    // Check that getCustomerDevices was called with an empty string
    await waitFor(() => {
      expect(storeActions.getCustomerDevices).toHaveBeenCalledWith({
        params: {
          profileID: '',
          page: 0,
          size: 7,
          deviceType: '',
        },
        dispatch: mockDispatch,
      })
    })
  })

  it('renders the correct number of stacks', () => {
    render(<EmptySearchAndFilter />)

    // Get all stack elements
    const stacks = screen.getAllByTestId('stack')

    // Check that there are 6 stacks in total
    expect(stacks).toHaveLength(6)
  })

  it('renders the buttons stack with correct styling', () => {
    // Mock useAppSelector to return state with search
    vi.mocked(useAppSelector).mockImplementation((selector) =>
      selector(withSearchState as any)
    )

    render(<EmptySearchAndFilter />)

    // Get the buttons stack
    const buttonsStack = screen.getAllByTestId('stack')[5]

    // Check that the buttons stack has correct styling
    expect(buttonsStack).toHaveAttribute('data-justify-content', 'center')
    expect(buttonsStack).toHaveAttribute('data-flex-direction', 'row')
    expect(buttonsStack).toHaveAttribute('data-gap', '20px')
    expect(buttonsStack).toHaveAttribute('data-margin-top', '20px')
  })

  it('renders the header stack with correct styling', () => {
    render(<EmptySearchAndFilter />)

    // Get the header stack
    const headerStack = screen.getAllByTestId('stack')[4]

    // Check that the header stack has correct styling
    expect(headerStack).toHaveAttribute('data-justify-content', 'center')
    expect(headerStack).toHaveAttribute('data-align-items', 'center')
    expect(headerStack).toHaveAttribute('data-gap', '8px')
    expect(headerStack).toHaveAttribute('data-width', '65%')
  })

  it('centers the search message text', () => {
    // Mock useAppSelector to return state with search
    vi.mocked(useAppSelector).mockImplementation((selector) =>
      selector(withSearchState as any)
    )

    render(<EmptySearchAndFilter />)

    // Get the search message
    const searchMessage = screen.getByTestId('typography-subtitle3')

    // Check that the text is centered
    expect(searchMessage).toHaveAttribute('data-text-align', 'center')
  })
})
