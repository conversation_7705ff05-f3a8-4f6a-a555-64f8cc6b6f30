'use client'

import type React from 'react'

import { describe, it, expect, vi, beforeEach } from 'vitest'
import { render, fireEvent, waitFor, screen } from '../../../../test-utils'
import * as storeActions from '@/store/actions'
import { useAppDispatch } from '@/store'
import { mockCustomers } from '../../../../../stubs/customer-listing'
import type { ICustomer } from '@/store/interfaces'
import { PinDrawer } from '@/app/customers/customer/Details/Security/Drawer/PIN'

// Mock the store actions
vi.mock('@/store/actions', () => ({
  getCustomerPinLogsBackoffice: vi.fn().mockResolvedValue(undefined),
  getCustomerPinHistory: vi.fn().mockResolvedValue(undefined),
}))

// Mock the store hooks
vi.mock('@/store', () => ({
  useAppDispatch: vi.fn(),
}))

// Mock the custom components from @dtbx/ui/components/Input
vi.mock('@dtbx/ui/components/Input', () => ({
  CustomSearchInput: ({
    startAdornment,
    placeholder,
    sx,
    onChange,
  }: {
    startAdornment?: React.ReactNode
    placeholder?: string
    sx?: any
    onChange?: (e: any) => void
  }) => (
    <div data-testid="custom-search-input" data-placeholder={placeholder}>
      {startAdornment}
      <input onChange={onChange} data-testid="search-input" />
    </div>
  ),
}))

// Mock the custom components from @dtbx/ui/components
vi.mock('@dtbx/ui/components', () => ({
  CustomAntTab: ({
    label,
    ...props
  }: {
    label: string
    [key: string]: any
  }) => (
    <button
      data-testid={`tab-${label.toLowerCase().replace(/\s+/g, '-')}`}
      {...props}
    >
      {label}
    </button>
  ),
  CustomTabPanel: ({
    children,
    value,
    index,
  }: {
    children: React.ReactNode
    value: number
    index: number
  }) =>
    value === index ? (
      <div data-testid={`tab-panel-${index}`}>{children}</div>
    ) : null,
  CustomToggleTabs: ({
    children,
    value,
    onChange,
    sx,
    ...props
  }: {
    children: React.ReactNode
    value: number
    onChange: (event: React.SyntheticEvent, newValue: number) => void
    sx?: any
    [key: string]: any
  }) => (
    <div data-testid="custom-toggle-tabs" data-value={value} {...props}>
      {Array.isArray(children)
        ? children.map((child, index) => (
            <div key={index} onClick={(e) => onChange(e, index)}>
              {child}
            </div>
          ))
        : children}
    </div>
  ),
}))

// Mock the child components
vi.mock(
  '@/app/customers/customer/Details/Security/Drawer/BackOfficePinHistory',
  () => ({
    default: () => (
      <div data-testid="back-office-pin-history">Back Office Pin History</div>
    ),
  })
)

vi.mock(
  '@/app/customers/customer/Details/Security/Drawer/CustomerPinHistory',
  () => ({
    default: ({
      page,
      onPageChange,
    }: {
      page: number
      onPageChange: (newPage: number) => void
    }) => (
      <div data-testid="customer-pin-history" data-page={page}>
        Customer Pin History
        <button
          data-testid="next-page-button"
          onClick={() => onPageChange(page + 1)}
        >
          Next Page
        </button>
      </div>
    ),
  })
)

// Mock MUI components
vi.mock('@mui/material', async () => {
  const actual = await vi.importActual('@mui/material')
  return {
    ...actual,
    Button: ({
      children,
      onClick,
      variant,
      sx,
      ...props
    }: {
      children: React.ReactNode
      onClick?: () => void
      variant?: string
      sx?: any
      [key: string]: any
    }) => (
      <button
        data-testid="pin-history-button"
        onClick={onClick}
        data-variant={variant}
        {...props}
      >
        {children}
      </button>
    ),
    Drawer: ({
      open,
      children,
      anchor,
      variant,
      slotProps,
      ...props
    }: {
      open: boolean
      children: React.ReactNode
      anchor?: string
      variant?: string
      slotProps?: any
      [key: string]: any
    }) =>
      open ? (
        <div
          data-testid="drawer"
          data-anchor={anchor}
          data-variant={variant}
          {...props}
        >
          {children}
        </div>
      ) : null,
    IconButton: ({
      children,
      onClick,
      sx,
      ...props
    }: {
      children: React.ReactNode
      onClick?: () => void
      sx?: any
      [key: string]: any
    }) => (
      <button data-testid="icon-button" onClick={onClick} {...props}>
        {children}
      </button>
    ),
    Paper: ({
      children,
      elevation,
      sx,
      ...props
    }: {
      children: React.ReactNode
      elevation?: number
      sx?: any
      [key: string]: any
    }) => (
      <div data-testid="paper" data-elevation={elevation} {...props}>
        {children}
      </div>
    ),
    Box: ({
      children,
      sx,
      ...props
    }: {
      children: React.ReactNode
      sx?: any
      [key: string]: any
    }) => (
      <div data-testid="box" {...props}>
        {children}
      </div>
    ),
    Stack: ({
      children,
      direction,
      sx,
      ...props
    }: {
      children: React.ReactNode
      direction?: string
      sx?: any
      [key: string]: any
    }) => (
      <div data-testid="stack" data-direction={direction} {...props}>
        {children}
      </div>
    ),
    Typography: ({
      children,
      variant,
      sx,
      ...props
    }: {
      children: React.ReactNode
      variant?: string
      sx?: any
      [key: string]: any
    }) => (
      <span data-testid={`typography-${variant || 'default'}`} {...props}>
        {children}
      </span>
    ),
  }
})

// Mock MUI icons
vi.mock('@mui/icons-material', () => ({
  CloseRounded: () => <span data-testid="close-icon">×</span>,
  SearchRounded: () => <span data-testid="search-icon">🔍</span>,
}))

// Mock dispatch function
const mockDispatch = vi.fn()

describe('PinDrawer Component', () => {
  beforeEach(() => {
    // Reset mocks before each test
    vi.clearAllMocks()

    // Set up default mock for useAppDispatch
    vi.mocked(useAppDispatch).mockReturnValue(mockDispatch)
  })

  it('renders the pin history button correctly', () => {
    render(<PinDrawer customer={mockCustomers.active} />)

    const button = screen.getByTestId('pin-history-button')
    expect(button).toBeInTheDocument()
    expect(button).toHaveTextContent('Pin History')
    expect(button).toHaveAttribute('data-variant', 'outlined')
  })

  it('opens the drawer when the button is clicked', async () => {
    render(<PinDrawer customer={mockCustomers.active} />)

    // Initially, the drawer should not be visible
    expect(screen.queryByTestId('drawer')).not.toBeInTheDocument()

    // Click the button to open the drawer
    const button = screen.getByTestId('pin-history-button')
    fireEvent.click(button)

    // The drawer should now be visible
    await waitFor(() => {
      expect(screen.getByTestId('drawer')).toBeInTheDocument()
    })

    // Check drawer properties
    const drawer = screen.getByTestId('drawer')
    expect(drawer).toHaveAttribute('data-anchor', 'right')
    expect(drawer).toHaveAttribute('data-variant', 'temporary')
  })

  it('closes the drawer when the close button is clicked', async () => {
    render(<PinDrawer customer={mockCustomers.active} />)

    // Open the drawer
    const button = screen.getByTestId('pin-history-button')
    fireEvent.click(button)

    // Wait for the drawer to be visible
    await waitFor(() => {
      expect(screen.getByTestId('drawer')).toBeInTheDocument()
    })

    // Click the close button
    const closeButton = screen.getByTestId('icon-button')
    fireEvent.click(closeButton)

    // The drawer should now be hidden
    await waitFor(() => {
      expect(screen.queryByTestId('drawer')).not.toBeInTheDocument()
    })
  })

  it.skip('fetches customer pin logs when the drawer is opened', async () => {
    render(<PinDrawer customer={mockCustomers.active} />)

    // Open the drawer
    const button = screen.getByTestId('pin-history-button')
    fireEvent.click(button)

    // Wait for the drawer to be visible
    await waitFor(() => {
      expect(screen.getByTestId('drawer')).toBeInTheDocument()
    })

    // Check that getCustomerPinLogsBackoffice was called with the correct parameters
    expect(storeActions.getCustomerPinLogsBackoffice).toHaveBeenCalledWith(
      mockCustomers.active.id,
      mockDispatch,
      1,
      10
    )
  })

  it('displays the back office tab panel by default', async () => {
    render(<PinDrawer customer={mockCustomers.active} />)

    // Open the drawer
    const button = screen.getByTestId('pin-history-button')
    fireEvent.click(button)

    // Wait for the drawer to be visible
    await waitFor(() => {
      expect(screen.getByTestId('drawer')).toBeInTheDocument()
    })

    // Check that the back office tab panel is displayed
    expect(screen.getByTestId('tab-panel-0')).toBeInTheDocument()
    expect(screen.getByTestId('back-office-pin-history')).toBeInTheDocument()

    // The customer tab panel should not be displayed
    expect(screen.queryByTestId('tab-panel-1')).not.toBeInTheDocument()
  })

  it('switches to the customer tab when clicked', async () => {
    render(<PinDrawer customer={mockCustomers.active} />)

    // Open the drawer
    const button = screen.getByTestId('pin-history-button')
    fireEvent.click(button)

    // Wait for the drawer to be visible
    await waitFor(() => {
      expect(screen.getByTestId('drawer')).toBeInTheDocument()
    })

    // Click the customer tab
    const customerTab = screen.getByTestId('tab-customer')
    fireEvent.click(customerTab)

    // Check that the customer tab panel is displayed
    await waitFor(() => {
      expect(screen.getByTestId('tab-panel-1')).toBeInTheDocument()
      expect(screen.getByTestId('customer-pin-history')).toBeInTheDocument()
    })

    // The back office tab panel should not be displayed
    expect(screen.queryByTestId('tab-panel-0')).not.toBeInTheDocument()
  })

  it('handles pagination for customer pin history', async () => {
    render(<PinDrawer customer={mockCustomers.active} />)

    // Open the drawer
    const button = screen.getByTestId('pin-history-button')
    fireEvent.click(button)

    // Wait for the drawer to be visible
    await waitFor(() => {
      expect(screen.getByTestId('drawer')).toBeInTheDocument()
    })

    // Switch to the customer tab
    const customerTab = screen.getByTestId('tab-customer')
    fireEvent.click(customerTab)

    // Wait for the customer tab panel to be displayed
    await waitFor(() => {
      expect(screen.getByTestId('tab-panel-1')).toBeInTheDocument()
      expect(screen.getByTestId('customer-pin-history')).toBeInTheDocument()
    })

    // Check initial page
    expect(screen.getByTestId('customer-pin-history')).toHaveAttribute(
      'data-page',
      '1'
    )

    // Click the next page button
    const nextPageButton = screen.getByTestId('next-page-button')
    fireEvent.click(nextPageButton)

    // Check that the page has been updated
    await waitFor(() => {
      expect(screen.getByTestId('customer-pin-history')).toHaveAttribute(
        'data-page',
        '2'
      )
    })
  })

  it('resets page to 1 when switching tabs', async () => {
    render(<PinDrawer customer={mockCustomers.active} />)

    // Open the drawer
    const button = screen.getByTestId('pin-history-button')
    fireEvent.click(button)

    // Wait for the drawer to be visible
    await waitFor(() => {
      expect(screen.getByTestId('drawer')).toBeInTheDocument()
    })

    // Switch to the customer tab
    const customerTab = screen.getByTestId('tab-customer')
    fireEvent.click(customerTab)

    // Wait for the customer tab panel to be displayed
    await waitFor(() => {
      expect(screen.getByTestId('tab-panel-1')).toBeInTheDocument()
      expect(screen.getByTestId('customer-pin-history')).toBeInTheDocument()
    })

    // Click the next page button to change the page
    const nextPageButton = screen.getByTestId('next-page-button')
    fireEvent.click(nextPageButton)

    // Check that the page has been updated
    await waitFor(() => {
      expect(screen.getByTestId('customer-pin-history')).toHaveAttribute(
        'data-page',
        '2'
      )
    })

    // Switch back to the back office tab
    const backOfficeTab = screen.getByTestId('tab-back-office')
    fireEvent.click(backOfficeTab)

    // Switch to the customer tab again
    fireEvent.click(customerTab)

    // Check that the page has been reset to 1
    await waitFor(() => {
      expect(screen.getByTestId('customer-pin-history')).toHaveAttribute(
        'data-page',
        '1'
      )
    })
  })

  it('displays the search input in the drawer', async () => {
    render(<PinDrawer customer={mockCustomers.active} />)

    // Open the drawer
    const button = screen.getByTestId('pin-history-button')
    fireEvent.click(button)

    // Wait for the drawer to be visible
    await waitFor(() => {
      expect(screen.getByTestId('drawer')).toBeInTheDocument()
    })

    // Check that the search input is displayed
    const searchInput = screen.getByTestId('custom-search-input')
    expect(searchInput).toBeInTheDocument()
    expect(searchInput).toHaveAttribute('data-placeholder', 'Search Event')

    // Check that the search icon is displayed
    expect(screen.getByTestId('search-icon')).toBeInTheDocument()
  })

  it('displays the drawer header with the correct title', async () => {
    render(<PinDrawer customer={mockCustomers.active} />)

    // Open the drawer
    const button = screen.getByTestId('pin-history-button')
    fireEvent.click(button)

    // Wait for the drawer to be visible
    await waitFor(() => {
      expect(screen.getByTestId('drawer')).toBeInTheDocument()
    })

    // Check that the drawer header is displayed with the correct title
    const title = screen.getByTestId('typography-h6')
    expect(title).toBeInTheDocument()
    expect(title).toHaveTextContent('PIN History')
  })

  it.skip('handles different customer states correctly', async () => {
    // Test with one-time PIN customer
    const { rerender } = render(
      <PinDrawer customer={mockCustomers.oneTimePin} />
    )

    // Open the drawer
    let button = screen.getByTestId('pin-history-button')
    fireEvent.click(button)

    // Wait for the drawer to be visible
    await waitFor(() => {
      expect(screen.getByTestId('drawer')).toBeInTheDocument()
    })

    // Check that getCustomerPinLogsBackoffice was called with the correct ID
    expect(storeActions.getCustomerPinLogsBackoffice).toHaveBeenCalledWith(
      mockCustomers.oneTimePin.id,
      mockDispatch,
      1,
      10
    )

    // Close the drawer
    const closeButton = screen.getByTestId('icon-button')
    fireEvent.click(closeButton)

    // Clear mocks
    vi.clearAllMocks()

    // Test with not-set PIN customer
    rerender(<PinDrawer customer={mockCustomers.notSetPin} />)

    // Open the drawer again
    button = screen.getByTestId('pin-history-button')
    fireEvent.click(button)

    // Wait for the drawer to be visible
    await waitFor(() => {
      expect(screen.getByTestId('drawer')).toBeInTheDocument()
    })

    // Check that getCustomerPinLogsBackoffice was called with the correct ID
    expect(storeActions.getCustomerPinLogsBackoffice).toHaveBeenCalledWith(
      mockCustomers.notSetPin.id,
      mockDispatch,
      1,
      10
    )
  })

  it.skip('handles missing customer ID gracefully', async () => {
    // Create a customer with no ID
    const customerWithNoId: ICustomer = { ...mockCustomers.active, id: '' }

    render(<PinDrawer customer={customerWithNoId} />)

    // Open the drawer
    const button = screen.getByTestId('pin-history-button')
    fireEvent.click(button)

    // Wait for the drawer to be visible
    await waitFor(() => {
      expect(screen.getByTestId('drawer')).toBeInTheDocument()
    })

    // Check that getCustomerPinLogsBackoffice was called with an empty string
    expect(storeActions.getCustomerPinLogsBackoffice).toHaveBeenCalledWith(
      '',
      mockDispatch,
      1,
      10
    )
  })

  it('does not fetch data if the drawer is not opened', () => {
    render(<PinDrawer customer={mockCustomers.active} />)

    // The drawer should not be visible
    expect(screen.queryByTestId('drawer')).not.toBeInTheDocument()

    // getCustomerPinLogsBackoffice should not have been called
    expect(storeActions.getCustomerPinLogsBackoffice).not.toHaveBeenCalled()
  })

  it('renders both tab buttons', async () => {
    render(<PinDrawer customer={mockCustomers.active} />)

    // Open the drawer
    const button = screen.getByTestId('pin-history-button')
    fireEvent.click(button)

    // Wait for the drawer to be visible
    await waitFor(() => {
      expect(screen.getByTestId('drawer')).toBeInTheDocument()
    })

    // Check that both tab buttons are displayed
    expect(screen.getByTestId('tab-back-office')).toBeInTheDocument()
    expect(screen.getByTestId('tab-customer')).toBeInTheDocument()
  })
})
