'use client'

import type React from 'react'
import type { IDevice } from '@/store/interfaces'

import { describe, it, expect, vi, beforeEach } from 'vitest'
import { render, fireEvent, waitFor, screen } from '../../../test-utils'
import {
  DeviceMoreMenu,
  DeviceStatusChange,
} from '@/app/customers/customer/Details/Devices/MoreMenu'
import * as storeActions from '@/store/actions'
import * as storeReducers from '@/store/reducers'
import { useAppDispatch, useAppSelector } from '@/store'
import * as utils from '@dtbx/store/utils'
import { mockCustomers } from '../../../../stubs/customer-listing'

// Mock the store actions
vi.mock('@/store/actions', () => ({
  getCustomerDeviceDetail: vi.fn().mockResolvedValue(undefined),
  getCustomerDevices: vi.fn().mockResolvedValue(undefined),
  activateCustomerDevice: vi.fn().mockResolvedValue(undefined),
  deactivateCustomerDevice: vi.fn().mockResolvedValue(undefined),
  makerActivateCustomerDevice: vi.fn().mockResolvedValue(undefined),
  makerDeactivateCustomerDevice: vi.fn().mockResolvedValue(undefined),
}))

// Mock the store reducers
vi.mock('@/store/reducers', () => ({
  setOpenDevice: vi.fn().mockReturnValue({ type: 'customers/setOpenDevice' }),
}))

// Mock the store hooks
vi.mock('@/store', () => ({
  useAppDispatch: vi.fn(),
  useAppSelector: vi.fn(),
}))

// Mock the utils
vi.mock('@dtbx/store/utils', () => ({
  HasAccessToRights: vi.fn(),
}))

// Mock the custom components
vi.mock('@dtbx/ui/components/Dialogs', () => ({
  CustomDialog: ({
    open,
    onClose,
    children,
    ...props
  }: {
    open: boolean
    onClose: any
    children: React.ReactNode
  }) =>
    open ? (
      <div
        data-testid="custom-dialog"
        onClick={(e) => onClose(e, 'backdropClick')}
        {...props}
      >
        {children}
      </div>
    ) : null,
}))

vi.mock('@dtbx/ui/components/Input', () => ({
  CustomFormControlLabel: ({
    control,
    onChange,
    onClick,
    checked,
    label,
    labelPlacement,
  }: {
    control: React.ReactNode
    onChange: (e: any) => void
    onClick: (e: any) => void
    checked: boolean
    label: string
    labelPlacement: string
  }) => (
    <label
      data-testid={`form-control-label-${label.toLowerCase().replace(/\s+/g, '-')}`}
      data-checked={checked}
    >
      <input
        type="checkbox"
        checked={checked}
        onChange={onChange}
        onClick={onClick}
        data-testid={`checkbox-${label.toLowerCase().replace(/\s+/g, '-')}`}
      />
      {label}
    </label>
  ),
}))

vi.mock('@dtbx/ui/components/Loading', () => ({
  LoadingButton: () => <div data-testid="loading-button">Loading...</div>,
}))

vi.mock('@dtbx/ui/icons', () => ({
  CheckBoxIcon: () => <div data-testid="check-box-icon">☑</div>,
}))

// Mock MUI components
vi.mock('@mui/material', async () => {
  const actual = await vi.importActual('@mui/material')
  return {
    ...actual,
    Button: ({
      children,
      onClick,
      variant,
      disabled,
      endIcon,
      fullWidth,
      sx,
      ...props
    }: {
      children: React.ReactNode
      onClick?: (e: any) => void
      variant?: string
      disabled?: boolean
      endIcon?: React.ReactNode
      fullWidth?: boolean
      sx?: any
      [key: string]: any
    }) => (
      <button
        onClick={onClick}
        disabled={disabled}
        data-variant={variant}
        data-full-width={fullWidth}
        data-testid={`button-${typeof children === 'string' ? children.toLowerCase().replace(/\s+/g, '-') : 'custom'}`}
        {...props}
      >
        {children}
        {endIcon}
      </button>
    ),
    Menu: ({
      children,
      open,
      anchorEl,
      onClose,
      ...props
    }: {
      children: React.ReactNode
      open: boolean
      anchorEl: any
      onClose: (e: any) => void
      [key: string]: any
    }) =>
      open ? (
        <div data-testid="menu" {...props}>
          {children}
        </div>
      ) : null,
    MenuItem: ({
      children,
      onClick,
      ...props
    }: {
      children: React.ReactNode
      onClick?: (e: any) => void
      [key: string]: any
    }) => (
      <div
        data-testid={`menu-item-${typeof children === 'string' ? children.toLowerCase() : 'custom'}`}
        onClick={onClick}
        {...props}
      >
        {children}
      </div>
    ),
    DialogTitle: ({
      children,
      sx,
      ...props
    }: {
      children: React.ReactNode
      sx?: any
      [key: string]: any
    }) => (
      <div data-testid="dialog-title" {...props}>
        {children}
      </div>
    ),
    DialogContent: ({
      children,
      sx,
      ...props
    }: {
      children: React.ReactNode
      sx?: any
      [key: string]: any
    }) => (
      <div data-testid="dialog-content" {...props}>
        {children}
      </div>
    ),
    DialogActions: ({
      children,
      sx,
      ...props
    }: {
      children: React.ReactNode
      sx?: any
      [key: string]: any
    }) => (
      <div data-testid="dialog-actions" {...props}>
        {children}
      </div>
    ),
    IconButton: ({
      children,
      onClick,
      ...props
    }: {
      children: React.ReactNode
      onClick?: (e: any) => void
      [key: string]: any
    }) => (
      <button data-testid="icon-button" onClick={onClick} {...props}>
        {children}
      </button>
    ),
    Stack: ({
      children,
      sx,
      ...props
    }: {
      children: React.ReactNode
      sx?: any
      [key: string]: any
    }) => (
      <div data-testid="stack" {...props}>
        {children}
      </div>
    ),
    Typography: ({
      children,
      variant,
      sx,
      ...props
    }: {
      children: React.ReactNode
      variant?: string
      sx?: any
      [key: string]: any
    }) => (
      <div data-testid={`typography-${variant || 'default'}`} {...props}>
        {children}
      </div>
    ),
    FormControl: ({
      children,
      fullWidth,
      ...props
    }: {
      children: React.ReactNode
      fullWidth?: boolean
      [key: string]: any
    }) => (
      <div data-testid="form-control" data-full-width={fullWidth} {...props}>
        {children}
      </div>
    ),
    Checkbox: ({
      icon,
      ...props
    }: {
      icon: React.ReactNode
      [key: string]: any
    }) => (
      <div data-testid="checkbox" {...props}>
        {icon}
      </div>
    ),
    TextField: ({
      rows,
      placeholder,
      fullWidth,
      onChange,
      onClick,
      onKeyDown,
      error,
      helperText,
      ...props
    }: {
      rows?: number
      placeholder?: string
      fullWidth?: boolean
      onChange?: (e: any) => void
      onClick?: (e: any) => void
      onKeyDown?: (e: any) => void
      error?: boolean
      helperText?: string
      [key: string]: any
    }) => (
      <div
        data-testid="text-field"
        data-rows={rows}
        data-error={error}
        data-full-width={fullWidth}
        {...props}
      >
        <input
          placeholder={placeholder}
          onChange={onChange}
          onClick={onClick}
          onKeyDown={onKeyDown}
          data-testid="text-field-input"
        />
        {error && helperText && (
          <div data-testid="helper-text">{helperText}</div>
        )}
      </div>
    ),
  }
})

// Mock MUI icons
vi.mock('@mui/icons-material', () => ({
  CheckCircleOutline: () => (
    <span data-testid="check-circle-outline-icon">✓</span>
  ),
  DoDisturbRounded: () => <span data-testid="do-disturb-rounded-icon">✗</span>,
  KeyboardArrowDownIcon: () => (
    <span data-testid="keyboard-arrow-down-icon">▼</span>
  ),
}))

vi.mock('@mui/icons-material/Close', () => ({
  default: () => <span data-testid="close-icon">×</span>,
}))

vi.mock('@mui/icons-material/KeyboardArrowDown', () => ({
  default: () => <span data-testid="keyboard-arrow-down-icon">▼</span>,
}))

// Mock dispatch function
const mockDispatch = vi.fn()

// Create mock device data
const mockActiveDevice: IDevice = {
  deviceId: 'DEV-001',
  deviceType: 'Mobile',
  deviceName: 'iPhone 12',
  deviceModel: 'A2403',
  deviceStatus: 'ACTIVE',
  uuid: '12345',
  dateCreated: '2023-01-01T12:00:00Z',
  devicePlatform: 'iOS',
  phoneNumber: '+1234567890',
}

const mockInactiveDevice: IDevice = {
  deviceId: 'DEV-002',
  deviceType: 'Tablet',
  deviceName: 'iPad Pro',
  deviceModel: 'A2301',
  deviceStatus: 'INACTIVE',
  uuid: '67890',
  dateCreated: '2023-02-15T14:30:00Z',
  devicePlatform: 'iOS',
  phoneNumber: '+1987654321',
}

// Create mock states for different scenarios
const defaultState = {
  customers: {
    customer: mockCustomers.active,
    isLoadingDevice: false,
  },
}

const loadingState = {
  customers: {
    customer: mockCustomers.active,
    isLoadingDevice: true,
  },
}

const blockedCustomerState = {
  customers: {
    customer: { ...mockCustomers.active, isBlocked: true },
    isLoadingDevice: false,
  },
}

describe('DeviceMoreMenu Component', () => {
  beforeEach(() => {
    // Reset mocks before each test
    vi.clearAllMocks()

    // Set up default mock for useAppDispatch
    vi.mocked(useAppDispatch).mockReturnValue(mockDispatch)

    // Set up default mock for useAppSelector
    vi.mocked(useAppSelector).mockImplementation((selector) =>
      selector(defaultState as any)
    )
  })

  it('renders the actions button', () => {
    render(<DeviceMoreMenu device={mockActiveDevice} />)

    // Check that the actions button is rendered
    const actionsButton = screen.getByTestId('button-actions')
    expect(actionsButton).toBeInTheDocument()
    expect(actionsButton).toHaveTextContent('Actions')
    expect(actionsButton).not.toBeDisabled()
  })

  it('disables the actions button when customer is blocked', () => {
    // Mock useAppSelector to return blocked customer state
    vi.mocked(useAppSelector).mockImplementation((selector) =>
      selector(blockedCustomerState as any)
    )

    render(<DeviceMoreMenu device={mockActiveDevice} />)

    // Check that the actions button is disabled
    const actionsButton = screen.getByTestId('button-actions')
    expect(actionsButton).toBeDisabled()
  })

  it('opens the menu when actions button is clicked', () => {
    render(<DeviceMoreMenu device={mockActiveDevice} />)

    // Initially, the menu should not be visible
    expect(screen.queryByTestId('menu')).not.toBeInTheDocument()

    // Click the actions button
    const actionsButton = screen.getByTestId('button-actions')
    fireEvent.click(actionsButton)

    // The menu should now be visible
    expect(screen.getByTestId('menu')).toBeInTheDocument()
  })

  it.skip('displays the correct menu items', () => {
    render(<DeviceMoreMenu device={mockActiveDevice} />)

    // Click the actions button to open the menu
    const actionsButton = screen.getByTestId('button-actions')
    fireEvent.click(actionsButton)

    // Check that the menu items are displayed
    expect(
      screen.getByTestId('menu-item-view-more-details')
    ).toBeInTheDocument()
    expect(screen.getByTestId('menu-item-deactivate')).toBeInTheDocument()
  })

  it.skip('handles view more details click', async () => {
    render(<DeviceMoreMenu device={mockActiveDevice} />)

    // Click the actions button to open the menu
    const actionsButton = screen.getByTestId('button-actions')
    fireEvent.click(actionsButton)

    // Click the view more details menu item
    const viewMoreDetailsMenuItem = screen.getByTestId(
      'menu-item-view-more-details'
    )
    fireEvent.click(viewMoreDetailsMenuItem)

    // Check that setOpenDevice was called with true
    expect(storeReducers.setOpenDevice).toHaveBeenCalledWith(true)

    // Check that getCustomerDeviceDetail was called with the correct parameters
    await waitFor(() => {
      expect(storeActions.getCustomerDeviceDetail).toHaveBeenCalledWith({
        deviceID: mockActiveDevice.uuid,
        profileID: mockCustomers.active.id,
        dispatch: mockDispatch,
      })
    })
  })

  it.skip('closes the menu when clicking outside', () => {
    render(<DeviceMoreMenu device={mockActiveDevice} />)

    // Click the actions button to open the menu
    const actionsButton = screen.getByTestId('button-actions')
    fireEvent.click(actionsButton)

    // The menu should be visible
    expect(screen.getByTestId('menu')).toBeInTheDocument()

    // Simulate clicking outside the menu by calling onClose
    const menu = screen.getByTestId('menu')
    fireEvent.click(menu)

    // The menu should no longer be visible
    expect(screen.queryByTestId('menu')).not.toBeInTheDocument()
  })

  it.skip('stops event propagation when clicking the actions button', () => {
    // Create a mock event with stopPropagation method
    const mockEvent = {
      stopPropagation: vi.fn(),
    }

    render(<DeviceMoreMenu device={mockActiveDevice} />)

    // Get the actions button
    const actionsButton = screen.getByTestId('button-actions')

    // Simulate click with mock event
    fireEvent.click(actionsButton, mockEvent)

    // Check that stopPropagation was called
    expect(mockEvent.stopPropagation).toHaveBeenCalled()
  })

  it.skip('stops event propagation when closing the menu', () => {
    // Create a mock event with stopPropagation method
    const mockEvent = {
      stopPropagation: vi.fn(),
    }

    render(<DeviceMoreMenu device={mockActiveDevice} />)

    // Click the actions button to open the menu
    const actionsButton = screen.getByTestId('button-actions')
    fireEvent.click(actionsButton)

    // Get the menu
    const menu = screen.getByTestId('menu')

    // Simulate click with mock event
    fireEvent.click(menu, mockEvent)

    // Check that stopPropagation was called
    expect(mockEvent.stopPropagation).toHaveBeenCalled()
  })

  it.skip('handles missing customer ID gracefully', async () => {
    // Mock useAppSelector to return state with customer having no ID
    vi.mocked(useAppSelector).mockImplementation((selector) =>
      selector({
        ...defaultState,
        customers: {
          ...defaultState.customers,
          customer: { ...mockCustomers.active, id: '' },
        },
      } as any)
    )

    render(<DeviceMoreMenu device={mockActiveDevice} />)

    // Click the actions button to open the menu
    const actionsButton = screen.getByTestId('button-actions')
    fireEvent.click(actionsButton)

    // Click the view more details menu item
    const viewMoreDetailsMenuItem = screen.getByTestId(
      'menu-item-view-more-details'
    )
    fireEvent.click(viewMoreDetailsMenuItem)

    // Check that getCustomerDeviceDetail was called with an empty string
    await waitFor(() => {
      expect(storeActions.getCustomerDeviceDetail).toHaveBeenCalledWith({
        deviceID: mockActiveDevice.uuid,
        profileID: '',
        dispatch: mockDispatch,
      })
    })
  })
})

describe('DeviceStatusChange Component', () => {
  beforeEach(() => {
    // Reset mocks before each test
    vi.clearAllMocks()

    // Set up default mock for useAppDispatch
    vi.mocked(useAppDispatch).mockReturnValue(mockDispatch)

    // Set up default mock for useAppSelector
    vi.mocked(useAppSelector).mockImplementation((selector) =>
      selector(defaultState as any)
    )
  })

  it('renders the deactivate menu item for active device in list view', () => {
    render(
      <DeviceStatusChange
        device={mockActiveDevice}
        customer={mockCustomers.active}
        origin="list"
      />
    )

    // Check that the deactivate menu item is rendered
    expect(screen.getByTestId('menu-item-deactivate')).toBeInTheDocument()
    expect(screen.getByTestId('menu-item-deactivate')).toHaveTextContent(
      'Deactivate'
    )
  })

  it('renders the activate menu item for inactive device in list view', () => {
    render(
      <DeviceStatusChange
        device={mockInactiveDevice}
        customer={mockCustomers.active}
        origin="list"
      />
    )

    // Check that the activate menu item is rendered
    expect(screen.getByTestId('menu-item-activate')).toBeInTheDocument()
    expect(screen.getByTestId('menu-item-activate')).toHaveTextContent(
      'Activate'
    )
  })

  it('renders the deactivate button for active device in view mode', () => {
    render(
      <DeviceStatusChange
        device={mockActiveDevice}
        customer={mockCustomers.active}
        origin="view"
      />
    )

    // Check that the deactivate button is rendered
    const deactivateButton = screen.getByTestId('button-deactivate-device')
    expect(deactivateButton).toBeInTheDocument()
    expect(deactivateButton).toHaveTextContent('Deactivate Device')
  })

  it('renders the activate button for inactive device in view mode', () => {
    render(
      <DeviceStatusChange
        device={mockInactiveDevice}
        customer={mockCustomers.active}
        origin="view"
      />
    )

    // Check that the activate button is rendered
    const activateButton = screen.getByTestId('button-activate-device')
    expect(activateButton).toBeInTheDocument()
    expect(activateButton).toHaveTextContent('Activate Device')
  })

  it('opens the dialog when clicking the menu item', () => {
    render(
      <DeviceStatusChange
        device={mockActiveDevice}
        customer={mockCustomers.active}
        origin="list"
      />
    )

    // Initially, the dialog should not be visible
    expect(screen.queryByTestId('custom-dialog')).not.toBeInTheDocument()

    // Click the deactivate menu item
    const deactivateMenuItem = screen.getByTestId('menu-item-deactivate')
    fireEvent.click(deactivateMenuItem)

    // The dialog should now be visible
    expect(screen.getByTestId('custom-dialog')).toBeInTheDocument()
  })

  it('displays the correct dialog title for deactivation', () => {
    render(
      <DeviceStatusChange
        device={mockActiveDevice}
        customer={mockCustomers.active}
        origin="list"
      />
    )

    // Click the deactivate menu item to open the dialog
    const deactivateMenuItem = screen.getByTestId('menu-item-deactivate')
    fireEvent.click(deactivateMenuItem)

    // Check that the dialog title is correct
    const dialogTitle = screen.getByTestId('dialog-title')
    expect(dialogTitle).toHaveTextContent('Deactivate Device')
  })

  it('displays the correct dialog title for activation', () => {
    render(
      <DeviceStatusChange
        device={mockInactiveDevice}
        customer={mockCustomers.active}
        origin="list"
      />
    )

    // Click the activate menu item to open the dialog
    const activateMenuItem = screen.getByTestId('menu-item-activate')
    fireEvent.click(activateMenuItem)

    // Check that the dialog title is correct
    const dialogTitle = screen.getByTestId('dialog-title')
    expect(dialogTitle).toHaveTextContent('Activate Device')
  })

  it('displays deactivation reason options', () => {
    render(
      <DeviceStatusChange
        device={mockActiveDevice}
        customer={mockCustomers.active}
        origin="list"
      />
    )

    // Click the deactivate menu item to open the dialog
    const deactivateMenuItem = screen.getByTestId('menu-item-deactivate')
    fireEvent.click(deactivateMenuItem)

    // Check that the deactivation reason options are displayed
    expect(
      screen.getByTestId('form-control-label-suspicious-activity')
    ).toBeInTheDocument()
    expect(
      screen.getByTestId('form-control-label-reported-security-breach')
    ).toBeInTheDocument()
    expect(
      screen.getByTestId('form-control-label-fraudulent-account')
    ).toBeInTheDocument()
    expect(
      screen.getByTestId('form-control-label-account-closure')
    ).toBeInTheDocument()
    expect(
      screen.getByTestId('form-control-label-changing-phones')
    ).toBeInTheDocument()
    expect(screen.getByTestId('form-control-label-other')).toBeInTheDocument()
  })

  it('displays activation reason options', () => {
    render(
      <DeviceStatusChange
        device={mockInactiveDevice}
        customer={mockCustomers.active}
        origin="list"
      />
    )

    // Click the activate menu item to open the dialog
    const activateMenuItem = screen.getByTestId('menu-item-activate')
    fireEvent.click(activateMenuItem)

    // Check that the activation reason options are displayed
    expect(
      screen.getByTestId('form-control-label-device-verification-complete')
    ).toBeInTheDocument()
    expect(
      screen.getByTestId('form-control-label-device-returned')
    ).toBeInTheDocument()
    expect(
      screen.getByTestId('form-control-label-device-found')
    ).toBeInTheDocument()
    expect(screen.getByTestId('form-control-label-other')).toBeInTheDocument()
  })

  it('selects a reason when clicking a checkbox', () => {
    render(
      <DeviceStatusChange
        device={mockActiveDevice}
        customer={mockCustomers.active}
        origin="list"
      />
    )

    // Click the deactivate menu item to open the dialog
    const deactivateMenuItem = screen.getByTestId('menu-item-deactivate')
    fireEvent.click(deactivateMenuItem)

    // Click the "Suspicious Activity" checkbox
    const suspiciousActivityCheckbox = screen.getByTestId(
      'checkbox-suspicious-activity'
    )
    fireEvent.click(suspiciousActivityCheckbox)

    // Check that the checkbox is checked
    expect(
      screen.getByTestId('form-control-label-suspicious-activity')
    ).toHaveAttribute('data-checked', 'true')
  })

  it("shows text field when 'Other' reason is selected", () => {
    render(
      <DeviceStatusChange
        device={mockActiveDevice}
        customer={mockCustomers.active}
        origin="list"
      />
    )

    // Click the deactivate menu item to open the dialog
    const deactivateMenuItem = screen.getByTestId('menu-item-deactivate')
    fireEvent.click(deactivateMenuItem)

    // Initially, the text field should not be visible
    expect(screen.queryByTestId('text-field')).not.toBeInTheDocument()

    // Click the "Other" checkbox
    const otherCheckbox = screen.getByTestId('checkbox-other')
    fireEvent.click(otherCheckbox)

    // The text field should now be visible
    expect(screen.getByTestId('text-field')).toBeInTheDocument()
    expect(screen.getByTestId('text-field-input')).toHaveAttribute(
      'placeholder',
      'Type your reason here'
    )
  })

  it("validates the 'Other' reason text field", () => {
    render(
      <DeviceStatusChange
        device={mockActiveDevice}
        customer={mockCustomers.active}
        origin="list"
      />
    )

    // Click the deactivate menu item to open the dialog
    const deactivateMenuItem = screen.getByTestId('menu-item-deactivate')
    fireEvent.click(deactivateMenuItem)

    // Click the "Other" checkbox
    const otherCheckbox = screen.getByTestId('checkbox-other')
    fireEvent.click(otherCheckbox)

    // Get the text field input
    const textFieldInput = screen.getByTestId('text-field-input')

    // Type a short reason (less than 10 characters)
    fireEvent.change(textFieldInput, { target: { value: 'Short' } })

    // Check that the error message is displayed
    expect(screen.getByTestId('helper-text')).toBeInTheDocument()
    expect(screen.getByTestId('helper-text')).toHaveTextContent(
      'Reason must be at least 10 characters and contain valid characters.'
    )
  })

  it('clears the error when a valid reason is entered', () => {
    render(
      <DeviceStatusChange
        device={mockActiveDevice}
        customer={mockCustomers.active}
        origin="list"
      />
    )

    // Click the deactivate menu item to open the dialog
    const deactivateMenuItem = screen.getByTestId('menu-item-deactivate')
    fireEvent.click(deactivateMenuItem)

    // Click the "Other" checkbox
    const otherCheckbox = screen.getByTestId('checkbox-other')
    fireEvent.click(otherCheckbox)

    // Get the text field input
    const textFieldInput = screen.getByTestId('text-field-input')

    // Type a short reason (less than 10 characters)
    fireEvent.change(textFieldInput, { target: { value: 'Short' } })

    // Check that the error message is displayed
    expect(screen.getByTestId('helper-text')).toBeInTheDocument()

    // Type a valid reason (at least 10 characters)
    fireEvent.change(textFieldInput, {
      target: { value: 'This is a valid reason with more than 10 characters' },
    })

    // Check that the error message is no longer displayed
    expect(screen.queryByTestId('helper-text')).not.toBeInTheDocument()
  })

  it('disables the submit button when no reason is selected', () => {
    render(
      <DeviceStatusChange
        device={mockActiveDevice}
        customer={mockCustomers.active}
        origin="list"
      />
    )

    // Click the deactivate menu item to open the dialog
    const deactivateMenuItem = screen.getByTestId('menu-item-deactivate')
    fireEvent.click(deactivateMenuItem)

    // Check that the submit button is disabled
    const submitButton = screen.getByTestId('button-deactivate')
    expect(submitButton).toBeDisabled()
  })

  it("disables the submit button when 'Other' is selected but no reason is provided", () => {
    render(
      <DeviceStatusChange
        device={mockActiveDevice}
        customer={mockCustomers.active}
        origin="list"
      />
    )

    // Click the deactivate menu item to open the dialog
    const deactivateMenuItem = screen.getByTestId('menu-item-deactivate')
    fireEvent.click(deactivateMenuItem)

    // Click the "Other" checkbox
    const otherCheckbox = screen.getByTestId('checkbox-other')
    fireEvent.click(otherCheckbox)

    // Check that the submit button is disabled
    const submitButton = screen.getByTestId('button-deactivate')
    expect(submitButton).toBeDisabled()
  })

  it('enables the submit button when a valid reason is selected', () => {
    render(
      <DeviceStatusChange
        device={mockActiveDevice}
        customer={mockCustomers.active}
        origin="list"
      />
    )

    // Click the deactivate menu item to open the dialog
    const deactivateMenuItem = screen.getByTestId('menu-item-deactivate')
    fireEvent.click(deactivateMenuItem)

    // Click the "Suspicious Activity" checkbox
    const suspiciousActivityCheckbox = screen.getByTestId(
      'checkbox-suspicious-activity'
    )
    fireEvent.click(suspiciousActivityCheckbox)

    // Check that the submit button is enabled
    const submitButton = screen.getByTestId('button-deactivate')
    expect(submitButton).not.toBeDisabled()
  })

  it("enables the submit button when 'Other' is selected and a valid reason is provided", () => {
    render(
      <DeviceStatusChange
        device={mockActiveDevice}
        customer={mockCustomers.active}
        origin="list"
      />
    )

    // Click the deactivate menu item to open the dialog
    const deactivateMenuItem = screen.getByTestId('menu-item-deactivate')
    fireEvent.click(deactivateMenuItem)

    // Click the "Other" checkbox
    const otherCheckbox = screen.getByTestId('checkbox-other')
    fireEvent.click(otherCheckbox)

    // Get the text field input
    const textFieldInput = screen.getByTestId('text-field-input')

    // Type a valid reason
    fireEvent.change(textFieldInput, {
      target: { value: 'This is a valid reason with more than 10 characters' },
    })

    // Check that the submit button is enabled
    const submitButton = screen.getByTestId('button-deactivate')
    expect(submitButton).not.toBeDisabled()
  })

  it('calls deactivateCustomerDevice with super role when user has super rights', async () => {
    // Mock HasAccessToRights to return true
    vi.mocked(utils.HasAccessToRights).mockReturnValue(true)

    render(
      <DeviceStatusChange
        device={mockActiveDevice}
        customer={mockCustomers.active}
        origin="list"
      />
    )

    // Click the deactivate menu item to open the dialog
    const deactivateMenuItem = screen.getByTestId('menu-item-deactivate')
    fireEvent.click(deactivateMenuItem)

    // Click the "Suspicious Activity" checkbox
    const suspiciousActivityCheckbox = screen.getByTestId(
      'checkbox-suspicious-activity'
    )
    fireEvent.click(suspiciousActivityCheckbox)

    // Click the submit button
    const submitButton = screen.getByTestId('button-deactivate')
    fireEvent.click(submitButton)

    // Check that deactivateCustomerDevice was called with the correct parameters
    await waitFor(() => {
      expect(storeActions.deactivateCustomerDevice).toHaveBeenCalledWith({
        profileID: mockCustomers.active.id,
        deviceID: mockActiveDevice.uuid,
        dispatch: mockDispatch,
        comments: 'Suspicious Activity',
      })
    })

    // Check that getCustomerDevices was called to refresh the list
    await waitFor(() => {
      expect(storeActions.getCustomerDevices).toHaveBeenCalledWith({
        params: {
          profileID: mockCustomers.active.id,
          page: 0,
          size: 7,
        },
        dispatch: mockDispatch,
      })
    })
  })

  it('calls makerDeactivateCustomerDevice when user does not have super rights', async () => {
    // Mock HasAccessToRights to return false
    vi.mocked(utils.HasAccessToRights).mockReturnValue(false)

    render(
      <DeviceStatusChange
        device={mockActiveDevice}
        customer={mockCustomers.active}
        origin="list"
      />
    )

    // Click the deactivate menu item to open the dialog
    const deactivateMenuItem = screen.getByTestId('menu-item-deactivate')
    fireEvent.click(deactivateMenuItem)

    // Click the "Suspicious Activity" checkbox
    const suspiciousActivityCheckbox = screen.getByTestId(
      'checkbox-suspicious-activity'
    )
    fireEvent.click(suspiciousActivityCheckbox)

    // Click the submit button
    const submitButton = screen.getByTestId('button-deactivate')
    fireEvent.click(submitButton)

    // Check that makerDeactivateCustomerDevice was called with the correct parameters
    await waitFor(() => {
      expect(storeActions.makerDeactivateCustomerDevice).toHaveBeenCalledWith({
        profileID: mockCustomers.active.id,
        deviceID: mockActiveDevice.uuid,
        dispatch: mockDispatch,
        comments: 'Suspicious Activity',
      })
    })
  })

  it('calls activateCustomerDevice with super role when user has super rights', async () => {
    // Mock HasAccessToRights to return true
    vi.mocked(utils.HasAccessToRights).mockReturnValue(true)

    render(
      <DeviceStatusChange
        device={mockInactiveDevice}
        customer={mockCustomers.active}
        origin="list"
      />
    )

    // Click the activate menu item to open the dialog
    const activateMenuItem = screen.getByTestId('menu-item-activate')
    fireEvent.click(activateMenuItem)

    // Click the "Device Verification Complete" checkbox
    const deviceVerificationCompleteCheckbox = screen.getByTestId(
      'checkbox-device-verification-complete'
    )
    fireEvent.click(deviceVerificationCompleteCheckbox)

    // Click the submit button
    const submitButton = screen.getByTestId('button-activate')
    fireEvent.click(submitButton)

    // Check that activateCustomerDevice was called with the correct parameters
    await waitFor(() => {
      expect(storeActions.activateCustomerDevice).toHaveBeenCalledWith({
        profileID: mockCustomers.active.id,
        deviceID: mockInactiveDevice.uuid,
        dispatch: mockDispatch,
        comments: 'Device verification complete',
      })
    })
  })

  it('shows loading button during device status change', () => {
    // Mock useAppSelector to return loading state
    vi.mocked(useAppSelector).mockImplementation((selector) =>
      selector(loadingState as any)
    )

    render(
      <DeviceStatusChange
        device={mockActiveDevice}
        customer={mockCustomers.active}
        origin="list"
      />
    )

    // Click the deactivate menu item to open the dialog
    const deactivateMenuItem = screen.getByTestId('menu-item-deactivate')
    fireEvent.click(deactivateMenuItem)

    // Click the "Suspicious Activity" checkbox
    const suspiciousActivityCheckbox = screen.getByTestId(
      'checkbox-suspicious-activity'
    )
    fireEvent.click(suspiciousActivityCheckbox)

    // Check that the loading button is displayed instead of the submit button
    expect(screen.getByTestId('loading-button')).toBeInTheDocument()
    expect(screen.queryByTestId('button-deactivate')).not.toBeInTheDocument()
  })

  it('closes the dialog when cancel button is clicked', () => {
    render(
      <DeviceStatusChange
        device={mockActiveDevice}
        customer={mockCustomers.active}
        origin="list"
      />
    )

    // Click the deactivate menu item to open the dialog
    const deactivateMenuItem = screen.getByTestId('menu-item-deactivate')
    fireEvent.click(deactivateMenuItem)

    // The dialog should be visible
    expect(screen.getByTestId('custom-dialog')).toBeInTheDocument()

    // Click the cancel button
    const cancelButton = screen.getByTestId('button-cancel')
    fireEvent.click(cancelButton)

    // The dialog should no longer be visible
    expect(screen.queryByTestId('custom-dialog')).not.toBeInTheDocument()
  })

  it('prevents closing the dialog on backdrop click', () => {
    render(
      <DeviceStatusChange
        device={mockActiveDevice}
        customer={mockCustomers.active}
        origin="list"
      />
    )

    // Click the deactivate menu item to open the dialog
    const deactivateMenuItem = screen.getByTestId('menu-item-deactivate')
    fireEvent.click(deactivateMenuItem)

    // The dialog should be visible
    const dialog = screen.getByTestId('custom-dialog')
    expect(dialog).toBeInTheDocument()

    // Click the dialog backdrop
    fireEvent.click(dialog)

    // The dialog should still be visible
    expect(screen.getByTestId('custom-dialog')).toBeInTheDocument()
  })

  it('closes the dialog when the close icon is clicked', () => {
    render(
      <DeviceStatusChange
        device={mockActiveDevice}
        customer={mockCustomers.active}
        origin="list"
      />
    )

    // Click the deactivate menu item to open the dialog
    const deactivateMenuItem = screen.getByTestId('menu-item-deactivate')
    fireEvent.click(deactivateMenuItem)

    // The dialog should be visible
    expect(screen.getByTestId('custom-dialog')).toBeInTheDocument()

    // Click the close icon button
    const closeIconButton = screen.getByTestId('icon-button')
    fireEvent.click(closeIconButton)

    // The dialog should no longer be visible
    expect(screen.queryByTestId('custom-dialog')).not.toBeInTheDocument()
  })

  it.skip('resets state when dialog is closed', () => {
    render(
      <DeviceStatusChange
        device={mockActiveDevice}
        customer={mockCustomers.active}
        origin="list"
      />
    )

    // Click the deactivate menu item to open the dialog
    const deactivateMenuItem = screen.getByTestId('menu-item-deactivate')
    fireEvent.click(deactivateMenuItem)

    // Click the "Other" checkbox
    const otherCheckbox = screen.getByTestId('checkbox-other')
    fireEvent.click(otherCheckbox)

    // Get the text field input
    const textFieldInput = screen.getByTestId('text-field-input')

    // Type an invalid reason
    fireEvent.change(textFieldInput, { target: { value: 'Short' } })

    // Check that the error message is displayed
    expect(screen.getByTestId('helper-text')).toBeInTheDocument()

    // Click the cancel button to close the dialog
    const cancelButton = screen.getByTestId('button-cancel')
    fireEvent.click(cancelButton)

    // Reopen the dialog
    fireEvent.click(deactivateMenuItem)

    // The error message should not be displayed
    expect(screen.queryByTestId('helper-text')).not.toBeInTheDocument()

    // The text field should not be visible (Other not selected)
    expect(screen.queryByTestId('text-field')).not.toBeInTheDocument()
  })

  it('handles missing customer ID gracefully', async () => {
    // Mock HasAccessToRights to return true
    vi.mocked(utils.HasAccessToRights).mockReturnValue(true)

    render(
      <DeviceStatusChange
        device={mockActiveDevice}
        customer={{ ...mockCustomers.active, id: '' }}
        origin="list"
      />
    )

    // Click the deactivate menu item to open the dialog
    const deactivateMenuItem = screen.getByTestId('menu-item-deactivate')
    fireEvent.click(deactivateMenuItem)

    // Click the "Suspicious Activity" checkbox
    const suspiciousActivityCheckbox = screen.getByTestId(
      'checkbox-suspicious-activity'
    )
    fireEvent.click(suspiciousActivityCheckbox)

    // Click the submit button
    const submitButton = screen.getByTestId('button-deactivate')
    fireEvent.click(submitButton)

    // The function should return early without calling any actions
    expect(storeActions.deactivateCustomerDevice).not.toHaveBeenCalled()
    expect(storeActions.makerDeactivateCustomerDevice).not.toHaveBeenCalled()
  })

  it.skip('stops event propagation when clicking the menu item', () => {
    // Create a mock event with stopPropagation method
    const mockEvent = {
      stopPropagation: vi.fn(),
    }

    render(
      <DeviceStatusChange
        device={mockActiveDevice}
        customer={mockCustomers.active}
        origin="list"
      />
    )

    // Get the deactivate menu item
    const deactivateMenuItem = screen.getByTestId('menu-item-deactivate')

    // Simulate click with mock event
    fireEvent.click(deactivateMenuItem, mockEvent)

    // Check that stopPropagation was called
    expect(mockEvent.stopPropagation).toHaveBeenCalled()
  })

  it.skip('stops event propagation when clicking the dialog title', () => {
    // Create a mock event with stopPropagation method
    const mockEvent = {
      stopPropagation: vi.fn(),
    }

    render(
      <DeviceStatusChange
        device={mockActiveDevice}
        customer={mockCustomers.active}
        origin="list"
      />
    )

    // Click the deactivate menu item to open the dialog
    const deactivateMenuItem = screen.getByTestId('menu-item-deactivate')
    fireEvent.click(deactivateMenuItem)

    // Get the dialog title
    const dialogTitle = screen.getByTestId('dialog-title')

    // Simulate click with mock event
    fireEvent.click(dialogTitle, mockEvent)

    // Check that stopPropagation was called
    expect(mockEvent.stopPropagation).toHaveBeenCalled()
  })
})
