'use client'

import type React from 'react'
import type { IHeadCell } from '@dtbx/store/interfaces'

import { describe, it, expect, vi, beforeEach } from 'vitest'
import { render, fireEvent, waitFor, screen } from '../../../../test-utils'
import * as storeActions from '@/store/actions'
import { useAppDispatch, useAppSelector } from '@/store'
import * as utils from '@dtbx/store/utils'
import BackOfficeQuestionsHistory from '@/app/customers/customer/Details/Security/Drawer/BackOfficePinHistory'
import { mockCustomers } from '../../../../../stubs/customer-listing'

// Mock the store actions
vi.mock('@/store/actions', () => ({
  getCustomerPinLogsBackoffice: vi.fn().mockResolvedValue(undefined),
  getCustomerPinHistory: vi.fn().mockResolvedValue(undefined),
}))

// Mock the store hooks
vi.mock('@/store', () => ({
  useAppDispatch: vi.fn(),
  useAppSelector: vi.fn(),
}))

// Mock the utils
vi.mock('@dtbx/store/utils', () => ({
  formatTimestamp: vi.fn((timestamp) => `Formatted: ${timestamp}`),
  HasAccessToRights: vi.fn(),
}))

// Mock the PinHistoryEmptyState component
vi.mock('./EmptyState', () => ({
  PinHistoryEmptyState: () => (
    <div data-testid="pin-history-empty-state">No history available</div>
  ),
}))

// Mock the custom components from @dtbx/ui/components/Table
vi.mock('@dtbx/ui/components/Table', () => ({
  CustomPagination: ({
    options,
    handlePagination,
  }: {
    options: { page: number; size: number; totalPages: number }
    handlePagination: (newOptions: any) => void
  }) => (
    <div
      data-testid="custom-pagination"
      data-page={options.page}
      data-size={options.size}
      data-total-pages={options.totalPages}
    >
      <button
        data-testid="next-page-button"
        onClick={() =>
          handlePagination({ page: options.page + 1, size: options.size })
        }
      >
        Next Page
      </button>
    </div>
  ),
  CustomTableHeader: ({
    order,
    orderBy,
    rowCount,
    headLabel,
    numSelected,
  }: {
    order: string
    orderBy: string
    rowCount: number
    headLabel: IHeadCell[]
    numSelected: number
  }) => (
    <thead data-testid="custom-table-header">
      <tr>
        {headLabel.map((cell) => (
          <th key={cell.id} data-id={cell.id}>
            {cell.label}
          </th>
        ))}
      </tr>
    </thead>
  ),
}))

// Mock the custom components from @dtbx/ui/components
vi.mock('@dtbx/ui/components', () => ({
  CustomSkeleton: ({
    width,
    height,
  }: {
    width: string | number
    height: string | number
  }) => (
    <div data-testid="custom-skeleton" style={{ width, height }}>
      Loading...
    </div>
  ),
  LoadingListsSkeleton: () => (
    <div data-testid="loading-lists-skeleton">Loading lists...</div>
  ),
}))

// Mock MUI components
vi.mock('@mui/material', async () => {
  const actual = await vi.importActual('@mui/material')
  return {
    ...actual,
    Paper: ({
      children,
      elevation,
      sx,
      ...props
    }: {
      children: React.ReactNode
      elevation?: number
      sx?: any
      [key: string]: any
    }) => (
      <div data-testid="paper" data-elevation={elevation} {...props}>
        {children}
      </div>
    ),
    TableContainer: ({
      children,
      sx,
      ...props
    }: {
      children: React.ReactNode
      sx?: any
      [key: string]: any
    }) => (
      <div data-testid="table-container" {...props}>
        {children}
      </div>
    ),
    Table: ({
      children,
      stickyHeader,
      sx,
      ...props
    }: {
      children: React.ReactNode
      stickyHeader?: boolean
      sx?: any
      [key: string]: any
    }) => (
      <table data-testid="table" data-sticky-header={stickyHeader} {...props}>
        {children}
      </table>
    ),
    TableBody: ({
      children,
      ...props
    }: {
      children: React.ReactNode
      [key: string]: any
    }) => (
      <tbody data-testid="table-body" {...props}>
        {children}
      </tbody>
    ),
    TableRow: ({
      children,
      ...props
    }: {
      children: React.ReactNode
      [key: string]: any
    }) => (
      <tr data-testid="table-row" {...props}>
        {children}
      </tr>
    ),
    TableCell: ({
      children,
      sx,
      ...props
    }: {
      children: React.ReactNode
      sx?: any
      [key: string]: any
    }) => (
      <td data-testid="table-cell" data-text-wrap={sx?.textWrap} {...props}>
        {children}
      </td>
    ),
  }
})

// Mock dispatch function
const mockDispatch = vi.fn()

// Create mock states for different scenarios
const emptyState = {
  customers: {
    customer: mockCustomers.active,
    customerPinLogsBackOffice: {
      data: [],
      pageNumber: 1,
      pageSize: 10,
      totalNumberOfPages: 0,
    },
    isLoadingCustomerPinHistory: false,
  },
}

const loadingState = {
  customers: {
    customer: mockCustomers.active,
    customerPinLogsBackOffice: {
      data: [],
      pageNumber: 1,
      pageSize: 10,
      totalNumberOfPages: 0,
    },
    isLoadingCustomerPinHistory: true,
  },
}

const withDataState = {
  customers: {
    customer: mockCustomers.active,
    customerPinLogsBackOffice: {
      data: [
        {
          id: '1',
          makerCheckerType: { name: 'PIN_RESET' },
          maker: 'John Doe',
          dateCreated: '2023-01-01T12:00:00Z',
          makerComments: 'Reset PIN due to security concerns',
          checker: 'Jane Smith',
          dateModified: '2023-01-02T14:30:00Z',
          checkerComments: 'Approved',
        },
        {
          id: '2',
          makerCheckerType: { name: 'PIN_CHANGE' },
          maker: 'Alice Johnson',
          dateCreated: '2023-01-03T10:15:00Z',
          makerComments: 'Customer requested PIN change',
          checker: null,
          dateModified: null,
          checkerComments: null,
        },
      ],
      pageNumber: 1,
      pageSize: 10,
      totalNumberOfPages: 2,
    },
    isLoadingCustomerPinHistory: false,
  },
}

describe('BackOfficeQuestionsHistory Component', () => {
  beforeEach(() => {
    // Reset mocks before each test
    vi.clearAllMocks()

    // Set up default mock for useAppDispatch
    vi.mocked(useAppDispatch).mockReturnValue(mockDispatch)

    // Set up default mock for useAppSelector
    vi.mocked(useAppSelector).mockImplementation((selector) =>
      selector(emptyState as any)
    )
  })

  it('fetches customer pin logs on initial render', () => {
    render(<BackOfficeQuestionsHistory />)

    // Check that getCustomerPinLogsBackoffice was called with the correct parameters
    expect(storeActions.getCustomerPinLogsBackoffice).toHaveBeenCalledWith(
      mockCustomers.active.id,
      mockDispatch,
      1,
      10
    )
  })

  it('displays loading skeleton when data is loading', () => {
    // Mock useAppSelector to return loading state
    vi.mocked(useAppSelector).mockImplementation((selector) =>
      selector(loadingState as any)
    )

    render(<BackOfficeQuestionsHistory />)

    // Check that the loading skeleton is displayed
    expect(screen.getByTestId('loading-lists-skeleton')).toBeInTheDocument()

    // The table should not be displayed
    expect(screen.queryByTestId('table')).not.toBeInTheDocument()
  })

  it.skip('displays empty state when there is no data', () => {
    render(<BackOfficeQuestionsHistory />)

    // Check that the empty state is displayed
    expect(screen.getByTestId('pin-history-empty-state')).toBeInTheDocument()

    // The table should not be displayed
    expect(screen.queryByTestId('table')).not.toBeInTheDocument()
  })

  it('displays table with data when data is available', () => {
    // Mock useAppSelector to return state with data
    vi.mocked(useAppSelector).mockImplementation((selector) =>
      selector(withDataState as any)
    )

    render(<BackOfficeQuestionsHistory />)

    // Check that the table is displayed
    expect(screen.getByTestId('table')).toBeInTheDocument()
    expect(screen.getByTestId('table-body')).toBeInTheDocument()

    // Check that the table header is displayed with correct columns
    expect(screen.getByTestId('custom-table-header')).toBeInTheDocument()
    expect(screen.getByText('Event Type')).toBeInTheDocument()
    expect(screen.getByText('Maker')).toBeInTheDocument()
    expect(screen.getByText('Maker Timestamp')).toBeInTheDocument()
    expect(screen.getByText('Maker Comments')).toBeInTheDocument()
    expect(screen.getByText('Checker')).toBeInTheDocument()
    expect(screen.getByText('Checker Timestamp')).toBeInTheDocument()
    expect(screen.getByText('Checker Comments')).toBeInTheDocument()

    // Check that the table rows are displayed with correct data
    const tableRows = screen.getAllByTestId('table-row')
    expect(tableRows).toHaveLength(2)

    // Check the content of the first row
    const firstRowCells = tableRows[0].querySelectorAll(
      '[data-testid="table-cell"]'
    )
    expect(firstRowCells[0]).toHaveTextContent('PIN_RESET')
    expect(firstRowCells[1]).toHaveTextContent('John Doe')
    expect(firstRowCells[2]).toHaveTextContent(
      'Formatted: 2023-01-01T12:00:00Z'
    )
    expect(firstRowCells[3]).toHaveTextContent(
      'Reset PIN due to security concerns'
    )
    expect(firstRowCells[4]).toHaveTextContent('Jane Smith')
    expect(firstRowCells[5]).toHaveTextContent(
      'Formatted: 2023-01-02T14:30:00Z'
    )
    expect(firstRowCells[6]).toHaveTextContent('Approved')

    // Check the content of the second row
    const secondRowCells = tableRows[1].querySelectorAll(
      '[data-testid="table-cell"]'
    )
    expect(secondRowCells[0]).toHaveTextContent('PIN_CHANGE')
    expect(secondRowCells[1]).toHaveTextContent('Alice Johnson')
    expect(secondRowCells[2]).toHaveTextContent(
      'Formatted: 2023-01-03T10:15:00Z'
    )
    expect(secondRowCells[3]).toHaveTextContent('Customer requested PIN change')
    expect(secondRowCells[4]).toHaveTextContent('N/A') // null checker should display as "N/A"
    expect(secondRowCells[5]).toHaveTextContent('') // null dateModified should display as empty string
    expect(secondRowCells[6]).toHaveTextContent('') // null checkerComments should display as empty string
  })

  it('displays pagination when there are multiple pages', () => {
    // Mock useAppSelector to return state with data
    vi.mocked(useAppSelector).mockImplementation((selector) =>
      selector(withDataState as any)
    )

    render(<BackOfficeQuestionsHistory />)

    // Check that the pagination is displayed
    expect(screen.getByTestId('custom-pagination')).toBeInTheDocument()
    expect(screen.getByTestId('custom-pagination')).toHaveAttribute(
      'data-page',
      '1'
    )
    expect(screen.getByTestId('custom-pagination')).toHaveAttribute(
      'data-total-pages',
      '2'
    )
  })

  it('does not display pagination when there is only one page', () => {
    // Mock useAppSelector to return state with data but only one page
    vi.mocked(useAppSelector).mockImplementation((selector) =>
      selector({
        ...withDataState,
        customers: {
          ...withDataState.customers,
          customerPinLogsBackOffice: {
            ...withDataState.customers.customerPinLogsBackOffice,
            totalNumberOfPages: 0,
          },
        },
      } as any)
    )

    render(<BackOfficeQuestionsHistory />)

    // Check that the pagination is not displayed
    expect(screen.queryByTestId('custom-pagination')).not.toBeInTheDocument()
  })

  it.skip('updates local pagination state when pagination changes', async () => {
    // Mock useAppSelector to return state with data
    vi.mocked(useAppSelector).mockImplementation((selector) =>
      selector(withDataState as any)
    )

    render(<BackOfficeQuestionsHistory />)

    // Check that the pagination is displayed with initial values
    expect(screen.getByTestId('custom-pagination')).toBeInTheDocument()
    expect(screen.getByTestId('custom-pagination')).toHaveAttribute(
      'data-page',
      '1'
    )

    // Click the next page button
    const nextPageButton = screen.getByTestId('next-page-button')
    fireEvent.click(nextPageButton)

    // Check that getCustomerPinLogsBackoffice was called with the correct parameters
    await waitFor(() => {
      expect(storeActions.getCustomerPinLogsBackoffice).toHaveBeenCalledWith(
        mockCustomers.active.id,
        mockDispatch,
        2,
        10
      )
    })

    // The pagination component should now show page 2
    // Note: In a real component, this would update, but in our test environment
    // we need to simulate the state update by changing the mock state
    vi.mocked(useAppSelector).mockImplementation((selector) =>
      selector({
        ...withDataState,
        customers: {
          ...withDataState.customers,
          customerPinLogsBackOffice: {
            ...withDataState.customers.customerPinLogsBackOffice,
            pageNumber: 2,
          },
        },
      } as any)
    )

    // Re-render to see the updated state
    render(<BackOfficeQuestionsHistory />)
    expect(screen.getByTestId('custom-pagination')).toHaveAttribute(
      'data-page',
      '2'
    )
  })

  it('formats timestamps correctly', () => {
    // Mock useAppSelector to return state with data
    vi.mocked(useAppSelector).mockImplementation((selector) =>
      selector(withDataState as any)
    )

    render(<BackOfficeQuestionsHistory />)

    // Check that formatTimestamp was called with the correct parameters
    expect(utils.formatTimestamp).toHaveBeenCalledWith('2023-01-01T12:00:00Z')
    expect(utils.formatTimestamp).toHaveBeenCalledWith('2023-01-02T14:30:00Z')
    expect(utils.formatTimestamp).toHaveBeenCalledWith('2023-01-03T10:15:00Z')

    // Check that the formatted timestamps are displayed
    expect(
      screen.getByText('Formatted: 2023-01-01T12:00:00Z')
    ).toBeInTheDocument()
    expect(
      screen.getByText('Formatted: 2023-01-02T14:30:00Z')
    ).toBeInTheDocument()
    expect(
      screen.getByText('Formatted: 2023-01-03T10:15:00Z')
    ).toBeInTheDocument()
  })

  it('handles missing customer ID gracefully', () => {
    // Mock useAppSelector to return state with customer having no ID
    vi.mocked(useAppSelector).mockImplementation((selector) =>
      selector({
        ...emptyState,
        customers: {
          ...emptyState.customers,
          customer: { ...mockCustomers.active, id: '' },
        },
      } as any)
    )

    render(<BackOfficeQuestionsHistory />)

    // Check that getCustomerPinLogsBackoffice was called with an empty string
    expect(storeActions.getCustomerPinLogsBackoffice).toHaveBeenCalledWith(
      '',
      mockDispatch,
      1,
      10
    )
  })

  it('applies correct styling to table cells', () => {
    // Mock useAppSelector to return state with data
    vi.mocked(useAppSelector).mockImplementation((selector) =>
      selector(withDataState as any)
    )

    render(<BackOfficeQuestionsHistory />)

    // Check that the table cells have the correct styling
    const tableCells = screen.getAllByTestId('table-cell')

    // The first three columns should have nowrap styling
    expect(tableCells[0]).toHaveAttribute('data-text-wrap', 'nowrap')
    expect(tableCells[1]).toHaveAttribute('data-text-wrap', 'nowrap')
    expect(tableCells[2]).toHaveAttribute('data-text-wrap', 'nowrap')
  })

  it('renders the table with sticky header', () => {
    // Mock useAppSelector to return state with data
    vi.mocked(useAppSelector).mockImplementation((selector) =>
      selector(withDataState as any)
    )

    render(<BackOfficeQuestionsHistory />)

    // Check that the table has sticky header
    expect(screen.getByTestId('table')).toHaveAttribute(
      'data-sticky-header',
      'true'
    )
  })

  it('handles null checker values correctly', () => {
    // Mock useAppSelector to return state with data
    vi.mocked(useAppSelector).mockImplementation((selector) =>
      selector(withDataState as any)
    )

    render(<BackOfficeQuestionsHistory />)

    // Get all table rows
    const tableRows = screen.getAllByTestId('table-row')

    // Check the second row which has null checker values
    const secondRowCells = tableRows[1].querySelectorAll(
      '[data-testid="table-cell"]'
    )
    expect(secondRowCells[4]).toHaveTextContent('N/A') // null checker should display as "N/A"
    expect(secondRowCells[5]).toHaveTextContent('') // null dateModified should display as empty string
    expect(secondRowCells[6]).toHaveTextContent('') // null checkerComments should display as empty string
  })

  it('conditionally formats checker timestamp based on checker presence', () => {
    // Mock useAppSelector to return state with data
    vi.mocked(useAppSelector).mockImplementation((selector) =>
      selector(withDataState as any)
    )

    render(<BackOfficeQuestionsHistory />)

    // Get all table rows
    const tableRows = screen.getAllByTestId('table-row')

    // First row has a checker, so timestamp should be formatted
    const firstRowCells = tableRows[0].querySelectorAll(
      '[data-testid="table-cell"]'
    )
    expect(firstRowCells[5]).toHaveTextContent(
      'Formatted: 2023-01-02T14:30:00Z'
    )

    // Second row has no checker, so timestamp should be empty
    const secondRowCells = tableRows[1].querySelectorAll(
      '[data-testid="table-cell"]'
    )
    expect(secondRowCells[5]).toHaveTextContent('')

    // Verify formatTimestamp was called only for the first row's dateModified
    expect(utils.formatTimestamp).toHaveBeenCalledWith('2023-01-02T14:30:00Z')
    // It should not have been called with null
    expect(utils.formatTimestamp).not.toHaveBeenCalledWith(null)
  })

  it('initializes pagination options from store state', () => {
    // Mock useAppSelector to return state with custom pagination
    vi.mocked(useAppSelector).mockImplementation((selector) =>
      selector({
        ...withDataState,
        customers: {
          ...withDataState.customers,
          customerPinLogsBackOffice: {
            ...withDataState.customers.customerPinLogsBackOffice,
            pageNumber: 3,
            pageSize: 20,
          },
        },
      } as any)
    )

    render(<BackOfficeQuestionsHistory />)

    // Check that the pagination options are initialized from the store state
    expect(screen.getByTestId('custom-pagination')).toHaveAttribute(
      'data-page',
      '3'
    )
    expect(screen.getByTestId('custom-pagination')).toHaveAttribute(
      'data-size',
      '20'
    )
  })

  it('uses the correct key for pagination component', () => {
    // Mock useAppSelector to return state with data
    vi.mocked(useAppSelector).mockImplementation((selector) =>
      selector(withDataState as any)
    )

    // We can't directly test the key prop, but we can check that the pagination component renders correctly
    render(<BackOfficeQuestionsHistory />)
    expect(screen.getByTestId('custom-pagination')).toBeInTheDocument()
  })

  it('explicitly checks for customer.id before using it', () => {
    // Mock useAppSelector to return state with undefined customer id
    vi.mocked(useAppSelector).mockImplementation((selector) =>
      selector({
        ...emptyState,
        customers: {
          ...emptyState.customers,
          customer: { ...mockCustomers.active, id: undefined },
        },
      } as any)
    )

    render(<BackOfficeQuestionsHistory />)

    // Check that getCustomerPinLogsBackoffice was called with an empty string
    expect(storeActions.getCustomerPinLogsBackoffice).toHaveBeenCalledWith(
      '',
      mockDispatch,
      1,
      10
    )
  })
})
