import { describe, it, expect, vi } from 'vitest'
import { render } from '../../test-utils'
import Notifications from '@/app/customers/customer/Details/Notifications'

// Mock MUI components
vi.mock('@mui/material', async () => {
  const actual = await vi.importActual('@mui/material')
  return {
    ...actual,
    Stack: ({ children }: any) => <div data-testid="mui-stack">{children}</div>,
  }
})

describe('Notifications Component', () => {
  it('renders without errors', () => {
    const { container } = render(<Notifications />)
    expect(container).toBeInTheDocument()
  })

  it('renders a Stack component', () => {
    const { getByTestId } = render(<Notifications />)
    expect(getByTestId('mui-stack')).toBeInTheDocument()
  })

  it('displays the text Notifications', () => {
    const { getByText } = render(<Notifications />)
    expect(getByText('Notifications')).toBeInTheDocument()
  })
})
