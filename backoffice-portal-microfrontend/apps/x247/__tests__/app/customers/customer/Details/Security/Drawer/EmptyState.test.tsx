'use client'

import type React from 'react'

import { describe, it, expect, vi } from 'vitest'
import { render, screen } from '../../../../test-utils'
import { PinHistoryEmptyState } from '@/app/customers/customer/Details/Security/Drawer/EmptyState'

// Mock MUI components
vi.mock('@mui/material', async () => {
  const actual = await vi.importActual('@mui/material')
  return {
    ...actual,
    Stack: ({
      children,
      id,
      sx,
      ...props
    }: {
      children: React.ReactNode
      id?: string
      sx?: any
      [key: string]: any
    }) => (
      <div
        data-testid="stack"
        data-flex-direction={sx?.flexDirection}
        data-justify-content={sx?.justifyContent}
        data-align-items={sx?.alignItems}
        data-align-content={sx?.alignContent}
        data-py={sx?.py}
        data-px={sx?.px}
        data-gap={sx?.gap}
        data-background-image={sx?.backgroundImage}
        data-background-size={sx?.backgroundSize}
        data-background-repeat={sx?.backgroundRepeat}
        data-background-position={sx?.backgroundPosition}
        {...props}
      >
        {children}
      </div>
    ),
    Typography: ({
      children,
      variant,
      sx,
      ...props
    }: {
      children: React.ReactNode
      variant?: string
      sx?: any
      [key: string]: any
    }) => (
      <span
        data-testid={`typography-${variant || 'default'}`}
        data-px={sx?.px}
        data-text-align={sx?.textAlign}
        {...props}
      >
        {children}
      </span>
    ),
  }
})

describe('PinHistoryEmptyState Component', () => {
  it('renders the component correctly', () => {
    render(<PinHistoryEmptyState />)

    // Check that the outer Stack component is rendered with correct props
    const outerStack = screen.getAllByTestId('stack')[0]
    expect(outerStack).toBeInTheDocument()
    expect(outerStack).toHaveAttribute('data-flex-direction', 'column')
    expect(outerStack).toHaveAttribute('data-justify-content', 'center')
    expect(outerStack).toHaveAttribute('data-align-items', 'center')
    expect(outerStack).toHaveAttribute('data-align-content', 'center')
    expect(outerStack).toHaveAttribute('data-py', '30vh')
    expect(outerStack).toHaveAttribute(
      'data-background-image',
      "url('/background-empty.svg')"
    )
    expect(outerStack).toHaveAttribute('data-background-size', 'center')
    expect(outerStack).toHaveAttribute('data-background-repeat', 'no-repeat')
    expect(outerStack).toHaveAttribute('data-background-position', 'top')

    // Check that the inner Stack component is rendered with correct props
    const innerStack = screen.getAllByTestId('stack')[1]
    expect(innerStack).toBeInTheDocument()
    expect(innerStack).toHaveAttribute('data-px', '5%')
    expect(innerStack).toHaveAttribute('data-gap', '10px')
    expect(innerStack).toHaveAttribute('data-justify-content', 'center')
  })

  it('displays the correct empty state message', () => {
    render(<PinHistoryEmptyState />)

    // Check that the Typography component is rendered with the correct text
    const typography = screen.getByTestId('typography-h6')
    expect(typography).toBeInTheDocument()
    expect(typography).toHaveTextContent(
      "Seems like you don't have any history"
    )
  })

  it('renders only one Typography component', () => {
    render(<PinHistoryEmptyState />)

    // Check that there is only one Typography component
    const typographyElements = screen.getAllByTestId(/typography-/)
    expect(typographyElements).toHaveLength(1)
  })

  it('renders with the correct background image', () => {
    render(<PinHistoryEmptyState />)

    // Check that the background image is set correctly
    const outerStack = screen.getAllByTestId('stack')[0]
    expect(outerStack).toHaveAttribute(
      'data-background-image',
      "url('/background-empty.svg')"
    )
  })

  it('renders within a container', () => {
    const { container } = render(
      <div data-testid="container">
        <PinHistoryEmptyState />
      </div>
    )

    // Check that the component renders within a container
    const containerElement = screen.getByTestId('container')
    expect(containerElement).toContainElement(screen.getAllByTestId('stack')[1])
  })

  it('renders consistently across multiple renders', () => {
    const { rerender } = render(<PinHistoryEmptyState />)

    // Get the initial elements
    const initialOuterStack = screen.getAllByTestId('stack')[0]
    const initialTypography = screen.getByTestId('typography-h6')

    // Re-render the component
    rerender(<PinHistoryEmptyState />)

    // Get the new elements
    const newOuterStack = screen.getAllByTestId('stack')[0]
    const newTypography = screen.getByTestId('typography-h6')

    // Check that the elements are the same
    expect(newOuterStack).toHaveAttribute(
      'data-background-image',
      initialOuterStack.getAttribute('data-background-image')
    )
    expect(newTypography).toHaveTextContent(initialTypography.textContent || '')
  })
})
