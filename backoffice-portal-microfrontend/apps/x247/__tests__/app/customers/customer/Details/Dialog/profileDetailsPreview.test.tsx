import { beforeEach, describe, it, vi } from 'vitest'
import { render } from '../../../test-utils'
import { useAppDispatch, useAppSelector } from '@/store'
import { generateMockApprovalRequest } from '../../../../stubs/customer-listing'
import ProfileDetailsPreview from '@/app/customers/customer/Details/Dialog/profileDetailsPreview'
vi.mock('@/store', () => ({
  useAppSelector: vi.fn(),
  useAppDispatch: vi.fn(),
}))
const mockDispatch = vi.fn()
const mockApprovalRequest = generateMockApprovalRequest(100, 'profile-104')
const defaultState = {
  approvalRequests: {
    selectedApprovalRequest: mockApprovalRequest,
  },
}
const mockOnClose = vi.fn()
describe('ProfileDetailsPreview Component', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    vi.mocked(useAppDispatch).mockReturnValue(mockDispatch)

    // Set up default mock for useAppSelector
    vi.mocked(useAppSelector).mockImplementation((selector) =>
      selector(defaultState as any)
    )
  })
  it('Should render profile details preview component', () => {
    render(<ProfileDetailsPreview onClose={mockOnClose} />)
  })
})
