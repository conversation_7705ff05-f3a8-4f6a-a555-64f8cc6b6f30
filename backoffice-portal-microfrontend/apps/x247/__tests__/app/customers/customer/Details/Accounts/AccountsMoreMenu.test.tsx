import { describe, it, expect, beforeEach, vi } from 'vitest'
import { screen, waitFor, fireEvent, render } from '../../../test-utils'
import { AccountsMoreMenu } from '@/app/customers/customer/Details/Accounts/AccountsMoreMenu'
import { useAppDispatch } from '@/store'
import { mockCustomerProfileAccount } from '../../../../stubs/accounts'
import type React from 'react'

vi.mock('@/store', () => ({
  useAppDispatch: vi.fn(),
  useAppSelector: vi.fn(),
}))
vi.mock('@mui/material', async () => {
  const actual = await vi.importActual('@mui/material')
  return {
    ...actual,
    Button: ({
      children,
      onClick,
      variant,
      disabled,
      endIcon,
      fullWidth,
      sx,
      ...props
    }: {
      children: React.ReactNode
      onClick?: (e: any) => void
      variant?: string
      disabled?: boolean
      endIcon?: React.ReactNode
      fullWidth?: boolean
      sx?: any
      [key: string]: any
    }) => (
      <button
        onClick={onClick}
        disabled={disabled}
        data-variant={variant}
        data-full-width={fullWidth}
        data-testid={`button-${typeof children === 'string' ? children.toLowerCase().replace(/\s+/g, '-') : 'custom'}`}
        {...props}
      >
        {children}
        {endIcon}
      </button>
    ),
    Menu: ({
      children,
      open,
      anchorEl,
      onClose,
      ...props
    }: {
      children: React.ReactNode
      open: boolean
      anchorEl: any
      onClose: (e: any) => void
      [key: string]: any
    }) =>
      open ? (
        <div data-testid="menu" {...props}>
          {children}
        </div>
      ) : null,
    MenuItem: ({
      children,
      onClick,
      ...props
    }: {
      children: React.ReactNode
      onClick?: (e: any) => void
      [key: string]: any
    }) => (
      <div
        data-testid={`menu-item-${typeof children === 'string' ? children.toLowerCase() : 'custom'}`}
        onClick={onClick}
        {...props}
      >
        {children}
      </div>
    ),
    DialogTitle: ({
      children,
      sx,
      ...props
    }: {
      children: React.ReactNode
      sx?: any
      [key: string]: any
    }) => (
      <div data-testid="dialog-title" {...props}>
        {children}
      </div>
    ),
    DialogContent: ({
      children,
      sx,
      ...props
    }: {
      children: React.ReactNode
      sx?: any
      [key: string]: any
    }) => (
      <div data-testid="dialog-content" {...props}>
        {children}
      </div>
    ),
    DialogActions: ({
      children,
      sx,
      ...props
    }: {
      children: React.ReactNode
      sx?: any
      [key: string]: any
    }) => (
      <div data-testid="dialog-actions" {...props}>
        {children}
      </div>
    ),
    IconButton: ({
      children,
      onClick,
      ...props
    }: {
      children: React.ReactNode
      onClick?: (e: any) => void
      [key: string]: any
    }) => (
      <button data-testid="icon-button" onClick={onClick} {...props}>
        {children}
      </button>
    ),
    Stack: ({
      children,
      sx,
      ...props
    }: {
      children: React.ReactNode
      sx?: any
      [key: string]: any
    }) => (
      <div data-testid="stack" {...props}>
        {children}
      </div>
    ),
    Typography: ({
      children,
      variant,
      sx,
      ...props
    }: {
      children: React.ReactNode
      variant?: string
      sx?: any
      [key: string]: any
    }) => (
      <div data-testid={`typography-${variant || 'default'}`} {...props}>
        {children}
      </div>
    ),
    FormControl: ({
      children,
      fullWidth,
      ...props
    }: {
      children: React.ReactNode
      fullWidth?: boolean
      [key: string]: any
    }) => (
      <div data-testid="form-control" data-full-width={fullWidth} {...props}>
        {children}
      </div>
    ),
    Checkbox: ({
      icon,
      ...props
    }: {
      icon: React.ReactNode
      [key: string]: any
    }) => (
      <div data-testid="checkbox" {...props}>
        {icon}
      </div>
    ),
    TextField: ({
      rows,
      placeholder,
      fullWidth,
      onChange,
      onClick,
      onKeyDown,
      error,
      helperText,
      ...props
    }: {
      rows?: number
      placeholder?: string
      fullWidth?: boolean
      onChange?: (e: any) => void
      onClick?: (e: any) => void
      onKeyDown?: (e: any) => void
      error?: boolean
      helperText?: string
      [key: string]: any
    }) => (
      <div
        data-testid="text-field"
        data-rows={rows}
        data-error={error}
        data-full-width={fullWidth}
        {...props}
      >
        <input
          placeholder={placeholder}
          onChange={onChange}
          onClick={onClick}
          onKeyDown={onKeyDown}
          data-testid="text-field-input"
        />
        {error && helperText && (
          <div data-testid="helper-text">{helperText}</div>
        )}
      </div>
    ),
  }
})
const mockDispatch = vi.fn()
const mockActiveAccount = mockCustomerProfileAccount()
const loadedState = {
  isLoading: true,
  account: mockActiveAccount,
}

describe('AccountsMoreMenu', () => {
  beforeEach(() => {
    // Reset mocks before each test
    vi.clearAllMocks()

    // Set up default mock for useAppDispatch
    vi.mocked(useAppDispatch).mockReturnValue(mockDispatch)
  })
  it('renders the account more menu list', () => {
    render(<AccountsMoreMenu account={mockActiveAccount} />)
    const actionsButton = screen.getByTestId('button-actions')
    expect(actionsButton).toBeInTheDocument()
    expect(actionsButton).toHaveTextContent('Actions')
    expect(actionsButton).not.toBeDisabled()
  })
})
