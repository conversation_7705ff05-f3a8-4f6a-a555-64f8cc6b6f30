import { beforeEach, describe, it, vi } from 'vitest'
import { render } from '../../../test-utils'
import {
  generateMockApprovalRequest,
  mockCustomers,
} from '../../../../stubs/customer-listing'
import PendingApprovalRequests from '@/app/customers/customer/Details/Dialog/PendingApprovalRequests'
import { useAppDispatch, useAppSelector } from '@/store'
import { useAppSelector as globalAppSelector } from '@/store'
vi.mock('@/store', () => ({
  useAppSelector: vi.fn(),
  useAppDispatch: vi.fn(),
}))
vi.mock('@/dtbx/store', () => ({
  globalAppSelector: vi.fn(),
}))
const mockDispatch = vi.fn()
const mockApprovalRequest = generateMockApprovalRequest(100, 'profile-104')
const defaultState = {
  approvalRequests: {
    pendingSingleCustomerApprovalRequests: mockApprovalRequest,
  },

  customers: {
    customer: mockCustomers.active,
    devicesResponse: {},
  },
}
const defaultGlobal = {
  overlay: {
    drawer: {
      open: true,
      header: {},
    },
  },
}
describe('PendingApprovalRequests Component', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    vi.mocked(useAppDispatch).mockReturnValue(mockDispatch)

    // Set up default mock for useAppSelector
    vi.mocked(useAppSelector).mockImplementation((selector) =>
      selector(defaultState as any)
    )
    // vi.mocked(globalAppSelector).mockImplementation((selector) =>
    //   selector(defaultGlobal as any)
    // )
  })
  it.skip('Should render pending approval requests component', () => {
    render(<PendingApprovalRequests />)
  })
})
