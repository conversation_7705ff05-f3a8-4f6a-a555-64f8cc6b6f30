'use client'

import type React from 'react'
import type { IFilter } from '@dtbx/store/interfaces'

import { describe, it, expect, vi, beforeEach } from 'vitest'
import { render, fireEvent, waitFor, screen } from '../../../test-utils'
import PageHeader from '@/app/customers/customer/Details/Devices/pageHeader'
import * as storeActions from '@/store/actions'
import { useAppDispatch, useAppSelector } from '@/store'
import { mockCustomers } from '../../../../stubs/customer-listing'

// Mock the store actions
vi.mock('@/store/actions', () => ({
  getCustomerDevices: vi.fn().mockResolvedValue(undefined),
}))

// Mock the store hooks
vi.mock('@/store', () => ({
  useAppDispatch: vi.fn(),
  useAppSelector: vi.fn(),
}))

// Mock the custom components from @dtbx/ui/components
vi.mock('@dtbx/ui/components', () => ({
  CustomerModuleSearchFilterBox: ({
    openFilter,
    setOpenFilter,
    searchValue,
    searchByValues,
    handleSearch,
    filters,
    onFilterChange,
    setSearchByValue,
  }: {
    openFilter: boolean
    setOpenFilter: (open: boolean) => void
    searchValue: string
    searchByValues: Array<{ label: string; value: string }>
    handleSearch: (e: React.ChangeEvent<HTMLInputElement>) => void
    filters: IFilter[]
    onFilterChange: (filters: Record<string, string | string[]>) => void
    setSearchByValue: (value: string) => void
  }) => (
    <div data-testid="customer-module-search-filter-box">
      <input
        data-testid="search-input"
        value={searchValue}
        onChange={handleSearch}
        placeholder="Search"
      />
      <select
        data-testid="search-by-select"
        value={
          searchByValues.find((item) => item.value === searchValue)?.value || ''
        }
        onChange={(e) => setSearchByValue(e.target.value)}
      >
        {searchByValues.map((item) => (
          <option key={item.value} value={item.value}>
            {item.label}
          </option>
        ))}
      </select>
      <button
        data-testid="filter-button"
        onClick={() => setOpenFilter(!openFilter)}
      >
        {openFilter ? 'Close Filter' : 'Open Filter'}
      </button>
      {openFilter && (
        <div data-testid="filter-panel">
          {filters.map((filter) => (
            <div
              key={filter.filterName}
              data-testid={`filter-${filter.filterName.replace(/\s+/g, '-').toLowerCase()}`}
            >
              <span>{filter.filterName}</span>
              <select
                data-testid={`filter-select-${filter.filterName.replace(/\s+/g, '-').toLowerCase()}`}
                onChange={(e) => {
                  const newFilters: Record<string, string> = {}
                  newFilters[filter.filterName] = e.target.value
                  onFilterChange(newFilters)
                }}
              >
                <option value="">Select {filter.filterName}</option>
                {filter.options.map((option) => (
                  <option key={option.key} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
            </div>
          ))}
        </div>
      )}
    </div>
  ),
}))

// Mock MUI components
vi.mock('@mui/material', async () => {
  const actual = await vi.importActual('@mui/material')
  return {
    ...actual,
    Stack: ({
      children,
      sx,
      direction,
      ...props
    }: {
      children: React.ReactNode
      sx?: any
      direction?: string
      [key: string]: any
    }) => (
      <div data-testid="stack" data-direction={direction} {...props}>
        {children}
      </div>
    ),
  }
})

// Mock dispatch function
const mockDispatch = vi.fn()

// Create mock state
const defaultState = {
  customers: {
    customer: mockCustomers.active,
  },
}

describe('PageHeader Component', () => {
  beforeEach(() => {
    // Reset mocks before each test
    vi.clearAllMocks()

    // Set up default mock for useAppDispatch
    vi.mocked(useAppDispatch).mockReturnValue(mockDispatch)

    // Set up default mock for useAppSelector
    vi.mocked(useAppSelector).mockImplementation((selector) =>
      selector(defaultState as any)
    )
  })

  it('renders the search filter box', () => {
    render(<PageHeader />)

    // Check that the search filter box is rendered
    expect(
      screen.getByTestId('customer-module-search-filter-box')
    ).toBeInTheDocument()
    expect(screen.getByTestId('search-input')).toBeInTheDocument()
    expect(screen.getByTestId('search-by-select')).toBeInTheDocument()
    expect(screen.getByTestId('filter-button')).toBeInTheDocument()
  })

  it('handles search by device type', async () => {
    render(<PageHeader />)

    // Get the search input
    const searchInput = screen.getByTestId('search-input')

    // Type in the search input
    fireEvent.change(searchInput, { target: { value: 'App' } })

    // Check that getCustomerDevices was called with the correct parameters
    await waitFor(() => {
      expect(storeActions.getCustomerDevices).toHaveBeenCalledWith({
        dispatch: mockDispatch,
        params: {
          profileID: mockCustomers.active.id,
          page: 0,
          size: 7,
          deviceType: 'App',
        },
      })
    })
  })

  it('handles search by device status', async () => {
    render(<PageHeader />)

    // Get the search by select
    const searchBySelect = screen.getByTestId('search-by-select')

    // Change the search by value to deviceStatus
    fireEvent.change(searchBySelect, { target: { value: 'deviceStatus' } })

    // Get the search input
    const searchInput = screen.getByTestId('search-input')

    // Type "Active" in the search input
    fireEvent.change(searchInput, { target: { value: 'Active' } })

    // Check that getCustomerDevices was called with the correct parameters
    await waitFor(() => {
      expect(storeActions.getCustomerDevices).toHaveBeenCalledWith({
        dispatch: mockDispatch,
        params: {
          profileID: mockCustomers.active.id,
          page: 0,
          size: 7,
          deviceStatus: 'Active',
          status: 'ACTIVE',
        },
      })
    })
  })

  it('handles search by inactive device status', async () => {
    render(<PageHeader />)

    // Get the search by select
    const searchBySelect = screen.getByTestId('search-by-select')

    // Change the search by value to deviceStatus
    fireEvent.change(searchBySelect, { target: { value: 'deviceStatus' } })

    // Get the search input
    const searchInput = screen.getByTestId('search-input')

    // Type "Inactive" in the search input
    fireEvent.change(searchInput, { target: { value: 'Inactive' } })

    // Check that getCustomerDevices was called with the correct parameters
    await waitFor(() => {
      expect(storeActions.getCustomerDevices).toHaveBeenCalledWith({
        dispatch: mockDispatch,
        params: {
          profileID: mockCustomers.active.id,
          page: 0,
          size: 7,
          deviceStatus: 'Inactive',
          status: 'INACTIVE',
        },
      })
    })
  })

  it('opens and closes the filter panel', () => {
    render(<PageHeader />)

    // Get the filter button
    const filterButton = screen.getByTestId('filter-button')

    // Initially, the filter panel should not be visible
    expect(screen.queryByTestId('filter-panel')).not.toBeInTheDocument()

    // Click the filter button to open the filter panel
    fireEvent.click(filterButton)

    // The filter panel should now be visible
    expect(screen.getByTestId('filter-panel')).toBeInTheDocument()

    // Click the filter button again to close the filter panel
    fireEvent.click(filterButton)

    // The filter panel should no longer be visible
    expect(screen.queryByTestId('filter-panel')).not.toBeInTheDocument()
  })

  it('displays the correct filter options', () => {
    render(<PageHeader />)

    // Get the filter button
    const filterButton = screen.getByTestId('filter-button')

    // Click the filter button to open the filter panel
    fireEvent.click(filterButton)

    // Check that the filter panel is displayed with the correct filters
    expect(screen.getByTestId('filter-panel')).toBeInTheDocument()
    expect(screen.getByTestId('filter-device-type')).toBeInTheDocument()
    expect(screen.getByTestId('filter-device-status')).toBeInTheDocument()

    // Check that the filter options are displayed
    const deviceTypeFilter = screen.getByTestId('filter-select-device-type')
    expect(deviceTypeFilter).toBeInTheDocument()
    expect(deviceTypeFilter).toContainHTML('App')
    expect(deviceTypeFilter).toContainHTML('USSD')

    const deviceStatusFilter = screen.getByTestId('filter-select-device-status')
    expect(deviceStatusFilter).toBeInTheDocument()
    expect(deviceStatusFilter).toContainHTML('Active')
    expect(deviceStatusFilter).toContainHTML('Inactive')
  })

  it('handles filter changes for device type', async () => {
    render(<PageHeader />)

    // Get the filter button
    const filterButton = screen.getByTestId('filter-button')

    // Click the filter button to open the filter panel
    fireEvent.click(filterButton)

    // Get the device type filter select
    const deviceTypeFilter = screen.getByTestId('filter-select-device-type')

    // Change the device type filter value
    fireEvent.change(deviceTypeFilter, { target: { value: 'App' } })

    // Check that getCustomerDevices was called with the correct parameters
    await waitFor(() => {
      expect(storeActions.getCustomerDevices).toHaveBeenCalledWith({
        dispatch: mockDispatch,
        params: {
          profileID: mockCustomers.active.id,
          page: 0,
          size: 7,
          status: undefined,
          deviceType: 'App',
        },
      })
    })
  })

  it('handles filter changes for device status', async () => {
    render(<PageHeader />)

    // Get the filter button
    const filterButton = screen.getByTestId('filter-button')

    // Click the filter button to open the filter panel
    fireEvent.click(filterButton)

    // Get the device status filter select
    const deviceStatusFilter = screen.getByTestId('filter-select-device-status')

    // Change the device status filter value
    fireEvent.change(deviceStatusFilter, { target: { value: 'ACTIVE' } })

    // Check that getCustomerDevices was called with the correct parameters
    await waitFor(() => {
      expect(storeActions.getCustomerDevices).toHaveBeenCalledWith({
        dispatch: mockDispatch,
        params: {
          profileID: mockCustomers.active.id,
          page: 0,
          size: 7,
          status: 'ACTIVE',
          deviceType: undefined,
        },
      })
    })
  })

  it('handles missing customer ID gracefully', async () => {
    // Mock useAppSelector to return state with customer having no ID
    vi.mocked(useAppSelector).mockImplementation((selector) =>
      selector({
        ...defaultState,
        customers: {
          ...defaultState.customers,
          customer: { ...mockCustomers.active, id: '' },
        },
      } as any)
    )

    render(<PageHeader />)

    // Get the search input
    const searchInput = screen.getByTestId('search-input')

    // Type in the search input
    fireEvent.change(searchInput, { target: { value: 'App' } })

    // Check that getCustomerDevices was called with an empty string
    await waitFor(() => {
      expect(storeActions.getCustomerDevices).toHaveBeenCalledWith({
        dispatch: mockDispatch,
        params: {
          profileID: '',
          page: 0,
          size: 7,
          deviceType: 'App',
        },
      })
    })
  })

  it('handles error during search', async () => {
    // Mock getCustomerDevices to reject with an error
    vi.mocked(storeActions.getCustomerDevices).mockRejectedValueOnce(
      new Error('API error')
    )

    // Mock console.error to prevent error output in test logs
    const originalConsoleError = console.error
    console.error = vi.fn()

    render(<PageHeader />)

    // Get the search input
    const searchInput = screen.getByTestId('search-input')

    // Type in the search input
    fireEvent.change(searchInput, { target: { value: 'App' } })

    // Check that console.error was called
    await waitFor(() => {
      expect(console.error).toHaveBeenCalledWith(
        'Error fetching customer devices:',
        expect.any(Error)
      )
    })

    // Restore console.error
    console.error = originalConsoleError
  })

  it('initializes with default search by value', () => {
    render(<PageHeader />)

    // Get the search by select
    const searchBySelect = screen.getByTestId('search-by-select')

    // Check that the default value is deviceType
    expect(searchBySelect).toHaveValue('deviceType')
  })

  it.skip('maintains search value when changing search by value', async () => {
    render(<PageHeader />)

    // Get the search input
    const searchInput = screen.getByTestId('search-input')

    // Type in the search input
    fireEvent.change(searchInput, { target: { value: 'App' } })

    // Get the search by select
    const searchBySelect = screen.getByTestId('search-by-select')

    // Change the search by value to deviceStatus
    fireEvent.change(searchBySelect, { target: { value: 'deviceStatus' } })

    // The search input value should still be "App"
    expect(searchInput).toHaveValue('App')

    // Check that getCustomerDevices was called with the correct parameters
    await waitFor(() => {
      expect(storeActions.getCustomerDevices).toHaveBeenCalledWith({
        dispatch: mockDispatch,
        params: {
          profileID: mockCustomers.active.id,
          page: 0,
          size: 7,
          deviceStatus: 'App',
          status: undefined,
        },
      })
    })
  })
})
