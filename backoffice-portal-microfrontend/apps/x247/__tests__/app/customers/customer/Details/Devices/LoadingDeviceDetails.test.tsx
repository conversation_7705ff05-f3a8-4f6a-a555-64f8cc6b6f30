'use client'

import type React from 'react'

import { describe, it, expect, vi } from 'vitest'
import { render, screen } from '../../../test-utils'
import LoadingDeviceDetails from '@/app/customers/customer/Details/Devices/LoadingDeviceDetails'

// Mock the custom components
vi.mock('@dtbx/ui/components/Loading', () => ({
  CustomSkeleton: ({
    variant,
    width,
    height,
    sx,
    ...props
  }: {
    variant: string
    width?: string | number
    height: string | number
    sx?: any
    [key: string]: any
  }) => (
    <div
      data-testid="custom-skeleton"
      data-variant={variant}
      data-width={width}
      data-height={height}
      data-border-radius={sx?.borderRadius}
      {...props}
    />
  ),
}))

// Mock MUI components
vi.mock('@mui/material', async () => {
  const actual = await vi.importActual('@mui/material')
  return {
    ...actual,
    Stack: ({
      children,
      sx,
      ...props
    }: {
      children: React.ReactNode
      sx?: any
      [key: string]: any
    }) => (
      <div
        data-testid="stack"
        data-height={sx?.height}
        data-width={sx?.width}
        data-gap={sx?.gap}
        data-padding={sx?.padding}
        data-px={sx?.px}
        data-py={sx?.py}
        data-border={sx?.border}
        data-flex-direction={sx?.flexDirection}
        data-justify-content={sx?.justifyContent}
        data-align-items={sx?.alignItems}
        data-display={sx?.display}
        data-grid-template-columns={sx?.gridTemplateColumns}
        {...props}
      >
        {children}
      </div>
    ),
    Box: ({
      children,
      sx,
      ...props
    }: {
      children: React.ReactNode
      sx?: any
      [key: string]: any
    }) => (
      <div
        data-testid="box"
        data-display={sx?.display}
        data-flex-direction={sx?.flexDirection}
        data-gap={sx?.gap}
        data-justify-content={sx?.justifyContent}
        data-align-items={sx?.alignItems}
        {...props}
      >
        {children}
      </div>
    ),
  }
})

describe('LoadingDeviceDetails Component', () => {
  it('renders the main stack with correct styling', () => {
    render(<LoadingDeviceDetails />)

    // Check that the main stack is rendered with correct styling
    const mainStack = screen.getAllByTestId('stack')[0]
    expect(mainStack).toBeInTheDocument()
    expect(mainStack).toHaveAttribute('data-gap', '17px')
    expect(mainStack).toHaveAttribute('data-px', '1%')
    expect(mainStack).toHaveAttribute('data-py', '1%')
    expect(mainStack).toHaveAttribute('data-height', '100%')
  })

  it('renders the content stack with correct styling', () => {
    render(<LoadingDeviceDetails />)

    // Check that the content stack is rendered with correct styling
    const contentStack = screen.getAllByTestId('stack')[1]
    expect(contentStack).toBeInTheDocument()
    expect(contentStack).toHaveAttribute('data-width', '100%')
    expect(contentStack).toHaveAttribute('data-height', '50vh')
    expect(contentStack).toHaveAttribute('data-padding', '20px 40px')
    expect(contentStack).toHaveAttribute('data-border', '1px solid #D0D5DD')
    expect(contentStack).toHaveAttribute('data-gap', '10px')
    expect(contentStack).toHaveAttribute(
      'data-justify-content',
      'space-between'
    )
  })

  it('renders the header skeleton', () => {
    render(<LoadingDeviceDetails />)

    // Get all skeleton elements
    const skeletons = screen.getAllByTestId('custom-skeleton')

    // Check that the header skeleton is rendered with correct properties
    const headerSkeleton = skeletons[0]
    expect(headerSkeleton).toBeInTheDocument()
    expect(headerSkeleton).toHaveAttribute('data-variant', 'rectangular')
    expect(headerSkeleton).toHaveAttribute('data-width', '150px')
    expect(headerSkeleton).toHaveAttribute('data-height', '40px')
  })

  it('renders the avatar section correctly', () => {
    render(<LoadingDeviceDetails />)

    // Check that the avatar box is rendered
    const avatarBox = screen.getAllByTestId('box')[0]
    expect(avatarBox).toBeInTheDocument()
    expect(avatarBox).toHaveAttribute('data-display', 'flex')
    expect(avatarBox).toHaveAttribute('data-flex-direction', 'row')
    expect(avatarBox).toHaveAttribute('data-gap', '20px')
    expect(avatarBox).toHaveAttribute('data-justify-content', 'flex-start')
    expect(avatarBox).toHaveAttribute('data-align-items', 'center')

    // Get all skeleton elements
    const skeletons = screen.getAllByTestId('custom-skeleton')

    // Check that the avatar skeleton is rendered with correct properties
    const avatarSkeleton = skeletons[1]
    expect(avatarSkeleton).toBeInTheDocument()
    expect(avatarSkeleton).toHaveAttribute('data-variant', 'circular')
    expect(avatarSkeleton).toHaveAttribute('data-width', '60px')
    expect(avatarSkeleton).toHaveAttribute('data-height', '60px')
    expect(avatarSkeleton).toHaveAttribute('data-border-radius', '50%')

    // Check that the device name skeleton is rendered
    const deviceNameSkeleton = skeletons[2]
    expect(deviceNameSkeleton).toBeInTheDocument()
    expect(deviceNameSkeleton).toHaveAttribute('data-variant', 'rectangular')
    expect(deviceNameSkeleton).toHaveAttribute('data-width', '150px')
    expect(deviceNameSkeleton).toHaveAttribute('data-height', '30px')

    // Check that the device info skeleton is rendered
    const deviceInfoSkeleton = skeletons[3]
    expect(deviceInfoSkeleton).toBeInTheDocument()
    expect(deviceInfoSkeleton).toHaveAttribute('data-variant', 'rectangular')
    expect(deviceInfoSkeleton).toHaveAttribute('data-width', '75px')
    expect(deviceInfoSkeleton).toHaveAttribute('data-height', '20px')
  })

  it('renders the action buttons correctly', () => {
    render(<LoadingDeviceDetails />)

    // Check that the buttons box is rendered
    const buttonsBox = screen.getAllByTestId('box')[2]
    expect(buttonsBox).toBeInTheDocument()
    expect(buttonsBox).toHaveAttribute('data-display', 'flex')
    expect(buttonsBox).toHaveAttribute('data-gap', '20px')

    // Get all skeleton elements
    const skeletons = screen.getAllByTestId('custom-skeleton')

    // Check that the first button skeleton is rendered
    const firstButtonSkeleton = skeletons[4]
    expect(firstButtonSkeleton).toBeInTheDocument()
    expect(firstButtonSkeleton).toHaveAttribute('data-variant', 'rectangular')
    expect(firstButtonSkeleton).toHaveAttribute('data-width', '150px')
    expect(firstButtonSkeleton).toHaveAttribute('data-height', '40px')

    // Check that the second button skeleton is rendered
    const secondButtonSkeleton = skeletons[5]
    expect(secondButtonSkeleton).toBeInTheDocument()
    expect(secondButtonSkeleton).toHaveAttribute('data-variant', 'rectangular')
    expect(secondButtonSkeleton).toHaveAttribute('data-width', '150px')
    expect(secondButtonSkeleton).toHaveAttribute('data-height', '40px')
  })

  it.skip('renders the grid of device details skeletons', () => {
    render(<LoadingDeviceDetails />)

    // Check that the grid stack is rendered with correct styling
    const gridStack = screen.getAllByTestId('stack')[5]
    expect(gridStack).toBeInTheDocument()
    expect(gridStack).toHaveAttribute('data-display', 'grid')
    expect(gridStack).toHaveAttribute(
      'data-grid-template-columns',
      'repeat(3, 2fr)'
    )
    expect(gridStack).toHaveAttribute('data-width', '100%')
    expect(gridStack).toHaveAttribute('data-height', '60%')
    expect(gridStack).toHaveAttribute('data-gap', '10px')

    // Get all skeleton elements
    const skeletons = screen.getAllByTestId('custom-skeleton')

    // Check that there are 12 grid item skeletons
    const gridItemSkeletons = skeletons.slice(6)
    expect(gridItemSkeletons).toHaveLength(12)

    // Check that each grid item skeleton has the correct properties
    gridItemSkeletons.forEach((skeleton) => {
      expect(skeleton).toHaveAttribute('data-variant', 'rectangular')
      expect(skeleton).toHaveAttribute('data-height', '50px')
    })
  })

  it('renders the correct total number of skeletons', () => {
    render(<LoadingDeviceDetails />)

    // Get all skeleton elements
    const skeletons = screen.getAllByTestId('custom-skeleton')

    // Check that there are 18 skeletons in total (1 header + 1 avatar + 2 device info + 2 buttons + 12 grid items)
    expect(skeletons).toHaveLength(18)
  })

  it.skip('renders nested stacks with correct structure', () => {
    render(<LoadingDeviceDetails />)

    // Get all stack elements
    const stacks = screen.getAllByTestId('stack')

    // Check that there are 6 stacks in total
    expect(stacks).toHaveLength(6)

    // Check the structure of the stacks
    // Main stack
    expect(stacks[0]).toBeInTheDocument()

    // Content stack
    expect(stacks[1]).toBeInTheDocument()

    // Device info container stack
    expect(stacks[2]).toBeInTheDocument()
    expect(stacks[2]).toHaveAttribute('data-width', '100%')
    expect(stacks[2]).toHaveAttribute('data-height', '30%')

    // Device info row stack
    expect(stacks[3]).toBeInTheDocument()
    expect(stacks[3]).toHaveAttribute('data-flex-direction', 'row')
    expect(stacks[3]).toHaveAttribute('data-width', '100%')
    expect(stacks[3]).toHaveAttribute('data-justify-content', 'space-between')
    expect(stacks[3]).toHaveAttribute('data-align-items', 'center')

    // Grid container stack
    expect(stacks[5]).toBeInTheDocument()
    expect(stacks[5]).toHaveAttribute('data-display', 'grid')
  })

  it('renders boxes with correct structure', () => {
    render(<LoadingDeviceDetails />)

    // Get all box elements
    const boxes = screen.getAllByTestId('box')

    // Check that there are 3 boxes in total
    expect(boxes).toHaveLength(3)

    // Avatar box
    expect(boxes[0]).toBeInTheDocument()
    expect(boxes[0]).toHaveAttribute('data-display', 'flex')
    expect(boxes[0]).toHaveAttribute('data-flex-direction', 'row')

    // Device info box
    expect(boxes[1]).toBeInTheDocument()
    expect(boxes[1]).toHaveAttribute('data-display', 'flex')
    expect(boxes[1]).toHaveAttribute('data-flex-direction', 'column')
    expect(boxes[1]).toHaveAttribute('data-gap', '10px')

    // Buttons box
    expect(boxes[2]).toBeInTheDocument()
    expect(boxes[2]).toHaveAttribute('data-display', 'flex')
    expect(boxes[2]).toHaveAttribute('data-gap', '20px')
  })
})
