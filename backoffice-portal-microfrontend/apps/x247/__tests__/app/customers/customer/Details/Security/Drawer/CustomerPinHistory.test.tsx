'use client'

import type React from 'react'
import type { IHeadCell } from '@dtbx/store/interfaces'

import { describe, it, expect, vi, beforeEach } from 'vitest'
import { render, fireEvent, waitFor, screen } from '../../../../test-utils'
import * as storeActions from '@/store/actions'
import { useAppDispatch, useAppSelector } from '@/store'
import * as utils from '@dtbx/store/utils'
import { mockCustomers } from '../../../../../stubs/customer-listing'
import CustomerPinHistory from '@/app/customers/customer/Details/Security/Drawer/CustomerPinHistory'

// Mock the store actions
vi.mock('@/store/actions', () => ({
  getCustomerPinHistory: vi.fn().mockResolvedValue(undefined),
}))

// Mock the store hooks
vi.mock('@/store', () => ({
  useAppDispatch: vi.fn(),
  useAppSelector: vi.fn(),
}))

// Mock the utils
vi.mock('@dtbx/store/utils', () => ({
  formatTimestamp: vi.fn((timestamp) => `Formatted: ${timestamp}`),
  HasAccessToRights: vi.fn(),
}))

// Mock the PinHistoryEmptyState component
vi.mock('./EmptyState', () => ({
  PinHistoryEmptyState: () => (
    <div data-testid="pin-history-empty-state">No history available</div>
  ),
}))

// Mock the custom components from @dtbx/ui/components/Table
vi.mock('@dtbx/ui/components/Table', () => ({
  CustomPagination: ({
    options,
    handlePagination,
  }: {
    options: { page: number; size: number; totalPages: number }
    handlePagination: (newOptions: any) => void
  }) => (
    <div
      data-testid="custom-pagination"
      data-page={options.page}
      data-size={options.size}
      data-total-pages={options.totalPages}
    >
      <button
        data-testid="next-page-button"
        onClick={() =>
          handlePagination({ page: options.page + 1, size: options.size })
        }
      >
        Next Page
      </button>
    </div>
  ),
  CustomTableHeader: ({
    order,
    orderBy,
    rowCount,
    headLabel,
    numSelected,
  }: {
    order: string
    orderBy: string
    rowCount: number
    headLabel: IHeadCell[]
    numSelected: number
  }) => (
    <thead data-testid="custom-table-header">
      <tr>
        {headLabel.map((cell) => (
          <th key={cell.id} data-id={cell.id}>
            {cell.label}
          </th>
        ))}
      </tr>
    </thead>
  ),
}))

// Mock the custom components from @dtbx/ui/components
vi.mock('@dtbx/ui/components', () => ({
  CustomSkeleton: ({
    width,
    height,
  }: {
    width: string | number
    height: string | number
  }) => (
    <div data-testid="custom-skeleton" style={{ width, height }}>
      Loading...
    </div>
  ),
  LoadingListsSkeleton: () => (
    <div data-testid="loading-lists-skeleton">Loading lists...</div>
  ),
}))

// Mock MUI components
vi.mock('@mui/material', async () => {
  const actual = await vi.importActual('@mui/material')
  return {
    ...actual,
    Paper: ({
      children,
      elevation,
      sx,
      ...props
    }: {
      children: React.ReactNode
      elevation?: number
      sx?: any
      [key: string]: any
    }) => (
      <div data-testid="paper" data-elevation={elevation} {...props}>
        {children}
      </div>
    ),
    TableContainer: ({
      children,
      sx,
      ...props
    }: {
      children: React.ReactNode
      sx?: any
      [key: string]: any
    }) => (
      <div data-testid="table-container" {...props}>
        {children}
      </div>
    ),
    Table: ({
      children,
      stickyHeader,
      sx,
      ...props
    }: {
      children: React.ReactNode
      stickyHeader?: boolean
      sx?: any
      [key: string]: any
    }) => (
      <table
        data-testid="table"
        data-sticky-header={stickyHeader}
        data-min-width={sx?.minWidth}
        {...props}
      >
        {children}
      </table>
    ),
    TableBody: ({
      children,
      ...props
    }: {
      children: React.ReactNode
      [key: string]: any
    }) => (
      <tbody data-testid="table-body" {...props}>
        {children}
      </tbody>
    ),
    TableRow: ({
      children,
      ...props
    }: {
      children: React.ReactNode
      [key: string]: any
    }) => (
      <tr data-testid="table-row" {...props}>
        {children}
      </tr>
    ),
    TableCell: ({
      children,
      sx,
      ...props
    }: {
      children: React.ReactNode
      sx?: any
      [key: string]: any
    }) => (
      <td data-testid="table-cell" data-text-wrap={sx?.textWrap} {...props}>
        {children}
      </td>
    ),
  }
})

// Mock dispatch function
const mockDispatch = vi.fn()

// Mock onPageChange function
const mockOnPageChange = vi.fn()

// Create mock states for different scenarios
const emptyState = {
  customers: {
    customer: mockCustomers.active,
    customerPinLogResponse: {
      data: [],
      pageNumber: 1,
      pageSize: 10,
      totalNumberOfPages: 0,
    },
    isLoadingCustomerPinHistory: false,
  },
}

const loadingState = {
  customers: {
    customer: mockCustomers.active,
    customerPinLogResponse: {
      data: [],
      pageNumber: 1,
      pageSize: 10,
      totalNumberOfPages: 0,
    },
    isLoadingCustomerPinHistory: true,
  },
}

const withDataState = {
  customers: {
    customer: mockCustomers.active,
    customerPinLogResponse: {
      data: [
        {
          id: '1',
          event: 'PIN_RESET',
          eventDate: '2023-01-01T12:00:00Z',
          eventSource: 'MOBILE_APP',
          deviceId: 'device-123',
        },
        {
          id: '2',
          event: 'PIN_CHANGE',
          eventDate: '2023-01-02T14:30:00Z',
          eventSource: 'WEB',
          deviceId: null,
        },
      ],
      pageNumber: 1,
      pageSize: 10,
      totalNumberOfPages: 2,
    },
    isLoadingCustomerPinHistory: false,
  },
}

describe('CustomerPinHistory Component', () => {
  beforeEach(() => {
    // Reset mocks before each test
    vi.clearAllMocks()

    // Set up default mock for useAppDispatch
    vi.mocked(useAppDispatch).mockReturnValue(mockDispatch)

    // Set up default mock for useAppSelector
    vi.mocked(useAppSelector).mockImplementation((selector) =>
      selector(emptyState as any)
    )
  })

  it('fetches customer pin history on initial render', () => {
    render(<CustomerPinHistory page={1} onPageChange={mockOnPageChange} />)

    // Check that getCustomerPinHistory was called with the correct parameters
    expect(storeActions.getCustomerPinHistory).toHaveBeenCalledWith({
      profileID: mockCustomers.active.id,
      dispatch: mockDispatch,
      page: 1,
      size: 10,
      pinType: 'PIN',
    })
  })

  it('displays loading skeleton when data is loading', () => {
    // Mock useAppSelector to return loading state
    vi.mocked(useAppSelector).mockImplementation((selector) =>
      selector(loadingState as any)
    )

    render(<CustomerPinHistory page={1} onPageChange={mockOnPageChange} />)

    // Check that the loading skeleton is displayed
    expect(screen.getByTestId('loading-lists-skeleton')).toBeInTheDocument()

    // The table should not be displayed
    expect(screen.queryByTestId('table')).not.toBeInTheDocument()
  })

  it.skip('displays empty state when there is no data', () => {
    render(<CustomerPinHistory page={1} onPageChange={mockOnPageChange} />)

    // Check that the empty state is displayed
    expect(screen.getByTestId('pin-history-empty-state')).toBeInTheDocument()

    // The table should not be displayed
    expect(screen.queryByTestId('table')).not.toBeInTheDocument()
  })

  it('displays table with data when data is available', () => {
    // Mock useAppSelector to return state with data
    vi.mocked(useAppSelector).mockImplementation((selector) =>
      selector(withDataState as any)
    )

    render(<CustomerPinHistory page={1} onPageChange={mockOnPageChange} />)

    // Check that the table is displayed
    expect(screen.getByTestId('table')).toBeInTheDocument()
    expect(screen.getByTestId('table-body')).toBeInTheDocument()

    // Check that the table header is displayed with correct columns
    expect(screen.getByTestId('custom-table-header')).toBeInTheDocument()
    expect(screen.getByText('Event Type')).toBeInTheDocument()
    expect(screen.getByText('Event Timestamp')).toBeInTheDocument()
    expect(screen.getByText('Event Source')).toBeInTheDocument()
    expect(screen.getByText('Device Id')).toBeInTheDocument()

    // Check that the table rows are displayed with correct data
    const tableRows = screen.getAllByTestId('table-row')
    expect(tableRows).toHaveLength(2)

    // Check the content of the first row
    const firstRowCells = tableRows[0].querySelectorAll(
      '[data-testid="table-cell"]'
    )
    expect(firstRowCells[0]).toHaveTextContent('PIN_RESET')
    expect(firstRowCells[1]).toHaveTextContent(
      'Formatted: 2023-01-01T12:00:00Z'
    )
    expect(firstRowCells[2]).toHaveTextContent('MOBILE_APP')
    expect(firstRowCells[3]).toHaveTextContent('device-123')

    // Check the content of the second row
    const secondRowCells = tableRows[1].querySelectorAll(
      '[data-testid="table-cell"]'
    )
    expect(secondRowCells[0]).toHaveTextContent('PIN_CHANGE')
    expect(secondRowCells[1]).toHaveTextContent(
      'Formatted: 2023-01-02T14:30:00Z'
    )
    expect(secondRowCells[2]).toHaveTextContent('WEB')
    expect(secondRowCells[3]).toHaveTextContent('-') // null deviceId should display as "-"
  })

  it('displays pagination when there are multiple pages', () => {
    // Mock useAppSelector to return state with data
    vi.mocked(useAppSelector).mockImplementation((selector) =>
      selector(withDataState as any)
    )

    render(<CustomerPinHistory page={1} onPageChange={mockOnPageChange} />)

    // Check that the pagination is displayed
    expect(screen.getByTestId('custom-pagination')).toBeInTheDocument()
    expect(screen.getByTestId('custom-pagination')).toHaveAttribute(
      'data-page',
      '1'
    )
    expect(screen.getByTestId('custom-pagination')).toHaveAttribute(
      'data-total-pages',
      '2'
    )
  })

  it('does not display pagination when there is only one page', () => {
    // Mock useAppSelector to return state with data but only one page
    vi.mocked(useAppSelector).mockImplementation((selector) =>
      selector({
        ...withDataState,
        customers: {
          ...withDataState.customers,
          customerPinLogResponse: {
            ...withDataState.customers.customerPinLogResponse,
            totalNumberOfPages: 0,
          },
        },
      } as any)
    )

    render(<CustomerPinHistory page={1} onPageChange={mockOnPageChange} />)

    // Check that the pagination is not displayed
    expect(screen.queryByTestId('custom-pagination')).not.toBeInTheDocument()
  })

  it('calls onPageChange when pagination changes', async () => {
    // Mock useAppSelector to return state with data
    vi.mocked(useAppSelector).mockImplementation((selector) =>
      selector(withDataState as any)
    )

    render(<CustomerPinHistory page={1} onPageChange={mockOnPageChange} />)

    // Check that the pagination is displayed
    expect(screen.getByTestId('custom-pagination')).toBeInTheDocument()

    // Click the next page button
    const nextPageButton = screen.getByTestId('next-page-button')
    fireEvent.click(nextPageButton)

    // Check that onPageChange was called with the correct page number
    expect(mockOnPageChange).toHaveBeenCalledWith(2)

    // Check that getCustomerPinHistory was called with the correct parameters
    await waitFor(() => {
      expect(storeActions.getCustomerPinHistory).toHaveBeenCalledWith({
        profileID: mockCustomers.active.id,
        dispatch: mockDispatch,
        page: 2,
        size: 10,
        pinType: 'PIN',
      })
    })
  })

  it('formats timestamps correctly', () => {
    // Mock useAppSelector to return state with data
    vi.mocked(useAppSelector).mockImplementation((selector) =>
      selector(withDataState as any)
    )

    render(<CustomerPinHistory page={1} onPageChange={mockOnPageChange} />)

    // Check that formatTimestamp was called with the correct parameters
    expect(utils.formatTimestamp).toHaveBeenCalledWith('2023-01-01T12:00:00Z')
    expect(utils.formatTimestamp).toHaveBeenCalledWith('2023-01-02T14:30:00Z')

    // Check that the formatted timestamps are displayed
    expect(
      screen.getByText('Formatted: 2023-01-01T12:00:00Z')
    ).toBeInTheDocument()
    expect(
      screen.getByText('Formatted: 2023-01-02T14:30:00Z')
    ).toBeInTheDocument()
  })

  it('handles missing customer ID gracefully', () => {
    // Mock useAppSelector to return state with customer having no ID
    vi.mocked(useAppSelector).mockImplementation((selector) =>
      selector({
        ...emptyState,
        customers: {
          ...emptyState.customers,
          customer: { ...mockCustomers.active, id: '' },
        },
      } as any)
    )

    render(<CustomerPinHistory page={1} onPageChange={mockOnPageChange} />)

    // Check that getCustomerPinHistory was called with an empty string
    expect(storeActions.getCustomerPinHistory).toHaveBeenCalledWith({
      profileID: '',
      dispatch: mockDispatch,
      page: 1,
      size: 10,
      pinType: 'PIN',
    })
  })

  it('applies correct styling to table cells', () => {
    // Mock useAppSelector to return state with data
    vi.mocked(useAppSelector).mockImplementation((selector) =>
      selector(withDataState as any)
    )

    render(<CustomerPinHistory page={1} onPageChange={mockOnPageChange} />)

    // Check that the table cells have the correct styling
    const tableCells = screen.getAllByTestId('table-cell')

    // The first three columns should have nowrap styling
    expect(tableCells[0]).toHaveAttribute('data-text-wrap', 'nowrap')
    expect(tableCells[1]).toHaveAttribute('data-text-wrap', 'nowrap')
    expect(tableCells[2]).toHaveAttribute('data-text-wrap', 'nowrap')
  })

  it('renders the table with sticky header and correct min width', () => {
    // Mock useAppSelector to return state with data
    vi.mocked(useAppSelector).mockImplementation((selector) =>
      selector(withDataState as any)
    )

    render(<CustomerPinHistory page={1} onPageChange={mockOnPageChange} />)

    // Check that the table has sticky header and correct min width
    expect(screen.getByTestId('table')).toHaveAttribute(
      'data-sticky-header',
      'true'
    )
    expect(screen.getByTestId('table')).toHaveAttribute(
      'data-min-width',
      '70vw'
    )
  })

  it('uses the page prop for initial pagination', () => {
    // Mock useAppSelector to return state with data
    vi.mocked(useAppSelector).mockImplementation((selector) =>
      selector(withDataState as any)
    )

    // Render with page 2
    render(<CustomerPinHistory page={2} onPageChange={mockOnPageChange} />)

    // Check that the pagination options include the correct page
    expect(screen.getByTestId('custom-pagination')).toHaveAttribute(
      'data-page',
      '1'
    )
  })

  it('updates pagination when props change', () => {
    // Mock useAppSelector to return state with data
    vi.mocked(useAppSelector).mockImplementation((selector) =>
      selector(withDataState as any)
    )

    const { rerender } = render(
      <CustomerPinHistory page={1} onPageChange={mockOnPageChange} />
    )

    // Check initial pagination
    expect(screen.getByTestId('custom-pagination')).toHaveAttribute(
      'data-page',
      '1'
    )

    // Rerender with new page prop
    rerender(<CustomerPinHistory page={3} onPageChange={mockOnPageChange} />)

    // The pagination should still show page 1 because it's driven by the customerPinLogResponse.pageNumber
    // not directly by the page prop
    expect(screen.getByTestId('custom-pagination')).toHaveAttribute(
      'data-page',
      '1'
    )
  })

  it('initializes pagination options from store state', () => {
    // Mock useAppSelector to return state with custom pagination
    vi.mocked(useAppSelector).mockImplementation((selector) =>
      selector({
        ...withDataState,
        customers: {
          ...withDataState.customers,
          customerPinLogResponse: {
            ...withDataState.customers.customerPinLogResponse,
            pageNumber: 3,
            pageSize: 20,
          },
        },
      } as any)
    )

    render(<CustomerPinHistory page={1} onPageChange={mockOnPageChange} />)

    // Check that the pagination options are initialized from the store state
    expect(screen.getByTestId('custom-pagination')).toHaveAttribute(
      'data-page',
      '3'
    )
    expect(screen.getByTestId('custom-pagination')).toHaveAttribute(
      'data-size',
      '20'
    )
  })
})
