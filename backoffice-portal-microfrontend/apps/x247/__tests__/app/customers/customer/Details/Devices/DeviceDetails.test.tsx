'use client'

import type React from 'react'

import { describe, it, expect, vi, beforeEach } from 'vitest'
import { render, fireEvent, screen } from '../../../test-utils'
import { DeviceDetail } from '@/app/customers/customer/Details/Devices/DeviceDetails'
import * as storeReducers from '@/store/reducers'
import { useAppDispatch, useAppSelector } from '@/store'
import * as utils from '@dtbx/store/utils'
import { mockCustomers } from '../../../../stubs/customer-listing'
import { mockActiveDevice, mockDevice } from '../../../../stubs/stubs'

// Mock the store reducers
vi.mock('@/store/reducers', () => ({
  setOpenDevice: vi.fn().mockReturnValue({ type: 'customers/setOpenDevice' }),
}))

// Mock the store hooks
vi.mock('@/store', () => ({
  useAppDispatch: vi.fn(),
  useAppSelector: vi.fn(),
}))

// Mock the utils
vi.mock('@dtbx/store/utils', () => ({
  formatTimestamp: vi.fn((timestamp) => `Formatted: ${timestamp}`),
}))

// Mock tiny-case
vi.mock('tiny-case', () => ({
  sentenceCase: (str: string) =>
    str.charAt(0).toUpperCase() + str.slice(1).toLowerCase(),
}))

// Mock the child components
vi.mock(
  '@/app/customers/customer/Details/Devices/LoadingDeviceDetails',
  () => ({
    default: () => (
      <div data-testid="loading-device-details">Loading Device Details</div>
    ),
  })
)

vi.mock('@/app/customers/customer/Details/Devices/MoreMenu', () => ({
  DeviceStatusChange: ({
    device,
    customer,
    origin,
  }: {
    device: any
    customer: any
    origin: string
  }) => (
    <div
      data-testid="device-status-change"
      data-device-id={device?.uuid}
      data-origin={origin}
    >
      Device Status Change
    </div>
  ),
}))

vi.mock('@/app/customers/customer/Details/Devices/DeviceEventHistory', () => ({
  default: () => (
    <div data-testid="device-event-history">Device Event History</div>
  ),
}))

vi.mock('@dtbx/ui/components', () => ({
  ReadOnlyTypography: ({ label, value }: { label: string; value: string }) => (
    <div
      data-testid={`readonly-typography-${label.toLowerCase().replace(/\s+/g, '-')}`}
      data-value={value}
    >
      {label}: {value}
    </div>
  ),
}))

vi.mock('@dtbx/ui/components/Chip', () => ({
  CustomerStatusChip: ({ label }: { label: string }) => (
    <div data-testid="customer-status-chip" data-label={label}>
      {label}
    </div>
  ),
}))

vi.mock('@dtbx/ui/components/SvgIcons', () => ({
  DeviceIcons: () => <div data-testid="device-icons">Device Icon</div>,
}))

// Mock MUI components
vi.mock('@mui/material', async () => {
  const actual = await vi.importActual('@mui/material')
  return {
    ...actual,
    Avatar: ({
      children,
      sx,
      ...props
    }: {
      children: React.ReactNode
      sx?: any
      [key: string]: any
    }) => (
      <div
        data-testid="avatar"
        data-width={sx?.width}
        data-height={sx?.height}
        data-background-color={sx?.backgroundColor}
        {...props}
      >
        {children}
      </div>
    ),
    Button: ({
      children,
      startIcon,
      variant,
      sx,
      onClick,
      ...props
    }: {
      children: React.ReactNode
      startIcon?: React.ReactNode
      variant?: string
      sx?: any
      onClick?: () => void
      [key: string]: any
    }) => (
      <button
        data-testid="button"
        data-variant={variant}
        data-width={sx?.width}
        data-height={sx?.height}
        onClick={onClick}
        {...props}
      >
        {startIcon}
        {children}
      </button>
    ),
    Stack: ({
      children,
      sx,
      ...props
    }: {
      children: React.ReactNode
      sx?: any
      [key: string]: any
    }) => (
      <div
        data-testid="stack"
        data-px={sx?.px}
        data-py={sx?.py}
        data-pt={sx?.pt}
        data-pb={sx?.pb}
        data-width={sx?.width}
        data-background={sx?.background}
        data-border={sx?.border}
        data-border-radius={sx?.borderRadius}
        data-gap={sx?.gap}
        data-flex-direction={sx?.flexDirection}
        data-justify-content={sx?.justifyContent}
        data-align-items={sx?.alignItems}
        data-align-content={sx?.alignContent}
        {...props}
      >
        {children}
      </div>
    ),
    Typography: ({
      children,
      variant,
      sx,
      onClick,
      ...props
    }: {
      children: React.ReactNode
      variant?: string
      sx?: any
      onClick?: () => void
      [key: string]: any
    }) => (
      <div
        data-testid={`typography-${variant || 'default'}`}
        data-fontSize={sx?.fontSize}
        data-color={sx?.color}
        data-text-wrap={sx?.textWrap}
        data-text-align={sx?.textAlign}
        onClick={onClick}
        {...props}
      >
        {children}
      </div>
    ),
  }
})

// Mock MUI icons
vi.mock('@mui/icons-material', () => ({
  ArrowBackRounded: () => <span data-testid="arrow-back-rounded-icon">←</span>,
}))

// Mock dispatch function
const mockDispatch = vi.fn()

// Create mock device data

// Create mock states for different scenarios
const loadingState = {
  customers: {
    isLoadingDevice: true,
    device: null,
    customer: mockCustomers.active,
  },
}

const loadedState = {
  customers: {
    isLoadingDevice: false,
    device: mockActiveDevice,
    customer: mockCustomers.active,
  },
}

describe('DeviceDetail Component', () => {
  beforeEach(() => {
    // Reset mocks before each test
    vi.clearAllMocks()

    // Set up default mock for useAppDispatch
    vi.mocked(useAppDispatch).mockReturnValue(mockDispatch)

    // Set up default mock for useAppSelector
    vi.mocked(useAppSelector).mockImplementation((selector) =>
      selector(loadedState as any)
    )
  })

  it('renders the loading state when isLoadingDevice is true', () => {
    // Mock useAppSelector to return loading state
    vi.mocked(useAppSelector).mockImplementation((selector) =>
      selector(loadingState as any)
    )

    render(<DeviceDetail />)

    // Check that the loading component is rendered
    expect(screen.getByTestId('loading-device-details')).toBeInTheDocument()

    // Check that the device details are not rendered
    expect(screen.queryByTestId('stack')).not.toBeInTheDocument()
  })

  it.skip('renders the device details when isLoadingDevice is false', () => {
    render(<DeviceDetail />)

    // Check that the device details are rendered
    expect(screen.getAllByTestId('stack')).toHaveLength(8)

    // Check that the loading component is not rendered
    expect(
      screen.queryByTestId('loading-device-details')
    ).not.toBeInTheDocument()
  })

  it('renders the back button with correct styling', () => {
    render(<DeviceDetail />)

    // Check that the back button is rendered
    const backButton = screen.getByTestId('button')
    expect(backButton).toBeInTheDocument()
    expect(backButton).toHaveAttribute('data-variant', 'outlined')
    expect(backButton).toHaveAttribute('data-width', '10%')
    expect(backButton).toHaveAttribute('data-height', '34px')

    // Check that the back arrow icon is rendered
    expect(screen.getByTestId('arrow-back-rounded-icon')).toBeInTheDocument()

    // Check that the button text is correct
    const buttonText = screen.getByTestId('typography-label1')
    expect(buttonText).toBeInTheDocument()
    expect(buttonText).toHaveTextContent('All Devices')
  })

  it('navigates back to device list when back button is clicked', () => {
    render(<DeviceDetail />)

    // Get the button text element
    const buttonText = screen.getByTestId('typography-label1')

    // Click the button text
    fireEvent.click(buttonText)

    // Check that setOpenDevice was called with false
    expect(storeReducers.setOpenDevice).toHaveBeenCalledWith(false)
  })

  it.skip('renders the device avatar and name', () => {
    render(<DeviceDetail />)

    // Check that the avatar is rendered
    const avatar = screen.getByTestId('avatar')
    expect(avatar).toBeInTheDocument()
    expect(avatar).toHaveAttribute('data-width', '50px')
    expect(avatar).toHaveAttribute('data-height', '50px')
    expect(avatar).toHaveAttribute('data-background-color', '#E7E8E9')

    // Check that the device icon is rendered inside the avatar
    expect(screen.getByTestId('device-icons')).toBeInTheDocument()

    // Check that the device name is rendered
    const deviceName = screen.getByTestId('typography-subtitle1')
    expect(deviceName).toBeInTheDocument()
    expect(deviceName).toHaveTextContent('iPhone 12')
    expect(deviceName).toHaveAttribute('data-font-size', '20px')
    expect(deviceName).toHaveAttribute('data-color', '#000509')
  })

  it('renders the device status chip', () => {
    render(<DeviceDetail />)

    // Check that the status chip is rendered
    const statusChip = screen.getByTestId('customer-status-chip')
    expect(statusChip).toBeInTheDocument()
    expect(statusChip).toHaveAttribute('data-label', 'ACTIVE')
  })

  it('renders the device action buttons', () => {
    render(<DeviceDetail />)

    // Check that the device event history button is rendered
    expect(screen.getByTestId('device-event-history')).toBeInTheDocument()

    // Check that the device status change button is rendered
    const statusChangeButton = screen.getByTestId('device-status-change')
    expect(statusChangeButton).toBeInTheDocument()
    expect(statusChangeButton).toHaveAttribute(
      'data-device-id',
      'a695fe20-fc53-4042-a57f-e68be4f597e3'
    )
    expect(statusChangeButton).toHaveAttribute('data-origin', 'view')
  })

  it.skip('renders all device details with correct values', () => {
    render(<DeviceDetail />)

    // Check that all ReadOnlyTypography components are rendered with correct values
    const profileDeviceId = screen.getByTestId(
      'readonly-typography-profile-device-id'
    )
    expect(profileDeviceId).toBeInTheDocument()
    expect(profileDeviceId).toHaveAttribute('data-value', '12345')

    const deviceType = screen.getByTestId('readonly-typography-device-type')
    expect(deviceType).toBeInTheDocument()
    expect(deviceType).toHaveAttribute('data-value', 'Mobile')

    const deviceName = screen.getByTestId('readonly-typography-device-name')
    expect(deviceName).toBeInTheDocument()
    expect(deviceName).toHaveAttribute('data-value', 'iPhone 12')

    const imsi = screen.getByTestId('readonly-typography-imsi')
    expect(imsi).toBeInTheDocument()
    expect(imsi).toHaveAttribute('data-value', '12345')

    const deviceStatus = screen.getByTestId('readonly-typography-device-status')
    expect(deviceStatus).toBeInTheDocument()
    expect(deviceStatus).toHaveAttribute('data-value', 'Active')

    const deviceModel = screen.getByTestId('readonly-typography-device-model')
    expect(deviceModel).toBeInTheDocument()
    expect(deviceModel).toHaveAttribute('data-value', 'A2403')

    const devicePlatform = screen.getByTestId(
      'readonly-typography-device-platform'
    )
    expect(devicePlatform).toBeInTheDocument()
    expect(devicePlatform).toHaveAttribute('data-value', 'iOS')

    const phoneNumber = screen.getByTestId('readonly-typography-phone-number')
    expect(phoneNumber).toBeInTheDocument()
    expect(phoneNumber).toHaveAttribute('data-value', '+1234567890')

    const dateCreated = screen.getByTestId('readonly-typography-date-created')
    expect(dateCreated).toBeInTheDocument()
    expect(dateCreated).toHaveAttribute(
      'data-value',
      'Formatted: 2023-01-01T12:00:00Z'
    )

    const dateInstalled = screen.getByTestId(
      'readonly-typography-date-installed'
    )
    expect(dateInstalled).toBeInTheDocument()
    expect(dateInstalled).toHaveAttribute(
      'data-value',
      'Formatted: 2023-01-01T12:00:00Z'
    )
  })

  it.skip('formats timestamps correctly', () => {
    render(<DeviceDetail />)

    // Check that formatTimestamp was called with the correct parameters
    expect(utils.formatTimestamp).toHaveBeenCalledWith('2023-01-01T12:00:00Z')
    expect(utils.formatTimestamp).toHaveBeenCalledTimes(2) // Called for both dateCreated and dateInstalled
  })

  it('formats device status with sentence case', () => {
    render(<DeviceDetail />)

    // Check that the device status is formatted with sentence case
    const deviceStatus = screen.getByTestId('readonly-typography-device-status')
    expect(deviceStatus).toHaveAttribute('data-value', 'Active') // Transformed from "ACTIVE"
  })

  it.skip('formats device model with sentence case', () => {
    render(<DeviceDetail />)

    // Check that the device model is formatted with sentence case
    const deviceModel = screen.getByTestId('readonly-typography-device-model')
    expect(deviceModel).toHaveAttribute('data-value', 'A2403') // Transformed from "A2403"
  })

  it.skip('renders the device details in a structured layout', () => {
    render(<DeviceDetail />)

    // Check the main container stack
    const mainStack = screen.getAllByTestId('stack')[0]
    expect(mainStack).toHaveAttribute('data-px', '1%')
    expect(mainStack).toHaveAttribute('data-py', '1%')

    // Check the content container stack
    const contentStack = screen.getAllByTestId('stack')[1]
    expect(contentStack).toHaveAttribute('data-width', '100%')
    expect(contentStack).toHaveAttribute('data-px', '3%')
    expect(contentStack).toHaveAttribute('data-pt', '1%')
    expect(contentStack).toHaveAttribute('data-pb', '3%')
    expect(contentStack).toHaveAttribute('data-background', '#FFFFFF')
    expect(contentStack).toHaveAttribute('data-border', '1px solid #D0D5DD')
    expect(contentStack).toHaveAttribute('data-border-radius', '4px')
    expect(contentStack).toHaveAttribute('data-gap', '4vh')

    // Check the device info container stack
    const deviceInfoStack = screen.getAllByTestId('stack')[2]
    expect(deviceInfoStack).toHaveAttribute('data-flex-direction', 'column')
    expect(deviceInfoStack).toHaveAttribute(
      'data-justify-content',
      'space-between'
    )
    expect(deviceInfoStack).toHaveAttribute('data-gap', '2vh')

    // Check the header row stack
    const headerRowStack = screen.getAllByTestId('stack')[3]
    expect(headerRowStack).toHaveAttribute('data-flex-direction', 'row')
    expect(headerRowStack).toHaveAttribute(
      'data-justify-content',
      'space-between'
    )
    expect(headerRowStack).toHaveAttribute('data-align-items', 'center')

    // Check the details grid container
    const detailsGridStack = screen.getAllByTestId('stack')[6]
    expect(detailsGridStack).toHaveAttribute('data-flex-direction', 'column')
    expect(detailsGridStack).toHaveAttribute(
      'data-justify-content',
      'space-between'
    )
    expect(detailsGridStack).toHaveAttribute(
      'data-align-content',
      'space-between'
    )
    expect(detailsGridStack).toHaveAttribute('data-gap', '3vh')

    // Check the first row of details
    const firstRowStack = screen.getAllByTestId('stack')[7]
    expect(firstRowStack).toHaveAttribute(
      'data-justify-content',
      'space-between'
    )
    expect(firstRowStack).toHaveAttribute('data-flex-direction', 'row')
    expect(firstRowStack).toHaveAttribute('data-gap', '3vh')
  })
})
