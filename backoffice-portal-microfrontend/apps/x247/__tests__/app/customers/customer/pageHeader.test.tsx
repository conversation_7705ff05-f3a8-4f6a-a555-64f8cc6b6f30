import { describe, it, expect, vi, beforeEach } from 'vitest'
import { render, fireEvent, waitFor, screen } from '../test-utils'
import PageHeader from '@/app/customers/customer/pageHeader'
import {
  fetchPendingSingleCustomerApprovals,
  getLinkedCustomerAccountsByProfileId,
} from '@/store/actions'
import { setOpenChangeLogsDrawer, setSelectedCustomer } from '@/store/reducers'
import { setDrawer } from '@dtbx/store/reducers'
import { useAppDispatch, useAppSelector } from '@/store'
import {
  mockCustomers,
  mockApprovalRequests,
} from '../../stubs/customer-listing'
import React from 'react'

// Mock the dependencies
vi.mock('@/store/actions', () => ({
  fetchPendingSingleCustomerApprovals: vi.fn().mockResolvedValue([]),
  getLinkedCustomerAccountsByProfileId: vi.fn().mockResolvedValue([]),
}))

vi.mock('@/store/reducers', () => ({
  setDrawer: vi.fn(),
  setOpenChangeLogsDrawer: vi.fn(),
  setSelectedCustomer: vi.fn(),
}))

vi.mock('@/store', () => ({
  useAppDispatch: vi.fn(),
  useAppSelector: vi.fn(),
}))

vi.mock('@dtbx/store/reducers', () => ({
  setDrawer: vi.fn(),
}))

vi.mock('@dtbx/ui/components/Chip', () => ({
  CustomerStatusChip: ({ label }: any) => (
    <div
      data-testid={`status-chip-${label.toLowerCase().replace(/\s+/g, '-')}`}
    >
      {label}
    </div>
  ),
  CustomerInfoChip: ({ label, requests }: any) => (
    <div data-testid="customer-info-chip">
      {label} {requests?.join(', ')}
    </div>
  ),
}))

vi.mock('@/app/customers/customer/Details/Dialog/ViewRequestDetails', () => ({
  default: () => (
    <button data-testid="view-request-details-button">
      View Request Details
    </button>
  ),
}))

vi.mock(
  '@/app/customers/customer/Details/Dialog/PendingApprovalRequests',
  () => ({
    default: () => (
      <div data-testid="pending-approval-requests">
        Pending Approval Requests
      </div>
    ),
  })
)

vi.mock('@/app/customers/customers/CustomerLists/customerMoreMenu', () => ({
  ActivateCustomer: ({ customer, origin }: any) => (
    <button
      data-testid={`activate-customer-button-${origin}`}
      disabled={!customer.isBlocked}
    >
      {customer.isBlocked ? 'Activate' : 'Deactivate'} Customer
    </button>
  ),
  DeleteCustomer: ({ customer, origin }: any) => (
    <button data-testid={`delete-customer-button-${origin}`}>
      Delete Customer
    </button>
  ),
}))

// Mock MUI components
vi.mock('@mui/material', async () => {
  const actual = await vi.importActual('@mui/material')
  return {
    ...actual,
    Breadcrumbs: ({ children }: any) => (
      <div data-testid="breadcrumbs">{children}</div>
    ),
    Link: ({ children, onClick, sx }: any) => (
      <a data-testid="breadcrumb-link" href="#" onClick={onClick}>
        {children}
      </a>
    ),
    Typography: ({ children, sx, variant, color }: any) => (
      <span
        data-testid={`typography-${variant || 'default'}`}
        style={{ color }}
      >
        {children}
      </span>
    ),
    Button: ({ children, onClick, variant, sx, disabled }: any) => {
      // Extract the text content for the data-testid
      let buttonText = ''

      // Special case for "View approval request details" button
      if (
        Array.isArray(children) &&
        typeof children[0] === 'string' &&
        children[0] === 'View approval request details'
      ) {
        return (
          <button
            data-testid="view-approval-request-button"
            onClick={onClick}
            disabled={disabled}
            data-variant={variant}
          >
            {children}
          </button>
        )
      }

      // Handle other cases
      if (typeof children === 'string') {
        buttonText = children
      } else if (Array.isArray(children)) {
        const textElement = children.find((child) => typeof child === 'string')
        buttonText = textElement || 'button'
      } else if (React.isValidElement(children)) {
        buttonText = 'button'
      } else {
        buttonText = String(children || 'button')
      }

      return (
        <button
          data-testid={`button-${buttonText.toLowerCase().replace(/\s+/g, '-')}`}
          onClick={onClick}
          disabled={disabled}
          data-variant={variant}
        >
          {children}
        </button>
      )
    },
    Stack: ({ children, sx }: any) => <div data-testid="stack">{children}</div>,
  }
})

// Mock dispatch function
const mockDispatch = vi.fn()

// Create mock states for different scenarios
const activeCustomerState = {
  customers: {
    customer: mockCustomers.active,
    isCustomerApprovalBarOpen: false,
  },
  approvalRequests: {
    selectedApprovalRequest: {},
    pendingSingleCustomerApprovalRequests: [],
    customersWithPendingApprovals: [],
  },
}

const inactiveCustomerState = {
  customers: {
    customer: mockCustomers.blocked,
    isCustomerApprovalBarOpen: false,
  },
  approvalRequests: {
    selectedApprovalRequest: {},
    pendingSingleCustomerApprovalRequests: [],
    customersWithPendingApprovals: [],
  },
}

const customerWithPendingApprovalsState = {
  customers: {
    customer: mockCustomers.standard[0],
    isCustomerApprovalBarOpen: false,
  },
  approvalRequests: {
    selectedApprovalRequest: {},
    pendingSingleCustomerApprovalRequests: [
      mockApprovalRequests.pendingPinReset,
      mockApprovalRequests.approvedProfileUpdate,
    ],
    customersWithPendingApprovals: [],
  },
}

const customerWithSelectedApprovalState = {
  customers: {
    customer: mockCustomers.standard[0],
    isCustomerApprovalBarOpen: true,
  },
  approvalRequests: {
    selectedApprovalRequest: {
      ...mockApprovalRequests.pendingPinReset,
      status: 'PENDING',
    },
    pendingSingleCustomerApprovalRequests: [],
    customersWithPendingApprovals: [],
  },
}

const customerWithApprovedRequestState = {
  customers: {
    customer: mockCustomers.standard[0],
    isCustomerApprovalBarOpen: true,
  },
  approvalRequests: {
    selectedApprovalRequest: {
      ...mockApprovalRequests.approvedProfileUpdate,
      status: 'APPROVED',
    },
    pendingSingleCustomerApprovalRequests: [],
    customersWithPendingApprovals: [],
  },
}

describe('PageHeader Component', () => {
  beforeEach(() => {
    vi.clearAllMocks()

    // Set up default mock for useAppDispatch
    vi.mocked(useAppDispatch).mockReturnValue(mockDispatch)
  })

  it('renders the component with active customer', () => {
    // Mock useAppSelector to return active customer state
    vi.mocked(useAppSelector).mockImplementation((selector) =>
      selector(activeCustomerState as any)
    )

    const { getByTestId } = render(<PageHeader />)

    // Check that breadcrumbs are rendered
    expect(getByTestId('breadcrumbs')).toBeInTheDocument()
    expect(getByTestId('breadcrumb-link')).toBeInTheDocument()
    expect(getByTestId('breadcrumb-link')).toHaveTextContent('All Customers')

    // Check that customer name is displayed
    const customerName = `${mockCustomers.active.firstName} ${mockCustomers.active.lastName}`
    expect(getByTestId('typography-default')).toHaveTextContent(customerName)

    // Check that active status chip is displayed
    expect(getByTestId('status-chip-active')).toBeInTheDocument()

    // Check that activate/deactivate and delete buttons are rendered
    expect(getByTestId('activate-customer-button-view')).toBeInTheDocument()
    expect(getByTestId('delete-customer-button-view')).toBeInTheDocument()

    // Check that API calls were made
    expect(fetchPendingSingleCustomerApprovals).toHaveBeenCalledWith(
      mockDispatch,
      mockCustomers.active.id,
      {},
      []
    )
    expect(getLinkedCustomerAccountsByProfileId).toHaveBeenCalledWith(
      mockCustomers.active.id,
      mockDispatch
    )
  })

  it('renders the component with inactive customer', () => {
    // Mock useAppSelector to return inactive customer state
    vi.mocked(useAppSelector).mockImplementation((selector) =>
      selector(inactiveCustomerState as any)
    )

    const { getByTestId } = render(<PageHeader />)

    // Check that inactive status chip is displayed
    expect(getByTestId('status-chip-inactive')).toBeInTheDocument()

    // Check that block reason is displayed
    expect(getByTestId('typography-body2')).toHaveTextContent(
      mockCustomers.blocked.blockReason
    )

    // Check that activate button is enabled for blocked customer
    const activateButton = getByTestId('activate-customer-button-view')
    expect(activateButton).toBeInTheDocument()
    expect(activateButton).toHaveTextContent('Activate Customer')
    expect(activateButton).not.toBeDisabled()
  })

  it('renders the component with pending approval requests', () => {
    // Mock useAppSelector to return customer with pending approvals state
    vi.mocked(useAppSelector).mockImplementation((selector) =>
      selector(customerWithPendingApprovalsState as any)
    )

    const { getByTestId } = render(<PageHeader />)

    // Check that pending approval info chip is displayed
    expect(getByTestId('customer-info-chip')).toBeInTheDocument()

    // Check that view approval request details button is displayed
    expect(getByTestId('view-approval-request-button')).toBeInTheDocument()

    // Check that pending approval requests component is rendered
    expect(getByTestId('pending-approval-requests')).toBeInTheDocument()
  })

  it('handles view approval request details button click', () => {
    // Mock useAppSelector to return customer with pending approvals state
    vi.mocked(useAppSelector).mockImplementation((selector) =>
      selector(customerWithPendingApprovalsState as any)
    )

    const { getByTestId } = render(<PageHeader />)

    // Click the view approval request details button
    fireEvent.click(getByTestId('view-approval-request-button'))

    // Check that setDrawer was called with correct parameters
    expect(setDrawer).toHaveBeenCalledWith({
      drawerChildren: {
        childType: 'pending_approval_requests',
        data: [],
      },
      header: 'Approval request details',
      open: true,
    })
  })

  it('renders the component with selected pending approval request', () => {
    // Mock useAppSelector to return customer with selected approval state
    vi.mocked(useAppSelector).mockImplementation((selector) =>
      selector(customerWithSelectedApprovalState as any)
    )

    const { getByTestId } = render(<PageHeader />)

    // Check that pending approval details status chip is displayed
    expect(
      getByTestId('status-chip-pending-approval-details')
    ).toBeInTheDocument()

    // Check that view request details button is displayed
    expect(getByTestId('view-request-details-button')).toBeInTheDocument()
  })

  it('renders the component with approved request', () => {
    // Mock useAppSelector to return customer with approved request state
    vi.mocked(useAppSelector).mockImplementation((selector) =>
      selector(customerWithApprovedRequestState as any)
    )

    const { getByTestId, queryByTestId } = render(<PageHeader />)

    // Check that changes log button is rendered (but hidden with display: none)
    const changesLogButton = getByTestId('button-changes-log')
    expect(changesLogButton).toBeInTheDocument()

    // Check that view request details button is not displayed
    expect(queryByTestId('view-request-details-button')).not.toBeInTheDocument()
  })

  it('handles breadcrumb link click', () => {
    // Mock useAppSelector to return active customer state
    vi.mocked(useAppSelector).mockImplementation((selector) =>
      selector(activeCustomerState as any)
    )

    // Mock window.history.back
    const mockHistoryBack = vi.fn()
    Object.defineProperty(window.history, 'back', {
      configurable: true,
      value: mockHistoryBack,
    })

    const { getByTestId } = render(<PageHeader />)

    // Click the breadcrumb link
    fireEvent.click(getByTestId('breadcrumb-link'))

    // Check that setSelectedCustomer was called with correct parameters
    expect(setSelectedCustomer).toHaveBeenCalledWith({
      customer: null,
      isPendingAction: false,
      openDetails: false,
    })

    // Check that window.history.back was called
    expect(mockHistoryBack).toHaveBeenCalled()
  })

  it('handles changes log button click', () => {
    // Mock useAppSelector to return active customer state
    vi.mocked(useAppSelector).mockImplementation((selector) =>
      selector(activeCustomerState as any)
    )

    const { getByTestId } = render(<PageHeader />)

    // Find the changes log button
    const changesLogButton = getByTestId('button-changes-log')

    // Click the changes log button
    fireEvent.click(changesLogButton)

    // Check that setOpenChangeLogsDrawer was called with correct parameters
    expect(setOpenChangeLogsDrawer).toHaveBeenCalledWith(true)
  })

  it('handles many pending requests correctly', () => {
    // Create state with more than 3 pending requests
    const manyPendingRequestsState = {
      ...customerWithPendingApprovalsState,
      approvalRequests: {
        ...customerWithPendingApprovalsState.approvalRequests,
        pendingSingleCustomerApprovalRequests: [
          mockApprovalRequests.pendingPinReset,
          mockApprovalRequests.approvedProfileUpdate,
          mockApprovalRequests.rejectedBlockCustomer,
          mockApprovalRequests.multipleRequests.requests[0],
        ],
      },
    }

    // Mock useAppSelector to return state with many pending requests
    vi.mocked(useAppSelector).mockImplementation((selector) =>
      selector(manyPendingRequestsState as any)
    )

    const { getByTestId } = render(<PageHeader />)

    // Check that pending approval info chip is displayed with truncated list
    const infoChip = getByTestId('customer-info-chip')
    expect(infoChip).toBeInTheDocument()
    expect(infoChip.textContent).toContain('+1')
  })

  it('does not fetch data for CREATE_CUSTOMERS approval type', () => {
    // Create state with CREATE_CUSTOMERS approval type
    const createCustomersApprovalState = {
      ...customerWithSelectedApprovalState,
      approvalRequests: {
        ...customerWithSelectedApprovalState.approvalRequests,
        selectedApprovalRequest: {
          ...mockApprovalRequests.pendingPinReset,
          makerCheckerType: {
            ...mockApprovalRequests.pendingPinReset.makerCheckerType,
            type: 'CREATE_CUSTOMERS',
          },
        },
      },
    }

    // Mock useAppSelector to return state with CREATE_CUSTOMERS approval type
    vi.mocked(useAppSelector).mockImplementation((selector) =>
      selector(createCustomersApprovalState as any)
    )

    render(<PageHeader />)

    // Check that API calls were not made
    expect(fetchPendingSingleCustomerApprovals).not.toHaveBeenCalled()
    expect(getLinkedCustomerAccountsByProfileId).not.toHaveBeenCalled()
  })

  it('handles missing customer data gracefully', () => {
    // Create state with missing customer data
    const missingDataState = {
      ...activeCustomerState,
      customers: {
        ...activeCustomerState.customers,
        customer: {
          ...mockCustomers.standard[0],
          firstName: null,
          lastName: null,
        },
      },
    }

    // Mock useAppSelector to return state with missing customer data
    vi.mocked(useAppSelector).mockImplementation((selector) =>
      selector(missingDataState as any)
    )

    // This should not throw an error
    const { getByTestId } = render(<PageHeader />)

    // Check that the component still renders
    expect(getByTestId('breadcrumbs')).toBeInTheDocument()
  })

  it('renders correctly with different customer types', () => {
    // Test with one-time PIN customer
    vi.mocked(useAppSelector).mockImplementation((selector) =>
      selector({
        ...activeCustomerState,
        customers: {
          ...activeCustomerState.customers,
          customer: mockCustomers.oneTimePin,
        },
      } as any)
    )

    const { getByTestId, rerender } = render(<PageHeader />)

    // Check that customer name is displayed correctly
    expect(getByTestId('typography-default')).toHaveTextContent(
      `${mockCustomers.oneTimePin.firstName} ${mockCustomers.oneTimePin.lastName}`
    )

    // Test with not-set PIN customer
    vi.mocked(useAppSelector).mockImplementation((selector) =>
      selector({
        ...activeCustomerState,
        customers: {
          ...activeCustomerState.customers,
          customer: mockCustomers.notSetPin,
        },
      } as any)
    )

    rerender(<PageHeader />)

    // Check that customer name is displayed correctly
    expect(getByTestId('typography-default')).toHaveTextContent(
      `${mockCustomers.notSetPin.firstName} ${mockCustomers.notSetPin.lastName}`
    )
  })
})
