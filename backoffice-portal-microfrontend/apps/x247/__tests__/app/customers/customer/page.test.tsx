import { describe, it, expect, vi, beforeEach } from 'vitest'
import { render, waitFor } from '../test-utils'
import CustomerPage from '@/app/customers/customer/page'
import { getCustomerProfile } from '@/store/actions'
import { useAppDispatch, useAppSelector } from '@/store'
import { mockCustomers } from '../../stubs/customer-listing'

// Mock the dependencies
vi.mock('@/store/actions', () => ({
  getCustomerProfile: vi.fn().mockResolvedValue({}),
}))

vi.mock('@/store', () => ({
  useAppDispatch: vi.fn(),
  useAppSelector: vi.fn(),
}))

vi.mock('@/app/customers/customer/pageHeader', () => ({
  default: () => <div data-testid="page-header">Page Header Component</div>,
}))

vi.mock('@/app/customers/customer/Details', () => ({
  default: () => (
    <div data-testid="customer-details">Customer Details Component</div>
  ),
}))

// Mock dispatch function
const mockDispatch = vi.fn()

describe('CustomerPage Component', () => {
  beforeEach(() => {
    vi.clearAllMocks()

    // Set up default mock for useAppDispatch
    vi.mocked(useAppDispatch).mockReturnValue(mockDispatch)
  })

  it('renders the component with PageHeader and CustomerDetails', async () => {
    // Mock useAppSelector to return a customer with ID
    vi.mocked(useAppSelector).mockImplementation((selector) =>
      selector({
        customers: {
          customer: mockCustomers.standard[0],
        },
      } as any)
    )

    const { getByTestId } = render(<CustomerPage />)

    // Check that PageHeader and CustomerDetails are rendered
    expect(getByTestId('page-header')).toBeInTheDocument()
    expect(getByTestId('customer-details')).toBeInTheDocument()

    // Check that getCustomerProfile was called with correct parameters
    await waitFor(() => {
      expect(getCustomerProfile).toHaveBeenCalledWith(
        mockCustomers.standard[0].id,
        mockDispatch
      )
    })
  })

  it('calls getCustomerProfile when customer with ID is available', async () => {
    // Mock useAppSelector to return a customer with ID
    vi.mocked(useAppSelector).mockImplementation((selector) =>
      selector({
        customers: {
          customer: mockCustomers.standard[0],
        },
      } as any)
    )

    render(<CustomerPage />)

    // Check that getCustomerProfile was called with correct parameters
    await waitFor(() => {
      expect(getCustomerProfile).toHaveBeenCalledWith(
        mockCustomers.standard[0].id,
        mockDispatch
      )
    })
  })

  it('does not call getCustomerProfile when customer has no ID', () => {
    // Create a customer without ID
    const customerWithoutId = { ...mockCustomers.standard[0], id: undefined }

    // Mock useAppSelector to return a customer without ID
    vi.mocked(useAppSelector).mockImplementation((selector) =>
      selector({
        customers: {
          customer: customerWithoutId,
        },
      } as any)
    )

    render(<CustomerPage />)

    // Check that getCustomerProfile was not called
    expect(getCustomerProfile).not.toHaveBeenCalled()
  })

  it('does not call getCustomerProfile when no customer is available', () => {
    // Mock useAppSelector to return no customer
    vi.mocked(useAppSelector).mockImplementation((selector) =>
      selector({
        customers: {
          customer: null,
        },
      } as any)
    )

    render(<CustomerPage />)

    // Check that getCustomerProfile was not called
    expect(getCustomerProfile).not.toHaveBeenCalled()
  })

  it('handles errors when fetching customer profile', async () => {
    // Mock console.error to prevent test output pollution
    const consoleErrorSpy = vi
      .spyOn(console, 'error')
      .mockImplementation(() => {})

    // Mock getCustomerProfile to throw an error
    vi.mocked(getCustomerProfile).mockRejectedValueOnce(
      new Error('Failed to get customer profile')
    )

    // Mock useAppSelector to return a customer with ID
    vi.mocked(useAppSelector).mockImplementation((selector) =>
      selector({
        customers: {
          customer: mockCustomers.standard[0],
        },
      } as any)
    )

    // This should not throw an error
    render(<CustomerPage />)

    // Wait for the async operation to complete
    await new Promise(process.nextTick)

    // Restore console.error
    consoleErrorSpy.mockRestore()
  })

  it('uses different customer types from mock file', async () => {
    // Test with blocked customer
    vi.mocked(useAppSelector).mockImplementation((selector) =>
      selector({
        customers: {
          customer: mockCustomers.blocked,
        },
      } as any)
    )

    render(<CustomerPage />)

    // Check that getCustomerProfile was called with correct parameters
    await waitFor(() => {
      expect(getCustomerProfile).toHaveBeenCalledWith(
        mockCustomers.blocked.id,
        mockDispatch
      )
    })

    // Clear mocks
    vi.clearAllMocks()

    // Test with one-time PIN customer
    vi.mocked(useAppSelector).mockImplementation((selector) =>
      selector({
        customers: {
          customer: mockCustomers.oneTimePin,
        },
      } as any)
    )

    render(<CustomerPage />)

    // Check that getCustomerProfile was called with correct parameters
    await waitFor(() => {
      expect(getCustomerProfile).toHaveBeenCalledWith(
        mockCustomers.oneTimePin.id,
        mockDispatch
      )
    })
  })

  it('tests with incomplete customer data', () => {
    // Mock useAppSelector to return a customer with incomplete data
    vi.mocked(useAppSelector).mockImplementation((selector) =>
      selector({
        customers: {
          customer: mockCustomers.incomplete,
        },
      } as any)
    )

    const { getByTestId } = render(<CustomerPage />)

    // Check that the components are still rendered correctly
    expect(getByTestId('page-header')).toBeInTheDocument()
    expect(getByTestId('customer-details')).toBeInTheDocument()

    // Check that getCustomerProfile was called with correct parameters
    expect(getCustomerProfile).toHaveBeenCalledWith(
      mockCustomers.incomplete.id,
      mockDispatch
    )
  })

  it('tests the useEffect dependency array behavior', async () => {
    // Mock useAppSelector to return a customer with ID
    vi.mocked(useAppSelector).mockImplementation((selector) =>
      selector({
        customers: {
          customer: mockCustomers.standard[0],
        },
      } as any)
    )

    const { rerender } = render(<CustomerPage />)

    // Check that getCustomerProfile was called once
    await waitFor(() => {
      expect(getCustomerProfile).toHaveBeenCalledTimes(1)
    })

    // Clear mocks
    vi.clearAllMocks()

    // Re-render with the same props
    rerender(<CustomerPage />)

    // Since useEffect has an empty dependency array, it should not call getCustomerProfile again
    expect(getCustomerProfile).not.toHaveBeenCalled()

    // Update mock to return a different customer
    vi.mocked(useAppSelector).mockImplementation((selector) =>
      selector({
        customers: {
          customer: mockCustomers.standard[1],
        },
      } as any)
    )

    // Re-render with different props
    rerender(<CustomerPage />)

    // Since useEffect has an empty dependency array, it should not call getCustomerProfile again
    // even though the customer has changed
    expect(getCustomerProfile).not.toHaveBeenCalled()
  })
})
