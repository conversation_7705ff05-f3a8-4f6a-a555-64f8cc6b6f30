import { describe, it, expect, vi } from 'vitest'
import { render, screen } from '@testing-library/react'
import CustomersLayout from '@/app/customers/layout'
import React from 'react'

// Mock the MUI components
vi.mock('@mui/material', () => ({
  Stack: ({ children, sx }: { children: React.ReactNode; sx?: any }) => (
    <div data-testid="stack" data-sx={JSON.stringify(sx)}>
      {children}
    </div>
  ),
  Typography: ({
    children,
    variant,
  }: {
    children: React.ReactNode
    variant?: string
  }) => (
    <div data-testid="typography" data-variant={variant}>
      {children}
    </div>
  ),
}))

// Mock the CustomersIcon
vi.mock('@dtbx/ui/icons', () => ({
  CustomersIcon: ({ width, height }: { width: string; height: string }) => (
    <div data-testid="customers-icon" data-width={width} data-height={height}>
      Icon
    </div>
  ),
}))

describe('CustomersLayout', () => {
  it('renders the layout with icon and title', () => {
    render(
      <CustomersLayout>
        <div data-testid="child-content">Child Content</div>
      </CustomersLayout>
    )

    // Check if the icon is rendered with correct props
    const icon = screen.getByTestId('customers-icon')
    expect(icon).toBeInTheDocument()
    expect(icon).toHaveAttribute('data-width', '28')
    expect(icon).toHaveAttribute('data-height', '26')

    // Check if the title is rendered
    const title = screen.getByTestId('typography')
    expect(title).toBeInTheDocument()
    expect(title).toHaveAttribute('data-variant', 'h5')
    expect(title).toHaveTextContent('Customers')

    // Check if the child content is rendered
    const childContent = screen.getByTestId('child-content')
    expect(childContent).toBeInTheDocument()
    expect(childContent).toHaveTextContent('Child Content')
  })

  it('applies the correct styling to the header', () => {
    render(
      <CustomersLayout>
        <div>Child Content</div>
      </CustomersLayout>
    )

    // Get all stack components
    const stacks = screen.getAllByTestId('stack')

    // The header stack should be the second one (index 1)
    // The first one (index 0) is the main wrapper
    const headerStack = stacks[1]

    // Parse the sx prop to check styling
    const sx = JSON.parse(headerStack.getAttribute('data-sx') || '{}')

    // Check specific style properties
    expect(sx.marginLeft).toBe('1.5%')
    expect(sx.marginTop).toBe('0.2%')
    expect(sx.flexDirection).toBe('row')
    expect(sx.justifyContent).toBe('flex-start')
    expect(sx.alignItems).toBe('center')
    expect(sx.gap).toBe('8px')
    expect(sx.padding).toBe('8px')
  })

  it('renders children correctly', () => {
    // Test with multiple children
    render(
      <CustomersLayout>
        <div data-testid="first-child">First Child</div>
        <div data-testid="second-child">Second Child</div>
      </CustomersLayout>
    )

    expect(screen.getByTestId('first-child')).toBeInTheDocument()
    expect(screen.getByTestId('second-child')).toBeInTheDocument()
  })
})
