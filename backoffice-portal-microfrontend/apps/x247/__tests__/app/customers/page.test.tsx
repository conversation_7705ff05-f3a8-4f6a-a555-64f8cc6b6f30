import { describe, it, expect, vi, beforeEach } from 'vitest'
import { render, screen } from './test-utils'
import CustomersPage from '@/app/customers/page'
// Mock the Redux store and actions
vi.mock('@/store', () => ({
  useAppDispatch: vi.fn(() => vi.fn()),
  useAppSelector: vi.fn(() => ({
    customers: {
      customersResponse: {
        data: [],
        pageNumber: 1,
        totalNumberOfPages: 0,
      },
      isCustomersLoading: false,
      isCustomersSuccess: true,
      search: '',
      selectedFilters: [],
    },
    approvalRequests: {
      customersWithPendingApprovals: [],
    },
  })),
}))

vi.mock('@/store/actions', () => ({
  getAllCustomers: vi.fn(),
  getCustomerProfileById: vi.fn(),
  fetchPendingSingleCustomerApprovals: vi.fn(),
}))

vi.mock('@/store/reducers', () => ({
  setOpenChangeLogsDrawer: vi.fn(),
  setCustomerApprovalBarOpen: vi.fn(),
  setCustomersWithPendingApprovals: vi.fn(),
  setPendingSingleCustomerApprovals: vi.fn(),
}))

// Mock the components used in the page
vi.mock('@/app/customers/customers/CustomerLists/CustomerList', () => ({
  default: vi.fn(() => (
    <div data-testid="customers-list-page">Customers List Page</div>
  )),
}))

vi.mock('@/app/customers/ChangeLogDrawer', () => ({
  CustomerChangeLogDrawer: vi.fn(() => (
    <div data-testid="customer-change-log-drawer">Change Log Drawer</div>
  )),
}))

// Import the mocked modules to use in tests
import { getAllCustomers } from '@/store/actions'
import { useAppDispatch } from '@/store'

describe('Main CustomersPage', () => {
  const mockDispatch = vi.fn()

  beforeEach(() => {
    vi.clearAllMocks()
    vi.mocked(useAppDispatch).mockReturnValue(mockDispatch)
  })

  it('renders the customers page component correctly', () => {
    render(<CustomersPage />)

    // Check if both components are rendered
    expect(screen.getByTestId('customer-change-log-drawer')).toBeInTheDocument()
    expect(screen.getByTestId('customers-list-page')).toBeInTheDocument()
  })

  it('dispatches getAllCustomers on mount with correct parameters', () => {
    render(<CustomersPage />)

    // Check if getAllCustomers was called with the correct parameters
    expect(getAllCustomers).toHaveBeenCalledTimes(1)
    expect(getAllCustomers).toHaveBeenCalledWith(
      {
        page: 1,
        size: 10,
      },
      mockDispatch
    )
  })
})
