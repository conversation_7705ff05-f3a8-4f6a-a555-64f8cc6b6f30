import { describe, it, expect, vi, beforeEach } from 'vitest'
import {
  fireEvent,
  waitFor,
  act,
  screen,
  render,
  customRouterMock,
} from '../../test-utils'
import List from '@/app/customers/customers/CustomerLists/CustomerList'
import {
  fetchPendingSingleCustomerApprovals,
  getAllCustomers,
  getCustomerProfileById,
} from '@/store/actions'
import {
  setCustomerApprovalBarOpen,
  setCustomersWithPendingApprovals,
  setPendingSingleCustomerApprovals,
} from '@/store/reducers'
import { formatTimestamp } from '@dtbx/store/utils'
import { useAppDispatch, useAppSelector } from '@/store'
import React from 'react'
import {
  generateMockCustomers,
  generateMockCustomerApprovals,
  mockCustomers,
  generateMockCustomer,
} from '../../../stubs/customer-listing'
// Mock the dependencies
vi.mock('@/store/actions', () => ({
  fetchPendingSingleCustomerApprovals: vi.fn().mockResolvedValue([]),
  getAllCustomers: vi.fn(),
  getCustomerProfileById: vi.fn(),
}))

vi.mock('@/store/reducers', () => ({
  setCustomerApprovalBarOpen: vi.fn(),
  setCustomersWithPendingApprovals: vi.fn(),
  setPendingSingleCustomerApprovals: vi.fn(),
}))

vi.mock('@dtbx/store/utils', () => ({
  formatTimestamp: vi.fn((date) =>
    date ? new Date(date).toLocaleDateString() : 'Invalid Date'
  ),
  HasAccessToRights: vi.fn(),
  // Add the missing AccessControlWrapper export
  AccessControlWrapper: ({ children }: { children: React.ReactNode }) => (
    <>{children}</>
  ),
  ACCESS_CONTROLS: {
    CREATE_CUSTOMERS: ['SUPER_CREATE_CUSTOMERS', 'MAKE_CREATE_CUSTOMERS'],
    UPDATE_CUSTOMERS: ['SUPER_UPDATE_CUSTOMERS', 'MAKE_UPDATE_CUSTOMERS'],
    // Add other access controls as needed
  },
}))
vi.mock('@dtbx/ui/components/Loading', () => ({
  CustomSkeleton: ({ children, ...props }: any) => (
    <div data-testid="custom-skeleton" {...props}>
      {children}
    </div>
  ),
}))

vi.mock('@dtbx/ui/components/Table', () => ({
  CustomPagination: ({ options, handlePagination }: any) => (
    <div data-testid="custom-pagination">
      <button
        data-testid="next-page-button"
        onClick={() =>
          handlePagination({
            ...options,
            page: options.page + 1,
          })
        }
      >
        Next Page
      </button>
      <span data-testid="current-page">{options.page}</span>
      <span data-testid="total-pages">{options.totalPages}</span>
    </div>
  ),
  CustomTableHeader: ({ headLabel }: any) => (
    <thead data-testid="custom-table-header">
      <tr>
        {headLabel.map((head: any) => (
          <th key={head.id}>{head.label}</th>
        ))}
      </tr>
    </thead>
  ),
  PaginationOptions: vi.fn(),
}))

vi.mock('@dtbx/ui/components/Chip', () => ({
  CustomerStatusChip: ({ label }: any) => (
    <div
      data-testid={`status-chip-${label.toLowerCase().replace(/\s+/g, '-')}`}
    >
      {label}
    </div>
  ),
}))

vi.mock('@/app/customers/customers/CustomerLists/NoCustomerFound', () => ({
  default: () => (
    <div data-testid="no-customer-found">
      <div data-testid="no-customer-image" />
      <div>No Customers Found</div>
      <div>
        There are no customers in the system yet. You can add a new customer by
        clicking the button below.
      </div>
      <button data-testid="add-customer-button">Add New Customer</button>
    </div>
  ),
}))
vi.mock('@/app/customers/customers/CustomerLists/pageHeader', () => ({
  default: ({ setDateCreatedFrom, setDateCreatedTo }: any) => (
    <div data-testid="page-header">
      <button
        data-testid="set-date-from"
        onClick={() => setDateCreatedFrom('2023-01-01')}
      >
        Set Date From
      </button>
      <button
        data-testid="set-date-to"
        onClick={() => setDateCreatedTo('2023-12-31')}
      >
        Set Date To
      </button>
    </div>
  ),
}))

vi.mock('@/app/customers/customers/CustomerLists/customerMoreMenu', () => ({
  CustomerMoreMenu: ({ customer, menuItems }: any) => (
    <div data-testid={`more-menu-${customer.id}`}>
      {menuItems.map(
        (item: any, index: number) =>
          item.visible && (
            <button
              key={index}
              data-testid={`menu-item-${index}-${customer.id}`}
              onClick={item.onClick}
              disabled={item.disabled}
            >
              {item.label}
            </button>
          )
      )}
    </div>
  ),
}))

// Mock localStorage
const localStorageMock = (() => {
  let store: Record<string, string> = {}
  return {
    getItem: (key: string) => store[key] || null,
    setItem: (key: string, value: string) => {
      store[key] = value.toString()
    },
    removeItem: (key: string) => {
      delete store[key]
    },
    clear: () => {
      store = {}
    },
  }
})()
Object.defineProperty(window, 'localStorage', { value: localStorageMock })

describe('List Component', () => {
  // Use the mock customers from our utility
  const testCustomers = generateMockCustomers(4)

  // Generate mock approval requests for the first customer
  const testApprovalRequests = generateMockCustomerApprovals([
    testCustomers[0].id!,
  ])

  // Mock dispatch function
  const mockDispatch = vi.fn()

  beforeEach(() => {
    vi.clearAllMocks()

    // Reset router mocks
    Object.keys(customRouterMock).forEach((key) => {
      if (
        typeof customRouterMock[key as keyof typeof customRouterMock] ===
        'function'
      ) {
        ;(
          customRouterMock[key as keyof typeof customRouterMock] as any
        ).mockClear()
      }
    })

    // Setup mock useAppSelector
    vi.mocked(useAppSelector).mockImplementation((selector) => {
      return selector({
        customers: {
          customersResponse: {
            data: testCustomers,
            pageNumber: 1,
            totalNumberOfPages: 3,
          },
          isCustomersLoading: false,
          isCustomersSuccess: true,
          searchValue: '',
        },
        notifications: {},
        approvalRequests: {
          customersWithPendingApprovals: testApprovalRequests,
        },
        loans: {
          bankBranches: [],
        },
      } as any)
    })

    // Setup mock useAppDispatch
    vi.mocked(useAppDispatch).mockReturnValue(mockDispatch)

    // Clear localStorage
    localStorageMock.clear()
  })

  it('renders the list component with customers', async () => {
    render(<List />)
    // Check if the component is rendered
    expect(screen.getByTestId('customer-list')).toBeInTheDocument()
    // Check if page header is rendered
    expect(screen.getByTestId('page-header')).toBeInTheDocument()

    // Check if customer rows are rendered
    expect(
      screen.getByText(
        `${testCustomers[0].firstName} ${testCustomers[0].lastName}`
      )
    ).toBeInTheDocument()
    expect(
      screen.getByText(
        `${testCustomers[1].firstName} ${testCustomers[1].lastName}`
      )
    ).toBeInTheDocument()
    expect(
      screen.getByText(
        `${testCustomers[2].firstName} ${testCustomers[2].lastName}`
      )
    ).toBeInTheDocument()

    // Check if pagination is rendered
    expect(screen.getByTestId('custom-pagination')).toBeInTheDocument()
  })

  it('renders loading skeleton when loading', async () => {
    // Override the mock for this test
    vi.mocked(useAppSelector).mockImplementation((selector) => {
      return selector({
        customers: {
          customersResponse: {
            data: [],
            pageNumber: 1,
            totalNumberOfPages: 0,
          },
          isCustomersLoading: true,
          isCustomersSuccess: false,
        },
        approvalRequests: {
          customersWithPendingApprovals: [],
        },
      } as any)
    })

    render(<List />)

    expect(screen.getByTestId('custom-skeleton')).toBeInTheDocument()
  })

  it('renders no customer found when no customers', async () => {
    // Override the mock for this test
    vi.mocked(useAppSelector).mockImplementation((selector) => {
      return selector({
        customers: {
          customersResponse: {
            data: [],
            pageNumber: 1,
            totalNumberOfPages: 0,
          },
          isCustomersLoading: false,
          isCustomersSuccess: true,
        },
        approvalRequests: {
          customersWithPendingApprovals: [],
        },
      } as any)
    })

    render(<List />)

    expect(screen.getByTestId('no-customer-found')).toBeInTheDocument()
  })

  it('handles row click to navigate to customer details', async () => {
    render(<List />)

    const customerRow = screen
      .getByText(`${testCustomers[0].firstName} ${testCustomers[0].lastName}`)
      .closest('td')

    await act(async () => {
      fireEvent.click(customerRow!)
    })

    expect(getCustomerProfileById).toHaveBeenCalledWith(
      testCustomers[0].id,
      expect.anything()
    )
    expect(customRouterMock.push).toHaveBeenCalledWith('/customers/customer')
  })

  it('handles pagination', async () => {
    render(<List />)

    const nextPageButton = screen.getByTestId('next-page-button')

    await act(async () => {
      fireEvent.click(nextPageButton)
    })

    expect(getAllCustomers).toHaveBeenCalledWith(
      expect.objectContaining({ page: 2 }),
      expect.anything()
    )
  })

  it('displays correct status chips', async () => {
    render(<List />)

    // Check active status
    expect(screen.getAllByTestId('status-chip-active').length).toBeGreaterThan(
      0
    )

    // Check inactive status
    expect(screen.getByTestId('status-chip-inactive')).toBeInTheDocument()

    // Check one time pin status
    expect(screen.getByTestId('status-chip-one-time-pin')).toBeInTheDocument()

    // Check not set status
    expect(screen.getByTestId('status-chip-not_set')).toBeInTheDocument()
  })

  it('handles date filter changes', async () => {
    render(<List />)

    const setDateFromButton = screen.getByTestId('set-date-from')
    const setDateToButton = screen.getByTestId('set-date-to')

    await act(async () => {
      fireEvent.click(setDateFromButton)
      fireEvent.click(setDateToButton)
    })

    // Trigger pagination to check if dates are passed
    const nextPageButton = screen.getByTestId('next-page-button')

    await act(async () => {
      fireEvent.click(nextPageButton)
    })

    expect(getAllCustomers).toHaveBeenCalledWith(
      expect.objectContaining({
        dateCreatedFrom: '2023-01-01',
        dateCreatedTo: '2023-12-31',
      }),
      expect.anything()
    )
  })

  it('handles "See more Details" menu item click', async () => {
    render(<List />)

    const moreDetailsButton = screen.getByTestId(
      `menu-item-0-${testCustomers[0].id}`
    )

    await act(async () => {
      fireEvent.click(moreDetailsButton)
    })

    expect(localStorage.getItem('customerId')).toBe(testCustomers[0].id)
    expect(getCustomerProfileById).toHaveBeenCalledWith(
      testCustomers[0].id,
      expect.anything()
    )
    expect(customRouterMock.push).toHaveBeenCalledWith('/customers/customer')
  })

  it('handles "See pending requests" menu item click when available', async () => {
    render(<List />)

    // Find the "See pending requests" button for customer1 (which has pending requests)
    const pendingRequestsButtons = screen.getAllByText('See pending requests')

    await act(async () => {
      fireEvent.click(pendingRequestsButtons[0])
    })

    expect(fetchPendingSingleCustomerApprovals).toHaveBeenCalledWith(
      expect.anything(),
      testCustomers[0].id,
      expect.anything(),
      testApprovalRequests
    )
    expect(getCustomerProfileById).toHaveBeenCalledWith(
      testCustomers[0].id,
      expect.anything()
    )
    expect(customRouterMock.push).toHaveBeenCalledWith('/customers/customer')
  })

  it('clears localStorage and resets approval state on mount', async () => {
    // Set some values in localStorage
    localStorage.setItem('tab', 'someTab')
    localStorage.setItem('customerId', 'someCustomerId')

    render(<List />)

    // Check if localStorage items were removed
    expect(localStorage.getItem('tab')).toBeNull()
    expect(localStorage.getItem('customerId')).toBeNull()

    // Check if approval state was reset
    expect(setCustomerApprovalBarOpen).toHaveBeenCalledWith(false)
    expect(setPendingSingleCustomerApprovals).toHaveBeenCalledWith([])
  })

  it('fetches pending approvals for customers on load', async () => {
    // Mock fetchPendingSingleCustomerApprovals to return some approvals
    vi.mocked(fetchPendingSingleCustomerApprovals).mockResolvedValue([
      { id: 'newRequest', type: 'ACCOUNT_ACTIVATION' },
    ])

    // Create a state with customers but no pending approvals for customer2 and customer3
    vi.mocked(useAppSelector).mockImplementation((selector) => {
      return selector({
        customers: {
          customersResponse: {
            data: testCustomers,
            pageNumber: 1,
            totalNumberOfPages: 3,
          },
          isCustomersLoading: false,
          isCustomersSuccess: true,
        },
        approvalRequests: {
          customersWithPendingApprovals: testApprovalRequests,
        },
      } as any)
    })

    render(<List />)

    // Wait for useEffect to run
    await waitFor(() => {
      // Check if fetchPendingSingleCustomerApprovals was called for customer2 and customer3
      expect(fetchPendingSingleCustomerApprovals).toHaveBeenCalledWith(
        expect.anything(),
        testCustomers[1].id
      )
      expect(fetchPendingSingleCustomerApprovals).toHaveBeenCalledWith(
        expect.anything(),
        testCustomers[2].id
      )

      // Check if setCustomersWithPendingApprovals was called with updated approvals
      expect(setCustomersWithPendingApprovals).toHaveBeenCalled()
    })
  })

  it('displays fallback values for missing customer data', async () => {
    // Override the mock for this test to include the incomplete customer
    vi.mocked(useAppSelector).mockImplementation((selector) => {
      return selector({
        customers: {
          customersResponse: {
            data: [mockCustomers.incomplete], // Use the incomplete customer from our mock file
            pageNumber: 1,
            totalNumberOfPages: 1,
          },
          isCustomersLoading: false,
          isCustomersSuccess: true,
        },
        approvalRequests: {
          customersWithPendingApprovals: [],
        },
      } as any)
    })

    render(<List />)

    // Check for the customer name
    expect(screen.getByText('Incomplete Data')).toBeInTheDocument()

    // Check for fallback values for missing data
    expect(screen.getByText('No-email')).toBeInTheDocument()
    expect(screen.getByText('No-ID Number')).toBeInTheDocument()

    // Check for the Not Set pin status
    const notSetChips = screen.getAllByText(/Not_set/i)
    expect(notSetChips.length).toBeGreaterThan(0)

    // Verify that the customer is displayed with the correct data
    const row = screen.getByText('Incomplete Data').closest('tr')
    expect(row).toBeInTheDocument()

    // Check that the row contains the fallback values
    expect(row).toHaveTextContent('No-email')
    expect(row).toHaveTextContent('No-ID Number')
  })

  it('formats customer names correctly', async () => {
    // Create specific test customers with the name formats we want to test
    const customersWithFormattedNames = [
      // Customer with first and last name only
      generateMockCustomer(1, {
        id: 'customer-name-format-1',
        firstName: 'John',
        lastName: 'Doe',
        otherNames: '',
      }),
      // Customer with otherNames included
      generateMockCustomer(3, {
        id: 'customer-name-format-2',
        firstName: 'Jane',
        lastName: 'Smith',
        otherNames: 'Middle',
      }),
      // Customer with mixed case names to test sentence case formatting
      generateMockCustomer(5, {
        id: 'customer-name-format-3',
        firstName: 'rOBERT',
        lastName: 'JOHNSON',
        otherNames: '',
      }),
    ]

    // Override the mock for this test to use our specific customers
    vi.mocked(useAppSelector).mockImplementation((selector) => {
      return selector({
        customers: {
          customersResponse: {
            data: customersWithFormattedNames,
            pageNumber: 1,
            totalNumberOfPages: 1,
          },
          isCustomersLoading: false,
          isCustomersSuccess: true,
        },
        approvalRequests: {
          customersWithPendingApprovals: [],
        },
      } as any)
    })

    render(<List />)

    // Check if names are formatted correctly
    expect(screen.getByText('John Doe')).toBeInTheDocument()

    // Check if otherNames is included when available
    const janeRow = screen.getByText(/Jane.*Smith/)
    expect(janeRow).toBeInTheDocument()
    expect(janeRow.textContent).toContain('Middle')

    // Check if mixed case names are formatted in sentence case
    // This assumes the List component formats names in sentence case
    expect(screen.getByText('R obert Johnson')).toBeInTheDocument()
  })

  it('formats timestamps correctly', async () => {
    render(<List />)

    // Check if formatTimestamp was called for each customer
    expect(formatTimestamp).toHaveBeenCalledWith(testCustomers[0].dateCreated)
    expect(formatTimestamp).toHaveBeenCalledWith(testCustomers[1].dateCreated)
    expect(formatTimestamp).toHaveBeenCalledWith(testCustomers[2].dateCreated)
  })

  it.skip('handles empty customersWithPendingApprovals', async () => {
    // Override the mock for this test
    vi.mocked(useAppSelector).mockImplementation((selector) => {
      return selector({
        customers: {
          customersResponse: {
            data: mockCustomers,
            pageNumber: 1,
            totalNumberOfPages: 3,
          },
          isCustomersLoading: false,
          isCustomersSuccess: true,
        },
        approvalRequests: {
          customersWithPendingApprovals: null, // Test with null
        },
      } as any)
    })

    // This should not throw an error
    render(<List />)

    // The component should render without errors
    expect(
      screen.getByText(
        `${testCustomers[0].firstName} ${testCustomers[0].lastName}`
      )
    ).toBeInTheDocument()
  })

  it('handles different pin status values correctly', async () => {
    render(<List />)

    // Check for "One Time Pin" status (from One_Time_PIN)
    expect(screen.getByTestId('status-chip-one-time-pin')).toBeInTheDocument()

    // Check for "ACTIVE" status (from Active_PIN)
    expect(screen.getAllByTestId('status-chip-active').length).toBeGreaterThan(
      0
    )

    // Check for "Not Set" status (from null)
    expect(screen.getByTestId('status-chip-not_set')).toBeInTheDocument()
  })
})
