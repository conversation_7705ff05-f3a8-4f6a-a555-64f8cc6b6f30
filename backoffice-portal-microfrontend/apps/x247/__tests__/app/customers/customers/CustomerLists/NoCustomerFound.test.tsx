import { describe, it, expect, vi, beforeEach } from 'vitest'
import { render, fireEvent, screen } from '../../test-utils'
import NoCustomerFound from '@/app/customers/customers/CustomerLists/NoCustomerFound'
import { getAllCustomers } from '@/store/actions'

// Mock the dependencies
vi.mock('@/store/actions', () => ({
  getAllCustomers: vi.fn().mockResolvedValue({}),
}))

vi.mock('@/app/customers/customers/Create', () => ({
  CreateCustomerDialog: () => (
    <button data-testid="create-customer-button">Add New Customer</button>
  ),
}))
vi.mock('@/store', () => ({
  useAppSelector: vi.fn(),
  useAppDispatch: vi.fn(),
}))
// Mock the MUI components
vi.mock('@mui/material', async () => {
  const actual = await vi.importActual('@mui/material')
  return {
    ...actual,
    Button: ({ children, onClick, variant }: any) => (
      <button
        data-testid={`button-${children?.toString().toLowerCase().replace(/\s+/g, '-')}`}
        onClick={onClick}
        data-variant={variant}
      >
        {children}
      </button>
    ),
    Typography: ({ children, variant, sx }: any) => (
      <div data-testid={`typography-${variant}`} style={sx}>
        {children}
      </div>
    ),
    Stack: ({ children, sx }: any) => (
      <div data-testid="stack" style={sx}>
        {children}
      </div>
    ),
    Box: ({ children, sx }: any) => (
      <div data-testid="box" style={sx}>
        {children}
      </div>
    ),
  }
})

// Mock Next.js Image component
vi.mock('next/image', () => ({
  default: ({ src, alt, width, height }: any) => (
    <img
      src={src || '/placeholder.svg'}
      alt={alt}
      width={width}
      height={height}
      data-testid="next-image"
    />
  ),
}))

// Mock dispatch function
const mockDispatch = vi.fn()

// Mock different search states
const noSearchState = {
  customers: {
    search: {
      searchValue: '',
      searchBy: [],
    },
    selectedFilters: [],
  },
}

const searchByNameState = {
  customers: {
    search: {
      searchValue: 'John Doe',
      searchBy: ['name'],
    },
    selectedFilters: [],
  },
}

const searchWithFiltersState = {
  customers: {
    search: {
      searchValue: 'John Doe',
      searchBy: ['name'],
    },
    selectedFilters: ['status:active', 'country:USA'],
  },
}

const filtersOnlyState = {
  customers: {
    search: {
      searchValue: '',
      searchBy: [],
    },
    selectedFilters: ['status:active', 'country:USA'],
  },
}
import { useAppSelector, useAppDispatch } from '@/store'
describe('NoCustomerFound Component', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('renders the component with no search criteria', () => {
    // Mock useAppSelector to return no search state
    vi.mocked(useAppSelector).mockImplementation((selector) =>
      selector(noSearchState as any)
    )

    render(<NoCustomerFound />)

    // Check that the component renders
    expect(screen.getByTestId('typography-h6')).toHaveTextContent(
      'No customers found'
    )

    // Check that the correct message is displayed
    const subtitle = screen.getByTestId('typography-subtitle3')
    expect(subtitle).toHaveTextContent(
      'Your filter criteria did not match any customers'
    )

    // Check that the image is rendered
    expect(screen.getByTestId('next-image')).toBeInTheDocument()
    expect(screen.getByTestId('next-image')).toHaveAttribute(
      'src',
      '/dashboard/icons/search-lg-2-x.svg'
    )

    // Check that the buttons are rendered
    expect(screen.getByTestId('button-clear-search')).toBeInTheDocument()
    expect(screen.getByTestId('create-customer-button')).toBeInTheDocument()
  })

  it('renders the component with search by name', () => {
    // Mock useAppSelector to return search by name state
    vi.mocked(useAppSelector).mockImplementation((selector) =>
      selector(searchByNameState as any)
    )

    render(<NoCustomerFound />)

    // Check that the correct message is displayed
    const subtitle = screen.getByTestId('typography-subtitle3')
    expect(subtitle).toHaveTextContent(
      'Your search by Name for "John Doe" did not match any customers'
    )
  })

  it.skip('renders the component with search and filters', () => {
    // Mock useAppSelector to return search with filters state
    vi.mocked(useAppSelector).mockImplementation((selector) =>
      selector(searchWithFiltersState as any)
    )

    render(<NoCustomerFound />)

    // Check that the correct message is displayed
    const subtitle = screen.getByTestId('typography-subtitle3')
    expect(subtitle).toHaveTextContent(
      'Your search by name for "John Doe" and filter criteria did not match any customers'
    )
  })

  it('renders the component with filters only', () => {
    // Mock useAppSelector to return filters only state
    vi.mocked(useAppSelector).mockImplementation((selector) =>
      selector(filtersOnlyState as any)
    )

    render(<NoCustomerFound />)

    // Check that the correct message is displayed
    const subtitle = screen.getByTestId('typography-subtitle3')
    expect(subtitle).toHaveTextContent(
      'Your filter criteria did not match any customers'
    )
  })

  it('calls getAllCustomers when clear search button is clicked', async () => {
    // Mock useAppSelector to return search by name state
    vi.mocked(useAppSelector).mockImplementation((selector) =>
      selector(searchByNameState as any)
    )

    // Mock useAppDispatch
    vi.mocked(useAppDispatch).mockReturnValue(mockDispatch)

    render(<NoCustomerFound />)

    // Click the clear search button
    fireEvent.click(screen.getByTestId('button-clear-search'))

    // Check that getAllCustomers was called with correct parameters
    expect(getAllCustomers).toHaveBeenCalledWith(
      {
        page: 1,
        size: 10,
      },
      mockDispatch
    )
  })

  it('renders the CreateCustomerDialog component', () => {
    // Mock useAppSelector to return no search state
    vi.mocked(useAppSelector).mockImplementation((selector) =>
      selector(noSearchState as any)
    )

    render(<NoCustomerFound />)

    // Check that the CreateCustomerDialog component is rendered
    expect(screen.getByTestId('create-customer-button')).toBeInTheDocument()
  })

  it.skip('handles sentence case conversion correctly', () => {
    // Mock useAppSelector to return search with a field that needs sentence case
    vi.mocked(useAppSelector).mockImplementation((selector) =>
      selector({
        customers: {
          search: {
            searchValue: '<EMAIL>',
            searchBy: ['emailAddress'],
          },
          selectedFilters: [],
        },
      } as any)
    )

    render(<NoCustomerFound />)

    // Check that the field name is converted to sentence case
    const subtitle = screen.getByTestId('typography-subtitle3')
    expect(subtitle).toHaveTextContent(
      'Your search by Email Address for "<EMAIL>" did not match any customers'
    )
  })

  it('renders with the correct styling', () => {
    // Mock useAppSelector to return no search state
    vi.mocked(useAppSelector).mockImplementation((selector) =>
      selector(noSearchState as any)
    )

    render(<NoCustomerFound />)

    // Check that the main Stack has the correct styling
    const stacks = screen.getAllByTestId('stack')
    expect(stacks[0]).toHaveStyle({
      height: '100%',
      width: '100%',
      justifyContent: 'center',
      alignItems: 'center',
    })

    // Check that the background image is set correctly
    expect(stacks[1]).toHaveStyle({
      backgroundImage: 'url(/dashboard/background-pattern.svg)',
    })
  })

  it('handles errors when clearing search', async () => {
    // Mock console.error to prevent test output pollution
    const consoleErrorSpy = vi
      .spyOn(console, 'error')
      .mockImplementation(() => {})

    // Mock getAllCustomers to throw an error
    vi.mocked(getAllCustomers).mockRejectedValueOnce(
      new Error('Failed to get customers')
    )

    // Mock useAppSelector to return search by name state
    vi.mocked(useAppSelector).mockImplementation((selector) =>
      selector(searchByNameState as any)
    )

    // Mock useAppDispatch
    vi.mocked(useAppDispatch).mockReturnValue(mockDispatch)

    render(<NoCustomerFound />)

    // Click the clear search button
    fireEvent.click(screen.getByTestId('button-clear-search'))

    // Check that getAllCustomers was called
    expect(getAllCustomers).toHaveBeenCalled()

    // Wait for the promise to reject
    await new Promise(process.nextTick)

    // Restore console.error
    consoleErrorSpy.mockRestore()
  })
})
