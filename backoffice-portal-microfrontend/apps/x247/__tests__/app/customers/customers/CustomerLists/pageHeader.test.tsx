import { describe, it, expect, vi, beforeEach } from 'vitest'
import { fireEvent, screen, waitFor, render } from '../../test-utils'
import PageHeader, {
  customerSearchByItems,
} from '@/app/customers/customers/CustomerLists/pageHeader'
import { getAllCustomers } from '@/store/actions'
import { setCustomerSearch, setSelectedFilters } from '@/store/reducers'

// Mock the dependencies
vi.mock('@/store/actions', () => ({
  getAllCustomers: vi.fn().mockResolvedValue({}),
}))

vi.mock('@/store/reducers', () => ({
  setCustomerSearch: vi.fn(),
  setSelectedFilters: vi.fn(),
}))

vi.mock('@/store', () => ({
  useAppSelector: vi.fn(),
  useAppDispatch: vi.fn(),
}))
vi.mock('@/app/customers/customers/Create', () => ({
  CreateCustomerDialog: () => (
    <button data-testid="create-customer-button">Add New Customer</button>
  ),
}))

// Mock the MUI components
vi.mock('@mui/material', async () => {
  const actual = await vi.importActual('@mui/material')
  return {
    ...actual,
    IconButton: ({ children, onClick, sx }: any) => (
      <button data-testid="filter-button" onClick={onClick}>
        {children}
      </button>
    ),
    Typography: ({ children }: any) => (
      <span data-testid="typography">{children}</span>
    ),
    Stack: ({ children, sx }: any) => <div data-testid="stack">{children}</div>,
    Box: ({ children, sx }: any) => <div data-testid="box">{children}</div>,
    FormControl: ({ children, sx }: any) => (
      <div data-testid="form-control">{children}</div>
    ),
    InputLabel: ({ children, id }: any) => (
      <label data-testid="input-label" htmlFor={id}>
        {children}
      </label>
    ),
    Select: ({ children, value, onChange, label, labelId, id, size }: any) => (
      <select
        data-testid="select"
        value={value}
        onChange={onChange}
        aria-labelledby={labelId}
        id={id}
      >
        {children}
      </select>
    ),
    MenuItem: ({ children, value }: any) => (
      <option data-testid={`menu-item-${value}`} value={value}>
        {children}
      </option>
    ),
  }
})

// Mock the MUI icons
vi.mock('@mui/icons-material', () => ({
  FilterListRounded: () => <span data-testid="filter-icon">Filter Icon</span>,
}))

// Mock the custom UI components
vi.mock('@dtbx/ui/components/Input', () => ({
  CustomSearchByInput: ({
    searchByDropDownItems,
    onChange,
    value,
    onSearchBySelect,
    searchByValue,
    onKeyDown,
  }: any) => (
    <div data-testid="custom-search-input">
      <input
        data-testid="search-input"
        value={value}
        onChange={(e) => onChange(e.target.value)}
        onKeyDown={(e) => e.key === 'Enter' && onKeyDown()}
      />
      <select
        data-testid="search-by-dropdown"
        value={searchByValue.label}
        onChange={(e) => {
          const selected = searchByDropDownItems.find(
            (item: any) => item.label === e.target.value
          )
          onSearchBySelect(selected)
        }}
      >
        {searchByDropDownItems.map((item: any) => (
          <option key={item.label} value={item.label}>
            {item.label}
          </option>
        ))}
      </select>
    </div>
  ),
}))

vi.mock('@dtbx/ui/components/Transitions', () => ({
  GrowProvider: ({ children, in: isIn }: any) =>
    isIn ? <div data-testid="grow-provider">{children}</div> : null,
}))

vi.mock('@dtbx/ui/components/DropDownMenus', () => ({
  DateRangePicker: ({ buttonText, onApplyDateRange }: any) => (
    <div data-testid="date-range-picker">
      <button data-testid="date-range-button">{buttonText}</button>
      <button
        data-testid="apply-date-range"
        onClick={() =>
          onApplyDateRange({
            start: { format: () => '2023-01-01' },
            end: { format: () => '2023-12-31' },
          })
        }
      >
        Apply
      </button>
    </div>
  ),
}))
import { useAppDispatch, useAppSelector } from '@/store'

// Mock dispatch function
const mockDispatch = vi.fn()

// Mock setDateCreatedFrom and setDateCreatedTo functions
const mockSetDateCreatedFrom = vi.fn()
const mockSetDateCreatedTo = vi.fn()

describe('PageHeader Component', () => {
  beforeEach(() => {
    vi.clearAllMocks()

    // Mock useAppDispatch
    vi.mocked(useAppDispatch).mockReturnValue(mockDispatch)
  })

  it('renders the component with all elements', () => {
    render(
      <PageHeader
        setDateCreatedFrom={mockSetDateCreatedFrom}
        setDateCreatedTo={mockSetDateCreatedTo}
      />
    )

    // Check that the search input is rendered
    expect(screen.getByTestId('custom-search-input')).toBeInTheDocument()

    // Check that the filter button is rendered
    expect(screen.getByTestId('filter-button')).toBeInTheDocument()
    expect(screen.getByTestId('filter-icon')).toBeInTheDocument()
    expect(screen.getByText('Filter')).toBeInTheDocument()

    // Check that the create customer button is rendered
    expect(screen.getByTestId('create-customer-button')).toBeInTheDocument()

    // Filter bar should not be visible initially
    expect(screen.queryByTestId('grow-provider')).not.toBeInTheDocument()
  })

  it('toggles the filter bar when filter button is clicked', () => {
    render(
      <PageHeader
        setDateCreatedFrom={mockSetDateCreatedFrom}
        setDateCreatedTo={mockSetDateCreatedTo}
      />
    )

    // Filter bar should not be visible initially
    expect(screen.queryByTestId('grow-provider')).not.toBeInTheDocument()

    // Click the filter button
    fireEvent.click(screen.getByTestId('filter-button'))

    // Filter bar should now be visible
    expect(screen.getByTestId('grow-provider')).toBeInTheDocument()
    expect(screen.getByTestId('date-range-picker')).toBeInTheDocument()
    expect(screen.getByTestId('form-control')).toBeInTheDocument()

    // Click the filter button again
    fireEvent.click(screen.getByTestId('filter-button'))

    // Filter bar should be hidden again
    expect(screen.queryByTestId('grow-provider')).not.toBeInTheDocument()
  })

  it('handles search input changes', () => {
    render(
      <PageHeader
        setDateCreatedFrom={mockSetDateCreatedFrom}
        setDateCreatedTo={mockSetDateCreatedTo}
      />
    )

    const searchInput = screen.getByTestId('search-input')

    // Enter search term
    fireEvent.change(searchInput, { target: { value: 'John Doe' } })

    // Check that setCustomerSearch was called with correct parameters
    expect(setCustomerSearch).toHaveBeenCalledWith({
      searchBy: ['firstName'], // Default search by
      searchValue: 'John Doe',
    })

    // Check that getAllCustomers was called with correct parameters
    expect(getAllCustomers).toHaveBeenCalledWith(
      {
        page: 1,
        size: 7,
        firstName: 'John Doe',
      },
      mockDispatch
    )
  })

  it('handles search by dropdown changes', () => {
    render(
      <PageHeader
        setDateCreatedFrom={mockSetDateCreatedFrom}
        setDateCreatedTo={mockSetDateCreatedTo}
      />
    )

    const searchByDropdown = screen.getByTestId('search-by-dropdown')

    // Change search by to Email
    fireEvent.change(searchByDropdown, { target: { value: 'Email' } })

    // Enter search term
    const searchInput = screen.getByTestId('search-input')
    fireEvent.change(searchInput, { target: { value: '<EMAIL>' } })

    // Check that getAllCustomers was called with correct parameters
    expect(getAllCustomers).toHaveBeenCalledWith(
      {
        page: 1,
        size: 7,
        email: '<EMAIL>',
      },
      mockDispatch
    )
  })

  it('handles Enter key press in search input', () => {
    render(
      <PageHeader
        setDateCreatedFrom={mockSetDateCreatedFrom}
        setDateCreatedTo={mockSetDateCreatedTo}
      />
    )

    const searchInput = screen.getByTestId('search-input')

    // Enter search term
    fireEvent.change(searchInput, { target: { value: 'John Doe' } })

    // Clear mocks to check only the Enter key press effect
    vi.clearAllMocks()

    // Press Enter key
    fireEvent.keyDown(searchInput, { key: 'Enter' })

    // Check that getAllCustomers was called with correct parameters
    expect(getAllCustomers).toHaveBeenCalledWith(
      {
        page: 1,
        size: 7,
        firstName: 'John Doe',
      },
      mockDispatch
    )
  })

  it('handles date range selection', () => {
    render(
      <PageHeader
        setDateCreatedFrom={mockSetDateCreatedFrom}
        setDateCreatedTo={mockSetDateCreatedTo}
      />
    )

    // Open filter bar
    fireEvent.click(screen.getByTestId('filter-button'))

    // Apply date range
    fireEvent.click(screen.getByTestId('apply-date-range'))

    // Check that setDateCreatedFrom and setDateCreatedTo were called with correct parameters
    expect(mockSetDateCreatedFrom).toHaveBeenCalledWith('2023-01-01')
    expect(mockSetDateCreatedTo).toHaveBeenCalledWith('2023-12-31')

    // Check that getAllCustomers was called with correct parameters
    expect(getAllCustomers).toHaveBeenCalledWith(
      {
        page: 1,
        size: 10,
        dateCreatedFrom: '2023-01-01',
        dateCreatedTo: '2023-12-31',
      },
      mockDispatch
    )

    // Check that setSelectedFilters was called with correct parameters
    expect(setSelectedFilters).toHaveBeenCalledWith([
      {
        filterName: 'Date Created',
        options: [
          {
            key: 'start',
            label: 'start',
            value: '2023-01-01',
          },
          {
            key: 'end',
            label: 'end',
            value: '2023-12-31',
          },
        ],
        type: 'date',
      },
    ])
  })

  it('handles customer status selection', async () => {
    render(
      <PageHeader
        setDateCreatedFrom={mockSetDateCreatedFrom}
        setDateCreatedTo={mockSetDateCreatedTo}
      />
    )

    // Open filter bar
    fireEvent.click(screen.getByTestId('filter-button'))

    // Select status dropdown should be visible
    const selectElement = screen.getByTestId('select')
    expect(selectElement).toBeInTheDocument()

    // Change status to Inactive
    fireEvent.change(selectElement, { target: { value: 'Inactive' } })

    // Check that getAllCustomers was called with correct parameters
    expect(getAllCustomers).toHaveBeenCalledWith(
      {
        isBlocked: 'true',
        page: 1,
        size: 10,
      },
      mockDispatch
    )

    // Change status to Active
    fireEvent.change(selectElement, { target: { value: 'Active' } })

    // Check that getAllCustomers was called with correct parameters
    expect(getAllCustomers).toHaveBeenCalledWith(
      {
        isBlocked: 'false',
        page: 1,
        size: 10,
      },
      mockDispatch
    )
  })

  it('exports customerSearchByItems correctly', () => {
    // Check that customerSearchByItems is exported and has the correct structure
    expect(customerSearchByItems).toBeDefined()
    expect(Array.isArray(customerSearchByItems)).toBe(true)
    expect(customerSearchByItems.length).toBeGreaterThan(0)

    // Check the structure of the first item
    const firstItem = customerSearchByItems[0]
    expect(firstItem).toHaveProperty('label')
    expect(firstItem).toHaveProperty('value')
    expect(Array.isArray(firstItem.value)).toBe(true)
  })

  it('handles errors when fetching customers', async () => {
    // Mock console.error to prevent test output pollution
    const consoleErrorSpy = vi
      .spyOn(console, 'error')
      .mockImplementation(() => {})

    // Mock getAllCustomers to throw an error
    vi.mocked(getAllCustomers).mockRejectedValueOnce(
      new Error('Failed to get customers')
    )

    render(
      <PageHeader
        setDateCreatedFrom={mockSetDateCreatedFrom}
        setDateCreatedTo={mockSetDateCreatedTo}
      />
    )

    const searchInput = screen.getByTestId('search-input')

    // Enter search term
    fireEvent.change(searchInput, { target: { value: 'John Doe' } })

    // Wait for the promise to reject
    await new Promise(process.nextTick)

    // Restore console.error
    consoleErrorSpy.mockRestore()
  })

  it('combines search and filters correctly', async () => {
    render(
      <PageHeader
        setDateCreatedFrom={mockSetDateCreatedFrom}
        setDateCreatedTo={mockSetDateCreatedTo}
      />
    )

    // Enter search term
    const searchInput = screen.getByTestId('search-input')
    fireEvent.change(searchInput, { target: { value: 'John Doe' } })

    // Open filter bar
    fireEvent.click(screen.getByTestId('filter-button'))

    // Apply date range
    fireEvent.click(screen.getByTestId('apply-date-range'))

    // Select status
    const selectElement = screen.getByTestId('select')
    fireEvent.change(selectElement, { target: { value: 'Inactive' } })

    // Check that all actions were dispatched
    expect(setCustomerSearch).toHaveBeenCalled()
    expect(setSelectedFilters).toHaveBeenCalled()
    expect(getAllCustomers).toHaveBeenCalledTimes(3) // Once for search, once for date, once for status
  })
})
