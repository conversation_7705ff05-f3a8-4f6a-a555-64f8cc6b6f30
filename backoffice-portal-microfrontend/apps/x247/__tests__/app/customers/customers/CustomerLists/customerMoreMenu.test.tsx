import { describe, it, expect, vi, beforeEach } from 'vitest'
import { fireEvent, waitFor, screen, render } from '../../test-utils'
import {
  CustomerMoreMenu,
  ActivateCustomer,
  DeleteCustomer,
} from '@/app/customers/customers/CustomerLists/customerMoreMenu'
import {
  activateCustomerProfile,
  deactivateCustomer,
  deleteCustomerProfile,
  fetchPendingSingleCustomerApprovals,
} from '@/store/actions'
import { mockCustomers } from '../../../stubs/customer-listing'
import { HasAccessToRights } from '@dtbx/store/utils'

// Mock the dependencies
vi.mock('@/store/actions', () => ({
  activateCustomerProfile: vi.fn(),
  deactivateCustomer: vi.fn(),
  deleteCustomerProfile: vi.fn(),
  fetchPendingSingleCustomerApprovals: vi.fn(),
}))

// Mock the MUI components
vi.mock('@mui/material', async () => {
  const actual = await vi.importActual('@mui/material')
  return {
    ...actual,
    Menu: ({ children, open, onClose, anchorEl }: any) =>
      open ? <div data-testid="mui-menu">{children}</div> : null,
    MenuItem: ({ children, onClick, disabled }: any) => (
      <button
        data-testid={`menu-item-${children?.toString().toLowerCase().replace(/\s+/g, '-')}`}
        onClick={onClick}
        disabled={disabled}
      >
        {children}
      </button>
    ),
    Button: ({ children, onClick, variant, sx, endIcon, disabled }: any) => (
      <button
        data-testid={`button-${children?.toString().toLowerCase().replace(/\s+/g, '-')}`}
        onClick={onClick}
        disabled={disabled}
      >
        {children}
        {endIcon && <span data-testid="button-icon">{endIcon}</span>}
      </button>
    ),
  }
})

// Mock the Dialog component
vi.mock('@dtbx/ui/components/Overlay', () => ({
  Dialog: ({
    open,
    setOpen,
    title,
    buttonText,
    descriptionText,
    onClick,
    reasons,
  }: any) =>
    open ? (
      <div data-testid="dialog-component">
        <div data-testid="dialog-title">{title}</div>
        <div data-testid="dialog-description">{descriptionText}</div>
        {reasons.map((reason: string, index: number) => (
          <div
            key={index}
            data-testid={`dialog-reason-${reason.toLowerCase().replace(/\s+/g, '-')}`}
          >
            <input
              type="checkbox"
              data-testid={`reason-checkbox-${reason.toLowerCase().replace(/\s+/g, '-')}`}
            />
            {reason}
          </div>
        ))}
        <button
          data-testid="dialog-confirm-button"
          onClick={() => onClick(['Reason 1', 'Reason 2'])}
        >
          {buttonText}
        </button>
        <button
          data-testid="dialog-cancel-button"
          onClick={() => setOpen(false)}
        >
          Cancel
        </button>
      </div>
    ) : null,
}))

// Mock the store utils
vi.mock('@dtbx/store/utils', () => ({
  ACCESS_CONTROLS: {
    ACTIVATE_CUSTOMERS: ['ACTIVATE_CUSTOMERS'],
    DEACTIVATE_CUSTOMERS: ['DEACTIVATE_CUSTOMERS'],
    DELETE_CUSTOMERS: ['DELETE_CUSTOMERS'],
  },
  AccessControlWrapper: ({ children, rights }: any) => (
    <div data-testid={`access-control-${rights.join('-').toLowerCase()}`}>
      {children}
    </div>
  ),
  HasAccessToRights: vi.fn().mockImplementation((rights) => {
    // Mock to return true for SUPER_DELETE_CUSTOMERS and false for others
    return rights.includes('SUPER_DELETE_CUSTOMERS')
  }),
}))

// Mock dispatch function
const mockDispatch = vi.fn()

// Mock useAppSelector
vi.mock('@/store', () => ({
  useAppDispatch: () => mockDispatch,
  useAppSelector: vi.fn().mockImplementation((selector) =>
    selector({
      approvalRequests: {
        customersWithPendingApprovals: [],
      },
    })
  ),
}))

describe('CustomerMoreMenu Component', () => {
  const mockMenuItems = [
    {
      label: 'View Details',
      onClick: vi.fn(),
      disabled: false,
      visible: true,
    },
    {
      label: 'Edit Customer',
      onClick: vi.fn(),
      disabled: false,
      visible: true,
    },
    {
      label: 'Hidden Option',
      onClick: vi.fn(),
      disabled: false,
      visible: false,
    },
  ]

  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('renders the action button correctly', () => {
    render(
      <CustomerMoreMenu
        customer={mockCustomers.standard[0]}
        menuItems={mockMenuItems}
      />
    )

    const actionButton = screen.getByTestId('button-action')
    expect(actionButton).toBeInTheDocument()
    expect(actionButton).toHaveTextContent('Action')
  })

  it('opens the menu when action button is clicked', () => {
    render(
      <CustomerMoreMenu
        customer={mockCustomers.standard[0]}
        menuItems={mockMenuItems}
      />
    )

    // Menu should not be visible initially
    expect(screen.queryByTestId('mui-menu')).not.toBeInTheDocument()

    // Click the action button
    fireEvent.click(screen.getByTestId('button-action'))

    // Menu should now be visible
    expect(screen.getByTestId('mui-menu')).toBeInTheDocument()
  })

  it('renders only visible menu items', () => {
    render(
      <CustomerMoreMenu
        customer={mockCustomers.standard[0]}
        menuItems={mockMenuItems}
      />
    )

    // Click the action button to open the menu
    fireEvent.click(screen.getByTestId('button-action'))

    // Check that visible menu items are rendered
    expect(screen.getByTestId('menu-item-view-details')).toBeInTheDocument()
    expect(screen.getByTestId('menu-item-edit-customer')).toBeInTheDocument()

    // Check that hidden menu items are not rendered
    expect(
      screen.queryByTestId('menu-item-hidden-option')
    ).not.toBeInTheDocument()
  })

  it('calls the onClick handler when a menu item is clicked', () => {
    render(
      <CustomerMoreMenu
        customer={mockCustomers.standard[0]}
        menuItems={mockMenuItems}
      />
    )

    // Click the action button to open the menu
    fireEvent.click(screen.getByTestId('button-action'))

    // Click a menu item
    fireEvent.click(screen.getByTestId('menu-item-view-details'))

    // Check that the onClick handler was called
    expect(mockMenuItems[0].onClick).toHaveBeenCalledTimes(1)
  })
})

describe('ActivateCustomer Component', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('renders activate button for blocked customer', () => {
    render(<ActivateCustomer customer={mockCustomers.blocked} origin="view" />)

    // Check that the button is rendered with correct text
    const activateButton = screen.getByTestId('button-activate')
    expect(activateButton).toBeInTheDocument()
  })

  it('renders deactivate button for active customer', () => {
    // Use a non-blocked customer
    const activeCustomer = { ...mockCustomers.standard[0], isBlocked: false }

    render(<ActivateCustomer customer={activeCustomer} origin="view" />)

    // Check that the button is rendered with correct text
    const deactivateButton = screen.getByTestId('button-deactivate')
    expect(deactivateButton).toBeInTheDocument()
  })

  it('opens dialog when button is clicked', () => {
    render(<ActivateCustomer customer={mockCustomers.blocked} origin="view" />)

    // Dialog should not be visible initially
    expect(screen.queryByTestId('dialog-component')).not.toBeInTheDocument()

    // Click the activate button
    fireEvent.click(screen.getByTestId('button-activate'))

    // Dialog should now be visible
    expect(screen.getByTestId('dialog-component')).toBeInTheDocument()
    expect(screen.getByTestId('dialog-title')).toHaveTextContent(
      'Activate customer'
    )
  })

  it.skip('calls activateCustomerProfile when confirming activation', async () => {
    vi.mocked(activateCustomerProfile).mockResolvedValue(undefined)

    render(<ActivateCustomer customer={mockCustomers.blocked} origin="view" />)

    // Click the activate button to open dialog
    fireEvent.click(screen.getByTestId('button-activate'))

    // Click the confirm button
    fireEvent.click(screen.getByTestId('dialog-confirm-button'))

    // Check that activateCustomerProfile was called with correct parameters
    await waitFor(() => {
      expect(activateCustomerProfile).toHaveBeenCalledWith({
        profileId: mockCustomers.blocked.id,
        comments: 'Reason 1, Reason 2',
        dispatch: mockDispatch,
      })
    })

    // Check that fetchPendingSingleCustomerApprovals was called
    expect(fetchPendingSingleCustomerApprovals).toHaveBeenCalled()
  })

  it.skip('calls deactivateCustomer when confirming deactivation', async () => {
    vi.mocked(deactivateCustomer).mockResolvedValue(undefined)

    // Use a non-blocked customer
    const activeCustomer = { ...mockCustomers.standard[0], isBlocked: false }

    render(<ActivateCustomer customer={activeCustomer} origin="view" />)

    // Click the deactivate button to open dialog
    fireEvent.click(screen.getByTestId('button-deactivate'))

    // Click the confirm button
    fireEvent.click(screen.getByTestId('dialog-confirm-button'))

    // Check that deactivateCustomer was called with correct parameters
    await waitFor(() => {
      expect(deactivateCustomer).toHaveBeenCalledWith({
        profileID: activeCustomer.id,
        reason: 'Reason 1, Reason 2',
        dispatch: mockDispatch,
      })
    })

    // Check that fetchPendingSingleCustomerApprovals was called
    expect(fetchPendingSingleCustomerApprovals).toHaveBeenCalled()
  })

  it('renders as menu item when origin is not "view"', () => {
    render(<ActivateCustomer customer={mockCustomers.blocked} />)

    // Should render as a menu item
    expect(screen.getByTestId('menu-item-activate')).toBeInTheDocument()
  })
})

describe('DeleteCustomer Component', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('renders delete button when origin is "view"', () => {
    render(
      <DeleteCustomer customer={mockCustomers.standard[0]} origin="view" />
    )

    // Check that the button is rendered with correct text
    const deleteButton = screen.getByTestId('button-delete')
    expect(deleteButton).toBeInTheDocument()
  })

  it('opens dialog when button is clicked', () => {
    render(
      <DeleteCustomer customer={mockCustomers.standard[0]} origin="view" />
    )

    // Dialog should not be visible initially
    expect(screen.queryByTestId('dialog-component')).not.toBeInTheDocument()

    // Click the delete button
    fireEvent.click(screen.getByTestId('button-delete'))

    // Dialog should now be visible
    expect(screen.getByTestId('dialog-component')).toBeInTheDocument()
    expect(screen.getByTestId('dialog-title')).toHaveTextContent(
      'Delete Customer Profile'
    )
  })

  it.skip('calls deleteCustomerProfile with "super" type when user has super rights', async () => {
    vi.mocked(deleteCustomerProfile).mockResolvedValue(undefined)

    render(
      <DeleteCustomer customer={mockCustomers.standard[0]} origin="view" />
    )

    // Click the delete button to open dialog
    fireEvent.click(screen.getByTestId('button-delete'))

    // Click the confirm button
    fireEvent.click(screen.getByTestId('dialog-confirm-button'))

    // Check that deleteCustomerProfile was called with correct parameters
    await waitFor(() => {
      expect(deleteCustomerProfile).toHaveBeenCalledWith({
        profileID: mockCustomers.standard[0].id,
        comments: 'Reason 1, Reason 2',
        type: 'super',
        dispatch: mockDispatch,
      })
    })

    // Check that fetchPendingSingleCustomerApprovals was called
    expect(fetchPendingSingleCustomerApprovals).toHaveBeenCalled()
  })

  it.skip('calls deleteCustomerProfile with "make" type when user does not have super rights', async () => {
    // Mock HasAccessToRights to return false for this test
    vi.mocked(HasAccessToRights).mockReturnValueOnce(false)
    vi.mocked(deleteCustomerProfile).mockResolvedValue(undefined)

    render(
      <DeleteCustomer customer={mockCustomers.standard[0]} origin="view" />
    )

    // Click the delete button to open dialog
    fireEvent.click(screen.getByTestId('button-delete'))

    // Click the confirm button
    fireEvent.click(screen.getByTestId('dialog-confirm-button'))

    // Check that deleteCustomerProfile was called with correct parameters
    await waitFor(() => {
      expect(deleteCustomerProfile).toHaveBeenCalledWith({
        profileID: mockCustomers.standard[0].id,
        comments: 'Reason 1, Reason 2',
        type: 'make',
        dispatch: mockDispatch,
      })
    })
  })

  it('renders as menu item when origin is not "view"', () => {
    render(<DeleteCustomer customer={mockCustomers.standard[0]} />)

    // Should render as a menu item
    expect(screen.getByTestId('menu-item-delete')).toBeInTheDocument()
  })

  it('handles errors during delete operation', async () => {
    // Mock console.error to prevent test output pollution
    const consoleErrorSpy = vi
      .spyOn(console, 'error')
      .mockImplementation(() => {})

    // Mock deleteCustomerProfile to throw an error
    vi.mocked(deleteCustomerProfile).mockRejectedValue(
      new Error('Delete failed')
    )

    render(
      <DeleteCustomer customer={mockCustomers.standard[0]} origin="view" />
    )

    // Click the delete button to open dialog
    fireEvent.click(screen.getByTestId('button-delete'))

    // Click the confirm button
    fireEvent.click(screen.getByTestId('dialog-confirm-button'))

    // Check that error was logged
    await waitFor(() => {
      expect(consoleErrorSpy).toHaveBeenCalledWith(
        'Error Deleting customer profile:',
        expect.any(Error)
      )
    })

    // Check that fetchPendingSingleCustomerApprovals was still called
    expect(fetchPendingSingleCustomerApprovals).toHaveBeenCalled()

    // Restore console.error
    consoleErrorSpy.mockRestore()
  })
})
