import { describe, it, vi } from 'vitest'
import PageHeader from '@/app/customers/customers/Requests/pageHeader'
import { render } from '../../../../test-utils'

const mockSetSelectedFilters = vi.fn()
const mockSetDateRange = vi.fn()
const mockSetPage = vi.fn()
describe('PageHeader Customers Requests', () => {
  it('should render page header correctly', () => {
    render(
      <PageHeader
        page={1}
        setPage={mockSetPage}
        selectedFilters={{}}
        setSelectedFilters={mockSetSelectedFilters}
        dateRange={null}
        setDateRange={mockSetDateRange}
      />
    )
  })
})
