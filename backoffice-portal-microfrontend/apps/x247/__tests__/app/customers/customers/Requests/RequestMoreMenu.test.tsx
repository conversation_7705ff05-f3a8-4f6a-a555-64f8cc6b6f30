import { beforeEach, describe, it, vi } from 'vitest'
import { render } from '../../test-utils'
import { RequestMoreMenu } from '@/app/customers/customers/Requests/RequestMoreMenu'
import { generateMockApprovalRequest } from '../../../stubs/customer-listing'
import { useAppDispatch, useAppSelector } from '@/store'

vi.mock('@/store', () => ({
  useAppSelector: vi.fn(),
  useAppDispatch: vi.fn(),
}))
vi.mock('@dtbx/ui/hooks', () => ({
  useCustomRouter: vi.fn(),
}))
const approvalRequest = generateMockApprovalRequest(100, 'profile-101')
const mockDispatch = vi.fn()
const defaultState = {
  approvalRequests: {
    selectedApprovalRequest: approvalRequest,
  },
}
describe('Requests/RequestMoreMenu Customers Requests', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    vi.mocked(useAppDispatch).mockReturnValue(mockDispatch)

    // Set up default mock for useAppSelector
    vi.mocked(useAppSelector).mockImplementation((selector) =>
      selector(defaultState as any)
    )
  })
  it('should render requests more menu correctly', () => {
    render(<RequestMoreMenu request={approvalRequest} />)
  })
})
