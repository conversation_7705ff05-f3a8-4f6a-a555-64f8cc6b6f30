import { beforeEach, describe, it, vi } from 'vitest'
import { render } from '../../test-utils'
import Requests from '@/app/customers/customers/Requests/Requests'
import { useAppDispatch, useAppSelector } from '@/store'
import React from 'react'

vi.mock('@/store', () => ({
  useAppDispatch: vi.fn(),
  useAppSelector: vi.fn(),
}))
const mockDispatch = vi.fn()
const defaultState = {
  approvalRequests: {
    isLoadingRequests: false,
    approvalRequestResponse: {},
  },
}

vi.mock('@/app/customers/customers/Requests/pageHeader', () => ({
  default: ({
    setDateCreatedFrom,
    setDateCreatedTo,
    page,
    setPage,
    selectedFilters,
    setSelectedFilters,
  }: any) => (
    <div data-testid="page-header">
      <button
        data-testid="set-date-from"
        onClick={() => setDateCreatedFrom('2023-01-01')}
      >
        Set Date From
      </button>
      <button
        data-testid="set-date-to"
        onClick={() => setDateCreatedTo('2023-12-31')}
      >
        Set Date To
      </button>
    </div>
  ),
}))
describe('Requests/Requests Component', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    vi.mocked(useAppDispatch).mockReturnValue(mockDispatch)

    // Set up default mock for useAppSelector
    vi.mocked(useAppSelector).mockImplementation((selector) =>
      selector(defaultState as any)
    )
  })
  it('Should render requests listing page correctly', () => {
    render(<Requests />)
  })
})
