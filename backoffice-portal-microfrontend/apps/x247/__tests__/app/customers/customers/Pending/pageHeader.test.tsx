import { beforeEach, describe, it, vi } from 'vitest'
import PageHeader from '@/app/customers/customers/Pending/pageHeader'
import { render } from '../../test-utils'
import { useAppSelector, useAppDispatch } from '@/store'
vi.mock('@/store', () => ({
  useAppSelector: vi.fn(),
  useAppDispatch: vi.fn(),
}))
const defaultState = {
  customers: {
    search: { searchBy: ['name'], searchValue: '<PERSON>' },
    selectedFilters: [],
  },
  users: {
    searchUserValue: 'Pat',
  },
}
const mockDispatch = vi.fn()
const mockSetPage = vi.fn()
describe('Pending/PageHeader Component', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    vi.mocked(useAppDispatch).mockReturnValue(mockDispatch)

    // Set up default mock for useAppSelector
    vi.mocked(useAppSelector).mockImplementation((selector) =>
      selector(defaultState as any)
    )
  })
  it.skip('renders PageHeader page', () => {
    render(<PageHeader page={1} setPage={mockSetPage} />)
  })
})
