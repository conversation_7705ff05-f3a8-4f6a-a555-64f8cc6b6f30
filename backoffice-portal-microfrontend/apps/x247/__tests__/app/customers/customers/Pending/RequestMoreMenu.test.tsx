import { beforeEach, describe, it, vi } from 'vitest'
import { RequestMoreMenu } from '@/app/customers/customers/Pending/RequestMoreMenu'
import { render } from '../../test-utils'
import { useAppSelector, useAppDispatch } from '@/store'
import { generateMockApprovalRequest } from '../../../stubs/customer-listing'
vi.mock('@/store', () => ({
  useAppSelector: vi.fn(),
  useAppDispatch: vi.fn(),
}))
const approvalRequest = generateMockApprovalRequest(102, 'profile-102')

const mockApprovalRequestRouting = vi.fn()
const mockPush = vi.fn()

vi.mock('@/app/approval-requests/RequestRouting', () => ({
  ApprovalRequestRouting: vi.fn().mockImplementation(async () => {
    mockApprovalRequestRouting()
  }),
}))
vi.mock('next/navigation', async (importOriginal) => {
  const mod = await importOriginal<typeof import('next/navigation')>()
  return {
    ...mod,
    useRouter: vi.fn(() => ({
      push: mockPush,
      refresh: vi.fn(),
      prefetch: vi.fn(),
      replace: vi.fn(),
      back: vi.fn(),
      forward: vi.fn(),
    })),
  }
})
const defaultState = {
  customers: {
    search: { searchBy: ['name'], searchValue: 'Desmond Hart' },
    selectedFilters: [],
  },
  approvalRequests: {
    selectedApprovalRequest: approvalRequest,
  },
}
const mockDispatch = vi.fn()
const mockSetPage = vi.fn()
describe('Pending/RequestMoreMenu Component', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    vi.mocked(useAppDispatch).mockReturnValue(mockDispatch)

    // Set up default mock for useAppSelector
    vi.mocked(useAppSelector).mockImplementation((selector) =>
      selector(defaultState as any)
    )
  })
  it('renders RequestMoreMenu page', () => {
    render(<RequestMoreMenu request={approvalRequest} />)
  })
})
