import { beforeEach, describe, it, vi } from 'vitest'
import List from '@/app/customers/customers/Pending/CustomerList'
import { render } from '../../test-utils'
import { useAppDispatch, useAppSelector } from '@/store'
vi.mock('@/store', () => ({
  useAppDispatch: vi.fn(),
  useAppSelector: vi.fn(),
}))
const mockDispatch = vi.fn()
const defaultValue = {
  approvalRequests: {
    pendingCustomerApprovalRequestResponse: {},
    isLoadingRequests: false,
  },
}
describe('Pending/CustomerList', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    vi.mocked(useAppDispatch).mockResolvedValue(mockDispatch)
    vi.mocked(useAppSelector).mockImplementation((selector: any) =>
      selector(defaultValue)
    )
  })
  it.skip('renders Pending creation customers requests list', () => {
    render(<List />)
  })
})
