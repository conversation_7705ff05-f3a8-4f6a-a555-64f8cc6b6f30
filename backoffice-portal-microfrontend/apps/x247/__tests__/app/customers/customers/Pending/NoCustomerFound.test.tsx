import { beforeEach, describe, it, vi } from 'vitest'
import NoCustomerFound from '@/app/customers/customers/Pending/NoCustomerFound'
import { render } from '../../test-utils'
import { useAppSelector, useAppDispatch } from '@/store'
vi.mock('@/store', () => ({
  useAppSelector: vi.fn(),
  useAppDispatch: vi.fn(),
}))
const defaultState = {
  customers: {
    search: { searchBy: ['name'], searchValue: '<PERSON>' },
    selectedFilters: [],
  },
}
const mockDispatch = vi.fn()
describe('Pending/NoCustomerFound Component', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    vi.mocked(useAppDispatch).mockReturnValue(mockDispatch)

    // Set up default mock for useAppSelector
    vi.mocked(useAppSelector).mockImplementation((selector) =>
      selector(defaultState as any)
    )
  })
  it.skip('renders NoCustomerFound page', () => {
    render(<NoCustomerFound />)
  })
})
