import { describe, it, expect, vi, beforeEach } from 'vitest'
import { render, screen, fireEvent, waitFor, act } from '../../test-utils'
import { Accounts } from '@/app/customers/customers/Create/Accounts'
import { useAppSelector } from '@/store'
import type { FormikProps } from 'formik'
import type {
  ICustomerAccountDetails,
  ICustomerAccount,
} from '@/store/interfaces'

// Mock the dependencies
vi.mock('@/store', () => ({
  useAppSelector: vi.fn(),
}))

vi.mock(
  '@/app/customers/customer/Details/Accounts/Create/AccountsCard',
  () => ({
    AccountsCard: ({
      account,
      selectedAccounts,
      handleLinkedStatus,
      handleExpandCard,
      handleExpand,
      onUpdateAccount,
      expandedCard,
    }: any) => (
      <div
        data-testid={`account-card-${account.accNumber}`}
        data-selected={selectedAccounts.some(
          (acc: any) => acc.accNumber === account.accNumber
        )}
        data-expanded={expandedCard.includes(account.accNumber)}
      >
        <div>{account.accNumber}</div>
        <div>{account.accClass}</div>
        <button
          data-testid={`select-account-${account.accNumber}`}
          onClick={() => handleLinkedStatus(account.accNumber)}
        >
          {selectedAccounts.some(
            (acc: any) => acc.accNumber === account.accNumber
          )
            ? 'Unselect'
            : 'Select'}
        </button>
        <button
          data-testid={`expand-card-${account.accNumber}`}
          onClick={() => handleExpandCard(account.accNumber)}
        >
          {expandedCard.includes(account.accNumber) ? 'Collapse' : 'Expand'}
        </button>
        {expandedCard.includes(account.accNumber) && (
          <div data-testid={`expanded-content-${account.accNumber}`}>
            <button
              data-testid={`expand-tariffs-${account.accNumber}`}
              onClick={() => handleExpand(account.accNumber, 'tariffs')}
            >
              Tariffs
            </button>
            <button
              data-testid={`expand-notifications-${account.accNumber}`}
              onClick={() => handleExpand(account.accNumber, 'notifications')}
            >
              Notifications
            </button>
            <button
              data-testid={`expand-statements-${account.accNumber}`}
              onClick={() => handleExpand(account.accNumber, 'statements')}
            >
              Statements
            </button>
            <button
              data-testid={`expand-balance-alerts-${account.accNumber}`}
              onClick={() => handleExpand(account.accNumber, 'balanceAlerts')}
            >
              Balance Alerts
            </button>
            <button
              data-testid={`update-account-${account.accNumber}`}
              onClick={() =>
                onUpdateAccount({ ...account, tariffName: 'Updated Tariff' })
              }
            >
              Update Account
            </button>
          </div>
        )}
      </div>
    ),
  })
)

describe('Accounts', () => {
  // Mock customer accounts
  const mockActiveAccount1: ICustomerAccount = {
    accNumber: 'ACC123456',
    accOpenDate: '2023-01-01',
    customerType: 'Individual',
    customerCategory: 'Standard',
    accBranchCode: 'BR001',
    accClass: 'Savings',
    accClassDesc: 'Standard Savings Account',
    accCurrency: 'USD',
    accDormant: 'N',
    accStatus: 'Active',
    accRecordStatus: 'A',
    accStatBlock: 'N',
    accFrozen: 'N',
    accNoDebit: 'N',
    accNoCredit: 'N',
    accStopPay: 'N',
    jointAccIndicator: 'N',
    customerRecordStatus: 'A',
    accountClass: 'Savings',
    isMobileLinkable: true,
    tariffName: 'Standard',
  }

  const mockActiveAccount2: ICustomerAccount = {
    accNumber: 'ACC789012',
    accOpenDate: '2023-02-01',
    customerType: 'Individual',
    customerCategory: 'Premium',
    accBranchCode: 'BR002',
    accClass: 'Current',
    accClassDesc: 'Premium Current Account',
    accCurrency: 'USD',
    accDormant: 'N',
    accStatus: 'Active',
    accRecordStatus: 'A',
    accStatBlock: 'N',
    accFrozen: 'N',
    accNoDebit: 'N',
    accNoCredit: 'N',
    accStopPay: 'N',
    jointAccIndicator: 'N',
    customerRecordStatus: 'A',
    accountClass: 'Current',
    isMobileLinkable: true,
    tariffName: 'Premium',
  }

  const mockDormantAccount: ICustomerAccount = {
    accNumber: 'ACC345678',
    accOpenDate: '2022-01-01',
    customerType: 'Individual',
    customerCategory: 'Standard',
    accBranchCode: 'BR001',
    accClass: 'Savings',
    accClassDesc: 'Standard Savings Account',
    accCurrency: 'USD',
    accDormant: 'Y',
    accStatus: 'Dormant',
    accRecordStatus: 'A',
    accStatBlock: 'N',
    accFrozen: 'N',
    accNoDebit: 'N',
    accNoCredit: 'N',
    accStopPay: 'N',
    jointAccIndicator: 'N',
    customerRecordStatus: 'A',
    accountClass: 'Savings',
    isMobileLinkable: true,
    tariffName: 'Standard',
  }

  // Mock props
  const mockFormik: FormikProps<ICustomerAccountDetails> = {
    values: {
      firstName: 'John',
      lastName: 'Doe',
      email: '<EMAIL>',
      phoneNumber: '+**********',
      postalAddress: '123 Postal St',
      gender: 'Male',
      idType: 'NationalId',
      idValue: 'AB123456',
      cif: 'CIF123456',
      physicalAddress: '123 Main St',
      country: 'United States',
      customerPrefix: 'Mr',
      dateOfBirth: '1990-01-01',
      nationality: 'American',
      customerCategory: 'Standard',
      customerType: 'Individual',
      isM247Customer: false,
      customerAccounts: [],
      comments: null,
    },
    errors: {},
    touched: {},
    isSubmitting: false,
    isValidating: false,
    submitCount: 0,
    initialValues: {
      firstName: '',
      lastName: '',
      email: '',
      phoneNumber: '',
      postalAddress: '',
      gender: '',
      idType: '',
      idValue: '',
      cif: '',
      physicalAddress: '',
      country: '',
      customerPrefix: '',
      dateOfBirth: '',
      nationality: '',
      customerCategory: '',
      customerType: '',
      isM247Customer: false,
      customerAccounts: [],
      comments: null,
    },
    initialErrors: {},
    initialTouched: {},
    handleBlur: vi.fn(),
    handleChange: vi.fn(),
    handleReset: vi.fn(),
    handleSubmit: vi.fn(),
    resetForm: vi.fn(),
    setErrors: vi.fn(),
    setFieldError: vi.fn(),
    setFieldTouched: vi.fn(),
    setFieldValue: vi.fn(),
    setFormikState: vi.fn(),
    setStatus: vi.fn(),
    setSubmitting: vi.fn(),
    setTouched: vi.fn(),
    setValues: vi.fn(),
    submitForm: vi.fn(),
    validateForm: vi.fn(),
    validateField: vi.fn(),
    getFieldProps: vi.fn(),
    getFieldMeta: vi.fn(),
    getFieldHelpers: vi.fn(),
    dirty: false,
    isValid: true,
    status: undefined,
    registerField: vi.fn(),
    unregisterField: vi.fn(),
  }

  const mockSetStep = vi.fn()
  const mockSetOpen = vi.fn()

  // Mock state
  const mockState = {
    customers: {
      customerAccountDetails: {
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        phoneNumber: '+**********',
        gender: 'M',
        idType: 'ID_NO',
        idValue: 'AB123456',
        cif: 'CIF123456',
        postalAddress: '123 Postal St',
        physicalAddress: '123 Main St',
        isM247Customer: false,
        customerAccounts: [
          mockActiveAccount1,
          mockActiveAccount2,
          mockDormantAccount,
        ],
      },
    },
  }

  beforeEach(() => {
    vi.clearAllMocks()

    // Setup mock state
    vi.mocked(useAppSelector).mockImplementation((selector) => {
      return selector(mockState as any)
    })
  })

  it('renders the accounts step', () => {
    render(
      <Accounts
        formik={mockFormik}
        setStep={mockSetStep}
        setOpen={mockSetOpen}
      />
    )

    expect(screen.getByTestId('accounts-step')).toBeInTheDocument()
    expect(screen.getByText('STEP 2 OF 3')).toBeInTheDocument()
    expect(screen.getByText('Link Accounts')).toBeInTheDocument()
  })

  it('displays active and dormant accounts', () => {
    render(
      <Accounts
        formik={mockFormik}
        setStep={mockSetStep}
        setOpen={mockSetOpen}
      />
    )

    // Check for active accounts
    expect(screen.getByText('Active')).toBeInTheDocument()
    expect(
      screen.getByTestId(`account-card-${mockActiveAccount1.accNumber}`)
    ).toBeInTheDocument()
    expect(
      screen.getByTestId(`account-card-${mockActiveAccount2.accNumber}`)
    ).toBeInTheDocument()

    // Check for dormant accounts
    expect(screen.getByText('Dormant')).toBeInTheDocument()
    expect(
      screen.getByTestId(`account-card-${mockDormantAccount.accNumber}`)
    ).toBeInTheDocument()
  })

  it('allows selecting and unselecting accounts', async () => {
    render(
      <Accounts
        formik={mockFormik}
        setStep={mockSetStep}
        setOpen={mockSetOpen}
      />
    )

    // Initially, no accounts should be selected
    const account1Card = screen.getByTestId(
      `account-card-${mockActiveAccount1.accNumber}`
    )
    expect(account1Card).toHaveAttribute('data-selected', 'false')

    // Select an account
    const selectButton = screen.getByTestId(
      `select-account-${mockActiveAccount1.accNumber}`
    )

    await act(async () => {
      fireEvent.click(selectButton)
    })

    // Account should now be selected
    expect(account1Card).toHaveAttribute('data-selected', 'true')

    // Unselect the account
    await act(async () => {
      fireEvent.click(selectButton)
    })

    // Account should now be unselected
    expect(account1Card).toHaveAttribute('data-selected', 'false')
  })

  it('enables the Next button when at least one account is selected', async () => {
    render(
      <Accounts
        formik={mockFormik}
        setStep={mockSetStep}
        setOpen={mockSetOpen}
      />
    )

    // Initially, Next button should be disabled
    const nextButton = screen.getByText('Next').closest('button')
    expect(nextButton).toBeDisabled()

    // Select an account
    const selectButton = screen.getByTestId(
      `select-account-${mockActiveAccount1.accNumber}`
    )

    await act(async () => {
      fireEvent.click(selectButton)
    })

    // Next button should now be enabled
    expect(nextButton).not.toBeDisabled()
  })

  it('navigates to the previous step when Back button is clicked', async () => {
    render(
      <Accounts
        formik={mockFormik}
        setStep={mockSetStep}
        setOpen={mockSetOpen}
      />
    )

    const backButton = screen.getByText('Back').closest('button')

    await act(async () => {
      fireEvent.click(backButton!)
    })

    expect(mockSetStep).toHaveBeenCalledWith('Personal Details')
  })

  it('navigates to the next step and updates formik values when Next button is clicked', async () => {
    render(
      <Accounts
        formik={mockFormik}
        setStep={mockSetStep}
        setOpen={mockSetOpen}
      />
    )

    // Select an account
    const selectButton = screen.getByTestId(
      `select-account-${mockActiveAccount1.accNumber}`
    )

    await act(async () => {
      fireEvent.click(selectButton)
    })

    // Click Next button
    const nextButton = screen.getByText('Next').closest('button')

    await act(async () => {
      fireEvent.click(nextButton!)
    })

    // Check if formik.setFieldValue was called with the selected accounts
    expect(mockFormik.setFieldValue).toHaveBeenCalledWith(
      'customerAccounts',
      expect.arrayContaining([
        expect.objectContaining({ accNumber: mockActiveAccount1.accNumber }),
      ])
    )

    // Check if setStep was called to navigate to the next step
    expect(mockSetStep).toHaveBeenCalledWith('Summary')
  })

  it('allows expanding and collapsing account cards', async () => {
    render(
      <Accounts
        formik={mockFormik}
        setStep={mockSetStep}
        setOpen={mockSetOpen}
      />
    )

    const account1Card = screen.getByTestId(
      `account-card-${mockActiveAccount1.accNumber}`
    )
    const expandButton = screen.getByTestId(
      `expand-card-${mockActiveAccount1.accNumber}`
    )

    // Initially, card should not be expanded
    expect(account1Card).toHaveAttribute('data-expanded', 'false')

    // Expand the card
    await act(async () => {
      fireEvent.click(expandButton)
    })

    // Card should now be expanded
    expect(account1Card).toHaveAttribute('data-expanded', 'true')

    // Expanded content should be visible
    expect(
      screen.getByTestId(`expanded-content-${mockActiveAccount1.accNumber}`)
    ).toBeInTheDocument()

    // Collapse the card
    await act(async () => {
      fireEvent.click(expandButton)
    })

    // Card should now be collapsed
    expect(account1Card).toHaveAttribute('data-expanded', 'false')
  })

  it('allows expanding different views within an account card', async () => {
    render(
      <Accounts
        formik={mockFormik}
        setStep={mockSetStep}
        setOpen={mockSetOpen}
      />
    )

    // Expand the card first
    const expandButton = screen.getByTestId(
      `expand-card-${mockActiveAccount1.accNumber}`
    )

    await act(async () => {
      fireEvent.click(expandButton)
    })

    // Expanded content should be visible
    const expandedContent = screen.getByTestId(
      `expanded-content-${mockActiveAccount1.accNumber}`
    )
    expect(expandedContent).toBeInTheDocument()

    // Expand tariffs view
    const expandTariffsButton = screen.getByTestId(
      `expand-tariffs-${mockActiveAccount1.accNumber}`
    )

    await act(async () => {
      fireEvent.click(expandTariffsButton)
    })

    // Expand notifications view
    const expandNotificationsButton = screen.getByTestId(
      `expand-notifications-${mockActiveAccount1.accNumber}`
    )

    await act(async () => {
      fireEvent.click(expandNotificationsButton)
    })

    // Expand statements view
    const expandStatementsButton = screen.getByTestId(
      `expand-statements-${mockActiveAccount1.accNumber}`
    )

    await act(async () => {
      fireEvent.click(expandStatementsButton)
    })

    // Expand balance alerts view
    const expandBalanceAlertsButton = screen.getByTestId(
      `expand-balance-alerts-${mockActiveAccount1.accNumber}`
    )

    await act(async () => {
      fireEvent.click(expandBalanceAlertsButton)
    })

    // All these clicks should not cause any errors
    // We're just testing that the handleExpand function works without errors
  })

  it('allows updating account details', async () => {
    render(
      <Accounts
        formik={mockFormik}
        setStep={mockSetStep}
        setOpen={mockSetOpen}
      />
    )

    // Select an account first
    const selectButton = screen.getByTestId(
      `select-account-${mockActiveAccount1.accNumber}`
    )

    await act(async () => {
      fireEvent.click(selectButton)
    })

    // Expand the card
    const expandButton = screen.getByTestId(
      `expand-card-${mockActiveAccount1.accNumber}`
    )

    await act(async () => {
      fireEvent.click(expandButton)
    })

    // Update the account
    const updateButton = screen.getByTestId(
      `update-account-${mockActiveAccount1.accNumber}`
    )

    await act(async () => {
      fireEvent.click(updateButton)
    })

    // Click Next button to check if the updated account is passed to formik
    const nextButton = screen.getByText('Next').closest('button')

    await act(async () => {
      fireEvent.click(nextButton!)
    })

    // Check if formik.setFieldValue was called with the updated account
    expect(mockFormik.setFieldValue).toHaveBeenCalledWith(
      'customerAccounts',
      expect.arrayContaining([
        expect.objectContaining({
          accNumber: mockActiveAccount1.accNumber,
          tariffName: 'Updated Tariff',
        }),
      ])
    )
  })

  it('handles selecting multiple accounts', async () => {
    render(
      <Accounts
        formik={mockFormik}
        setStep={mockSetStep}
        setOpen={mockSetOpen}
      />
    )

    // Select first account
    const selectButton1 = screen.getByTestId(
      `select-account-${mockActiveAccount1.accNumber}`
    )

    await act(async () => {
      fireEvent.click(selectButton1)
    })

    // Select second account
    const selectButton2 = screen.getByTestId(
      `select-account-${mockActiveAccount2.accNumber}`
    )

    await act(async () => {
      fireEvent.click(selectButton2)
    })

    // Both accounts should be selected
    const account1Card = screen.getByTestId(
      `account-card-${mockActiveAccount1.accNumber}`
    )
    const account2Card = screen.getByTestId(
      `account-card-${mockActiveAccount2.accNumber}`
    )

    expect(account1Card).toHaveAttribute('data-selected', 'true')
    expect(account2Card).toHaveAttribute('data-selected', 'true')

    // Click Next button
    const nextButton = screen.getByText('Next').closest('button')

    await act(async () => {
      fireEvent.click(nextButton!)
    })

    // Check if formik.setFieldValue was called with both selected accounts
    expect(mockFormik.setFieldValue).toHaveBeenCalledWith(
      'customerAccounts',
      expect.arrayContaining([
        expect.objectContaining({ accNumber: mockActiveAccount1.accNumber }),
        expect.objectContaining({ accNumber: mockActiveAccount2.accNumber }),
      ])
    )
  })

  it('initializes with accounts from formik values', () => {
    // Set initial selected accounts in formik values
    const formikWithAccounts = {
      ...mockFormik,
      values: {
        ...mockFormik.values,
        customerAccounts: [mockActiveAccount1],
      },
    }

    render(
      <Accounts
        formik={formikWithAccounts}
        setStep={mockSetStep}
        setOpen={mockSetOpen}
      />
    )

    // The account should be pre-selected
    const account1Card = screen.getByTestId(
      `account-card-${mockActiveAccount1.accNumber}`
    )
    expect(account1Card).toHaveAttribute('data-selected', 'true')

    // Next button should be enabled
    const nextButton = screen.getByText('Next').closest('button')
    expect(nextButton).not.toBeDisabled()
  })
})
