import { describe, it, expect, vi, beforeEach } from 'vitest'
import {
  render,
  screen,
  fireEvent,
  waitFor,
  act,
  customRouterMock,
} from '../../test-utils'
import { PersonalDetails } from '@/app/customers/customers/Create/PersonalDetails'
import {
  fetchCustomerAccount,
  getCustomerProfile,
  resetCustomerAccountDetails,
} from '@/store/actions'
import {
  setChangeTab,
  setCustomerAccountSearch,
  setCustomerProfileAccount,
  setIsViewAccountOpen,
} from '@/store/reducers'
import type { FormikProps } from 'formik'
import type { ICustomerAccountDetails } from '@/store/interfaces'
import { useAppSelector, useAppDispatch } from '@/store'
import { mockCustomerSearchByIdData } from '../../../stubs/customer-creation'

// Mock the actions
vi.mock('@/store/actions', () => ({
  fetchCustomerAccount: vi.fn(),
  getCustomerProfile: vi.fn(),
  resetCustomerAccountDetails: vi.fn(),
}))

vi.mock('@/store/reducers', () => ({
  setChangeTab: vi.fn(),
  setCustomerAccountSearch: vi.fn(),
  setCustomerProfileAccount: vi.fn(),
  setIsViewAccountOpen: vi.fn(),
}))

vi.mock('@dtbx/ui/components/Input', () => ({
  CustomSearchInput: ({
    value,
    onChange,
    placeholder,
    sx,
    endAdornment,
  }: any) => (
    <div data-testid="custom-search-input">
      <input
        data-testid="account-search-input"
        value={value}
        onChange={onChange}
        placeholder={placeholder}
      />
      {endAdornment}
    </div>
  ),
}))

// Mock lodash debounce to execute immediately in tests
vi.mock('lodash', async () => {
  const actual = await vi.importActual('lodash')
  return {
    ...actual,
    debounce: (fn: Function) => fn,
  }
})

describe('PersonalDetails', () => {
  // Mock props
  const mockFormik: FormikProps<ICustomerAccountDetails> = {
    values: {
      firstName: '',
      lastName: '',
      email: '',
      phoneNumber: '',
      postalAddress: '',
      gender: '',
      idType: '',
      idValue: '',
      cif: '',
      physicalAddress: '',
      country: '',
      customerPrefix: '',
      dateOfBirth: '',
      nationality: '',
      customerCategory: '',
      customerType: '',
      isM247Customer: false,
      customerAccounts: [],
      comments: null,
    },
    errors: {},
    touched: {},
    isSubmitting: false,
    isValidating: false,
    submitCount: 0,
    initialValues: {
      firstName: '',
      lastName: '',
      email: '',
      phoneNumber: '',
      postalAddress: '',
      gender: '',
      idType: '',
      idValue: '',
      cif: '',
      physicalAddress: '',
      country: '',
      customerPrefix: '',
      dateOfBirth: '',
      nationality: '',
      customerCategory: '',
      customerType: '',
      isM247Customer: false,
      customerAccounts: [],
      comments: null,
    },
    initialErrors: {},
    initialTouched: {},
    handleBlur: vi.fn(),
    handleChange: vi.fn(),
    handleReset: vi.fn(),
    handleSubmit: vi.fn(),
    resetForm: vi.fn(),
    setErrors: vi.fn(),
    setFieldError: vi.fn(),
    setFieldTouched: vi.fn(),
    setFieldValue: vi.fn(),
    setFormikState: vi.fn(),
    setStatus: vi.fn(),
    setSubmitting: vi.fn(),
    setTouched: vi.fn(),
    setValues: vi.fn(),
    submitForm: vi.fn(),
    validateForm: vi.fn(),
    validateField: vi.fn(),
    getFieldProps: vi.fn().mockImplementation((name) => ({
      name,
      value: mockFormik.values[name as keyof ICustomerAccountDetails] || '',
      onChange: mockFormik.handleChange,
      onBlur: mockFormik.handleBlur,
    })),
    getFieldMeta: vi.fn(),
    getFieldHelpers: vi.fn(),
    dirty: false,
    isValid: true,
    status: undefined,
    registerField: vi.fn(),
    unregisterField: vi.fn(),
  }

  const mockSetStep = vi.fn()
  const mockSetOpen = vi.fn()
  const mockDispatch = vi.fn()

  beforeEach(() => {
    vi.clearAllMocks()

    // Setup mock dispatch
    vi.mocked(useAppDispatch).mockReturnValue(mockDispatch)

    // Reset router mocks
    Object.values(customRouterMock).forEach((mock) => {
      if (typeof mock === 'function') {
        ;(mock as any).mockReset()
      }
    })
  })

  // Helper function to setup mock state for different test scenarios
  const setupMockState = (customState: any) => {
    const accDetails = mockCustomerSearchByIdData()
    const defaultState = {
      loans: {
        bankBranches: [
          { branchCode: '001', branchName: 'Main Branch' },
          { branchCode: '002', branchName: 'Downtown Branch' },
        ],
      },
      customers: {
        customerAccountDetails: accDetails,
        customerAccountSearch: {
          searchValue: '********',
        },
        customerProfileExists: false,
        customer: {
          id: '',
        },
        customerLinkedAccountsList: [],
        isCustomerLoading: false,
        customersResponse: {
          data: [],
          pageNumber: 1,
          totalNumberOfPages: 0,
        },
        isCustomersLoading: false,
        isCustomersSuccess: true,
      },
      approvalRequests: {
        customersWithPendingApprovals: [],
      },
    }

    const mergedState = {
      ...defaultState,
      ...customState,
    }

    vi.mocked(useAppSelector).mockImplementation((selector) => {
      return selector(mergedState)
    })
  }

  it('renders the personal details step', () => {
    setupMockState({})

    render(
      <PersonalDetails
        formik={mockFormik}
        setStep={mockSetStep}
        setOpen={mockSetOpen}
      />
    )

    expect(screen.getByTestId('personal-details-step')).toBeInTheDocument()
    expect(screen.getByText('STEP 1 OF 3')).toBeInTheDocument()
    expect(screen.getByText('Personal Details')).toBeInTheDocument()
  })

  it('displays the search input with the correct value', () => {
    setupMockState({})

    render(
      <PersonalDetails
        formik={mockFormik}
        setStep={mockSetStep}
        setOpen={mockSetOpen}
      />
    )

    const searchInput = screen.getByTestId('account-search-input')
    expect(searchInput).toBeInTheDocument()
    expect(searchInput).toHaveAttribute('value', '********')
  })

  it.skip('handles search input change', async () => {
    setupMockState({})

    render(
      <PersonalDetails
        formik={mockFormik}
        setStep={mockSetStep}
        setOpen={mockSetOpen}
      />
    )

    const searchInput = screen.getByTestId('account-search-input')
    console.log('Initial search value:', searchInput.getAttribute('value'))

    await act(async () => {
      fireEvent.change(searchInput, { target: { value: '**********' } })
    })

    console.log('Current search value:', searchInput.getAttribute('value'))

    expect(setCustomerAccountSearch).toHaveBeenCalledWith({
      searchValue: '**********',
    })

    // Since we mocked debounce to execute immediately
    expect(fetchCustomerAccount).toHaveBeenCalledWith({
      account: '**********',
      dispatch: mockDispatch,
    })
  })

  it('disables the Next button when account is not fetched', () => {
    // Mock empty account details
    setupMockState({
      customers: {
        customerAccountDetails: {},
        customerAccountSearch: { searchValue: '********' },
        customerProfileExists: false,
        customer: { id: '' },
        customerLinkedAccountsList: [],
        isCustomerLoading: false,
      },
    })

    render(
      <PersonalDetails
        formik={mockFormik}
        setStep={mockSetStep}
        setOpen={mockSetOpen}
      />
    )

    const nextButton = screen.getByText('Next').closest('button')
    expect(nextButton).toBeDisabled()
  })

  it('enables the Next button when account is fetched', () => {
    setupMockState({})

    render(
      <PersonalDetails
        formik={mockFormik}
        setStep={mockSetStep}
        setOpen={mockSetOpen}
      />
    )

    const nextButton = screen.getByText('Next').closest('button')
    expect(nextButton).not.toBeDisabled()
  })

  it('navigates to the next step when Next button is clicked', async () => {
    setupMockState({})

    render(
      <PersonalDetails
        formik={mockFormik}
        setStep={mockSetStep}
        setOpen={mockSetOpen}
      />
    )

    const nextButton = screen.getByText('Next').closest('button')

    await act(async () => {
      fireEvent.click(nextButton!)
    })

    expect(mockSetStep).toHaveBeenCalledWith('Link and setup accounts')
  })

  it('closes the form when Cancel button is clicked', async () => {
    setupMockState({})

    render(
      <PersonalDetails
        formik={mockFormik}
        setStep={mockSetStep}
        setOpen={mockSetOpen}
      />
    )

    const cancelButton = screen.getByText('Cancel').closest('button')

    await act(async () => {
      fireEvent.click(cancelButton!)
    })

    expect(mockSetOpen).toHaveBeenCalledWith(false)
    expect(resetCustomerAccountDetails).toHaveBeenCalledWith({
      dispatch: mockDispatch,
    })
    expect(setCustomerAccountSearch).toHaveBeenCalledWith({ searchValue: '' })
  })

  it('displays warning when customer has M24/7 profile', () => {
    // Mock customer with M24/7 profile
    setupMockState({
      customers: {
        customerAccountDetails: {
          firstName: 'John',
          lastName: 'Doe',
          email: '<EMAIL>',
          phoneNumber: '+**********',
          gender: 'M',
          idType: 'ID_NO',
          idValue: 'AB123456',
          cif: 'CIF123456',
          postalAddress: '123 Postal St',
          physicalAddress: '123 Main St',
          isM247Customer: true,
        },
        customerAccountSearch: { searchValue: '********' },
        customerProfileExists: false,
        customer: { id: '' },
        customerLinkedAccountsList: [],
        isCustomerLoading: false,
      },
    })

    render(
      <PersonalDetails
        formik={mockFormik}
        setStep={mockSetStep}
        setOpen={mockSetOpen}
      />
    )

    expect(
      screen.getByText(/has an existing m24\/7 profile/)
    ).toBeInTheDocument()

    // Next button should be disabled
    const nextButton = screen.getByText('Next').closest('button')
    expect(nextButton).toBeDisabled()
  })

  it('displays profile exists message when customer has an X24/7 profile', () => {
    // Mock customer with existing profile
    setupMockState({
      customers: {
        customerAccountDetails: {
          firstName: 'John',
          lastName: 'Doe',
          email: '<EMAIL>',
          phoneNumber: '+**********',
          gender: 'M',
          idType: 'ID_NO',
          idValue: 'AB123456',
          cif: 'CIF123456',
          postalAddress: '123 Postal St',
          physicalAddress: '123 Main St',
          isM247Customer: false,
        },
        customerAccountSearch: { searchValue: '********' },
        customerProfileExists: true,
        customer: { id: 'CUST123' },
        customerLinkedAccountsList: [],
        isCustomerLoading: false,
      },
    })

    render(
      <PersonalDetails
        formik={mockFormik}
        setStep={mockSetStep}
        setOpen={mockSetOpen}
      />
    )

    expect(
      screen.getByText(/already has an x24\/7 profile/)
    ).toBeInTheDocument()
    expect(screen.getByText('Go to customer profile')).toBeInTheDocument()
  })

  it('navigates to customer profile when Go to profile button is clicked', async () => {
    // Mock customer with existing profile
    setupMockState({
      customers: {
        customerAccountDetails: {
          firstName: 'John',
          lastName: 'Doe',
          email: '<EMAIL>',
          phoneNumber: '+**********',
          gender: 'M',
          idType: 'ID_NO',
          idValue: 'AB123456',
          cif: 'CIF123456',
          postalAddress: '123 Postal St',
          physicalAddress: '123 Main St',
          isM247Customer: false,
        },
        customerAccountSearch: { searchValue: '********' },
        customerProfileExists: true,
        customer: { id: 'CUST123' },
        customerLinkedAccountsList: [],
        isCustomerLoading: false,
      },
    })

    render(
      <PersonalDetails
        formik={mockFormik}
        setStep={mockSetStep}
        setOpen={mockSetOpen}
      />
    )

    const profileButton = screen
      .getByText('Go to customer profile')
      .closest('button')

    await act(async () => {
      fireEvent.click(profileButton!)
    })

    expect(mockSetOpen).toHaveBeenCalledWith(false)
    expect(getCustomerProfile).toHaveBeenCalledWith('CUST123', mockDispatch)
    expect(customRouterMock.push).toHaveBeenCalledWith('/customers/customer')
    expect(setChangeTab).toHaveBeenCalledWith(0)
  })

  it('displays linked account option when customer has linked accounts', () => {
    // Mock customer with linked accounts
    const mockLinkedAccount = {
      id: {
        accountNo: '********',
      },
    }

    setupMockState({
      customers: {
        customerAccountDetails: {
          firstName: 'John',
          lastName: 'Doe',
          email: '<EMAIL>',
          phoneNumber: '+**********',
          gender: 'M',
          idType: 'ID_NO',
          idValue: 'AB123456',
          cif: 'CIF123456',
          postalAddress: '123 Postal St',
          physicalAddress: '123 Main St',
          isM247Customer: false,
        },
        customerAccountSearch: { searchValue: '********' },
        customerProfileExists: true,
        customer: { id: 'CUST123' },
        customerLinkedAccountsList: [mockLinkedAccount],
        isCustomerLoading: false,
      },
    })

    render(
      <PersonalDetails
        formik={mockFormik}
        setStep={mockSetStep}
        setOpen={mockSetOpen}
      />
    )

    expect(screen.getByText(/Go to account ********/)).toBeInTheDocument()
  })

  it('navigates to account when Go to account button is clicked', async () => {
    // Mock customer with linked accounts
    const mockLinkedAccount = {
      id: {
        accountNo: '********',
      },
    }

    setupMockState({
      customers: {
        customerAccountDetails: {
          firstName: 'John',
          lastName: 'Doe',
          email: '<EMAIL>',
          phoneNumber: '+**********',
          gender: 'M',
          idType: 'ID_NO',
          idValue: 'AB123456',
          cif: 'CIF123456',
          postalAddress: '123 Postal St',
          physicalAddress: '123 Main St',
          isM247Customer: false,
        },
        customerAccountSearch: { searchValue: '********' },
        customerProfileExists: true,
        customer: { id: 'CUST123' },
        customerLinkedAccountsList: [mockLinkedAccount],
        isCustomerLoading: false,
      },
    })

    render(
      <PersonalDetails
        formik={mockFormik}
        setStep={mockSetStep}
        setOpen={mockSetOpen}
      />
    )

    const accountButton = screen
      .getByText(/Go to account ********/)
      .closest('button')

    await act(async () => {
      fireEvent.click(accountButton!)
    })

    expect(mockSetOpen).toHaveBeenCalledWith(false)
    expect(getCustomerProfile).toHaveBeenCalledWith('CUST123', mockDispatch)
    expect(customRouterMock.push).toHaveBeenCalledWith('/customers/customer')
    expect(setChangeTab).toHaveBeenCalledWith(0)
    expect(setChangeTab).toHaveBeenCalledWith(3)
    expect(setIsViewAccountOpen).toHaveBeenCalledWith(true)
    expect(setCustomerProfileAccount).toHaveBeenCalledWith(mockLinkedAccount)
  })

  it('displays Link account option when customer has no linked accounts', () => {
    // Mock customer with no linked accounts
    setupMockState({
      customers: {
        customerAccountDetails: {
          firstName: 'John',
          lastName: 'Doe',
          email: '<EMAIL>',
          phoneNumber: '+**********',
          gender: 'M',
          idType: 'ID_NO',
          idValue: 'AB123456',
          cif: 'CIF123456',
          postalAddress: '123 Postal St',
          physicalAddress: '123 Main St',
          isM247Customer: false,
        },
        customerAccountSearch: { searchValue: '********' },
        customerProfileExists: true,
        customer: { id: 'CUST123' },
        customerLinkedAccountsList: [],
        isCustomerLoading: false,
      },
    })

    render(
      <PersonalDetails
        formik={mockFormik}
        setStep={mockSetStep}
        setOpen={mockSetOpen}
      />
    )

    // Since there's no linked account, we won't see the account number
    expect(screen.getByText('Link account')).toBeInTheDocument()
  })

  it.skip('shows loading indicator when fetching customer data', () => {
    // Mock loading state
    setupMockState({
      customers: {
        customerAccountDetails: {
          firstName: 'John',
          lastName: 'Doe',
          email: '<EMAIL>',
          phoneNumber: '+**********',
          gender: 'M',
          idType: 'ID_NO',
          idValue: 'AB123456',
          cif: 'CIF123456',
          postalAddress: '123 Postal St',
          physicalAddress: '123 Main St',
          isM247Customer: false,
        },
        customerAccountSearch: { searchValue: '********' },
        customerProfileExists: false,
        customer: { id: '' },
        customerLinkedAccountsList: [],
        isCustomerLoading: true,
      },
    })

    render(
      <PersonalDetails
        formik={mockFormik}
        setStep={mockSetStep}
        setOpen={mockSetOpen}
      />
    )

    // CircularProgress should be rendered
    const circularProgress = document.querySelector('.MuiCircularProgress-root')
    expect(circularProgress).toBeInTheDocument()

    // Next button should be disabled during loading
    const nextButton = screen.getByText('Next').closest('button')
    expect(nextButton).toBeDisabled()
  })

  it('updates formik values when account details are available', async () => {
    const mockAccountDetails = {
      firstName: 'Jane',
      lastName: 'Smith',
      email: '<EMAIL>',
      phoneNumber: '+9********0',
      gender: 'F',
      idType: 'ID_NO',
      idValue: 'XY987654',
      cif: 'CIF987654',
      postalAddress: '456 Postal Ave',
      physicalAddress: '456 Main Ave',
      isM247Customer: false,
    }

    // Mock account details
    setupMockState({
      customers: {
        customerAccountDetails: mockAccountDetails,
        customerAccountSearch: { searchValue: '********' },
        customerProfileExists: false,
        customer: { id: '' },
        customerLinkedAccountsList: [],
        isCustomerLoading: false,
      },
    })

    render(
      <PersonalDetails
        formik={mockFormik}
        setStep={mockSetStep}
        setOpen={mockSetOpen}
      />
    )

    // Check if formik.setValues was called with the correct transformed data
    await waitFor(() => {
      expect(mockFormik.setValues).toHaveBeenCalledWith({
        ...mockAccountDetails,
        gender: 'Female', // Transformed from 'F'
        idType: 'NationalId', // Transformed from 'ID_NO'
        customerAccounts: [],
      })
    })
  })
})
