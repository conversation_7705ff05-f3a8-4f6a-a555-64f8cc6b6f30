import { describe, it, expect, vi, beforeEach } from 'vitest'
import { render, screen, fireEvent, waitFor, act } from '../../test-utils'
import {
  Summary,
  ProfileView,
  AccountsView,
  CustomChip,
} from '@/app/customers/customers/Create/Summary'
import { useAppSelector, useAppDispatch } from '@/store'
import { setSelectedCustomerSummaryAccount } from '@/store/reducers'
import { accountType, createCustomerAccount } from '@/store/actions'
import type { FormikProps } from 'formik'
import type {
  ICustomerAccountDetails,
  ICustomerAccount,
} from '@/store/interfaces'
import { HasAccessToRights } from '@dtbx/store/utils'

// Mock the dependencies
vi.mock('@/store', () => ({
  useAppSelector: vi.fn(),
  useAppDispatch: vi.fn(),
}))

vi.mock('@/store/reducers', () => ({
  setSelectedCustomerSummaryAccount: vi.fn(),
}))

vi.mock('@/store/actions', () => ({
  accountType: vi.fn(),
  createCustomerAccount: vi.fn(),
}))

vi.mock('@dtbx/store/utils', () => ({
  HasAccessToRights: vi.fn(),
}))

vi.mock('@dtbx/ui/icons', () => ({
  AccountsSummaryIcon: () => <div data-testid="accounts-summary-icon" />,
  CustomerSettingsIcon: () => <div data-testid="customer-settings-icon" />,
}))

vi.mock('@mui/icons-material', () => ({
  ArrowOutward: () => <div data-testid="arrow-outward-icon" />,
  Edit: () => <div data-testid="edit-icon" />,
  default: () => <div data-testid="default-icon" />,
}))

vi.mock('@mui/icons-material/ArrowBack', () => ({
  default: () => <div data-testid="arrow-back-icon" />,
}))

vi.mock('@mui/icons-material/ArrowForward', () => ({
  default: () => <div data-testid="arrow-forward-icon" />,
}))

describe('Summary Components', () => {
  // Mock customer account
  const mockAccount: ICustomerAccount = {
    accNumber: 'ACC123456',
    accOpenDate: '2023-01-01',
    customerType: 'Individual',
    customerCategory: 'Standard',
    accBranchCode: 'BR001',
    accClass: 'Savings',
    accClassDesc: 'Standard Savings Account',
    accCurrency: 'USD',
    accDormant: 'N',
    accStatus: 'Active',
    accRecordStatus: 'A',
    accStatBlock: 'N',
    accFrozen: 'N',
    accNoDebit: 'N',
    accNoCredit: 'N',
    accStopPay: 'N',
    jointAccIndicator: 'N',
    customerRecordStatus: 'A',
    accountClass: 'Savings',
    isMobileLinkable: true,
    tariffName: 'Standard',
  }

  // Mock props
  const mockFormik: FormikProps<ICustomerAccountDetails> = {
    values: {
      firstName: 'John',
      lastName: 'Doe',
      email: '<EMAIL>',
      phoneNumber: '+**********',
      postalAddress: '123 Postal St',
      gender: 'Male',
      idType: 'NationalId',
      idValue: 'AB123456',
      cif: 'CIF123456',
      physicalAddress: '123 Main St',
      country: 'United States',
      customerPrefix: 'Mr',
      dateOfBirth: '1990-01-01',
      nationality: 'American',
      customerCategory: 'Standard',
      customerType: 'Individual',
      isM247Customer: false,
      customerAccounts: [mockAccount],
      comments: null,
    },
    errors: {},
    touched: {},
    isSubmitting: false,
    isValidating: false,
    submitCount: 0,
    initialValues: {
      firstName: '',
      lastName: '',
      email: '',
      phoneNumber: '',
      postalAddress: '',
      gender: '',
      idType: '',
      idValue: '',
      cif: '',
      physicalAddress: '',
      country: '',
      customerPrefix: '',
      dateOfBirth: '',
      nationality: '',
      customerCategory: '',
      customerType: '',
      isM247Customer: false,
      customerAccounts: [],
      comments: null,
    },
    initialErrors: {},
    initialTouched: {},
    handleBlur: vi.fn(),
    handleChange: vi.fn(),
    handleReset: vi.fn(),
    handleSubmit: vi.fn(),
    resetForm: vi.fn(),
    setErrors: vi.fn(),
    setFieldError: vi.fn(),
    setFieldTouched: vi.fn(),
    setFieldValue: vi.fn(),
    setFormikState: vi.fn(),
    setStatus: vi.fn(),
    setSubmitting: vi.fn(),
    setTouched: vi.fn(),
    setValues: vi.fn(),
    submitForm: vi.fn(),
    validateForm: vi.fn(),
    validateField: vi.fn(),
    getFieldProps: vi.fn().mockReturnValue({
      name: 'comments',
      value: '',
      onChange: vi.fn(),
      onBlur: vi.fn(),
    }),
    getFieldMeta: vi.fn(),
    getFieldHelpers: vi.fn(),
    dirty: false,
    isValid: true,
    status: undefined,
    registerField: vi.fn(),
    unregisterField: vi.fn(),
  }

  const mockSetStep = vi.fn()
  const mockSetOpen = vi.fn()
  const mockDispatch = vi.fn()

  // Mock state
  const mockState = {
    customers: {
      customerAccountDetails: {
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        phoneNumber: '+**********',
        gender: 'M',
        idType: 'ID_NO',
        idValue: 'AB123456',
        cif: 'CIF123456',
        postalAddress: '123 Postal St',
        physicalAddress: '123 Main St',
        isM247Customer: false,
        customerAccounts: [mockAccount],
      },
      isCustomerLoading: false,
      selectedCustomerSummaryAccount: mockAccount,
    },
    loans: {
      bankBranches: [{ branchCode: 'BR001', branchName: 'Main Branch' }],
    },
  }

  beforeEach(() => {
    vi.clearAllMocks()

    // Setup mock state
    vi.mocked(useAppSelector).mockImplementation((selector) => {
      return selector(mockState as any)
    })

    // Setup mock dispatch
    vi.mocked(useAppDispatch).mockReturnValue(mockDispatch)

    // Setup mock accountType
    vi.mocked(accountType).mockReturnValue('Individual')

    // Setup mock HasAccessToRights
    vi.mocked(HasAccessToRights).mockReturnValue(false)
  })

  describe('CustomChip', () => {
    it('renders with the correct prop', () => {
      render(<CustomChip prop="Test Prop" />)

      expect(screen.getByText('Test Prop')).toBeInTheDocument()
    })
  })

  describe('Summary', () => {
    it('renders the summary step', () => {
      render(
        <Summary
          formik={mockFormik}
          setStep={mockSetStep}
          setOpen={mockSetOpen}
        />
      )

      expect(screen.getByTestId('summary-step')).toBeInTheDocument()
      expect(screen.getByText('STEP 3 OF 3')).toBeInTheDocument()
      expect(screen.getByText('Summary')).toBeInTheDocument()
    })

    it.skip('displays customer name and profile details button', () => {
      render(
        <Summary
          formik={mockFormik}
          setStep={mockSetStep}
          setOpen={mockSetOpen}
        />
      )

      expect(screen.getByText('John Doe')).toBeInTheDocument()
      expect(screen.getByText('Profile Details')).toBeInTheDocument()
    })

    it('displays account information for each linked account', () => {
      render(
        <Summary
          formik={mockFormik}
          setStep={mockSetStep}
          setOpen={mockSetOpen}
        />
      )

      expect(
        screen.getByText(`Account ${mockAccount.accNumber}`)
      ).toBeInTheDocument()
      expect(screen.getByText('Account Details')).toBeInTheDocument()
    })

    it('navigates to the previous step when Back button is clicked', async () => {
      render(
        <Summary
          formik={mockFormik}
          setStep={mockSetStep}
          setOpen={mockSetOpen}
        />
      )

      const backButton = screen.getByText('Back').closest('button')

      await act(async () => {
        fireEvent.click(backButton!)
      })

      expect(mockSetStep).toHaveBeenCalledWith('Link and setup accounts')
    })

    it('shows loading indicator when isCustomerLoading is true', () => {
      // Override the mock state for this test
      vi.mocked(useAppSelector).mockImplementation((selector) => {
        if (typeof selector === 'function') {
          return selector({
            ...mockState,
            customers: {
              ...mockState.customers,
              isCustomerLoading: true,
            },
          })
        }
        return undefined
      })

      render(
        <Summary
          formik={mockFormik}
          setStep={mockSetStep}
          setOpen={mockSetOpen}
        />
      )

      expect(screen.getByRole('progressbar')).toBeInTheDocument()
    })

    it('shows "Submit to checker" button for regular users', () => {
      vi.mocked(HasAccessToRights).mockReturnValue(false)

      render(
        <Summary
          formik={mockFormik}
          setStep={mockSetStep}
          setOpen={mockSetOpen}
        />
      )

      expect(screen.getByText('Submit to checker')).toBeInTheDocument()
    })

    it('shows "Submit" button for super users', () => {
      vi.mocked(HasAccessToRights).mockReturnValue(true)

      render(
        <Summary
          formik={mockFormik}
          setStep={mockSetStep}
          setOpen={mockSetOpen}
        />
      )

      expect(screen.getByText('Submit')).toBeInTheDocument()
    })

    it('disables the submit button when there are form errors', () => {
      const formikWithErrors = {
        ...mockFormik,
        errors: { firstName: 'Required' },
      }

      render(
        <Summary
          formik={formikWithErrors}
          setStep={mockSetStep}
          setOpen={mockSetOpen}
        />
      )

      const submitButton = screen
        .getByText('Submit to checker')
        .closest('button')
      expect(submitButton).toBeDisabled()
    })

    it.skip('changes view to Profile Details when the button is clicked', async () => {
      render(
        <Summary
          formik={mockFormik}
          setStep={mockSetStep}
          setOpen={mockSetOpen}
        />
      )

      const profileDetailsButton = screen
        .getByText('Profile Details')
        .closest('button')

      await act(async () => {
        fireEvent.click(profileDetailsButton!)
      })

      // The ProfileView is rendered by default, so we need to check for specific elements
      // that would indicate the view has been reset/rerendered
      expect(screen.getAllByText('Profile Details').length).toBeGreaterThan(1)
    })

    it('changes view to Account Details when the button is clicked', async () => {
      render(
        <Summary
          formik={mockFormik}
          setStep={mockSetStep}
          setOpen={mockSetOpen}
        />
      )

      const accountDetailsButton = screen
        .getByText('Account Details')
        .closest('button')

      await act(async () => {
        fireEvent.click(accountDetailsButton!)
      })

      // Check for elements specific to the AccountsView
      expect(screen.getByText('Accounts Details')).toBeInTheDocument()
      expect(
        screen.getByText('Subscriptions (E-statements)')
      ).toBeInTheDocument()

      // Verify that the dispatch was called with the correct action
      expect(mockDispatch).toHaveBeenCalledWith(
        setSelectedCustomerSummaryAccount(mockAccount)
      )
    })

    it('renders the comments textarea', () => {
      render(
        <Summary
          formik={mockFormik}
          setStep={mockSetStep}
          setOpen={mockSetOpen}
        />
      )

      expect(screen.getByLabelText('Comments')).toBeInTheDocument()
    })
  })

  describe('ProfileView', () => {
    it('renders the profile details', () => {
      render(<ProfileView setStep={mockSetStep} />)

      expect(screen.getByText('Profile Details')).toBeInTheDocument()
      expect(screen.getByText('First Name')).toBeInTheDocument()
      expect(screen.getByText('Last Name')).toBeInTheDocument()
      expect(screen.getByText('John')).toBeInTheDocument()
      expect(screen.getByText('Doe')).toBeInTheDocument()
    })

    it('navigates to Personal Details step when Edit button is clicked', async () => {
      render(<ProfileView setStep={mockSetStep} />)

      const editButton = screen.getByText('Edit').closest('button')

      await act(async () => {
        fireEvent.click(editButton!)
      })

      expect(mockSetStep).toHaveBeenCalledWith('Personal Details')
    })

    it('displays gender correctly', () => {
      render(<ProfileView setStep={mockSetStep} />)

      expect(screen.getByText('Gender')).toBeInTheDocument()
      expect(screen.getByText('Male')).toBeInTheDocument()
    })

    it('displays all customer details in the table', () => {
      render(<ProfileView setStep={mockSetStep} />)

      // Check for all fields
      const fieldsToCheck = [
        'First Name',
        'Last Name',
        'Gender',
        'Phone number',
        'Email',
        'ID type',
        'ID number',
        'CIF number',
        'Postal address',
        'Physical address',
      ]

      fieldsToCheck.forEach((field) => {
        expect(screen.getByText(field)).toBeInTheDocument()
      })

      // Check for specific values
      expect(screen.getByText('<EMAIL>')).toBeInTheDocument()
      expect(screen.getByText('+**********')).toBeInTheDocument()
      expect(screen.getByText('ID_NO')).toBeInTheDocument()
      expect(screen.getByText('AB123456')).toBeInTheDocument()
      expect(screen.getByText('CIF123456')).toBeInTheDocument()
      expect(screen.getByText('123 Postal St')).toBeInTheDocument()
      expect(screen.getByText('123 Main St')).toBeInTheDocument()
    })
  })

  describe('AccountsView', () => {
    it('renders the account details', () => {
      render(<AccountsView setStep={mockSetStep} />)

      expect(screen.getByText('Accounts Details')).toBeInTheDocument()
      expect(screen.getByText('Account name')).toBeInTheDocument()
      expect(screen.getByText('Account number')).toBeInTheDocument()
      expect(screen.getByText('Standard Savings Account')).toBeInTheDocument()
      expect(screen.getByText('ACC123456')).toBeInTheDocument()
    })

    it('navigates to Link and setup accounts step when Edit button is clicked', async () => {
      render(<AccountsView setStep={mockSetStep} />)

      const editButton = screen.getByText('Edit').closest('button')

      await act(async () => {
        fireEvent.click(editButton!)
      })

      expect(mockSetStep).toHaveBeenCalledWith('Link and setup accounts')
    })

    it.skip('displays account type correctly', () => {
      render(<AccountsView setStep={mockSetStep} />)

      expect(screen.getByText('Type')).toBeInTheDocument()
      expect(screen.getByText('Individual')).toBeInTheDocument()
      expect(accountType).toHaveBeenCalledWith('Individual')
    })

    it('displays branch name correctly', () => {
      render(<AccountsView setStep={mockSetStep} />)

      expect(screen.getByText('Branch')).toBeInTheDocument()
      expect(screen.getByText('Main Branch')).toBeInTheDocument()
    })

    it('displays "No branch" when branch is not found', () => {
      // Override the mock account for this test
      const accountWithUnknownBranch = {
        ...mockAccount,
        accBranchCode: 'UNKNOWN',
      }

      // Override the mock state for this test
      vi.mocked(useAppSelector).mockImplementation((selector) => {
        if (typeof selector === 'function') {
          return selector({
            ...mockState,
            customers: {
              ...mockState.customers,
              selectedCustomerSummaryAccount: accountWithUnknownBranch,
            },
          })
        }
        return undefined
      })

      render(<AccountsView setStep={mockSetStep} />)

      expect(screen.getByText('Branch')).toBeInTheDocument()
      expect(screen.getByText('No branch')).toBeInTheDocument()
    })

    it.skip('displays subscriptions section', () => {
      render(<AccountsView setStep={mockSetStep} />)

      expect(
        screen.getByText('Subscriptions (E-statements)')
      ).toBeInTheDocument()
      expect(screen.getByText('Daily')).toBeInTheDocument()
      expect(screen.getByText('Loan Installment Due')).toBeInTheDocument()

      // Check for email chips
      const emailChips = screen.getAllByText('<EMAIL>')
      expect(emailChips.length).toBe(2) // One for each subscription type
    })

    it.skip('displays notifications section', () => {
      render(<AccountsView setStep={mockSetStep} />)

      expect(screen.getByText('Notifications')).toBeInTheDocument()
      expect(screen.getByText('Credit & Debit')).toBeInTheDocument()

      // Check for channel chips
      expect(screen.getAllByText('SMS').length).toBeGreaterThan(0)
      expect(screen.getByText('Email')).toBeInTheDocument()

      // Check for phone number chip
      expect(screen.getAllByText('+**********').length).toBeGreaterThan(0)
    })

    it.skip('displays alerts section', () => {
      render(<AccountsView setStep={mockSetStep} />)

      expect(screen.getByText('Alerts')).toBeInTheDocument()
      expect(screen.getByText('Daily')).toBeInTheDocument()
      expect(screen.getByText('Weekly')).toBeInTheDocument()

      // Check for SMS chips
      const smsChips = screen.getAllByText('SMS')
      expect(smsChips.length).toBeGreaterThan(0)

      // Check for phone number chips
      const phoneChips = screen.getAllByText('+**********')
      expect(phoneChips.length).toBeGreaterThan(0)
    })
  })
})
