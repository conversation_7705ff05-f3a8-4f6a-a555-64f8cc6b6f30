import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { render, screen, fireEvent, waitFor, act } from '../../test-utils'
import { CreateCustomerDialog } from '@/app/customers/customers/Create'
import { useAppSelector, useAppDispatch } from '@/store'
import { createCustomerAccount } from '@/store/actions'
import { clearNotification } from '@dtbx/store/reducers'
import type { FormikProps } from 'formik'
import type { ICustomerAccountDetails } from '@/store/interfaces'
import React from 'react'
import { mockCustomerCreation } from '../../../stubs/customer-creation'

// Mock the dependencies
vi.mock('@/store', () => ({
  useAppSelector: vi.fn(),
  useAppDispatch: vi.fn(),
}))

vi.mock('@/store/actions', () => ({
  createCustomerAccount: vi.fn(),
}))

vi.mock('@dtbx/store/reducers', () => ({
  clearNotification: vi.fn(),
}))

vi.mock('@dtbx/store/utils', () => ({
  ACCESS_CONTROLS: {
    CREATE_CUSTOMERS: ['CREATE_CUSTOMERS'],
  },
  AccessControlWrapper: ({ children }: { children: React.ReactNode }) =>
    children,
}))
//Mock MUI components
vi.mock('@mui/material', async () => {
  const actual = await vi.importActual('@mui/material')
  return {
    ...actual,
    // Mock Drawer component as Temporary variant
    Drawer: ({ children, open, onClose, anchor = 'right', ...props }: any) => {
      return open ? (
        <div
          data-testid="create-customer-drawer"
          data-open={open}
          data-anchor={anchor}
          data-variant="temporary"
          aria-modal="true"
          role="presentation"
          {...props}
        >
          {/* Backdrop for temporary drawer */}
          <div
            data-testid="drawer-backdrop"
            onClick={onClose}
            style={{ position: 'fixed', top: 0, left: 0, right: 0, bottom: 0 }}
          />

          {/* Drawer paper */}
          <div
            className="MuiDrawer-paper"
            data-testid="drawer-paper"
            style={{
              position: 'fixed',
              [anchor]: 0,
              height: '100%',
              width: anchor === 'left' || anchor === 'right' ? '400px' : '100%',
            }}
          >
            {children}
          </div>
        </div>
      ) : null
    },
    // Mock IconButton
    IconButton: ({ children, onClick, ...props }: any) => (
      <button onClick={onClick} data-testid="mui-icon-button" {...props}>
        {children}
      </button>
    ),
    // Mock Typography
    Typography: ({ children, variant, ...props }: any) => (
      <div data-testid="mui-typography" data-variant={variant} {...props}>
        {children}
      </div>
    ),
    // Mock Stepper components
    Stepper: ({ children, activeStep, ...props }: any) => (
      <div data-testid="mui-stepper" data-active-step={activeStep} {...props}>
        {children}
      </div>
    ),
    Step: ({ children, ...props }: any) => (
      <div data-testid="mui-step" {...props}>
        {children}
      </div>
    ),
    StepLabel: ({ children, ...props }: any) => (
      <div data-testid="mui-step-label" {...props}>
        {children}
      </div>
    ),
    // Mock Button
    Button: ({ children, onClick, variant, ...props }: any) => (
      <button
        onClick={onClick}
        data-testid="mui-button"
        data-variant={variant}
        {...props}
      >
        {children}
      </button>
    ),
  }
})

// Mock the child components
vi.mock('@/app/customers/customers/Create/PersonalDetails', () => ({
  PersonalDetails: ({
    formik,
    setStep,
    setOpen,
  }: {
    formik: FormikProps<ICustomerAccountDetails>
    setStep: (step: string) => void
    setOpen: (open: boolean) => void
  }) => {
    React.useEffect(() => {
      const mockCustomer = mockCustomerCreation()
      formik.setValues({
        ...formik.values,
        firstName: mockCustomer.firstName,
        lastName: mockCustomer.lastName,
        email: mockCustomer.email,
        phoneNumber: mockCustomer.phoneNumber,
        postalAddress: mockCustomer.postalAddress,
        gender: mockCustomer.gender,
        idType: mockCustomer.idType,
        idValue: mockCustomer.idValue,
        physicalAddress: mockCustomer.physicalAddress,
        country: mockCustomer.country,
        customerPrefix: mockCustomer.customerPrefix,
        dateOfBirth: mockCustomer.dateOfBirth,
        nationality: mockCustomer.nationality,
        customerCategory: mockCustomer.customerCategory,
        customerType: mockCustomer.customerType,
      })
    }, [])
    return (
      <div data-testid="personal-details-step">
        <button
          data-testid="next-to-accounts-button"
          onClick={() => setStep('Link and setup accounts')}
        >
          Next
        </button>
        <button
          data-testid="cancel-personal-button"
          onClick={() => setOpen(false)}
        >
          Cancel
        </button>
      </div>
    )
  },
}))

vi.mock('@/app/customers/customers/Create/Accounts', () => ({
  Accounts: ({
    formik,
    setStep,
    setOpen,
  }: {
    formik: FormikProps<ICustomerAccountDetails>
    setStep: (step: string) => void
    setOpen: (open: boolean) => void
  }) => {
    React.useEffect(() => {
      const mockCustomer = mockCustomerCreation()
      formik.setValues({
        ...formik.values,
        isM247Customer: mockCustomer.isM247Customer,
        customerAccounts: mockCustomer.customerAccounts,
        cif: mockCustomer.cif,
      })
    }, [])
    return (
      <div data-testid="accounts-step">
        <button
          data-testid="back-to-personal-button"
          onClick={() => setStep('Personal Details')}
        >
          Back
        </button>
        <button
          data-testid="next-to-summary-button"
          onClick={() => setStep('Summary')}
        >
          Next
        </button>
        <button
          data-testid="cancel-accounts-button"
          onClick={() => setOpen(false)}
        >
          Cancel
        </button>
      </div>
    )
  },
}))

vi.mock('@/app/customers/customers/Create/Summary', () => ({
  Summary: ({
    formik,
    setStep,
    setOpen,
  }: {
    formik: FormikProps<ICustomerAccountDetails>
    setStep: (step: string) => void
    setOpen: (open: boolean) => void
  }) => (
    <div data-testid="summary-step">
      <button
        data-testid="back-to-accounts-button"
        onClick={() => setStep('Link and setup accounts')}
      >
        Back
      </button>
      <button
        data-testid="submit-form-button"
        onClick={() => formik.handleSubmit()}
      >
        Submit
      </button>
      <input
        data-testid="comments-input"
        name="comments"
        value={formik.values.comments || ''}
        onChange={formik.handleChange}
      />
    </div>
  ),
}))

// Mock the LocalNotification component
vi.mock('@dtbx/ui/components', () => ({
  LocalNotification: ({
    notification,
    notificationType,
    clearNotification,
  }: {
    notification: string
    notificationType: string
    clearNotification: () => void
  }) => {
    if (!notification) return null

    return (
      <div
        data-testid="notification-alert"
        data-type={notificationType}
        role="alert"
      >
        {notification}
        <button
          data-testid="close-notification-button"
          onClick={clearNotification}
          aria-label="Close notification"
        >
          Close
        </button>
      </div>
    )
  },
}))

// We don't need to mock Material UI components since we're using test IDs and roles

describe('CreateCustomerDialog', () => {
  const mockDispatch = vi.fn()

  beforeEach(() => {
    vi.clearAllMocks()
    vi.mocked(useAppDispatch).mockReturnValue(mockDispatch)
    vi.mocked(useAppSelector).mockImplementation((selector) => {
      const mockState = {
        notifications: {
          localNotification: 'Test Notification',
          localNotificationType: 'info',
        },
      }
      return selector(mockState as any)
    })

    // Reset timers
    vi.useFakeTimers()
  })

  afterEach(() => {
    vi.useRealTimers()
  })

  it('renders the create button', () => {
    render(<CreateCustomerDialog />)

    // Check if the button is rendered
    const createButton = screen.getByTestId('create-customer-drawer-button')
    expect(createButton).toBeInTheDocument()
    expect(createButton.textContent).toContain('Create new customer')
  })

  it('opens the drawer when create button is clicked', async () => {
    render(<CreateCustomerDialog />)

    // Click the create button
    await act(async () => {
      fireEvent.click(screen.getByTestId('create-customer-drawer-button'))
    })

    // Check if the drawer is opened
    const drawer = screen.getByTestId('create-customer-drawer')
    expect(drawer).toBeInTheDocument()

    // Check if the title is displayed
    expect(screen.getByText('Add new customer')).toBeInTheDocument()
  })

  it('closes the drawer when close button is clicked', async () => {
    render(<CreateCustomerDialog />)

    // Open the drawer
    await act(async () => {
      fireEvent.click(screen.getByTestId('create-customer-drawer-button'))
    })
    const drawer = screen.getByTestId('create-customer-drawer')
    expect(drawer).toBeInTheDocument()
    expect(screen.getByText('Add new customer')).toBeInTheDocument()
    const closeButton = screen.getByTestId(
      'close-create-customer-drawer-button'
    )
    expect(closeButton).toBeInTheDocument()
    // Click the close button
    await act(async () => {
      fireEvent.click(closeButton)
    })
    // Check if the drawer is closed
    expect(closeButton).not.toBeInTheDocument()
  })

  it('renders the personal details step by default', async () => {
    render(<CreateCustomerDialog />)

    // Open the drawer
    await act(async () => {
      fireEvent.click(screen.getByTestId('create-customer-drawer-button'))
    })

    // Check if the personal details component is rendered
    expect(screen.getByTestId('personal-details-step')).toBeInTheDocument()
  })

  it('navigates through the steps correctly', async () => {
    render(<CreateCustomerDialog />)

    // Open the drawer
    await act(async () => {
      fireEvent.click(screen.getByTestId('create-customer-drawer-button'))
    })

    // Initially, the personal details step should be active
    expect(screen.getByTestId('personal-details-step')).toBeInTheDocument()

    // Navigate to the accounts step
    await act(async () => {
      fireEvent.click(screen.getByTestId('next-to-accounts-button'))
    })

    // Now the accounts step should be active
    expect(screen.getByTestId('accounts-step')).toBeInTheDocument()

    // Navigate to the summary step
    await act(async () => {
      fireEvent.click(screen.getByTestId('next-to-summary-button'))
    })

    // Now the summary step should be active
    expect(screen.getByTestId('summary-step')).toBeInTheDocument()

    // Navigate back to the accounts step
    await act(async () => {
      fireEvent.click(screen.getByTestId('back-to-accounts-button'))
    })

    // Now the accounts step should be active again
    expect(screen.getByTestId('accounts-step')).toBeInTheDocument()

    // Navigate back to the personal details step
    await act(async () => {
      fireEvent.click(screen.getByTestId('back-to-personal-button'))
    })

    // Now the personal details step should be active again
    expect(screen.getByTestId('personal-details-step')).toBeInTheDocument()
  })

  it('submits the form with valid data', async () => {
    // Mock the createCustomerAccount to return success
    vi.mocked(createCustomerAccount).mockResolvedValueOnce('success')

    render(<CreateCustomerDialog />)

    // Open the drawer
    await act(async () => {
      fireEvent.click(screen.getByTestId('create-customer-drawer-button'))
    })

    const drawer = screen.getByTestId('create-customer-drawer')
    expect(drawer).toBeInTheDocument()
    // Navigate to the summary step
    expect(screen.getByTestId('next-to-accounts-button')).toBeInTheDocument()

    await act(async () => {
      fireEvent.click(screen.getByTestId('next-to-accounts-button'))
    })

    expect(screen.getByTestId('next-to-summary-button')).toBeInTheDocument()
    await act(async () => {
      fireEvent.click(screen.getByTestId('next-to-summary-button'))
    })

    // Fill in the required comments field
    expect(screen.getByTestId('comments-input')).toBeInTheDocument()

    const commentsInput = screen.getByTestId('comments-input')
    await act(async () => {
      fireEvent.change(commentsInput, { target: { value: 'Test comments' } })
    })

    // Submit the form
    await act(async () => {
      fireEvent.click(screen.getByTestId('submit-form-button'))
    })

    // Create the expected mock customer data
    const mockCustomer = mockCustomerCreation({ comments: 'Test comments' })
    console.log('mockCustomer', mockCustomer)

    // Check if createCustomerAccount was called with the correct data
    expect(createCustomerAccount).toHaveBeenCalledWith({
      account: expect.objectContaining({
        comments: 'Test comments',
      }),
      dispatch: mockDispatch,
    })
    // Check if the drawer was closed after successful submission
    expect(drawer).not.toBeInTheDocument()
    // expect(screen.queryByRole('dialog')).not.toBeInTheDocument()
  })

  it('displays notification when available', async () => {
    render(<CreateCustomerDialog />)

    // Open the drawer
    await act(async () => {
      fireEvent.click(screen.getByTestId('create-customer-drawer-button'))
    })

    // Check if the notification is displayed
    const notification = screen.getByTestId('notification-alert')
    expect(notification).toBeInTheDocument()
    expect(notification).toHaveAttribute('data-type', 'info')
    expect(notification.textContent).toContain('Test Notification')
  })

  it('clears notification when close button is clicked', async () => {
    render(<CreateCustomerDialog />)

    // Open the drawer
    await act(async () => {
      fireEvent.click(screen.getByTestId('create-customer-drawer-button'))
    })

    // Wait for the notification to be rendered
    expect(screen.getByTestId('close-notification-button')).toBeInTheDocument()

    // Click the close notification button
    await act(async () => {
      fireEvent.click(screen.getByTestId('close-notification-button'))
    })
    // Check if clearNotification was called
    expect(clearNotification).toHaveBeenCalled()
    expect(mockDispatch).toHaveBeenCalledWith(clearNotification())
  })

  it('handles different notification types', async () => {
    // Mock the notification state with different types
    vi.mocked(useAppSelector).mockImplementation((selector) => {
      const mockState = {
        notifications: {
          localNotification: 'Error notification',
          localNotificationType: 'error',
        },
      }
      return selector(mockState as any)
    })

    render(<CreateCustomerDialog />)

    // Open the drawer
    await act(async () => {
      fireEvent.click(screen.getByTestId('create-customer-drawer-button'))
    })

    // Check if the notification is displayed with the correct type
    const notification = screen.getByTestId('notification-alert')
    expect(notification).toBeInTheDocument()
    expect(notification).toHaveAttribute('data-type', 'error')
    expect(notification.textContent).toContain('Error notification')
  })

  it('cancels the form from personal details step', async () => {
    render(<CreateCustomerDialog />)

    // Open the drawer
    await act(async () => {
      fireEvent.click(screen.getByTestId('create-customer-drawer-button'))
    })

    // Wait for the personal details step to be rendered
    expect(screen.getByTestId('cancel-personal-button')).toBeInTheDocument()

    // Click the cancel button
    await act(async () => {
      fireEvent.click(screen.getByTestId('cancel-personal-button'))
    })

    // Check if the drawer was closed
    expect(screen.queryByRole('create-customer-drawer')).not.toBeInTheDocument()
  })

  it('cancels the form from accounts step', async () => {
    render(<CreateCustomerDialog />)

    // Open the drawer
    await act(async () => {
      fireEvent.click(screen.getByTestId('create-customer-drawer-button'))
    })

    // Navigate to the accounts step
    expect(screen.getByTestId('next-to-accounts-button')).toBeInTheDocument()

    await act(async () => {
      fireEvent.click(screen.getByTestId('next-to-accounts-button'))
    })

    // Wait for the accounts step to be rendered
    expect(screen.getByTestId('cancel-accounts-button')).toBeInTheDocument()

    // Click the cancel button
    await act(async () => {
      fireEvent.click(screen.getByTestId('cancel-accounts-button'))
    })

    // Check if the drawer was closed
    expect(screen.queryByRole('create-customer-drawer')).not.toBeInTheDocument()
  })

  it('handles form validation errors', async () => {
    //render the drawer content
    render(<CreateCustomerDialog />)

    // Open the drawer
    await act(async () => {
      fireEvent.click(screen.getByTestId('create-customer-drawer-button'))
    })

    // Navigate to the summary step
    expect(screen.getByTestId('next-to-accounts-button')).toBeInTheDocument()

    await act(async () => {
      fireEvent.click(screen.getByTestId('next-to-accounts-button'))
    })

    expect(screen.getByTestId('next-to-summary-button')).toBeInTheDocument()

    await act(async () => {
      fireEvent.click(screen.getByTestId('next-to-summary-button'))
    })

    // Submit the form without filling required fields
    expect(screen.getByTestId('submit-form-button')).toBeInTheDocument()

    await act(async () => {
      fireEvent.click(screen.getByTestId('submit-form-button'))
    })

    // Check that createCustomerAccount was not called
    expect(createCustomerAccount).not.toHaveBeenCalled()
  })

  it('handles API errors during form submission', async () => {
    // Mock the createCustomerAccount to return an error
    vi.mocked(createCustomerAccount).mockResolvedValueOnce('failed')

    // Mock the notification state to show an error
    vi.mocked(useAppSelector).mockImplementation((selector) => {
      const mockedState = {
        notifications: {
          localNotification: 'API Error',
          localNotificationType: 'error',
        },
      }
      return selector(mockedState as any)
    })

    render(<CreateCustomerDialog />)

    // Open the drawer
    await act(async () => {
      fireEvent.click(screen.getByTestId('create-customer-drawer-button'))
    })

    const drawer = screen.getByText('Add new customer')
    // Navigate to the summary step
    expect(screen.getByTestId('next-to-accounts-button')).toBeInTheDocument()

    await act(async () => {
      fireEvent.click(screen.getByTestId('next-to-accounts-button'))
    })

    expect(screen.getByTestId('next-to-summary-button')).toBeInTheDocument()

    fireEvent.click(screen.getByTestId('next-to-summary-button'))

    // Fill in the required comments field
    expect(screen.getByTestId('comments-input')).toBeInTheDocument()

    const commentsInput = screen.getByTestId('comments-input')
    fireEvent.change(commentsInput, { target: { value: 'Test comments' } })

    // Submit the form
    await act(async () => {
      fireEvent.click(screen.getByTestId('submit-form-button'))
    })

    // Check if createCustomerAccount was called
    expect(createCustomerAccount).toHaveBeenCalled()

    // Check if the drawer remains open (since submission returned 'error')
    expect(drawer).toBeInTheDocument()

    // Check if the error notification is displayed
    const notification = screen.getByTestId('notification-alert')
    expect(notification).toBeInTheDocument()
    expect(notification).toHaveAttribute('data-type', 'error')
    expect(notification.textContent).toContain('API Error')
  })
})
