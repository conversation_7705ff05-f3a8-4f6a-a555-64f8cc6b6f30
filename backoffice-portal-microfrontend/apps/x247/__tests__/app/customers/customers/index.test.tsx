import { describe, it, expect, vi, beforeEach } from 'vitest'
import { render, screen, fireEvent } from '../test-utils'
import CustomersListPage from '@/app/customers/customers'
import type { RootState } from '@/store'
import type React from 'react'
import { AntTab } from '@dtbx/ui/components/Tabs'

// Define interfaces for the mock state
interface ApprovalResponse {
  totalElements: number | null
}

interface ApprovalRequestsState {
  approvalRequestResponse: ApprovalResponse | null
  pendingCustomerApprovalRequestResponse: ApprovalResponse | null
}

// Mock the dependencies
vi.mock('@/store', () => ({
  useAppSelector: vi.fn(),
}))

vi.mock('@/app/customers/customers/CustomerLists/CustomerList', () => ({
  default: () => <div data-testid="customer-list">Customer List Component</div>,
}))

vi.mock('@/app/customers/customers/Requests/Requests', () => ({
  default: () => <div data-testid="requests">Requests Component</div>,
}))

vi.mock('@/app/customers/customers/Pending/CustomerList', () => ({
  default: () => <div data-testid="pending-list">Pending List Component</div>,
}))
import { useAppSelector } from '@/store'

describe('CustomersListPage', () => {
  // Default mock state
  const defaultMockState = {
    approvalRequests: {
      approvalRequestResponse: {
        totalElements: 5,
      },
      pendingCustomerApprovalRequestResponse: {
        totalElements: 3,
      },
    },
  }

  beforeEach(() => {
    vi.clearAllMocks()
    // Set up the default mock implementation
    vi.mocked(useAppSelector).mockImplementation(
      (selector: (state: RootState) => any) =>
        selector(defaultMockState as unknown as RootState)
    )
  })

  it('renders the component with default tab selected', () => {
    render(<CustomersListPage />)

    // Check if tabs are rendered
    expect(screen.getByTestId('ant-tabs')).toBeInTheDocument()
    expect(screen.getByTestId('tab-All Customers')).toBeInTheDocument()
    expect(screen.getByTestId('tab-Pending Creation')).toBeInTheDocument()
    expect(screen.getByTestId('tab-Approval Requests')).toBeInTheDocument()

    // Check if the first tab panel is visible and others are hidden
    const panel0 = screen.getByTestId('tab-panel-0')
    const panel1 = screen.getByTestId('tab-panel-1')
    const panel2 = screen.getByTestId('tab-panel-2')

    expect(panel0).not.toHaveAttribute('hidden')
    expect(panel1).toHaveAttribute('hidden')
    expect(panel2).toHaveAttribute('hidden')

    // Check if the List component is rendered in the first tab
    expect(screen.getByTestId('customer-list')).toBeInTheDocument()
  })

  it('displays correct counts in tab chips', () => {
    render(<CustomersListPage />)

    // Check if the counts are displayed correctly
    const pendingTab = screen.getByTestId('tab-Pending Creation')
    const approvalTab = screen.getByTestId('tab-Approval Requests')

    expect(pendingTab.textContent).toContain('3')
    expect(approvalTab.textContent).toContain('5')
  })

  it('handles empty counts correctly', () => {
    // Mock state with empty counts
    const emptyCountState: Partial<ApprovalRequestsState> = {
      approvalRequestResponse: {
        totalElements: 0,
      },
      pendingCustomerApprovalRequestResponse: {
        totalElements: 0,
      },
    }

    vi.mocked(useAppSelector).mockImplementation(() => emptyCountState)

    render(<CustomersListPage />)

    // Check if empty counts are handled correctly
    const pendingTab = screen.getByTestId('tab-Pending Creation')
    const approvalTab = screen.getByTestId('tab-Approval Requests')

    expect(pendingTab.textContent).toContain('')
    expect(approvalTab.textContent).toContain('')
  })

  it('handles null response objects correctly', () => {
    // Mock state with null response objects
    const nullResponseState: Partial<ApprovalRequestsState> = {
      approvalRequestResponse: null,
      pendingCustomerApprovalRequestResponse: null,
    }

    vi.mocked(useAppSelector).mockImplementation(() => nullResponseState)

    render(<CustomersListPage />)

    // Component should render without errors
    expect(screen.getByTestId('ant-tabs')).toBeInTheDocument()
  })

  it('switches tabs correctly when clicked', () => {
    render(<CustomersListPage />)

    // Get the tab elements
    const allCustomersTab = screen.getByTestId('tab-All Customers')
    const pendingTab = screen.getByTestId('tab-Pending Creation')
    const approvalTab = screen.getByTestId('tab-Approval Requests')

    // Initially, the first tab should be selected
    expect(screen.getByTestId('tab-panel-0')).not.toHaveAttribute('hidden')
    expect(screen.getByTestId('customer-list')).toBeInTheDocument()

    // Click on the second tab
    fireEvent.click(pendingTab)

    // Now the second tab should be selected
    expect(screen.getByTestId('tab-panel-0')).toHaveAttribute('hidden')
    expect(screen.getByTestId('tab-panel-1')).not.toHaveAttribute('hidden')
    expect(screen.getByTestId('pending-list')).toBeInTheDocument()

    // Click on the third tab
    fireEvent.click(approvalTab)

    // Now the third tab should be selected
    expect(screen.getByTestId('tab-panel-0')).toHaveAttribute('hidden')
    expect(screen.getByTestId('tab-panel-1')).toHaveAttribute('hidden')
    expect(screen.getByTestId('tab-panel-2')).not.toHaveAttribute('hidden')
    expect(screen.getByTestId('requests')).toBeInTheDocument()

    // Click back to the first tab
    fireEvent.click(allCustomersTab)

    // Now the first tab should be selected again
    expect(screen.getByTestId('tab-panel-0')).not.toHaveAttribute('hidden')
    expect(screen.getByTestId('tab-panel-1')).toHaveAttribute('hidden')
    expect(screen.getByTestId('tab-panel-2')).toHaveAttribute('hidden')
    expect(screen.getByTestId('customer-list')).toBeInTheDocument()
  })

  it('renders the correct component for each tab', () => {
    render(<CustomersListPage />)

    expect(screen.getByTestId('customer-list')).toBeInTheDocument()
    expect(screen.getByTestId('tab-panel-0')).toHaveStyle({ display: 'block' })

    // The other components should be rendered but hidden
    expect(screen.getByTestId('tab-panel-1')).toHaveStyle({ display: 'none' })
    expect(screen.getByTestId('tab-panel-2')).toHaveStyle({ display: 'none' })

    // Switch to the second tab
    fireEvent.click(screen.getByTestId('tab-Pending Creation'))

    // Now the Pending component should be visible
    expect(screen.getByTestId('tab-panel-0')).toHaveStyle({ display: 'none' })
    expect(screen.getByTestId('tab-panel-1')).toHaveStyle({ display: 'block' })
    expect(screen.getByTestId('tab-panel-2')).toHaveStyle({ display: 'none' })
    expect(screen.getByTestId('pending-list')).toBeInTheDocument()

    // Switch to the third tab
    fireEvent.click(screen.getByTestId('tab-Approval Requests'))

    // Now the Requests component should be visible
    expect(screen.getByTestId('tab-panel-0')).toHaveStyle({ display: 'none' })
    expect(screen.getByTestId('tab-panel-1')).toHaveStyle({ display: 'none' })
    expect(screen.getByTestId('tab-panel-2')).toHaveStyle({ display: 'block' })
    expect(screen.getByTestId('requests')).toBeInTheDocument()
  })

  it('handles large count numbers correctly', () => {
    // Mock state with large count numbers
    const largeCountState: Partial<ApprovalRequestsState> = {
      approvalRequestResponse: {
        totalElements: 999,
      },
      pendingCustomerApprovalRequestResponse: {
        totalElements: 1000,
      },
    }

    vi.mocked(useAppSelector).mockImplementation(() => largeCountState)

    render(<CustomersListPage />)

    // Check if large counts are displayed correctly
    const pendingTab = screen.getByTestId('tab-Pending Creation')
    const approvalTab = screen.getByTestId('tab-Approval Requests')

    expect(pendingTab.textContent).toContain('1000')
    expect(approvalTab.textContent).toContain('999')
  })
})
