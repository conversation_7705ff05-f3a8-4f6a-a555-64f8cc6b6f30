import {
  ICustomer,
  StoreOfValue,
  IprofileAccountStoreIds,
} from '@/store/interfaces'

export function createMockCustomer(
  overrides: Partial<ICustomer> = {}
): ICustomer {
  const id = overrides.id || '123'

  return {
    id,
    blockReason: '',
    country: 'USA',
    firstName: 'John',
    otherNames: 'Middle',
    lastName: 'Doe',
    phoneNumber: '+**********',
    accountNumber: 'ACC123456',
    email: '<EMAIL>',
    nationality: 'American',
    idNumber: 'ID12345',
    idType: 'National ID',
    onboardingType: 'Digital',
    pinStatus: 'Active_PIN',
    isBlocked: false,
    dateCreated: '2023-01-01T12:00:00Z',
    dateModified: '2023-01-15T14:30:00Z',
    sex: 'Male',
    postalAddress: 'P.O Box 12345, City',
    storeOfValues: [
      {
        storeCode: 'STORE001',
        customerId: id,
      },
    ],
    profileAccountStoreIds: [
      {
        profileId: id,
        storeCode: 'STORE001',
        description: 'Main Store',
        customerId: id,
      },
    ],
    ...overrides,
  }
}

// Helper functions for nested objects
export function createMockStoreOfValue(
  overrides: Partial<StoreOfValue> = {}
): StoreOfValue {
  return {
    storeCode: 'STORE001',
    customerId: '123',
    ...overrides,
  }
}

export function createMockProfileAccountStoreId(
  overrides: Partial<IprofileAccountStoreIds> = {}
): IprofileAccountStoreIds {
  return {
    profileId: '123',
    storeCode: 'STORE001',
    description: 'Main Store',
    customerId: '123',
    ...overrides,
  }
}
