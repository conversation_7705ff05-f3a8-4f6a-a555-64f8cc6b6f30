import { vi, expect, describe, it, beforeEach, Mock } from 'vitest'
import { secureapi } from '@dtbx/store/utils'
import { Dispatch } from 'redux'
import * as apiFunctions from '@/store/actions'
import {
  setLoadingState,
  setTariffConfig,
  setTariffs,
  setTariffsSummary,
  setConfigurableServices,
  setSelectedTariffState,
  setTariffIsError,
  setLoadingApprovals,
} from '@/store/reducers'
import { setNotification } from '@dtbx/store/reducers'

import { IConfiguredService, Service } from '@/store/interfaces'

vi.mock('@dtbx/store/utils', () => ({
  secureapi: {
    get: vi.fn(),
    post: vi.fn(),
    put: vi.fn(),
    patch: vi.fn(),
  },
}))

const mockDispatch: Dispatch = vi.fn()

describe.skip('Tariff API Actions', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('getAllTariffs', () => {
    const mockPagination = { page: 2, size: 20, totalPages: 5 }

    it('should fetch tariffs successfully', async () => {
      const mockResponse = { data: [{ id: '1' }], totalElements: 10 }
      ;(secureapi.get as Mock).mockResolvedValue({ data: mockResponse })

      await apiFunctions.getAllTariffs(mockDispatch, mockPagination)

      expect(secureapi.get).toHaveBeenCalledWith(
        '/backoffice-bff/fee/tariffs?page=2&size=20'
      )
      expect(mockDispatch).toHaveBeenCalledWith(setLoadingState(true))
      expect(mockDispatch).toHaveBeenCalledWith(setTariffs(mockResponse.data))
      expect(mockDispatch).toHaveBeenCalledWith(
        setTariffsSummary(expect.objectContaining({ totalElements: 10 }))
      )
      expect(mockDispatch).toHaveBeenCalledWith(setLoadingState(false))
    })

    it('should handle errors', async () => {
      const mockError = new Error('Network error')
      ;(secureapi.get as Mock).mockRejectedValue(mockError)

      await apiFunctions.getAllTariffs(mockDispatch)

      expect(mockDispatch).toHaveBeenCalledWith(
        setNotification({ message: 'Network error', type: 'error' })
      )
      expect(mockDispatch).toHaveBeenCalledWith(setLoadingState(false))
    })
  })

  describe('getTariffConfigs', () => {
    const mockParams = {
      serviceCode: 'SVC1',
      currency: 'USD',
      countryCode: 'US',
      tariff: 'T1',
    }

    it('should fetch configs with headers', async () => {
      const mockResponse = { data: [{ id: '1' }] }
      ;(secureapi.get as Mock).mockResolvedValue({ data: mockResponse })

      await apiFunctions.getTariffConfigs(mockParams, mockDispatch)

      expect(secureapi.get).toHaveBeenCalledWith(
        '/backoffice-bff/fee/service-configuration',
        expect.objectContaining({
          headers: { 'Content-Type': 'application/json' },
          data: mockParams,
        })
      )
      expect(mockDispatch).toHaveBeenCalledWith(setTariffs(mockResponse.data))
    })
  })

  describe('createServiceConfig', () => {
    const mockData = [{ serviceId: 1 }] as any

    it('should create config with "make" type', async () => {
      ;(secureapi.post as Mock).mockResolvedValue({})

      await apiFunctions.createServiceConfig(mockData, 'make', mockDispatch)

      expect(secureapi.post).toHaveBeenCalledWith(
        '/backoffice-bff/fee/service-configuration/make',
        {
          serviceConfigurations: mockData,
          comments: 'creating a service configuration',
        }
      )
      expect(mockDispatch).toHaveBeenCalledWith(
        setNotification({
          message:
            'Service configuration successfully created , awaiting approval',
          type: 'success',
        })
      )
    })

    it('should handle validation errors', async () => {
      ;(secureapi.post as Mock).mockRejectedValue(
        new Error('Validation failed')
      )

      await apiFunctions.createServiceConfig([], 'invalid-type', mockDispatch)

      expect(mockDispatch).toHaveBeenCalledWith(
        setNotification(expect.objectContaining({ type: 'error' }))
      )
    })
  })

  describe('deactivateServiceConfiguration', () => {
    it('should use PATCH for non-make type', async () => {
      ;(secureapi.patch as Mock).mockResolvedValue({})

      await apiFunctions.deactivateServiceConfiguration(
        '123',
        mockDispatch,
        'test',
        'update'
      )

      expect(secureapi.patch).toHaveBeenCalled()
      expect(mockDispatch).toHaveBeenCalledWith(
        setNotification(expect.objectContaining({ type: 'success' }))
      )
    })

    it('should use POST for make type', async () => {
      ;(secureapi.post as Mock).mockResolvedValue({})

      await apiFunctions.deactivateServiceConfiguration(
        '123',
        mockDispatch,
        'test',
        'make'
      )

      expect(secureapi.post).toHaveBeenCalled()
    })
  })

  describe('createTariffByName', () => {
    it('should create tariff and update state', async () => {
      ;(secureapi.post as Mock).mockResolvedValue({})

      await apiFunctions.createTariffByName(
        mockDispatch,
        'comment',
        'T1',
        'USD'
      )

      expect(mockDispatch).toHaveBeenCalledWith(setTariffIsError(false))
      expect(mockDispatch).toHaveBeenCalledWith(
        setSelectedTariffState({ name: 'T1', currency: 'USD' })
      )
    })
  })

  // Edge Case Tests
  describe('Edge Cases', () => {
    it('should handle empty response in getTariffConfigByTariffCurrency', async () => {
      ;(secureapi.get as Mock).mockResolvedValue({ data: null })

      await apiFunctions.getTariffConfigByTariffCurrency(
        { tariff: 'T1', currency: 'USD' },
        mockDispatch
      )

      expect(mockDispatch).toHaveBeenCalledWith(setTariffConfig(null))
    })

    it('should handle special characters in approveUpdateServiceConfiguration', async () => {
      const specialComments = 'Test & Comments <html>'
      ;(secureapi.put as Mock).mockResolvedValue({})

      await apiFunctions.approveUpdateServiceConfiguration(
        '123',
        mockDispatch,
        specialComments,
        'approve'
      )

      expect(secureapi.put).toHaveBeenCalledWith(expect.any(String), {
        comments: specialComments,
      })
    })

    it('should handle pagination edge cases in getAllTariffs', async () => {
      await apiFunctions.getAllTariffs(mockDispatch, {
        page: -1,
        size: 1000,
      } as any)

      expect(secureapi.get).toHaveBeenCalledWith(
        '/backoffice-bff/fee/tariffs?page=-1&size=1000'
      )
    })
  })

  describe('acceptRejectConfigCreate', () => {
    it('should dispatch success notification when configuration is approved', async () => {
      const mockDispatch = vi.fn()
      const mockId = '123'
      const mockComments = 'Approved'

      vi.spyOn(secureapi, 'put').mockResolvedValueOnce({})

      await apiFunctions.acceptRejectConfigCreate(
        'approve',
        mockId,
        mockDispatch,
        mockComments
      )

      expect(secureapi.put).toHaveBeenCalledWith(
        `/backoffice-bff/fee/service-configuration/approve/${mockId}`,
        { comments: mockComments }
      )

      expect(mockDispatch).toHaveBeenCalledWith(setLoadingState(true))
      expect(mockDispatch).toHaveBeenCalledWith(setLoadingState(false))
      expect(mockDispatch).toHaveBeenCalledWith(setLoadingApprovals(true))
      expect(mockDispatch).toHaveBeenCalledWith(setLoadingApprovals(false))

      expect(mockDispatch).toHaveBeenCalledWith(
        setNotification({
          message: 'Request successfully approved',
          type: 'success',
        })
      )
    })
    it('should dispatch error notification when API call fails', async () => {
      const mockDispatch = vi.fn()
      const mockId = '123'
      const mockComments = 'Approved'
      const mockError = new Error('API Error')

      vi.spyOn(secureapi, 'put').mockRejectedValueOnce(mockError)

      await apiFunctions.acceptRejectConfigCreate(
        'approve',
        mockId,
        mockDispatch,
        mockComments
      )

      expect(secureapi.put).toHaveBeenCalledWith(
        `/backoffice-bff/fee/service-configuration/approve/${mockId}`,
        { comments: mockComments }
      )

      expect(mockDispatch).toHaveBeenCalledWith(setLoadingState(true))
      expect(mockDispatch).toHaveBeenCalledWith(setLoadingState(false))
      expect(mockDispatch).toHaveBeenCalledWith(setLoadingApprovals(false))

      expect(mockDispatch).toHaveBeenCalledWith(
        setNotification({
          message: 'API Error',
          type: 'error',
        })
      )
    })
  })

  describe('approveRejectServiceConfig', () => {
    it('should approve service config deactivation', async () => {
      ;(secureapi.post as Mock).mockResolvedValue({})

      await apiFunctions.approveRejectServiceConfig(
        'CONFIG-456',
        'Approval comments',
        mockDispatch,
        'approve'
      )

      expect(secureapi.post).toHaveBeenCalledWith(
        '/backoffice-bff/fee/service-configuration/deactivate/approve/CONFIG-456',
        { comments: 'Approval comments' }
      )
      expect(mockDispatch).toHaveBeenCalledWith(
        setNotification({
          message: 'Service configuration successfully approved',
          type: 'success',
        })
      )
    })

    it('should handle rejection with special characters', async () => {
      ;(secureapi.post as Mock).mockResolvedValue({})

      await apiFunctions.approveRejectServiceConfig(
        'CONFIG-456',
        'Reject <script>',
        mockDispatch,
        'reject'
      )

      expect(secureapi.post).toHaveBeenCalledWith(
        '/backoffice-bff/fee/service-configuration/deactivate/reject/CONFIG-456',
        { comments: 'Reject <script>' }
      )
    })
  })

  describe('updateServiceConfig', () => {
    const mockData = { serviceId: 1, config: {} } as any

    it('should update config without approval', async () => {
      ;(secureapi.post as Mock).mockResolvedValue({})

      await apiFunctions.updateServiceConfig(
        'super',
        'CONFIG-789',
        mockData,
        mockDispatch
      )

      expect(secureapi.post).toHaveBeenCalledWith(
        '/backoffice-bff/fee/service-configuration/CONFIG-789/update/',
        mockData
      )
      expect(mockDispatch).toHaveBeenCalledWith(
        setNotification({
          message: 'Service configuration updated successfully ',
          type: 'success',
        })
      )
    })

    it('should dispatch success notification with pending approval message when type is make', async () => {
      const mockDispatch = vi.fn()
      const mockSecureApiPost = vi
        .spyOn(secureapi, 'post')
        .mockResolvedValue({})

      const type = 'make'
      const serviceConfigurationId = '123'
      const data: IConfiguredService = {
        feeConfiguration: {
          exciseDuty: 0,
          value: 0,
        },
      }

      await apiFunctions.updateServiceConfig(
        type,
        serviceConfigurationId,
        data,
        mockDispatch
      )

      expect(mockSecureApiPost).toHaveBeenCalledWith(
        '/backoffice-bff/fee/service-configuration/123/update/make',
        data
      )

      expect(mockDispatch).toHaveBeenCalledWith(setLoadingState(true))
      expect(mockDispatch).toHaveBeenCalledWith(setLoadingState(false))
      expect(mockDispatch).toHaveBeenCalledWith(
        setNotification({
          message:
            'Service configuration updated successfully, pending approval ',
          type: 'success',
        })
      )
    })
  })

  describe('approveUpdateServiceConfiguration', () => {
    it('should approve configuration update', async () => {
      ;(secureapi.put as Mock).mockResolvedValue({})

      await apiFunctions.approveUpdateServiceConfiguration(
        'UPDATE-123',
        mockDispatch,
        'Approval comments',
        'approve'
      )

      expect(secureapi.put).toHaveBeenCalledWith(
        '/backoffice-bff/fee/service-configuration/update/approve/UPDATE-123',
        { comments: 'Approval comments' }
      )
    })

    it('should reject configuration update', async () => {
      ;(secureapi.put as Mock).mockResolvedValue({})

      await apiFunctions.approveUpdateServiceConfiguration(
        'UPDATE-123',
        mockDispatch,
        'Rejection comments',
        'reject'
      )

      expect(secureapi.put).toHaveBeenCalledWith(
        '/backoffice-bff/fee/service-configuration/update/reject/UPDATE-123',
        { comments: 'Rejection comments' }
      )
    })
  })

  describe('getConfigurableServices', () => {
    it('should dispatch loading state and set configurable services when API call succeeds', async () => {
      const mockDispatch = vi.fn()
      const mockTariff = 'STANDARD'
      const mockCurrency = 'USD'
      const mockResponseData: Service[] = [
        {
          id: '1',
          createdOn: '2022-01-01',
          modifiedOn: '2022-01-01',
          serviceId: '1',
          serviceCode: 'CODE1',
          serviceName: 'Service 1',
          country: 'USA',
          serviceType: 'PAYMENT',
        },
        {
          id: '2',
          createdOn: '2022-01-02',
          modifiedOn: '2022-01-02',
          serviceId: '2',
          serviceCode: 'CODE2',
          serviceName: 'Service 2',
          country: 'USA',
          serviceType: 'NOTIFICATION',
        },
      ]

      vi.spyOn(secureapi, 'get').mockResolvedValueOnce({
        data: mockResponseData,
      })

      await apiFunctions.getConfigurableServices(
        mockTariff,
        mockCurrency,
        mockDispatch
      )

      expect(mockDispatch).toHaveBeenCalledWith(setLoadingState(true))
      expect(secureapi.get).toHaveBeenCalledWith(
        `/backoffice-bff/fee/payment-service/configurable/tariff/${mockTariff}/currency/${mockCurrency}`
      )
      expect(mockDispatch).toHaveBeenCalledWith(
        setConfigurableServices(mockResponseData)
      )
    })
    it('should handle empty response data from API correctly', async () => {
      const mockDispatch = vi.fn()
      const mockTariff = 'STANDARD'
      const mockCurrency = 'EUR'
      const emptyResponseData: Service[] = []

      vi.spyOn(secureapi, 'get').mockResolvedValueOnce({
        data: emptyResponseData,
      })

      await apiFunctions.getConfigurableServices(
        mockTariff,
        mockCurrency,
        mockDispatch,
        'PAYMENT'
      )

      expect(mockDispatch).toHaveBeenCalledWith(setLoadingState(true))
      expect(secureapi.get).toHaveBeenCalledWith(
        `/backoffice-bff/fee/payment-service/configurable/tariff/${mockTariff}/currency/${mockCurrency}?serviceType=PAYMENT`
      )
      expect(mockDispatch).toHaveBeenCalledWith(
        setConfigurableServices(emptyResponseData)
      )
    })
  })

  describe('getTariffConfigByTariffCurrency', () => {
    it('should fetch tariff configuration data and dispatch setTariffConfig when API call succeeds', async () => {
      const mockDispatch = vi.fn()
      const mockResponse = {
        data: {
          tariffData: 'test data',
          data: [],
          pageNumber: 1,
          pageSize: 20,
          totalNumberOfPages: 1,
          totalElements: 1,
        },
      }
      vi.spyOn(secureapi, 'get').mockResolvedValueOnce(mockResponse)

      const params = {
        tariff: 'testTariff',
        currency: 'USD',
        page: 1,
        size: 20,
        chargeType: 'testType',
      }

      await apiFunctions.getTariffConfigByTariffCurrency(params, mockDispatch)

      expect(secureapi.get).toHaveBeenCalledWith(
        `/backoffice-bff/fee/service-configuration/tariff/testTariff/currency/USD?chargeType=testType&page=1&size=20`
      )
      expect(mockDispatch).toHaveBeenCalledTimes(3)
      expect(mockDispatch).toHaveBeenNthCalledWith(1, setLoadingState(true))
      expect(mockDispatch).toHaveBeenNthCalledWith(
        2,
        setTariffConfig(mockResponse.data)
      )
      expect(mockDispatch).toHaveBeenNthCalledWith(3, setLoadingState(false))
    })

    it('should handle empty chargeType', async () => {
      ;(secureapi.get as Mock).mockResolvedValue({ data: {} })

      await apiFunctions.getTariffConfigByTariffCurrency(
        { tariff: 'T1', currency: 'USD' },
        mockDispatch
      )

      expect(secureapi.get).toHaveBeenCalledWith(
        '/backoffice-bff/fee/service-configuration/tariff/T1/currency/USD?chargeType=&page=0&size=10'
      )
    })
  })
})
