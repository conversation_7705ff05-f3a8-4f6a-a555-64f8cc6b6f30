import { vi, it, expect, describe, beforeEach, afterEach, Mock } from 'vitest'
import {
  setCustomers,
  setCustomersSuccess,
  setCustomersLoading,
  setChangeTab,
  setCustomersFailure,
  setCustomerLoading,
  setCustomer,
  setCustomerAccountsList,
  setCustomerFailure,
  setCustomerSuccess,
  setDevices,
  setDevicesFailure,
  setDevicesLoading,
  setDevicesSuccess,
  setDevice,
  setDeviceFailure,
  setDeviceLoading,
  setDeviceSuccess,
  setDeviceLogs,
  setIsLoadingDeviceLogs,
  setIsSuccessfulDeviceLogs,
  setCustomerAccountDetails,
  setCustomerProfileExists,
  setCustomerAccountSearch,
  setSelectedCustomer,
} from '@/store/reducers'
import { setNotification } from '@dtbx/store/reducers'
import {
  acceptCreateCustomer,
  acceptDeactivateCustomer,
  accountType,
  approveUpdateCustomer,
  deactivateCustomer,
  deactivateCustomerDevice,
  fetchCustomerAccount,
  getAllCustomers,
  getCustomerDeviceDetail,
  getCustomerDeviceHistory,
  getCustomerDevices,
  getCustomerProfile,
  getCustomerProfileById,
  makerUpdateCustomer,
  rejectCreateCustomer,
  rejectUpdateCustomer,
  resetCustomerAccountDetails,
  updateCustomerDetails,
} from '@/store/actions'
import { ICustomer } from '@/store/interfaces'
import { HasAccessToRights } from '@dtbx/store/utils'

vi.mock('@dtbx/store/utils', () => ({
  secureapi2: {
    get: vi.fn(),
    patch: vi.fn(),
  },
  HasAccessToRights: vi.fn(),
}))

interface MockAction {
  type: string
  payload?: any
}
import { secureapi2 } from '@dtbx/store/utils'
import { createMockCustomer } from './stubs'

describe('customers', () => {
  //Get Customer Actions
  //TODO: Create a stub for customers
  describe('getAllCustomers', () => {
    const mockDispatch = vi.fn()
    const mockFilters = {
      page: 1,
      size: 5,
      firstName: 'John',
      lastName: 'Doe',
    }
    beforeEach(() => {
      vi.clearAllMocks()
    })
    afterEach(() => {
      vi.restoreAllMocks()
    })

    it('should construct the correct URL and dispatch actions', async () => {
      const mockResponse = { data: [{ id: 1, name: 'Customer 1' }] }
      const mockAxiosGetHandler = vi
        .spyOn(secureapi2, 'get')
        .mockResolvedValue(mockResponse)

      await getAllCustomers(mockFilters, mockDispatch)

      expect(mockAxiosGetHandler).toHaveBeenCalledWith(
        expect.stringContaining('page=1&size=5&firstName=John&lastName=Doe')
      )

      expect(mockDispatch).toHaveBeenNthCalledWith(1, setCustomersLoading(true))
      expect(mockDispatch).toHaveBeenNthCalledWith(
        2,
        setCustomersSuccess(false)
      )
      expect(mockDispatch).toHaveBeenNthCalledWith(
        3,
        setCustomersFailure(false)
      )
      expect(mockDispatch).toHaveBeenNthCalledWith(4, setChangeTab(0))
      expect(mockDispatch).toHaveBeenNthCalledWith(
        5,
        setCustomers(mockResponse.data)
      )
      expect(mockDispatch).toHaveBeenNthCalledWith(6, setCustomersSuccess(true))
      expect(mockDispatch).toHaveBeenNthCalledWith(
        7,
        setCustomersLoading(false)
      )
    })

    it('should handle API errors correctly', async () => {
      const mockError = new Error('Network Error')
      // ;(secureapi2.get as any).mockRejectedValueOnce(mockError)
      vi.spyOn(secureapi2, 'get').mockRejectedValueOnce(mockError)

      await getAllCustomers(mockFilters, mockDispatch)

      expect(mockDispatch).toHaveBeenNthCalledWith(1, setCustomersLoading(true))
      expect(mockDispatch).toHaveBeenNthCalledWith(
        2,
        setCustomersSuccess(false)
      )
      expect(mockDispatch).toHaveBeenNthCalledWith(
        3,
        setCustomersFailure(false)
      )
      expect(mockDispatch).toHaveBeenNthCalledWith(4, setChangeTab(0))
      expect(mockDispatch).toHaveBeenNthCalledWith(
        5,
        setNotification({
          message: mockError.message,
          type: 'error',
        })
      )
      expect(mockDispatch).toHaveBeenNthCalledWith(
        6,
        setCustomersSuccess(false)
      )
      expect(mockDispatch).toHaveBeenNthCalledWith(
        7,
        setCustomersLoading(false)
      )
      expect(mockDispatch).toHaveBeenNthCalledWith(8, setCustomersFailure(true))
    })
  })

  //Get Customer Device Actions
  describe('getCustomerProfileById', () => {
    const ENDPOINTS = '/dbp/customers'

    it('should fetch customer profile and dispatch success actions', async () => {
      const mockDispatch = vi.fn()
      const profileId = '123'
      const mockCustomer = createMockCustomer({ id: profileId })
      const mockResponse = { data: mockCustomer }

      const mockAxiosGetHandler = vi
        .spyOn(secureapi2, 'get')
        .mockResolvedValueOnce(mockResponse)

      await getCustomerProfileById(profileId, mockDispatch)

      expect(mockAxiosGetHandler).toHaveBeenCalledWith(
        `${ENDPOINTS}/${profileId}`
      )

      expect(mockDispatch).toHaveBeenNthCalledWith(1, setCustomerLoading(true))
      expect(mockDispatch).toHaveBeenNthCalledWith(
        2,
        setCustomer(mockResponse.data)
      )
      expect(mockDispatch).toHaveBeenNthCalledWith(3, setCustomerLoading(false))
    })

    it('should handle API failure and dispatch error actions', async () => {
      const mockDispatch = vi.fn()
      const profileId = '123'
      const mockErrorMessage = 'Network Error'

      const mockAxiosGetHandler = vi
        .spyOn(secureapi2, 'get')
        .mockRejectedValueOnce(new Error(mockErrorMessage))

      await getCustomerProfileById(profileId, mockDispatch)

      expect(mockAxiosGetHandler).toHaveBeenCalledWith(
        `${ENDPOINTS}/${profileId}`
      )

      expect(mockDispatch).toHaveBeenNthCalledWith(1, setCustomerLoading(true))
      expect(mockDispatch).toHaveBeenNthCalledWith(2, setCustomerLoading(false))
      expect(mockDispatch).toHaveBeenNthCalledWith(
        3,
        setNotification({
          message: mockErrorMessage,
          type: 'error',
        })
      )
    })
  })

  describe('getCustomerProfile', () => {
    const ENDPOINTS = '/dbp/customers'
    let mockDispatch: ReturnType<typeof vi.fn>

    beforeEach(() => {
      mockDispatch = vi.fn()
      vi.resetAllMocks()
    })

    it('should fetch customer profile and dispatch success actions', async () => {
      // Arrange
      const profileId = '123'
      const mockCustomer = createMockCustomer({ id: profileId })
      const mockResponse = { data: mockCustomer }

      const mockAxiosGetHandler = vi
        .spyOn(secureapi2, 'get')
        .mockResolvedValueOnce(mockResponse)

      // Act
      const result = await getCustomerProfile(profileId, mockDispatch)

      // Assert
      // Check API call
      expect(mockAxiosGetHandler).toHaveBeenCalledWith(
        `${ENDPOINTS}/${profileId}`
      )

      // Check dispatch calls in order
      expect(mockDispatch).toHaveBeenNthCalledWith(1, setCustomerLoading(true))
      expect(mockDispatch).toHaveBeenNthCalledWith(2, setCustomer(mockCustomer))
      expect(mockDispatch).toHaveBeenNthCalledWith(3, setCustomerLoading(false))
      expect(mockDispatch).toHaveBeenNthCalledWith(4, setCustomerSuccess(true))

      // Check return value
      expect(result).toEqual(mockCustomer)
    })

    it('should handle API errors correctly', async () => {
      // Arrange
      const profileId = '123'
      const errorMessage = 'Network Error'
      const mockError = new Error(errorMessage)

      vi.spyOn(secureapi2, 'get').mockRejectedValueOnce(mockError)

      // Act
      const result = await getCustomerProfile(profileId, mockDispatch)

      // Assert
      // Check dispatch calls in order
      expect(mockDispatch).toHaveBeenNthCalledWith(1, setCustomerLoading(true))
      expect(mockDispatch).toHaveBeenNthCalledWith(2, setCustomerLoading(false))
      expect(mockDispatch).toHaveBeenNthCalledWith(3, setCustomerFailure(true))
      expect(mockDispatch).toHaveBeenNthCalledWith(
        4,
        setCustomerAccountsList([])
      )
      expect(mockDispatch).toHaveBeenNthCalledWith(
        5,
        setNotification({
          message: errorMessage,
          type: 'error',
        })
      )

      // Check return value
      expect(result).toBeNull()
    })

    it('should handle customers with missing optional fields', async () => {
      // Arrange
      const profileId = '456'
      const mockCustomer = createMockCustomer({
        id: profileId,
        postalAddress: undefined,
        accountNumber: undefined,
      })
      const mockResponse = { data: mockCustomer }

      vi.spyOn(secureapi2, 'get').mockResolvedValueOnce(mockResponse)

      // Act
      const result = await getCustomerProfile(profileId, mockDispatch)

      // Assert
      expect(mockDispatch).toHaveBeenNthCalledWith(2, setCustomer(mockCustomer))
      expect(result).toEqual(mockCustomer)
    })

    it('should handle customers with multiple store values', async () => {
      // Arrange
      const profileId = '789'
      const mockCustomer = createMockCustomer({
        id: profileId,
        storeOfValues: [
          { storeCode: 'STORE001', customerId: profileId },
          { storeCode: 'STORE002', customerId: profileId },
        ],
        profileAccountStoreIds: [
          {
            profileId,
            storeCode: 'STORE001',
            description: 'FLEXCUBE',
            customerId: profileId,
          },
          {
            profileId,
            storeCode: 'STORE002',
            description: 'ASTRA-WALLET',
            customerId: profileId,
          },
        ],
      })
      const mockResponse = { data: mockCustomer }

      vi.spyOn(secureapi2, 'get').mockResolvedValueOnce(mockResponse)

      // Act
      const result = await getCustomerProfile(profileId, mockDispatch)

      // Assert
      expect(mockDispatch).toHaveBeenNthCalledWith(2, setCustomer(mockCustomer))
      expect(result).toEqual(mockCustomer)
      expect(result?.storeOfValues.length).toBe(2)
      expect(result?.profileAccountStoreIds.length).toBe(2)
    })
  })
  // Customer Update Maker checker
  describe('getCustomerDevices', () => {
    it('should fetch customer devices when required parameters are provided', async () => {
      const mockDispatch = vi.fn()
      const mockResponse = { data: [{ id: 1, name: 'device1' }] }
      vi.spyOn(secureapi2, 'get').mockResolvedValueOnce(mockResponse)

      await getCustomerDevices({
        params: {
          profileID: '123',
          page: 1,
          size: 10,
        },
        dispatch: mockDispatch,
      })

      expect(secureapi2.get).toHaveBeenCalledWith(
        'dbp/customers/123/devices?page=1&size=10'
      )
      expect(mockDispatch).toHaveBeenCalledWith(setDevicesLoading(true))
      expect(mockDispatch).toHaveBeenCalledWith(setDevices(mockResponse.data))
      expect(mockDispatch).toHaveBeenCalledWith(setDevicesSuccess(true))
      expect(mockDispatch).toHaveBeenCalledWith(setDevicesFailure(false))
    })
    it('should construct URL with base path and mandatory query parameters when called', async () => {
      const mockDispatch = vi.fn()
      const mockResponse = { data: [{ id: 1, name: 'device1' }] }
      vi.spyOn(secureapi2, 'get').mockResolvedValueOnce(mockResponse)

      await getCustomerDevices({
        params: {
          profileID: '123',
          page: 1,
          size: 10,
        },
        dispatch: mockDispatch,
      })

      expect(secureapi2.get).toHaveBeenCalledWith(
        'dbp/customers/123/devices?page=1&size=10'
      )
      expect(mockDispatch).toHaveBeenCalledWith(setDevicesLoading(true))
      expect(mockDispatch).toHaveBeenCalledWith(setDevices(mockResponse.data))
      expect(mockDispatch).toHaveBeenCalledWith(setDevicesSuccess(true))
      expect(mockDispatch).toHaveBeenCalledWith(setDevicesFailure(false))
    })

    it('should filter devices by multiple criteria when all parameters are provided', async () => {
      const mockDispatch = vi.fn()
      const mockResponse = { data: [{ id: 1, name: 'device1' }] }
      vi.spyOn(secureapi2, 'get').mockResolvedValueOnce(mockResponse)

      await getCustomerDevices({
        params: {
          profileID: '123',
          dateCreatedFrom: '2023-01-01',
          dateCreatedTo: '2023-12-31',
          status: 'active',
          deviceType: 'mobile',
          deviceId: 'device123',
          page: 1,
          size: 10,
        },
        dispatch: mockDispatch,
      })

      expect(secureapi2.get).toHaveBeenCalledWith(
        'dbp/customers/123/devices?page=1&size=10&dateCreatedFrom=2023-01-01&dateCreatedTo=2023-12-31&status=active&deviceType=mobile&deviceId=device123'
      )
      expect(mockDispatch).toHaveBeenCalledWith(setDevicesLoading(true))
      expect(mockDispatch).toHaveBeenCalledWith(setDevices(mockResponse.data))
      expect(mockDispatch).toHaveBeenCalledWith(setDevicesSuccess(true))
      expect(mockDispatch).toHaveBeenCalledWith(setDevicesFailure(false))
    })

    it('should handle API errors and set failure states', async () => {
      const mockDispatch = vi.fn()
      const mockError = new Error('API Error')
      vi.spyOn(secureapi2, 'get').mockRejectedValueOnce(mockError)

      await getCustomerDevices({
        params: {
          profileID: '123',
          page: 1,
          size: 10,
        },
        dispatch: mockDispatch,
      })

      expect(mockDispatch).toHaveBeenCalledWith(setDevicesLoading(false))
      expect(mockDispatch).toHaveBeenCalledWith(setDevicesSuccess(false))
      expect(mockDispatch).toHaveBeenCalledWith(setDevicesFailure(true))
      expect(mockDispatch).toHaveBeenCalledWith(
        setNotification({
          message: mockError.message,
          type: 'error',
        })
      )
    })
  })
  describe('getCustomerDeviceDetail', () => {
    const mockProfileId = '12345'
    const mockDeviceId = 'device-123'
    let mockDispatch: ReturnType<typeof vi.fn>

    beforeEach(() => {
      mockDispatch = vi.fn()
      vi.clearAllMocks()
    })
    it('should fetch device details and update store when API call succeeds', async () => {
      const mockResponse = { data: { id: 'device-123', name: 'Test Device' } }

      ;(secureapi2.get as Mock).mockResolvedValue(mockResponse)

      const result = await getCustomerDeviceDetail({
        profileID: mockProfileId,
        deviceID: mockDeviceId,
        dispatch: mockDispatch,
      })

      expect(secureapi2.get).toHaveBeenCalledWith(
        `dbp/customers/${mockProfileId}/devices/${mockDeviceId}`
      )
      const expectedActions: MockAction[] = [
        { type: 'customers/setDeviceLoading', payload: true },
        { type: 'customers/setDeviceSuccess', payload: false },
        { type: 'customers/setDeviceFailure', payload: false },
        { type: 'customers/setDevice', payload: mockResponse.data },
        { type: 'customers/setDeviceLoading', payload: false },
        { type: 'customers/setDeviceSuccess', payload: true },
        { type: 'customers/setDeviceFailure', payload: false },
      ]
      expect(mockDispatch.mock.calls.flatMap((call) => call[0].type)).toEqual(
        expectedActions.map((action) => action.type)
      )

      expect(result).toEqual(mockResponse.data)
    })

    it('should handle API errors and update store with failure state', async () => {
      const mockDispatch = vi.fn()
      const mockProfileId = '12345'
      const mockDeviceId = 'device-123'
      const mockError = new Error('API Error')

      secureapi2.get = vi.fn().mockRejectedValue(mockError)

      await getCustomerDeviceDetail({
        profileID: mockProfileId,
        deviceID: mockDeviceId,
        dispatch: mockDispatch,
      })

      expect(mockDispatch).toHaveBeenCalledWith(setDeviceLoading(false))
      expect(mockDispatch).toHaveBeenCalledWith(setDeviceSuccess(false))
      expect(mockDispatch).toHaveBeenCalledWith(setDeviceFailure(true))
      expect(mockDispatch).toHaveBeenCalledWith(
        setNotification({
          message: mockError.message,
          type: 'error',
        })
      )
    })

    it('should construct correct API URL with given profileID and deviceID', async () => {
      const mockDispatch = vi.fn()
      const mockProfileId = '12345'
      const mockDeviceId = 'device-123'
      const mockResponse = { data: { id: 'device-123', name: 'Test Device' } }

      secureapi2.get = vi.fn().mockResolvedValue(mockResponse)

      await getCustomerDeviceDetail({
        profileID: mockProfileId,
        deviceID: mockDeviceId,
        dispatch: mockDispatch,
      })

      expect(secureapi2.get).toHaveBeenCalledWith(
        `dbp/customers/${mockProfileId}/devices/${mockDeviceId}`
      )
    })

    it('should handle API failure and update store accordingly', async () => {
      const mockDispatch = vi.fn()
      const mockProfileId = '12345'
      const mockDeviceId = 'device-123'
      const mockError = new Error('Network Error')

      secureapi2.get = vi.fn().mockRejectedValue(mockError)

      await getCustomerDeviceDetail({
        profileID: mockProfileId,
        deviceID: mockDeviceId,
        dispatch: mockDispatch,
      })

      expect(secureapi2.get).toHaveBeenCalledWith(
        `dbp/customers/${mockProfileId}/devices/${mockDeviceId}`
      )
      expect(mockDispatch).toHaveBeenCalledWith(setDeviceLoading(true))
      expect(mockDispatch).toHaveBeenCalledWith(setDeviceLoading(false))
      expect(mockDispatch).toHaveBeenCalledWith(setDeviceSuccess(false))
      expect(mockDispatch).toHaveBeenCalledWith(setDeviceFailure(true))
      expect(mockDispatch).toHaveBeenCalledWith(
        setNotification({
          message: mockError.message,
          type: 'error',
        })
      )
    })
  })

  describe('getCustomerDeviceHistory', () => {
    it('should fetch device logs and update store when API call succeeds', async () => {
      const mockDispatch = vi.fn()
      const mockProfileId = '123'
      const mockDeviceId = '456'
      const mockResponse = {
        data: {
          data: [{ id: 1, timestamp: '2023-01-01' }],
        },
      }

      vi.spyOn(secureapi2, 'get').mockResolvedValueOnce(mockResponse)

      await getCustomerDeviceHistory(mockDispatch, mockProfileId, mockDeviceId)

      expect(secureapi2.get).toHaveBeenCalledWith(
        'dbp/customers/123/devices/456/logs'
      )
      expect(mockDispatch).toHaveBeenCalledWith(setIsLoadingDeviceLogs(true))
      expect(mockDispatch).toHaveBeenCalledWith(setIsLoadingDeviceLogs(false))
      expect(mockDispatch).toHaveBeenCalledWith(setIsSuccessfulDeviceLogs(true))
      expect(mockDispatch).toHaveBeenCalledWith(
        setDeviceLogs(mockResponse.data.data)
      )
    })

    it('should handle API errors by updating store accordingly', async () => {
      const mockDispatch = vi.fn()
      const mockProfileId = '123'
      const mockDeviceId = '456'

      vi.spyOn(secureapi2, 'get').mockRejectedValueOnce(new Error('API Error'))

      await getCustomerDeviceHistory(mockDispatch, mockProfileId, mockDeviceId)

      expect(secureapi2.get).toHaveBeenCalledWith(
        'dbp/customers/123/devices/456/logs'
      )
      expect(mockDispatch).toHaveBeenCalledWith(setIsLoadingDeviceLogs(true))
      expect(mockDispatch).toHaveBeenCalledWith(setIsLoadingDeviceLogs(false))
      expect(mockDispatch).toHaveBeenCalledWith(
        setIsSuccessfulDeviceLogs(false)
      )
    })
  })
  // Customer Update Actions

  describe.skip('updateCustomerDetails', () => {
    const ENDPOINTS = '/dbp/customers'

    it('should update customer email and show success notification when valid data provided', async () => {
      const mockDispatch = vi.fn()
      const mockProfileId = '12345'
      const mockEmail = '<EMAIL>'

      vi.spyOn(secureapi2, 'patch').mockResolvedValueOnce({})

      await updateCustomerDetails({
        email: mockEmail,
        profileID: mockProfileId,
        dispatch: mockDispatch,
      })

      expect(secureapi2.patch).toHaveBeenCalledWith(
        `${ENDPOINTS}/${mockProfileId}`,
        {
          email: mockEmail,
          comments: '',
        }
      )

      expect(mockDispatch).toHaveBeenCalledWith(setCustomersLoading(true))
      expect(mockDispatch).toHaveBeenCalledWith(setCustomersLoading(false))
      expect(mockDispatch).toHaveBeenCalledWith(
        setNotification({
          message: 'Edit customer is successful',
          type: 'success',
        })
      )
    })

    it('should show success notification after successful update', async () => {
      const mockDispatch = vi.fn()
      const mockProfileId = '12345'
      const mockEmail = '<EMAIL>'

      vi.spyOn(secureapi2, 'patch').mockResolvedValueOnce({})

      await updateCustomerDetails({
        email: mockEmail,
        profileID: mockProfileId,
        dispatch: mockDispatch,
      })

      expect(secureapi2.patch).toHaveBeenCalledWith(
        `${ENDPOINTS}/${mockProfileId}`,
        {
          email: mockEmail,
          comments: '',
        }
      )

      expect(mockDispatch).toHaveBeenCalledWith(setCustomersLoading(true))
      expect(mockDispatch).toHaveBeenCalledWith(setCustomersLoading(false))
      expect(mockDispatch).toHaveBeenCalledWith(
        setNotification({
          message: 'Edit customer is successful',
          type: 'success',
        })
      )
    })

    it('should handle undefined email and show error notification', async () => {
      const mockDispatch = vi.fn()
      const mockProfileId = '12345'

      vi.spyOn(secureapi2, 'patch').mockRejectedValueOnce(
        new Error('Invalid email')
      )

      await updateCustomerDetails({
        email: undefined,
        profileID: mockProfileId,
        dispatch: mockDispatch,
      })

      expect(mockDispatch).toHaveBeenCalledWith(setCustomersLoading(true))
      expect(mockDispatch).toHaveBeenCalledWith(
        setNotification({
          message: 'Invalid email',
          type: 'error',
        })
      )
    })
  })

  describe.skip('makerUpdateCustomer', () => {
    const ENDPOINTS = '/dbp/customers'

    it('should dispatch success notification when API call succeeds', async () => {
      const mockDispatch = vi.fn()
      const profileID = '123'
      const email = '<EMAIL>'

      vi.spyOn(secureapi2, 'patch').mockResolvedValueOnce({})

      await makerUpdateCustomer({ email, profileID, dispatch: mockDispatch })

      expect(secureapi2.patch).toHaveBeenCalledWith(
        `${ENDPOINTS}/${profileID}/make`,
        {
          email,
          comments: '',
        }
      )

      expect(mockDispatch).toHaveBeenCalledWith(setCustomersLoading(true))
      expect(mockDispatch).toHaveBeenCalledWith(setCustomersLoading(false))
      expect(mockDispatch).toHaveBeenCalledWith(
        setNotification({
          message: 'Edit customer request is pending approval',
          type: 'success',
        })
      )
    })

    it('should set loading state correctly around API call', async () => {
      const mockDispatch = vi.fn()
      const profileID = '123'
      const email = '<EMAIL>'

      vi.spyOn(secureapi2, 'patch').mockResolvedValueOnce({})

      await makerUpdateCustomer({ email, profileID, dispatch: mockDispatch })

      expect(mockDispatch).toHaveBeenCalledWith(setCustomersLoading(true))
      expect(mockDispatch).toHaveBeenCalledWith(setCustomersLoading(false))
    })

    it('should make API patch request with correct endpoint and request data', async () => {
      const mockDispatch = vi.fn()
      const profileID = '123'
      const email = '<EMAIL>'

      vi.spyOn(secureapi2, 'patch').mockResolvedValueOnce({})

      await makerUpdateCustomer({ email, profileID, dispatch: mockDispatch })

      expect(secureapi2.patch).toHaveBeenCalledWith(
        `${ENDPOINTS}/${profileID}/make`,
        {
          email,
          comments: '',
        }
      )

      expect(mockDispatch).toHaveBeenCalledWith(setCustomersLoading(true))
      expect(mockDispatch).toHaveBeenCalledWith(setCustomersLoading(false))
      expect(mockDispatch).toHaveBeenCalledWith(
        setNotification({
          message: 'Edit customer request is pending approval',
          type: 'success',
        })
      )
    })

    it('should dispatch error notification when API call fails', async () => {
      const mockDispatch = vi.fn()
      const profileID = '123'
      const email = '<EMAIL>'
      const errorMessage = 'API Error'

      vi.spyOn(secureapi2, 'patch').mockRejectedValueOnce(
        new Error(errorMessage)
      )

      await makerUpdateCustomer({ email, profileID, dispatch: mockDispatch })

      expect(secureapi2.patch).toHaveBeenCalledWith(
        `${ENDPOINTS}/${profileID}/make`,
        {
          email,
          comments: '',
        }
      )

      expect(mockDispatch).toHaveBeenCalledWith(setCustomersLoading(true))
      expect(mockDispatch).toHaveBeenCalledWith(
        setNotification({
          message: errorMessage,
          type: 'error',
        })
      )
    })
  })

  describe.skip('rejectUpdateCustomerDetails', () => {
    const ENDPOINTS = '/dbp/customers'
    it('should dispatch success notification when customer update is rejected successfully', async () => {
      const mockDispatch = vi.fn()
      const approvalID = '123'
      const comments = 'Rejected due to invalid data'

      vi.spyOn(secureapi2, 'put').mockResolvedValueOnce({})

      await rejectUpdateCustomer({
        approvalID,
        comments,
        dispatch: mockDispatch,
      })

      expect(mockDispatch).toHaveBeenCalledWith(setCustomersLoading(true))

      expect(secureapi2.put).toHaveBeenCalledWith(
        `${ENDPOINTS}/reject/${approvalID}`,
        { comments }
      )

      expect(mockDispatch).toHaveBeenCalledWith(setCustomersLoading(false))

      expect(mockDispatch).toHaveBeenCalledWith(
        setNotification({
          message: 'Edit customer request has been rejected',
          type: 'success',
        })
      )
    })
    it('should dispatch error notification when API call fails', async () => {
      const mockDispatch = vi.fn()
      const approvalID = '123'
      const comments = 'Test comments'
      const errorMessage = 'API Error'

      vi.spyOn(secureapi2, 'put').mockRejectedValueOnce(new Error(errorMessage))

      await rejectUpdateCustomer({
        approvalID,
        comments,
        dispatch: mockDispatch,
      })

      expect(mockDispatch).toHaveBeenCalledWith(setCustomersLoading(true))

      expect(secureapi2.put).toHaveBeenCalledWith(
        `${ENDPOINTS}/reject/${approvalID}`,
        { comments }
      )

      expect(mockDispatch).toHaveBeenCalledWith(
        setNotification({
          message: errorMessage,
          type: 'error',
        })
      )
    })

    it('should call API endpoint with correct parameters and method when rejecting customer update', async () => {
      const mockDispatch = vi.fn()
      const approvalID = '123'
      const comments = 'Rejected due to invalid data'

      vi.spyOn(secureapi2, 'put').mockResolvedValueOnce({})

      await rejectUpdateCustomer({
        approvalID,
        comments,
        dispatch: mockDispatch,
      })

      expect(secureapi2.put).toHaveBeenCalledWith(
        `${ENDPOINTS}/reject/${approvalID}`,
        { comments }
      )
    })
    it('should dispatch success notification when customer update is rejected successfully', async () => {
      const mockDispatch = vi.fn()
      const approvalID = '123'
      const comments = 'Rejected due to invalid data'

      vi.spyOn(secureapi2, 'put').mockResolvedValueOnce({})

      await rejectUpdateCustomer({
        approvalID,
        comments,
        dispatch: mockDispatch,
      })

      expect(mockDispatch).toHaveBeenCalledWith(setCustomersLoading(true))

      expect(secureapi2.put).toHaveBeenCalledWith(
        `${ENDPOINTS}/reject/${approvalID}`,
        { comments }
      )

      expect(mockDispatch).toHaveBeenCalledWith(setCustomersLoading(false))

      expect(mockDispatch).toHaveBeenCalledWith(
        setNotification({
          message: 'Edit customer request has been rejected',
          type: 'success',
        })
      )
    })
  })

  describe.skip('approveUpdateCustomer', () => {
    const ENDPOINTS = '/dbp/customers'

    it('should dispatch success notification when API call succeeds', async () => {
      const mockDispatch = vi.fn()
      const approvalID = '123'
      const comments = 'Approved'

      vi.spyOn(secureapi2, 'put').mockResolvedValueOnce({})

      await approveUpdateCustomer({
        approvalID,
        comments,
        dispatch: mockDispatch,
      })

      expect(mockDispatch).toHaveBeenCalledWith(setCustomersLoading(true))
      expect(secureapi2.put).toHaveBeenCalledWith(
        `${ENDPOINTS}/update/approve/${approvalID}`,
        { comments }
      )
      expect(mockDispatch).toHaveBeenCalledWith(setCustomersLoading(false))
      expect(mockDispatch).toHaveBeenCalledWith(
        setNotification({
          message: 'Edit customer request has been approved',
          type: 'success',
        })
      )
    })
    it('should dispatch error notification when API call fails', async () => {
      const mockDispatch = vi.fn()
      const approvalID = '123'
      const comments = 'Approved'
      const errorMessage = 'API Error'

      vi.spyOn(secureapi2, 'put').mockRejectedValueOnce(new Error(errorMessage))

      await approveUpdateCustomer({
        approvalID,
        comments,
        dispatch: mockDispatch,
      })

      expect(mockDispatch).toHaveBeenCalledWith(setCustomersLoading(true))
      expect(mockDispatch).toHaveBeenCalledWith(
        setNotification({
          message: errorMessage,
          type: 'error',
        })
      )
    })

    it('should call API with correct path and payload structure when approving customer update', async () => {
      const mockDispatch = vi.fn()
      const approvalID = '123'
      const comments = 'Approved'

      vi.spyOn(secureapi2, 'put').mockResolvedValueOnce({})

      await approveUpdateCustomer({
        approvalID,
        comments,
        dispatch: mockDispatch,
      })

      expect(mockDispatch).toHaveBeenCalledWith(setCustomersLoading(true))
      expect(secureapi2.put).toHaveBeenCalledWith(
        `${ENDPOINTS}/update/approve/${approvalID}`,
        { comments }
      )
      expect(mockDispatch).toHaveBeenCalledWith(setCustomersLoading(false))
      expect(mockDispatch).toHaveBeenCalledWith(
        setNotification({
          message: 'Edit customer request has been approved',
          type: 'success',
        })
      )
    })
  })

  describe('deactivateCustomer', () => {
    const ENDPOINTS = '/dbp/customers' // Replace with your actual endpoint
    let mockDispatch: ReturnType<typeof vi.fn>

    beforeEach(() => {
      mockDispatch = vi.fn()
      vi.resetAllMocks()
    })

    it('should deactivate customer with super rights and dispatch success actions', async () => {
      // Arrange
      const profileID = '123'
      const reason = 'Security concern'

      // Mock HasAccessToRights to return true (user has super rights)
      vi.mocked(HasAccessToRights).mockReturnValue(true)

      // Mock the API call
      const mockPatchHandler = vi
        .spyOn(secureapi2, 'patch')
        .mockResolvedValueOnce({})

      // Act
      await deactivateCustomer({
        profileID,
        reason,
        dispatch: mockDispatch,
      })

      // Assert
      // Check that HasAccessToRights was called with the right argument
      expect(HasAccessToRights).toHaveBeenCalledWith([
        'SUPER_DEACTIVATE_CUSTOMERS',
      ])

      // Check that the API was called with the right URL and data
      expect(mockPatchHandler).toHaveBeenCalledWith(
        `${ENDPOINTS}/deactivate/${profileID}`,
        {
          blockReason: 'DataBreach',
          comments: reason,
        }
      )

      // Check dispatch calls in order
      expect(mockDispatch).toHaveBeenNthCalledWith(1, setCustomersLoading(true))
      expect(mockDispatch).toHaveBeenNthCalledWith(
        2,
        setNotification({
          message: 'Customer deactivated successfully.',
          type: 'success',
        })
      )
      //TODO: mock these api calls without interfering with the previous usages test
      // Check that the dependent functions were called
      // expect(getCustomerProfileById).toHaveBeenCalledWith(
      //   profileID,
      //   mockDispatch
      // )
      // expect(getAllCustomers).toHaveBeenCalledWith(
      //   { page: 1, size: 10 },
      //   mockDispatch
      // )

      // Check that loading was set to false
      expect(mockDispatch).toHaveBeenCalledWith(setCustomersLoading(false))
    })

    it('should create deactivation request without super rights and dispatch pending message', async () => {
      // Arrange
      const profileID = '123'
      const reason = 'Security concern'

      // Mock HasAccessToRights to return false (user doesn't have super rights)
      vi.mocked(HasAccessToRights).mockReturnValue(false)

      // Mock the API call
      const mockPatchHandler = vi
        .spyOn(secureapi2, 'patch')
        .mockResolvedValueOnce({})

      // Act
      await deactivateCustomer({
        profileID,
        reason,
        dispatch: mockDispatch,
      })

      // Assert
      // Check that the API was called with the right URL and data
      expect(mockPatchHandler).toHaveBeenCalledWith(
        `${ENDPOINTS}/deactivate/${profileID}/make`,
        {
          blockReason: 'DataBreach',
          comments: reason,
        }
      )

      // Check dispatch calls in order
      expect(mockDispatch).toHaveBeenNthCalledWith(1, setCustomersLoading(true))
      expect(mockDispatch).toHaveBeenNthCalledWith(
        2,
        setNotification({
          message: 'Deactivate customer request is pending approval.',
          type: 'success',
        })
      )

      //TODO: mock these api calls without interfering with the previous usages test
      // Check that the dependent functions were called
      // expect(getCustomerProfileById).toHaveBeenCalledWith(
      //   profileID,
      //   mockDispatch
      // )
      // expect(getAllCustomers).toHaveBeenCalledWith(
      //   { page: 1, size: 10 },
      //   mockDispatch
      // )

      // Check that loading was set to false
      expect(mockDispatch).toHaveBeenCalledWith(setCustomersLoading(false))
    })

    it('should handle API errors correctly', async () => {
      // Arrange
      const profileID = '123'
      const reason = 'Security concern'
      const errorMessage = 'Network Error'
      const mockError = new Error(errorMessage)

      // Mock HasAccessToRights to return true
      vi.mocked(HasAccessToRights).mockReturnValue(true)

      // Mock the API call to throw an error
      vi.spyOn(secureapi2, 'patch').mockRejectedValueOnce(mockError)

      // Mock console.error to prevent test output pollution
      const consoleErrorSpy = vi
        .spyOn(console, 'error')
        .mockImplementation(() => {})

      // Act
      await deactivateCustomer({
        profileID,
        reason,
        dispatch: mockDispatch,
      })

      // Assert
      // Check that the error was logged
      expect(consoleErrorSpy).toHaveBeenCalledWith(
        'Error updating customer details',
        mockError
      )

      // Check dispatch calls in order
      expect(mockDispatch).toHaveBeenNthCalledWith(1, setCustomersLoading(true))
      expect(mockDispatch).toHaveBeenNthCalledWith(
        2,
        setCustomersLoading(false)
      )
      expect(mockDispatch).toHaveBeenNthCalledWith(
        3,
        setNotification({
          message: errorMessage,
          type: 'error',
        })
      )

      // Check that the dependent functions were NOT called
      //TODO: mock these api calls without interfering with the previous usages test
      // expect(getCustomerProfileById).not.toHaveBeenCalled()
      // expect(getAllCustomers).not.toHaveBeenCalled()
    })

    it('should handle different error types correctly', async () => {
      // Arrange
      const profileID = '123'
      const reason = 'Security concern'

      // Create an error without a message property
      const customError = { status: 500, statusText: 'Internal Server Error' }

      // Mock HasAccessToRights to return true
      vi.mocked(HasAccessToRights).mockReturnValue(true)

      // Mock the API call to throw the custom error
      vi.spyOn(secureapi2, 'patch').mockRejectedValueOnce(customError)

      // Mock console.error
      vi.spyOn(console, 'error').mockImplementation(() => {})

      // Act
      await deactivateCustomer({
        profileID,
        reason,
        dispatch: mockDispatch,
      })

      // Assert
      // Check that the notification was dispatched with undefined message
      // This tests how the function handles errors that don't match the Error type
      expect(mockDispatch).toHaveBeenCalledWith(
        setNotification({
          message: undefined,
          type: 'error',
        })
      )
    })
  })
  describe.skip('acceptDeactivateCustomer', () => {
    it('should dispatch success notification when deactivation is approved', async () => {
      const mockDispatch = vi.fn()
      const approvalID = '123'
      const comments = 'Approved'

      vi.spyOn(secureapi2, 'put').mockResolvedValueOnce({})

      await acceptDeactivateCustomer({
        approvalID,
        comments,
        dispatch: mockDispatch,
      })

      expect(secureapi2.put).toHaveBeenCalledWith(
        '/dbp/customers/deactivate/approve/123',
        { comments: 'Approved' }
      )

      expect(mockDispatch).toHaveBeenCalledWith(setCustomersLoading(true))

      expect(mockDispatch).toHaveBeenCalledWith(
        setNotification({
          message: 'Customer deactivation has been approved',
          type: 'success',
        })
      )

      expect(mockDispatch).toHaveBeenCalledWith(setCustomersLoading(false))
    })

    it('should dispatch error notification when API call fails', async () => {
      const mockDispatch = vi.fn()
      const approvalID = '123'
      const comments = 'Test'
      const errorMessage = 'API Error'

      vi.spyOn(secureapi2, 'put').mockRejectedValueOnce(new Error(errorMessage))
      vi.spyOn(global.console, 'error').mockImplementation(() => {})
      await acceptDeactivateCustomer({
        approvalID,
        comments,
        dispatch: mockDispatch,
      })

      expect(mockDispatch).toHaveBeenCalledWith(setCustomersLoading(true))

      expect(mockDispatch).toHaveBeenCalledWith(
        setNotification({
          message: errorMessage,
          type: 'error',
        })
      )

      expect(mockDispatch).toHaveBeenCalledWith(setCustomersLoading(false))

      expect(console.error).toHaveBeenCalledWith(
        'Error updating customer details',
        expect.any(Error)
      )
    })
  })

  describe.skip('fetchCustomerAccount', () => {
    it('should set customer details and profile exists when API call succeeds', async () => {
      const mockDispatch = vi.fn()
      const mockResponse = {
        data: {
          profileId: '12345',
          accountDetails: 'test-details',
        },
      }

      vi.spyOn(secureapi2, 'get').mockResolvedValueOnce(mockResponse)

      await fetchCustomerAccount({
        account: 'test-account',
        dispatch: mockDispatch,
      })

      expect(mockDispatch).toHaveBeenCalledWith(
        setCustomerAccountDetails(mockResponse.data)
      )
      expect(mockDispatch).toHaveBeenCalledWith(setCustomerProfileExists(true))
      expect(mockDispatch).toHaveBeenCalledWith(setCustomerLoading(false))
    })

    it('should show error notification when API call fails', async () => {
      const mockDispatch = vi.fn()

      vi.spyOn(secureapi2, 'get').mockRejectedValueOnce(new Error('API Error'))

      await fetchCustomerAccount({
        account: 'test-account',
        dispatch: mockDispatch,
      })

      expect(mockDispatch).toHaveBeenCalledWith(setCustomerProfileExists(false))
      expect(mockDispatch).toHaveBeenCalledWith(setCustomerLoading(false))
      expect(mockDispatch).toHaveBeenCalledWith(
        setNotification({
          message: 'Customer profile not found',
          type: 'error',
        })
      )
    })
  })

  // describe('createCustomerAccount', () => {
  //   const mockDispatch = vi.fn()

  //   afterEach(() => {
  //     vi.restoreAllMocks()
  //     mockDispatch.mockClear()
  //   })

  //   it('should use /dbp/customers endpoint for SUPER_CREATE_CUSTOMERS', async () => {
  //     ;(HasAccessToRights as vi.Mock).mockReturnValue(true)
  //     ;(secureapi2.post as vi.Mock).mockResolvedValueOnce({})

  //     const account = { name: 'John Doe', email: '<EMAIL>' }

  //     await createCustomerAccount({ account, dispatch: mockDispatch })

  //     expect(HasAccessToRights).toHaveBeenCalledWith(['SUPER_CREATE_CUSTOMERS'])
  //     expect(secureapi2.post).toHaveBeenCalledWith('/dbp/customers', account)
  //     expect(mockDispatch).toHaveBeenCalledWith(setCustomerLoading(true))
  //     expect(mockDispatch).toHaveBeenCalledWith(
  //       setNotification({
  //         message: 'Customer profile created successfully',
  //         type: 'success',
  //       })
  //     )
  //     expect(mockDispatch).toHaveBeenCalledWith(setCustomerLoading(false))
  //   })

  //   it('should use /dbp/customers/make endpoint if SUPER_CREATE_CUSTOMERS is false', async () => {
  //     ;(HasAccessToRights as vi.Mock).mockReturnValue(false)
  //     ;(secureapi2.post as vi.Mock).mockResolvedValueOnce({})

  //     const account = { name: 'John Doe', email: '<EMAIL>' }

  //     await createCustomerAccount({ account, dispatch: mockDispatch })

  //     expect(HasAccessToRights).toHaveBeenCalledWith(['SUPER_CREATE_CUSTOMERS'])
  //     expect(secureapi2.post).toHaveBeenCalledWith('/dbp/customers/make', account)
  //     expect(mockDispatch).toHaveBeenCalledWith(setCustomerLoading(true))
  //     expect(mockDispatch).toHaveBeenCalledWith(
  //       setNotification({
  //         message: 'Create customer request is pending approval.',
  //         type: 'success',
  //       })
  //     )
  //     expect(mockDispatch).toHaveBeenCalledWith(setCustomerLoading(false))
  //   })

  //   it('should dispatch error notification if API call fails', async () => {
  //     ;(HasAccessToRights as vi.Mock).mockReturnValue(true)
  //     ;(secureapi2.post as vi.Mock).mockRejectedValueOnce(new Error('API Error'))

  //     const account = { name: 'John Doe', email: '<EMAIL>' }

  //     await createCustomerAccount({ account, dispatch: mockDispatch })

  //     expect(HasAccessToRights).toHaveBeenCalledWith(['SUPER_CREATE_CUSTOMERS'])
  //     expect(secureapi2.post).toHaveBeenCalledWith('/dbp/customers', account)
  //     expect(mockDispatch).toHaveBeenCalledWith(setCustomerLoading(true))
  //     expect(mockDispatch).toHaveBeenCalledWith(
  //       setNotification({
  //         message: 'Failed to create customer profile',
  //         type: 'error',
  //       })
  //     )
  //     expect(mockDispatch).toHaveBeenCalledWith(setCustomerLoading(false))
  //   })

  //   it('should handle missing account data gracefully', async () => {
  //     ;(HasAccessToRights as vi.Mock).mockReturnValue(true)

  //     await createCustomerAccount({
  //       account: null as any,
  //       dispatch: mockDispatch,
  //     })

  //     expect(mockDispatch).toHaveBeenCalledWith(setCustomerLoading(true))
  //     expect(mockDispatch).toHaveBeenCalledWith(
  //       setNotification({
  //         message: 'Failed to create customer profile',
  //         type: 'error',
  //       })
  //     )
  //     expect(mockDispatch).toHaveBeenCalledWith(setCustomerLoading(false))
  //   })
  // })

  describe.skip('acceptCreateCustomer', () => {
    it('should dispatch success notifications when API call succeeds', async () => {
      const mockDispatch = vi.fn()
      const approvalId = '123'
      const comments = 'Approved'

      vi.spyOn(secureapi2, 'put').mockResolvedValueOnce({})

      await acceptCreateCustomer({
        approvalId,
        comments,
        dispatch: mockDispatch,
      })

      expect(secureapi2.put).toHaveBeenCalledWith(
        `/dbp/customers/approve/${approvalId}`,
        { comments }
      )

      expect(mockDispatch).toHaveBeenCalledWith(setCustomerSuccess(true))
      expect(mockDispatch).toHaveBeenCalledWith(setCustomerFailure(false))
      expect(mockDispatch).toHaveBeenCalledWith(
        setSelectedCustomer({
          customer: {} as ICustomer,
          isPendingAction: false,
          openDetails: false,
        })
      )
      expect(mockDispatch).toHaveBeenCalledWith(
        setNotification({
          message: 'Create customer request has been approved.',
          type: 'success',
        })
      )
    })
    it('should dispatch error notification when API call fails', async () => {
      const mockDispatch = vi.fn()
      const approvalId = '123'
      const comments = 'Approved'
      const errorMessage = 'API Error'

      vi.spyOn(secureapi2, 'put').mockRejectedValueOnce(new Error(errorMessage))

      await acceptCreateCustomer({
        approvalId,
        comments,
        dispatch: mockDispatch,
      })

      expect(secureapi2.put).toHaveBeenCalledWith(
        `/dbp/customers/approve/${approvalId}`,
        { comments }
      )

      expect(mockDispatch).toHaveBeenCalledWith(
        setNotification({
          message: errorMessage,
          type: 'error',
        })
      )
    })
  })

  describe.skip('rejectCreateCustomer', () => {
    it('should dispatch success notifications when rejection is successful', async () => {
      const mockDispatch = vi.fn()
      const approvalId = '123'
      const comments = 'Rejection reason'

      vi.spyOn(secureapi2, 'put').mockResolvedValueOnce({})

      await rejectCreateCustomer({
        approvalId,
        comments,
        dispatch: mockDispatch,
      })

      expect(secureapi2.put).toHaveBeenCalledWith(
        `/dbp/customers/reject/${approvalId}`,
        { comments }
      )

      expect(mockDispatch).toHaveBeenCalledWith(setCustomerSuccess(true))
      expect(mockDispatch).toHaveBeenCalledWith(setCustomerFailure(false))
      expect(mockDispatch).toHaveBeenCalledWith(
        setSelectedCustomer({
          customer: {} as ICustomer,
          isPendingAction: false,
          openDetails: false,
        })
      )
      expect(mockDispatch).toHaveBeenCalledWith(
        setNotification({
          message: 'Create customer request has been rejected.',
          type: 'success',
        })
      )
    })
    it('should handle API error when comments are empty', async () => {
      const mockDispatch = vi.fn()
      const approvalId = '123'
      const comments = ''
      const errorMessage = 'Comments are required'

      vi.spyOn(secureapi2, 'put').mockRejectedValueOnce(new Error(errorMessage))

      await rejectCreateCustomer({
        approvalId,
        comments,
        dispatch: mockDispatch,
      })

      expect(secureapi2.put).toHaveBeenCalledWith(
        `/dbp/customers/reject/${approvalId}`,
        { comments }
      )

      expect(mockDispatch).toHaveBeenCalledWith(
        setNotification({
          message: errorMessage,
          type: 'error',
        })
      )
    })
  })

  describe('resetCustomerAccountDetails', () => {
    it('should dispatch setCustomerAccountDetails action with empty values for all fields', () => {
      const dispatch = vi.fn()

      resetCustomerAccountDetails({ dispatch })

      expect(dispatch).toHaveBeenCalledWith(
        setCustomerAccountDetails({
          firstName: '',
          lastName: '',
          email: '',
          phoneNumber: '',
          postalAddress: '',
          gender: '',
          idType: '',
          idValue: '',
          cif: '',
          physicalAddress: '',
          country: '',
          customerPrefix: '',
          dateOfBirth: '',
          nationality: '',
          customerCategory: '',
          customerType: '',
          customerAccounts: [],
          comments: null,
          isM247Customer: false,
        })
      )
    })
  })
  describe('accountType', () => {
    it('should return Individual when type is I', () => {
      const result = accountType('I')
      expect(result).toBe('Individual')
    })
    it('should return No account when type is empty string', () => {
      const result = accountType('')
      expect(result).toBe('No account')
    })
  })

  describe.skip('deactivateCustomerDevice', () => {
    it('should deactivate a customer device successfully', async () => {
      const mockDispatch = vi.fn()

      vi.spyOn(secureapi2, 'patch').mockResolvedValueOnce({})
      //  secureapi2.patch.mockResolvedValueOnce({})

      const spyGetCustomerDeviceDetail = vi
        .spyOn(getCustomerDeviceDetail, 'getCustomerDeviceDetail')
        .mockResolvedValueOnce({})

      const params = {
        profileID: '123',
        deviceID: '456',
        comments: 'Device reported lost',
        dispatch: mockDispatch,
      }

      await deactivateCustomerDevice(params)

      const expectedUrl = `dbp/customers/123/devices/456/deactivate`

      // Verify API call
      expect(secureapi2.patch).toHaveBeenCalledWith(expectedUrl, {
        comments: 'Device reported lost',
      })

      // Verify `getCustomerDeviceDetail` is called with the correct arguments
      expect(spyGetCustomerDeviceDetail).toHaveBeenCalledWith({
        profileID: '123',
        deviceID: '456',
        dispatch: mockDispatch,
      })

      // Dispatch verifications
      expect(mockDispatch).toHaveBeenCalledWith(setDeviceLoading(true))
      expect(mockDispatch).toHaveBeenCalledWith(setDeviceSuccess(true))
      expect(mockDispatch).toHaveBeenCalledWith(setDeviceFailure(false))
      expect(mockDispatch).toHaveBeenCalledWith(
        setNotification({
          message: 'Device has been deactivated successfully',
          type: 'success',
        })
      )

      // Ensure loading state is reset
      expect(mockDispatch).toHaveBeenCalledWith(setDeviceLoading(false))
    })

    it('should set error states when API call fails', async () => {
      const mockDispatch = vi.fn()
      const profileID = '123'
      const deviceID = '456'
      const comments = 'Test deactivation'
      const errorMessage = 'API Error'

      vi.spyOn(secureapi2, 'patch').mockRejectedValueOnce(
        new Error(errorMessage)
      )

      await deactivateCustomerDevice({
        profileID,
        deviceID,
        dispatch: mockDispatch,
        comments,
      })

      expect(mockDispatch).toHaveBeenCalledWith(setDeviceLoading(false))
      expect(mockDispatch).toHaveBeenCalledWith(setDeviceSuccess(false))
      expect(mockDispatch).toHaveBeenCalledWith(setDeviceFailure(true))
      expect(mockDispatch).toHaveBeenCalledWith(
        setNotification({
          message: errorMessage,
          type: 'error',
        })
      )
    })
  })
})
