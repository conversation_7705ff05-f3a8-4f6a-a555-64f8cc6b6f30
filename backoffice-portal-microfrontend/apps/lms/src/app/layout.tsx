'use client'

import React, { ReactNode, useEffect, useState } from 'react'
import { getLoanProducts, getMakerCheckerTypes } from '@/store/actions'
import { refreshToken } from '@dtbx/store/actions'
import { Box } from '@mui/material'
import { useAppDispatch, useAppSelector } from '@/store'
import {
  clearNotification,
  setDocumentToggle,
  setSidebarCollapsed,
} from '@dtbx/store/reducers'
import AppProvider from '@/store/AppProvider'
import { NextAppDirEmotionCacheProvider, ThemeConfig } from '@dtbx/ui/theme'
import {
  AuthWrapper,
  CustomScrollbar,
  InActivity,
  LocalNotification,
} from '@dtbx/ui/components'
import { ISidebarConfigItem, Sidebar } from '@dtbx/ui/components/Sidebar'
import { IDView } from '@dtbx/ui/components/Overlay'
import { InternalNavBar } from '@dtbx/ui/components/Appbar'
import { BeneficiaryBanksIcon } from '@dtbx/ui/icons'
import { useRouter } from 'next/navigation'

import { sidebarConfig as initialSidebarConfig } from './sidebar'
import { isLoggedIn } from '@dtbx/store/utils'
import '@dtbx/ui/theme/index.css'

export default function RootLayout({
  children,
}: Readonly<{
  children: ReactNode
}>) {
  return (
    <html lang="en">
      <body>
        <AppProvider>
          <NextAppDirEmotionCacheProvider options={{ key: 'mui' }}>
            <ThemeConfig>
              <CustomScrollbar>
                <InActivity isLoggedIn={isLoggedIn}>
                  <LMSLayout>{children}</LMSLayout>
                </InActivity>
              </CustomScrollbar>
            </ThemeConfig>
          </NextAppDirEmotionCacheProvider>
        </AppProvider>
      </body>
    </html>
  )
}

export function LMSLayout(props: { children: React.ReactNode }) {
  const { isSidebarCollapsed } = useAppSelector((state) => state.navigation)
  const { userProducts } = useAppSelector((state) => state.loans)
  const modules = useAppSelector((state) => state.auth.channelModules)
  const { open } = useAppSelector((state) => state.navigation.documentToggle)
  const dispatch = useAppDispatch()
  const [customSideBarConfig, setCustomSideBarConfig] =
    useState<ISidebarConfigItem[]>(initialSidebarConfig)
  const profile = useAppSelector((state) => state.auth.decodedToken)
  const notification = useAppSelector(
    (state) => state.notifications.localNotification
  )
  const notificationType =
    useAppSelector((state) => state.notifications.localNotificationType) ||
    'info'

  useEffect(() => {
    const params = 'page=1&size=30'
    getLoanProducts(dispatch, params)
    getMakerCheckerTypes(dispatch, '?channel=LMS')
  }, [dispatch])

  useEffect(() => {
    if (!modules.length || !userProducts.length) return

    const baseConfig = [...initialSidebarConfig]

    const settingsMenu = baseConfig.find((item) => item.title === 'Settings')

    // Filter sidebar items based on user permissions
    const filteredConfig = baseConfig.filter((item) => {
      if (item.title === 'Settings') return false
      return modules.some(
        (module) =>
          module.channel === 'LMS' &&
          (module.modules?.includes(item?.module) || item?.module === 'default')
      )
    })

    const configWithoutProducts = [...filteredConfig]

    userProducts.forEach((product) => {
      if (!configWithoutProducts.some((item) => item.id === product.id)) {
        filteredConfig.push({
          id: product.id,
          title: product.name,
          path: `/loans?productId=${product.id}`,
          module: 'default',
          icon: <BeneficiaryBanksIcon />,
          isProductionReady: true,
        })
      }
    })

    // "Settings" is always at the bottom
    const updatedConfig = settingsMenu
      ? [...filteredConfig, settingsMenu]
      : filteredConfig

    setCustomSideBarConfig(updatedConfig)
  }, [userProducts, modules])
  return (
    <AuthWrapper requiresAuth={true} isLoggedIn={isLoggedIn}>
      <Box sx={{ display: 'flex', flexDirection: 'row' }}>
        <Sidebar
          sidebarConfig={customSideBarConfig}
          sidebarCollapsed={(val: boolean) =>
            dispatch(setSidebarCollapsed(val))
          }
        />
        <Box
          sx={{
            display: 'flex',
            flexDirection: 'column',
            width: isSidebarCollapsed ? '95vw' : '88vw',
          }}
        >
          <IDView
            open={open}
            setDocumentViewer={(val) => dispatch(setDocumentToggle(val))}
          />
          <InternalNavBar profile={profile} refreshToken={refreshToken} />
          <Box
            sx={{
              width: '100%',
              backgroundColor: '#FCFCFC',
            }}
          >
            <LocalNotification
              clearNotification={() => dispatch(clearNotification())}
              notification={notification}
              notificationType={notificationType}
            />
            {props.children}
          </Box>
        </Box>
      </Box>
    </AuthWrapper>
  )
}
