{"name": "@dtbx/store", "version": "0.0.3", "sideEffects": false, "license": "UNLICENSED", "type": "module", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./actions": {"types": "./dist/actions/index.d.ts", "default": "./dist/actions/index.js"}, "./interfaces": {"types": "./dist/interfaces/index.d.ts", "default": "./dist/interfaces/index.js"}, "./reducers": {"types": "./dist/reducers/index.d.ts", "default": "./dist/reducers/index.js"}, "./utils": {"types": "./dist/utils/index.d.ts", "default": "./dist/utils/index.js"}, "./CustomStorage": {"types": "./dist/CustomStorage.d.ts", "default": "./dist/CustomStorage.js"}}, "publishConfig": {"access": "restricted"}, "files": ["dist/**"], "scripts": {"compile": "tsup", "lint": "eslint . --max-warnings 0", "clean": "rimraf .turbo __tests__/coverage", "test": "vitest run", "test:watch": "vitest --watch", "test:vitest-ui": "vitest --ui --coverage", "test:view-report": "open __tests__/coverage/index.html"}, "dependencies": {"@reduxjs/toolkit": "^2.5.0", "axios": "^1.8.4", "dayjs": "^1.11.13", "jwt-decode": "^4.0.0", "react-redux": "^9.2.0", "redux-persist": "^6.0.0"}, "peerDependencies": {"next": "15.2.3", "react": "^19.0.0", "redux": "^5.0.0"}, "devDependencies": {"@dtbx/eslint-config": "workspace:*", "@dtbx/typescript-config": "workspace:*", "@dtbx/vitest-config": "workspace:*", "@types/eslint": "^9.6.1", "@types/node": "^22.10.2", "@types/react": "^19.0.12", "@vitest/coverage-istanbul": "^3.0.9", "eslint": "^9.16.0", "prettier": "^3.5.3", "rimraf": "^6.0.1", "tsup": "^8.5.0", "typescript": "^5.8.2", "vitest": "^3.0.9"}}