import { Dispatch } from '@reduxjs/toolkit'
import { AppRouterInstance } from 'next/dist/shared/lib/app-router-context.shared-runtime'
import { jwtDecode } from 'jwt-decode'

import { openapi2, secureapi } from '../utils'
import {
  setChannelModules,
  setDecodedToken,
  setIsLoginError,
  setLoginErrorMessage,
  setNotification,
} from '../reducers'
import { IDecodeToken } from '../interfaces'

export const refreshToken = async () => {
  try {
    const refreshToken = localStorage.getItem('refreshToken')
      ? localStorage.getItem('refreshToken')
      : ''
    const config = { headers: { Authorization: `Bearer ${refreshToken}` } }
    const resp = await openapi2.post(
      '/backoffice-auth/login/refresh-token',
      {},
      config
    )
    localStorage.setItem('accessToken', resp.data.access_token)
    localStorage.setItem('refreshToken', resp.data.refresh_token)
  } catch (e) {
    console.error('ERROR ON REFRESH TOKEN', e)
  }
}
type TokenObject = {
  accessToken: string
  refreshToken: string
  success: boolean
}
export const handleLogin = async (
  tokenObject: TokenObject,
  dispatch: Dispatch,
  router: AppRouterInstance
) => {
  try {
    const tokenSecret = sessionStorage.getItem('tokenSecret') as string
    const accessTokenResponse = await openapi2.post(
      `/backoffice-auth/login/token`,
      { key: tokenObject.accessToken, secret: tokenSecret }
    )
    localStorage.setItem('accessToken', accessTokenResponse.data.access_token)
    localStorage.setItem('refreshToken', tokenObject.refreshToken)
    const decodedToken: IDecodeToken = jwtDecode(
      accessTokenResponse.data.access_token
    )
    dispatch(setDecodedToken(decodedToken))
    dispatch(
      setNotification({
        message: 'Login success',
        type: 'success',
      })
    )
    return router.push('/landing')
  } catch (e) {
    console.error('ERROR ON LOGIN', e)
    const message = (e as Error).message
    dispatch(setLoginErrorMessage(message))
    dispatch(setIsLoginError(true))
    return router.push('/')
  }
}
export const fetchChannelModules = async (dispatch: Dispatch) => {
  try {
    const modules = await secureapi.get(
      `/backoffice-auth/users/user/channel-modules`
    )
    const moduleList =
      modules?.data && modules?.data?.length > 0 ? modules?.data : []
    dispatch(setChannelModules(moduleList))
  } catch (e) {
    console.error(e)
  }
}
