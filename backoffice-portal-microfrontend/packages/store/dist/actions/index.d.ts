import { Dispatch } from '@reduxjs/toolkit';
import { AppRouterInstance } from 'next/dist/shared/lib/app-router-context.shared-runtime';

declare const refreshToken: () => Promise<void>;
type TokenObject = {
    accessToken: string;
    refreshToken: string;
    success: boolean;
};
declare const handleLogin: (tokenObject: TokenObject, dispatch: Dispatch, router: AppRouterInstance) => Promise<void>;
declare const fetchChannelModules: (dispatch: Dispatch) => Promise<void>;

export { fetchChannelModules, handleLogin, refreshToken };
