import "../chunk-6FNC3XMI.js";

// src/interfaces/notifications.ts
var initialApiError = {
  status: "",
  message: ""
};

// src/interfaces/changeList.ts
var changes = [
  {
    actionee: "<PERSON>",
    action: "create",
    previousState: null,
    actionSubject: "Patsheba Gikunda",
    date: "February 25, 2023 ",
    time: "10:00 PM",
    resource: null,
    comment: "Created customer",
    type: "creation"
  },
  {
    actionee: "<PERSON>",
    action: "create",
    previousState: null,
    actionSubject: "Patsheba Gikunda",
    resource: null,
    date: "February 25, 2023 ",
    time: "10:00 AM",
    comment: null,
    type: "approval"
  },
  {
    actionee: "<PERSON>",
    action: "edit",
    previousState: "Patsheba Gikunda2",
    actionSubject: "Patsheba Gikunda",
    resource: "name",
    date: "February 25, 2023 ",
    time: "10:00 PM",
    comment: "Edited customer",
    type: "approval"
  },
  {
    actionee: "<PERSON>",
    action: "edit",
    previousState: "Patsheba Gikunda2",
    actionSubject: "Patsheba Gikunda",
    date: "February 25, 2023 ",
    time: "10:00 PM",
    resource: "email",
    comment: "Edited customer",
    type: "edited"
  },
  {
    actionee: "<PERSON>e",
    action: "create",
    previousState: null,
    actionSubject: "Patsheba Gikunda",
    resource: null,
    date: "February 25, 2023 ",
    time: "10:00 AM",
    comment: null,
    type: "approval"
  },
  {
    actionee: "John Doe",
    action: "edit",
    previousState: "Patsheba Gikunda2",
    actionSubject: "Patsheba Gikunda",
    resource: "name",
    date: "February 25, 2023 ",
    time: "10:00 PM",
    comment: "Edited customer",
    type: "approval"
  },
  {
    actionee: "John Doe",
    action: "create",
    previousState: null,
    actionSubject: "Patsheba Gikunda",
    resource: null,
    date: "February 25, 2023 ",
    time: "10:00 AM",
    comment: null,
    type: "approval"
  },
  {
    actionee: "John Doe",
    action: "edit",
    previousState: "Patsheba Gikunda2",
    actionSubject: "Patsheba Gikunda",
    resource: "name",
    date: "February 25, 2023 ",
    time: "10:00 PM",
    comment: "Edited customer",
    type: "approval"
  },
  {
    actionee: "John Doe",
    action: "create",
    previousState: null,
    actionSubject: "Patsheba Gikunda",
    resource: null,
    date: "February 25, 2023 ",
    time: "10:00 AM",
    comment: null,
    type: "approval"
  },
  {
    actionee: "John Doe",
    action: "edit",
    previousState: "Patsheba Gikunda2",
    actionSubject: "Patsheba Gikunda",
    resource: "name",
    date: "February 25, 2023 ",
    time: "10:00 PM",
    comment: "Edited customer",
    type: "approval"
  },
  {
    actionee: "John Doe",
    action: "create",
    previousState: null,
    actionSubject: "Patsheba Gikunda",
    resource: null,
    date: "February 25, 2023 ",
    time: "10:00 AM",
    comment: null,
    type: "approval"
  },
  {
    actionee: "John Doe",
    action: "edit",
    previousState: "Patsheba Gikunda2",
    actionSubject: "Patsheba Gikunda",
    resource: "name",
    date: "February 25, 2023 ",
    time: "10:00 PM",
    comment: "Edited customer",
    type: "approval"
  },
  {
    actionee: "Jane Smith",
    action: "delete",
    previousState: "Patsheba Gikunda",
    actionSubject: "Patsheba Gikunda",
    resource: "customer",
    date: "February 26, 2023 ",
    time: "11:00 AM",
    comment: "Deleted customer",
    type: "deletion"
  },
  {
    actionee: "John Doe",
    action: "create",
    previousState: null,
    actionSubject: "Patsheba Gikunda",
    resource: null,
    date: "February 25, 2023 ",
    time: "10:00 AM",
    comment: null,
    type: "approval"
  },
  {
    actionee: "John Doe",
    action: "edit",
    previousState: "Patsheba Gikunda2",
    actionSubject: "Patsheba Gikunda",
    resource: "name",
    date: "February 25, 2023 ",
    time: "10:00 PM",
    comment: "Edited customer",
    type: "approval"
  }
];
export {
  changes,
  initialApiError
};
