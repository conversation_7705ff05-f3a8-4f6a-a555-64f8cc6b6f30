{"name": "@dtbx/eslint-config", "version": "0.0.1", "files": ["library.js", "next.js", "react-internal.js"], "devDependencies": {"@typescript-eslint/eslint-plugin": "^8.27.0", "@typescript-eslint/parser": "^8.27.0", "eslint-config-prettier": "^10.1.1", "eslint-config-turbo": "^2.3.7", "eslint-plugin-only-warn": "^1.1.0", "eslint-plugin-prettier": "^5.2.4", "prettier": "^3.5.3", "typescript": "^5.8.2"}}