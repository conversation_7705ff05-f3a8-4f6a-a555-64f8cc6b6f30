'use client'
import {
  App<PERSON><PERSON>,
  Chip,
  <PERSON>con<PERSON>utton,
  <PERSON>u,
  MenuItem,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
} from '@mui/material'
import Image from 'next/image'
import React, { useEffect, useRef, useState } from 'react'
import AccountCircleIcon from '@mui/icons-material/AccountCircle'

const topNavItems = [
  {
    label: 'Settings',
    icon: '/icons/settings.svg',
  },
  {
    label: 'FAQs',
    icon: '/icons/help-circle.svg',
  },
  {
    label: 'Notifications',
    icon: '/icons/bell.svg',
  },
]

interface IDecodeToken {
  username?: string
  last_name: string
  first_name: string
  user_id: string
  authorities: string[]
  sub: string
  iat: number
  exp: number
  resources?: IResource[]
}

interface IResource {
  resourceType: string
  resourceIds: string[]
}

export const InternalNavBar = ({
  profile,
  refreshToken,
  refreshInterval = 1000 * 60 * 20,
}: {
  profile: IDecodeToken
  handleLogout?: () => void
  refreshToken: () => void
  refreshInterval?: number
}) => {
  const handleOpenUserMenu = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorElUser(event.currentTarget)
  }

  const handleCloseUserMenu = () => {
    setAnchorElUser(null)
  }
  const formRef = useRef<HTMLFormElement>(null)

  const [anchorElUser, setAnchorElUser] = useState<null | HTMLElement>(null)
  useEffect(() => {
    const refresh = setInterval(refreshToken, refreshInterval)
    return () => clearInterval(refresh)
  }, [])

  const handleLogout = (
    e: React.MouseEvent<HTMLButtonElement> | React.MouseEvent<HTMLLIElement>
  ) => {
    e.preventDefault()
    formRef.current?.submit()
    //clear storage
    localStorage.clear()
    sessionStorage.clear()
  }

  return (
    <AppBar
      position="static"
      sx={{
        boxShadow: 'none',
        height: '70px',
        backgroundColor: '#FFFFFF',
        px: '2%',
        display: 'flex',
        flexDirection: 'row',
        borderBottom: '1px solid var(--Color-Stroke-Stroke-2, #E3E4E4)',
        justifyContent: 'space-between',
      }}
    >
      <Toolbar
        disableGutters
        sx={{
          justifyContent: 'flex-end',
          alignItems: 'centre',
          border: 'none',
          boxShadow: 'none',
          width: '100%',
        }}
      >
        
        <Tooltip title="Open profile">
          <IconButton
            onClick={handleOpenUserMenu}
            sx={{
              p: 0,
              borderRadius: '16px',
            }}
          >
            <Chip
              icon={
                <AccountCircleIcon
                  sx={{
                    color: '#000A12',
                    fontSize: 32,
                    marginLeft: '0px !important',
                  }}
                />
              }
              label={`${(profile && profile.first_name) || ''} ${
                (profile && profile.last_name) || profile.username
              }`}
              sx={{
                fontWeight: '500',
                fontSize: '14px',
                backgroundColor: '#EAECF0',
                gap: '8px',
              }}
            />
          </IconButton>
        </Tooltip>
        <form
          method="POST"
          ref={formRef}
          action={`${process.env.NEXT_PUBLIC_OPEN_API_BASE_URL}/users/logout`}
        >
          <input
            type="hidden"
            name="redirectUrl"
            value={`${process.env.NEXT_PUBLIC_POST_LOGOUT_REDIRECT_URL}`}
          />
          <Menu
            sx={{
              mt: '45px',
              display: 'flex',
              flexDirection: 'column',
            }}
            id="menu-appbar"
            anchorEl={anchorElUser}
            anchorOrigin={{
              vertical: 'top',
              horizontal: 'right',
            }}
            keepMounted
            transformOrigin={{
              vertical: 'top',
              horizontal: 'right',
            }}
            open={Boolean(anchorElUser)}
            onClose={handleCloseUserMenu}
          >
            <MenuItem
              component="button"
              type="submit"
              onClick={(e) => handleLogout(e)}
            >
              Logout
            </MenuItem>
          </Menu>
        </form>
      </Toolbar>
    </AppBar>
  )
}
