import type { Preview } from '@storybook/react-vite'
import { ThemeConfig } from '../src/theme'
import React from 'react'

const preview: Preview = {
  parameters: {
    layout: 'centered',
    options: {
      storySort: {
        method: 'alphabetical',
        locales: 'en-US',
      },
    },
    backgrounds: {
      options: {
        dark: { name: 'Dark', value: '#000000' },
        light: { name: 'Light', value: '#FFFFFF' },
      },
    },
    controls: {
      matchers: {
        color: /(background|color)$/i,
        date: /Date$/i,
      },
    },
  },
  //👇 Enables auto-generated documentation for all stories
  tags: ['autodocs'],
  // Decorators for all stories
  decorators: [
    (Story) => (
      <ThemeConfig>
        <Story />
      </ThemeConfig>
    ),
  ],
}

export default preview
