export const usePathname = () => '/dashboard/settings/profile'

export const useRouter = () => ({
  push: async () => true,
  replace: async () => true,
  refresh: () => {},
  back: () => {},
  forward: () => {},
  prefetch: async () => {},
  pathname: '/dashboard/settings',
  route: '/dashboard/settings',
  query: {},
  asPath: '/dashboard/settings',
  basePath: '',
  isFallback: false,
  isReady: true,
  events: {
    on: () => {},
    off: () => {},
    emit: () => {},
  },
  isPreview: false,
  isLocaleDomain: false,
})

export const useSearchParams = () => new URLSearchParams()
export const useParams = () => ({})

// Mock useServerInsertedHTML
export const useServerInsertedHTML = (fn: () => void) => {
  // This hook is only relevant in server rendering
  // So in Storybook (client-side), it can be a no-op or run immediately
  fn()
}
