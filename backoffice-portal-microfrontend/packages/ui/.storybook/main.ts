import type { StorybookConfig } from '@storybook/react-vite'
import { join, dirname, resolve } from 'path'
import { config as dotenvConfig } from 'dotenv'
import path from 'path'

// Load .env.storybook from root
dotenvConfig({ path: resolve(__dirname, '../.env.storybook') })

/**
 * This function is used to resolve the absolute path of a package.
 * It is needed in projects that use Yarn PnP or are set up within a monorepo.
 */
function getAbsolutePath(value: string): any {
  return dirname(require.resolve(join(value, 'package.json')))
}
const config: StorybookConfig = {
  stories: ['../src/**/*.mdx', '../src/**/*.stories.@(js|jsx|mjs|ts|tsx)'],
  addons: [
    getAbsolutePath('@storybook/addon-onboarding'),
    getAbsolutePath('@chromatic-com/storybook'),
    getAbsolutePath("@storybook/addon-docs")
  ],
  framework: {
    name: getAbsolutePath('@storybook/react-vite'),
    options: {},
  },
  typescript: {
    reactDocgen: 'react-docgen-typescript',
  },
  async viteFinal(config, { configType }) {
    // Inject NEXT_PUBLIC env variables manually
    config.define = {
      ...(config.define || {}),
      'process.env.NEXT_PUBLIC_OPEN_API_BASE_URL': JSON.stringify(
        process.env.NEXT_PUBLIC_OPEN_API_BASE_URL
      ),
      'process.env.NEXT_PUBLIC_POST_LOGOUT_REDIRECT_URL': JSON.stringify(
        process.env.NEXT_PUBLIC_POST_LOGOUT_REDIRECT_URL
      ),
    }

    config.resolve = {
      ...(config.resolve || {}),
      alias: {
        ...(config.resolve?.alias || {}),
        'next/navigation': path.resolve(__dirname, '__mocks__/navigation.ts'),
        'next/image': require.resolve('./__mocks__/NextImage.tsx'),
      },
    }

    return config
  },
}
export default config
