'use client'

import { configureStore } from '@reduxjs/toolkit'
import { persistReducer } from 'redux-persist'
import { TypedUseSelectorHook, useDispatch, useSelector } from 'react-redux'

import rootReducer from './reducers/store'
import storage from 'redux-persist/lib/storage'
import { createNoopStorage } from '@dtbx/store'

// Use localStorage on client, noop storage on server
const persistStorage = typeof window !== 'undefined' ? storage : createNoopStorage()

export const persistConfig = {
  key: 'backoffice',
  storage: persistStorage,
  blacklist: ['notification', 'overlays', 'chargeConfiguration'],
}

export const persistedReducer = persistReducer(persistConfig, rootReducer)

const store = configureStore({
  reducer: persistedReducer,
})

export type RootState = ReturnType<typeof store.getState>
export type AppDispatch = typeof store.dispatch
export const useAppDispatch = () => useDispatch<AppDispatch>()
export const useAppSelector: TypedUseSelectorHook<RootState> = useSelector
export default store
